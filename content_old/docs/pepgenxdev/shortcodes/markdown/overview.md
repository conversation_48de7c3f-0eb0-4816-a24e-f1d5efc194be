---
title:      Overview
weight:     1
---

# **Overview**

Nearly all Markdown applications support the basic syntax outlined in the original Markdown design document.
<PERSON> uses the **Goldmark Markdown** processor which is fully CommonMark-compliant.

{{< expand "GitHub" "..." >}}
**GitHub**: [yuin/goldmark](https://github.com/yuin/goldmark/)
**About**: A markdown parser written in Go. Easy to extend, standard(CommonMark) compliant, well structured.
{{< /expand >}}

# **PepGenX standard notation**

## **Headings**

First heading of a page must always start with ```#```.
All subsequent headings at the same level as first heading should also use ```#```.
Every heading must always be BOLD ```**heading**```.

```tpl
# **First Heading**
## **First Subsection**
# **Second Heading**
```

Allowed maximum depth of headings is 4 ```####```. H4 has the same font size as the website template font size.