---
weight: 1
title: "<PERSON>rzun"
---

# **To be done**

- Run web based markdown editor for developers writing documentation
- PepGenX Services Status page
- Required **container interfaces** and **SIGTERM** signals handling
    - Lifecycle hooks
- AKS deisgn and configuration for different **Data Classification**
- **Network Policy** configuration
- **Service Account** with no access
- **Function Container** in an application POD
- **DNS** on node
- **Topology aware routing**
- **Service internal traffic** policy

Istio / Kiali / Jaeger

Consumer access using Consumer pattern
Employee access

Text
Image/video
Voice
Multimodal

- ml/gen ai logic that can extend your microservice
- Ml/gen PepsiCo shared services
- Backend for end client chat app
- Backend for ML powered client chat applications

Total number of used tokens per application/api
Max number tokens per app request
Max numer of tokens per minute

Rag document authorizer - after retrieving document list, require user name, group

Rag service pattern templates: 
- basic rag
- Rag with graph
- Etc

Shared Helper service