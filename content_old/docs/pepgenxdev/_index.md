---
weight: 1
bookFlatSection: true
bookCollapseSection:    true
title: "PepGenX Platform Developers"
---

# **PepGenX Platform Development Team Site**

## **Platform Overview**

PepGenX platform offers a comprehensive suite of tools and services designed to empower developers in building innovative Artificial Intelligence-powered applications. By harnessing the power of Machine Learning and Generative AI, we unlock a world of possibilities for what these applications can achieve.

## **Core Components**

- AI-powered extension for Microservices
- PepGenX Shared Services
- Backend for End Client Chat Apps
- Backend for ML-powered Client Chat Apps

## **Target audience**

This section of the documentation website serves as a comprehensive resource for various technical professionals involved in the PepGenX Platform. 

* **Architects:** Individuals responsible for designing, planning, and overseeing the overall infrastructure and technical roadmap of the PepGenX Platform.
* **Developers:** Programmers who build, test, and maintain the PepGenX Platform's codebase. They ensure the platform functions as intended and meets user requirements.
* **Administrators:**  Associates tasked with managing user access, configurations, and system health of the PepGenX Platform. They ensure the platform runs smoothly and securely.
* **Operators:**  Associates who monitor the day-to-day operations of the PepGenX Platform and its components. They identify and troubleshoot any issues that may arise to maintain platform stability and performance.
