---
weight: 8
title: "Azure Services"
---

# **Microsoft Azure Services**

### **Introduction**

PepGenX Platform is designed to seamlessly integrate with various Azure services, enabling developers to build robust, scalable, and secure applications. Below, you'll find detailed insights into the core services provided by Microsoft Azure, which form the backbone of our platform infrastructure.

### **Azure Kubernetes Services**

1. ["Introduction to Azure Kubernetes Service"](https://learn.microsoft.com/en-us/training/modules/intro-to-azure-kubernetes-service/)

### **Azure Container Registries**

1. ["Introduction to Azure Container Registry"](https://learn.microsoft.com/en-us/azure/container-registry/container-registry-intro)

### **Azure Storage Account**

1. ["Introduction to Azure Storage"](https://learn.microsoft.com/en-us/azure/storage/common/storage-introduction)

### **Azure Key Vault**

1. ["About Azure Key Vault"](https://learn.microsoft.com/en-us/azure/key-vault/general/overview)

### **Azure AI Search**

1. ["What's Azure AI Search?"](https://learn.microsoft.com/en-us/azure/search/search-what-is-azure-search)
2. ["Fundamentals of Knowledge Mining and Azure AI Search"](https://learn.microsoft.com/en-us/training/modules/intro-to-azure-search/)

### **Azure Content Safety Studio**

1. ["What is Azure AI Content Safety?"](https://learn.microsoft.com/en-us/azure/ai-services/content-safety/overview) 

### **Azure AI Studio**

1. ["Introduction to Azure AI Studio"](https://learn.microsoft.com/en-us/training/modules/introduction-to-azure-ai-studio/)