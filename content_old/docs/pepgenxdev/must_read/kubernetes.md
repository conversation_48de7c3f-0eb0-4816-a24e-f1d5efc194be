---
weight: 2
title: "Kubernetes"
---

# **Kubernetes container orchestration**

### **Introduction**
If you're new to Kubernetes, here's a learning path to get you started:

- **Fundamentals**: Begin by understanding the core concepts of Kubernetes. This foundation will be crucial when you delve into more advanced topics like Admission Controllers and Operators. 
>  - Kubernetes basic technical concepts
>  - Kubernetes components, functionality, their interactions, and implementation options
- **Admission Controllers and Operators**: These two concepts are the crucial components powering PepGenX platform. Admission Controllers (Gatekeepers) intercept requests to the Kubernetes API server. They enforce predefined rules before allowing creation or modification within the cluster. Operators (Application Lifecycle) specialize in managing the complex lifecycles of applications running on Kubernetes from deployment and scaling to updates and health checks. 
>  - APIs and configuration management
>  - Examples in Python

### **Kubernetes basic technical concepts**

1. ["Containers vs Pods"](https://www.youtube.com/watch?v=vxtq_pJp7_A).
2. ["KubeAcademy"](https://kube.academy/courses).
3. ["KUBE Campus"](https://kubecampus.io/kubernetes/courses/).
4. IBM ["Introduction to Containers w/ Docker, Kubernetes & OpenShift"](https://www.coursera.org/learn/ibm-containers-docker-kubernetes-openshift).
5. Linux Foundation ["LinuxFoundationX: Introduction to Kubernetes"](https://www.edx.org/learn/kubernetes/the-linux-foundation-introduction-to-kubernetes).

### **Kubernetes components, functionality, their interactions, and implementation options**

1. The official [Kubernetes documentation](https://kubernetes.io/docs/concepts/overview/).
2. E-book ["Kubernetes from scratch"](https://github.com/rohitg00/DevOps_Books/blob/main/K8s_muhamad_eiemam.pdf) by Muhamad Eiemam.
3. E-book ["Kubernetes Patterns"](https://www.redhat.com/en/resources/oreilly-kubernetes-patterns-ebook?intcmp=701f2000000tjyaAAA) by Bilgin Ibryam and Roland Huß.

### **APIs and configuration management**

1. ["Introduction to Kubernetes for Developers"](https://www.youtube.com/watch?v=iIPPjzkPrss).
2. ["Kubernetes Operators Explained"](https://www.youtube.com/watch?v=UmIomb8aMkA).
3. ["What The Heck Are Kubernetes Resources, CRs, CRDs, Operators, etc.?"](https://www.youtube.com/watch?v=aM2Y9m2Kazk).
4. ["Service and Network APIs (Service, Ingress, GatewayAPI)"](https://www.youtube.com/watch?v=-1H0BeN9hIk).
5. ["Continuous Deployment to Kubernetes with ArgoCD"](https://www.youtube.com/watch?v=744HhkNnXHw).
6. ["Customizing and Extending the Kubernetes API with Admission Controllers"](https://www.youtube.com/watch?v=P7QAfjdbogY)
7. ["Admission Controllers Reference"](https://kubernetes.io/docs/reference/access-authn-authz/admission-controllers/)
8. ["Custom Resources"](https://kubernetes.io/docs/concepts/extend-kubernetes/api-extension/custom-resources/)
9. ["Operator pattern"](https://kubernetes.io/docs/concepts/extend-kubernetes/operator/)
10. ["Kopf: Kubernetes Operators Framework"](https://kopf.readthedocs.io/en/latest/)
11. ["Kubebuilder - SDK for building Kubernetes APIs using CRDs"](https://kubebuilder.io/introduction)
12. ["Kopf alternatives"](https://kopf.readthedocs.io/en/stable/alternatives/)

{{< hint info >}}
### **Examples in Python**
1. Operator
    - ["Kubernetes Operator Pythonic Framework (Kopf)"](https://github.com/nolar/kopf)
    - ["Kubebuilder - SDK for building Kubernetes APIs using CRDs"](https://github.com/kubernetes-sigs/kubebuilder)
    - ["Trivy Operator"](https://github.com/devopstales/trivy-operator)
2. Admission Controller
    - ["Kubernetes Admission Controller Demo in Python"](https://github.com/garethr/kubernetes-webhook-examples)
    - ["Writing a Kubernetes Validating Admission Webhook"](https://www.youtube.com/watch?v=RFQ30mhdf3c)

{{< /hint >}}