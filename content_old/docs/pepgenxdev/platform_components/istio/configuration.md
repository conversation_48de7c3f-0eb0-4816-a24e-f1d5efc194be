---
title:                  "Configuration"
weight:                 1
---

## Enable sidecar injection

To automatically install sidecar to any new pods, you need to annotate your namespaces with the revision label corresponding to the control plane revision currently installed.

If you're unsure which revision is installed, use:
Bash

az aks show --resource-group ${RESOURCE_GROUP} --name ${CLUSTER}  --query 'serviceMeshProfile.istio.revisions'

Apply the revision label:
Bash

kubectl label namespace default istio.io/rev=asm-X-Y

## Important

The default istio-injection=enabled labeling doesn't work. Explicit versioning matching the control plane revision (ex: istio.io/rev=asm-1-18) is required.

For manual injection of sidecar using istioctl kube-inject, you need to specify extra parameters for istioNamespace (-i) and revision (-r). For example:
Bash

kubectl apply -f <(istioctl kube-inject -f sample.yaml -i aks-istio-system -r asm-X-Y) -n foo
