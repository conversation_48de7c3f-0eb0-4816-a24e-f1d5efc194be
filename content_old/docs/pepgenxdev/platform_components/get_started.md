---
weight: 1
title: "Get Started"
---

# **Get Started**

## **PepGenX request parameters**

External REQUEST parameters:

| Parameter             | Description | Validation     | Used by |
| :---------             | :----------- | :---------- | :----------- |
| OKTA token            |authenticate a call from PepsiCo application | Registered in OKTA | Apigee and Istio |
| Microservice ID       | authenticate PepGenX registered application | Registered in PepGenX | IAM Service |
| Microservice Secret   | authenticate PepGenX registered application | Registered in PepGenX | IAM Service |
| EndUser ID            | Maybe EndUser OKTA token | Registered in OKTA | PepGenX Service delivery |

## **Flow**

{{< mermaid class="optional" >}}
flowchart TD
StorageAccountinVNet -->|TCP 443| APIG
subgraph Region: SCUS
subgraph VNet: sharedservice
APIG -->|TCP 443| VM1
end
VM1 --> WEB

end

{{< /mermaid >}} 

{{< mermaid class="optional" >}}
flowchart TD
    Confidential/Restricted
    REQ[Request] -->|API Call| API(Apigee)
    API --> IGG{IngressGateway<br>Standard Apps}
    subgraph API Applications
    IGG --> IGSTD(Standard Gateway)
    IGSTD --> VS1(VirtualService API1)
    IGSTD --> VS2(VirtualService API2)
    end
    API --> IGP{IngressGateway<br>PepGenX Services}
    subgraph PepGenX Services
    IGP -->IGPEP(PepGenX Gateway)
    IGPEP --> VSP1(VirtualService PepGX1)
    IGPEP --> VSP2(VirtualService PepGX2)
    end
    API --> IGD1{IngressGateway<br>Heavy-loaded App}
    API --> IGD2{IngressGateway<br>Heavy-loaded App}
    subgraph Heavy Loaded Application
    subgraph Heavy App1
    IGD1 --> VSH1
    end
    subgraph Heavy App2
    IGD2 --> VSH2
    end
    end
{{< /mermaid >}} -->

{{< figure src="/images/pepgenx_flow.svg" 
alt="Tree of Life Photo by Bino"
link="/images/pepgenx_flow.svg"
title="Click on the image to make it bigger" >}}

<!--
https://www.mermaidflow.app/editor
-->

<!-- {{< mermaid class="optional" >}}
sequenceDiagram
    autonumber
    participant CA as Client Application
    participant AG as Apigee
    participant OK as Okta
    participant IN as K8s Ingress
    participant IST as Istio
    participant TC as Traffic Controller
    participant CRD as Traffic Controller CRD
    participant JWT as JWT Auth service
    participant SA as Security Analyzer
    participant CAPI as Client Application API
    CA-->AG: Call Application API through Apigee
    AG->>+OK: Check Token
    OK-->>-AG: Token validated
    AG->>IN: Call PepGenX ingress
    IN->>IST: Load ballance to K8s nodes
    alt If Request originated FROM Apigee
        Note right of IST: Authorization Policy
    IST->>TC: Route to Traffic Controller
    else If Request originated NOT FROM Apigee
        Note left of IST: Authorization Policy
    IST->>+OK: Check Token
    OK-->>-IST: Token validated
        Note right of IST: Authorization Policy
    IST->>TC: Route to Traffic Controller
    end
    TC->>+CRD: Check Microservice_ID and Microservice_Secret
    alt If Microservice workflow definition exist
        Note right of TC: Imposed directly or inherited from a group
    CRD-->>TC: Return Traffic Controller workflow for the Microservice
    else If workflow doesn't exist DEFAULT is returned
    CRD-->>-TC: Return default Traffic Controller workflow
    end
    rect RGB(230,230,230)
        Note right of SA: Microservice Workflow
    rect RGB(245,245,245)
    TC->> IST: Call JWT Auth
        Note right of IST: Authorization Policy
    IST->>+JWT: Generate PepGenX session token
    JWT->>+CRD: Check Microservice permissions
    CRD-->>-JWT: Return Microservice permissions
    JWT-->>-TC: Return generated session token
    end
    rect RGB(245,245,245)
    TC->>+IST: Call Security Analyzer    
        Note right of IST: Authorization Policy
    IST->>+JWT: Check session token
    JWT-->>-IST: Token validated
    IST->>+SA: Check REQUEST content against Security Policies
    SA-->>-TC: Returns OK, Denied or Mutuated request
    end
    rect RGB(245,245,245)
    TC->>+IST: Call Client Microservice
        Note right of IST: Authorization Policy    
    IST->>+JWT: Check session token
    JWT-->>-IST: Token validated
    IST->>+CAPI: Send REQUEST to Client Microservice
    CAPI-->>-TC: RESPONSE from Client Microservice 
    end
    rect RGB(245,245,245)
    TC->>+IST: Call Security Analyzer    
        Note right of IST: Authorization Policy
    IST->>+JWT: Check session token
    JWT-->>-IST: Token validated
    IST->>+SA: Check RESPONSE content against Security Policies
    SA-->>-TC: Returns OK, Denied or Mutuated request
    end    
    TC-->>IST: RESPONSE from Client Microservice
    end    
    IST-->>IN: RESPONSE from Client Microservice
    IN-->>AG: RESPONSE from Client Microservice
    AG-->>CA: RESPONSE from Client Microservice
{{< /mermaid >}} -->

<!-- {{< mermaid class="optional" >}}
sequenceDiagram
    autonumber
    box PepGenX Platform
    participant JWT as JWT Auth service
    participant LMF as LLM Framework
    participant AL as API Abstraction Layer
    participant SD as Service Delivery
    participant LM as Language Model
    end
    LMF->>JWT: Validate session token
    JWT-->>LMF: Token validated
    LMF->>AL: Call API
    AL->>JWT: Validate session token
    JWT-->>AL: Token validated
    AL->>SD: Call Service Provider
    SD->>JWT: Validate session token
    JWT-->>SD: Token validated
        Note right of SD: TEST DRAWING
    alt Request
    SD->>LM: Call LLM service
    else Response
    LM->>SD: not authorized
    end
{{< /mermaid >}} -->