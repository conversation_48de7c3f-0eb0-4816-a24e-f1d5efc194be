---
weight: 1
title: "Test environment"
bookHidden: true
---

PAGE 378

# **Installation**

### **K8S**

| Parameter             | Selection |
| :---------            | :----------- |
| Networking            | kubenet |
| Network Policy        | none |
| Istio                 | manual install Ingress and Egress |
| Istio API             | Istio API (Virutal Service, Gateway)|
| Azure Policy          | enabled |

1. Istio Gateway handles the L4 and L5 concerns, while VirtualService handles the L7 concerns
2. Should we use mutual TLS for Restricted?
3. By default, Istio allows any traffic out of the service mesh
    - meshConfig.outboundTrafficPolicy.mode = REGISTRY_ONLY (ALLOW_ANY)
    - requires ServiceEntry with location: MESH_EXTERNAL
4. What response headers will be sent to end customer App


- istioctl x precheck
- istioctl install --set profile=demo -y
- istioctl verify-install
- kubectl apply -f ./samples/addons
- kubectl label namespace NAMESPACENAME istio-injection=enabled

Important:
- VirtualService / Retries
- DestinationRule / Filetring and routing (Load Balancing)
    - clien side (round robin/random/weight least)
    - least connection performs better than both random and round robin
        - The least-connection takes into account the latencies and queue depth
    - Fortio load-testing client (Fortio (http://github.com/fortio/fortio))
        - brew install fortio
    - Locality LB requires nodes to be labeled - should be used?
        topology.kubernetes.io/region
        topology.kubernetes.io/zone
- Retry policies (for layered system) / Status codes / Timeouts / No. of attempts / Request hedging / Max queue, connections
    - skipping retries with Circuit breaking
- Istio OutlierDetection instead of Apigee
- Handling deployemnt&release (iteratively) of new versions (dark launch):
    - PepGenX components
    - Sector Microservices
- Trffic shifting based on:
    - header matching
    - weights (eg.10%)
    - using Flagger (https://flagger.app) - requires Prometheus
- Trafic mirroring usecases
- The virtual service applies to all sidecars in the mesh.
    ```tpl
    kind: VirtualService
    spec:
    gateways:
    - mesh
    ```
- Enchanced observability
    - kube-prometheus (https://github.com/prometheus-operator/kube-prometheus)
        - helm repo add prometheus-community https:/./prometheus-community.github.io/helm-charts
        - helm repo update
        - kubectl create ns prometheus
        - helm install prom prometheus-community/kube-prometheus-stack --version 13.13.1 -n prometheus -f ch7/prom-values.yaml
- Istio Security
    - PeerAuthentication = STRICT (X)/ PERMISSIVE
        - Mesh-wide (X)
        - Namespace-wide
        - Workload-specific
            ```tpl
            apiVersion: "security.istio.io/v1beta1"
            kind: "PeerAuthentication"
            metadata:
            name: "default"
            namespace: "istio-system"
            spec:
            mtls:
                mode: STRICT
            ```
    - RequestAuthentication
    - AuthorizationPolicy
        - https://istio.io/latest/docs/reference/config/security/authorization-policy
        - https://istio.io/latest/docs/reference/config/security/conditions
        - selector - subset of workloads to which the policy applies
            ```tpl
            selector:
              matchLabels:
                app: webapp
            ```
        - action - ALLOW, DENY, or CUSTOM
        - rules
            - from - source
                - principals / notPrincipals (["cluster.local/ns/istioinaction/sa/webapp"])
                - requestPrincipals (from JWT)
                - namespaces
                - ipBlocks
            - to - target
            - when - list of conditions that need to be met after the rule has matched
                - operation
                  - methods:
                - key: (istio specific)
                  values:
                - value-match
                    - Exact matching
                    - Prefix matching (/api/catalog*)
                    - Suffix matching (*.istioinaction.io)
                    - Presence matching (*)
        - ![Istio policy](/images/istio_policy.png)
    - mutual TLS
- EnvoyFilter
    - HttpConnectionManager HTTP filters (https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/http_filters)
    - Lua scripts
    - Rate limiting
- Sidecar 
    - workloadSelector
        - mesh
        - namespace
        - workloadSelector
    - ingress
    - egress
    - outboundTrafficPolicy
        - REGISTRY_ONLY
        - ALLOW_ANY
- External Authorization: Open Policy Agent 
    - internal request authorizatio using envoyExtAuthz
        - includeHeadersInCheck: ["should include appname/app token or sessionID"] (for session replica synch would be required)
- Istio deiscovery namespace selection:
    - matchLabels
        ```tpl
        apiVersion: install.istio.io/v1alpha1
        kind: IstioOperator
        metadata:
          namespace: istio-system
        spec:
          meshConfig:
            discoverySelectors:
              - matchLabels:
                istio-discovery: enabled
        ```
    - matchExpressions
        ```tpl
        apiVersion: install.istio.io/v1alpha1
        kind: IstioOperator
        metadata:
          namespace: istio-system
        spec:
          meshConfig:
            discoverySelectors:
              - matchExpressions:
                - key: istio-exclude
                  operator: NotIn
                  values:
                    - "true"
        ```
- Autoscaling istiod deployment
    - istiod because it initiates a 30-minute connection with the workloads, which is used to configure and update the proxies using the Aggregated Discovery Service (ADS). So, newly spun up istiod replicas don’t receive any load until the connections between the service proxies and the previous pilot expire
- istio-init - requires elevated permissions to configure traffic redirection to the Envoy proxy. 


- Istioctl: 
    - e.g. brew install istioctl
- Helm:
    - helm repo add istio https://istio-release.storage.googleapis.com/charts
    - helm repo update
- Istio Operator
    - istioctl operator init
    - configuration yaml
    ```tpl
    apiVersion: install.istio.io/v1alpha1
    kind: IstioOperator
    metadata:
    name: istio-operator
    namespace: istio-system
    spec:
    profile: default
    components:
        ingressGateways:
        - name: istio-ingressgateway
        enabled: true
        egressGateways:
        - name: istio-egressgateway
        enabled: true
        addonComponents:
        kiali:
            enabled: true
        grafana:
            enabled: true
        prometheus:
            enabled: true
        tracing:
            enabled: true
    values:
        global:
        istioNamespace: istio-system
        configNamespace: istio-config
        telemetryNamespace: istio-telemetry
        gateways:
        istio-ingressgateway:
            autoscaleEnabled: true
            resources:
            requests:
                cpu: 100m
                memory: 128Mi
        istio-egressgateway:
            autoscaleEnabled: true
            resources:
            requests:
                cpu: 100m
                memory: 128Mi
        kiali:
        dashboard:
            enableGrafana: true
            enablePrometheus: true
        grafana:
        security:
            enabled: true
        prometheus:
        security:
            enabled: true
        tracing:
        provider: jaeger
        jaeger:
            tag:
            enabled: true
    ```
    - kubectl apply -f istio-operator.yaml
- Istio base:
    - kubectl create namespace istio-system
    - helm install istio-base istio/base -n istio-system --set defaultRevision=default
    - helm install istiod istio/istiod -n istio-system --wait
- Istio ingress
    - kubectl create namespace istio-ingress
    - helm install istio-ingress istio/gateway -n istio-ingress --wait


#### Additional K8s integrations

- cert-manager (without Prometheus)
    - helm repo add jetstack https://charts.jetstack.io --force-update
    - https://istio.io/latest/docs/ops/integrations/certmanager/
    - helm install \
        cert-manager jetstack/cert-manager \
        --namespace cert-manager \
        --create-namespace \
        --version v1.15.1 \
        --set crds.enabled=true \
        --set prometheus.enabled=false
- Prometheus
    - kubectl apply -f https://raw.githubusercontent.com/istio/istio/release-1.22/samples/addons/prometheus.yaml
- Grafana
    - kubectl apply -f https://raw.githubusercontent.com/istio/istio/release-1.22/samples/addons/grafana.yaml
- Kiali
    - kubectl apply -f https://raw.githubusercontent.com/istio/istio/release-1.22/samples/addons/kiali.yaml
    - istioctl dashboard kiali -n aks-istio-system
    - The issue occurs because Kiali now expects the istiod pod to have the app.kubernetes.io/name=istiod label. However, istiod is deployed with app=istiod
- Jaeger
    - kubectl apply -f https://raw.githubusercontent.com/istio/istio/release-1.22/samples/addons/jaeger.yaml
    - updating istio configuration
    ```tpl
    data:
        mesh: |-
            defaultConfig:
            discoveryAddress: istiod.istio-system.svc:15012
            defaultProviders:
            metrics:
            - prometheus
            enablePrometheusMerge: true
            rootNamespace: istio-system
            trustDomain: cluster.local
            tracing:
            sampling: 100.0
            zipkin:
                address: "jaeger-collector.default.svc.cluster.local:9411"
        meshNetworks: 'networks: {}'
    ```

#### **Configuration**

- meshConfig.outboundTrafficPolicy.mode
    -  If the option is set to REGISTRY_ONLY, then the Istio proxy blocks any host without an HTTP service or service entry defined within the mesh. ALLOW_ANY is the default value
    ```tpl
    spec:
    meshConfig:
        outboundTrafficPolicy:
        mode: REGISTRY_ONLY
    ```
    - access external service
    ```tpl
    apiVersion: networking.istio.io/v1alpha3
    kind: ServiceEntry
    metadata:
        name: httpbin-ext
    spec:
        hosts:
        - httpbin.org
        ports:
        - number: 80
            name: http
            protocol: HTTP
        resolution: DNS
        location: MESH_EXTERNAL
    ```
    ```tpl
    apiVersion: networking.istio.io/v1alpha3
    kind: ServiceEntry
    metadata:
        name: google
    spec:
        hosts:
        - www.google.com
        ports:
        - number: 443
            name: https
            protocol: HTTPS
        resolution: DNS
        location: MESH_EXTERNAL
    ```
    ```tpl
    apiVersion: gateway.networking.k8s.io/v1
    kind: HTTPRoute
    metadata:
        name: httpbin-ext
    spec:
    parentRefs:
    - kind: ServiceEntry
        group: networking.istio.io
        name: httpbin-ext
    hostnames:
    - httpbin.org
    rules:
    - timeouts:
        request: 3s
      backendRefs:
      - kind: Hostname
        group: networking.istio.io
        name: httpbin.org
        port: 80
    ```
