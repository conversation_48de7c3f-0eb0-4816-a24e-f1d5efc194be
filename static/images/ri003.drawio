<mxfile host="Electron" modified="2023-07-19T09:43:50.205Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/21.6.1 Chrome/112.0.5615.204 Electron/24.6.1 Safari/537.36" etag="XTBJM188-FVGcKmxobTn" version="21.6.1" type="device">
  <diagram name="RI003" id="Sex0QkKaUgo0coor4X9O">
    <mxGraphModel dx="3036" dy="2106" grid="1" gridSize="5" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2750" pageHeight="1600" math="0" shadow="0">
      <root>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-0" />
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-1" value="Network" parent="RsPSmVlmxrs_U-s3XhF0-0" />
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-2" value="Environment:&lt;span style=&quot;font-weight: normal;&quot;&gt;&amp;nbsp;Production&lt;/span&gt;&lt;br&gt;Data Classification:&lt;span style=&quot;font-weight: normal;&quot;&gt;&amp;nbsp;Confidential&lt;/span&gt;" style="text;whiteSpace=wrap;fontSize=23;fontFamily=Verdana;fontColor=#333333;html=1;fontStyle=1" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1505.0000000000005" y="375" width="420" height="40" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-3" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#BBBBBB;fillColor=#F7FAFD;fillStyle=solid;strokeWidth=1;dashed=1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="562.89" y="445" width="1342.11" height="965" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-4" value="&lt;b&gt;Region:&lt;/b&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;&amp;nbsp;SCUS&lt;/span&gt;&lt;/span&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=0;fontSize=14;fontFamily=Verdana;labelBackgroundColor=none;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="568.8900000000008" y="445" width="114" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-5" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#B3B3B3;dashed=1;fontFamily=Verdana;fillColor=default;strokeWidth=2;dashPattern=1 1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="596.3900000000001" y="507.5" width="458.61" height="277.5" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-6" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#CCCCCC;fillColor=#FAFAFA;fontFamily=Verdana;strokeWidth=1;dashed=1;dashPattern=1 1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="616.8900000000001" y="543.5" width="420" height="221.5" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-7" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#3399FF;dashed=1;dashPattern=1 1;fontFamily=Verdana;fillColor=default;strokeWidth=1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="636.2800000000002" y="575.8800000000001" width="380.61" height="169.12" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-8" value="&lt;b&gt;VNet:&lt;/b&gt;&amp;nbsp;pep-sharedservice-01-scus-vnet" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=0;fontSize=14;fontColor=#000000;fontFamily=Verdana;labelBackgroundColor=none;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="621.2800000000009" y="543.5" width="378.61" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-9" value="&lt;b&gt;Sub:&lt;/b&gt;&amp;nbsp;pep-sharedservice-01-sub" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=0;fontSize=14;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="602.3900000000008" y="507.5" width="346" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-10" value="Palo Alto" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=none;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;imageBorder=#FA592C;fontSize=15;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="872.2800000000009" y="607.5" width="68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#009900;startArrow=oval;startFill=0;endSize=12;startSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="785" y="614" as="sourcePoint" />
            <mxPoint x="872" y="614" as="targetPoint" />
            <Array as="points">
              <mxPoint x="805" y="614" />
              <mxPoint x="805" y="614" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-12" value="External AGW" style="sketch=0;aspect=fixed;html=1;points=[];align=center;image;fontSize=15;image=img/lib/mscae/Application_Gateway.svg;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="710.7800000000009" y="607.5" width="68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-13" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#B3B3B3;dashed=1;fontFamily=Verdana;fillColor=default;strokeWidth=2;dashPattern=1 1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="596.3900000000001" y="815" width="458.61" height="270" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-14" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#CCCCCC;fillColor=#FAFAFA;fontFamily=Verdana;strokeWidth=1;dashed=1;dashPattern=1 1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="616.8900000000001" y="851" width="418.11" height="214" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-15" value="&lt;b&gt;VNet:&lt;/b&gt;&amp;nbsp;pep-paas-infratools-prod-scus-01-vnet" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=0;fontSize=14;fontColor=#000000;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="620.3899999999999" y="851" width="348.5" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-16" value="&lt;b&gt;Sub:&lt;/b&gt;&amp;nbsp;pep-prod-infratools-01-sub" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=0;fontSize=14;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="604.3899999999999" y="815" width="307" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-17" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#3399FF;dashed=1;dashPattern=1 1;fontFamily=Verdana;fillColor=default;strokeWidth=1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="636.28" y="881" width="378.72" height="163.62" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-18" value="&#xa;&#xa;&#xa;&#xa;Apigee" style="group;fontFamily=Verdana;fontSize=15;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1" connectable="0">
          <mxGeometry x="871.5300000000009" y="914.6199999999999" width="68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-19" value="" style="whiteSpace=wrap;html=1;aspect=fixed;strokeColor=#fc4c01;" parent="RsPSmVlmxrs_U-s3XhF0-18" vertex="1">
          <mxGeometry width="68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-20" value="" style="shape=image;aspect=fixed;image=data:image/png,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;" parent="RsPSmVlmxrs_U-s3XhF0-18" vertex="1">
          <mxGeometry x="6.400000000000091" y="24.5" width="55.21" height="19" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-21" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#B3B3B3;dashed=1;fontFamily=Verdana;fillColor=default;strokeWidth=2;dashPattern=1 1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1085" y="507" width="785" height="763" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-22" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#CCCCCC;fillColor=#FAFAFA;fontFamily=Verdana;strokeWidth=1;dashed=1;dashPattern=1 1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1111" y="543.5" width="569" height="696.5" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-23" value="&lt;b&gt;VNet:&lt;/b&gt;&amp;nbsp;pep-eiap-prod-scus-network-01-vnet" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=0;fontSize=14;fontColor=#000000;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1114.5" y="543.5" width="390.5" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-24" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#3399FF;dashed=1;dashPattern=1 1;fontFamily=Verdana;fillColor=default;strokeWidth=1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1133.75" y="573.88" width="521.25" height="471.12" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-25" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#3399FF;dashed=1;dashPattern=1 1;fillColor=none;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1155" y="600.94" width="280" height="277" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-26" value="" style="aspect=fixed;html=1;points=[];align=center;image;fontSize=12;image=img/lib/azure2/compute/Kubernetes_Services.svg;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1409.9999999999995" y="585.5" width="51" height="45" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-27" value="EIAP" style="text;html=1;strokeColor=#3700CC;fillColor=#4C0099;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Verdana;fontSize=14;fontColor=#ffffff;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1172.4999999999995" y="585.5" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-30" value="&lt;b&gt;Sub:&lt;/b&gt;&amp;nbsp;pep-prod-01-sub" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=0;fontSize=14;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1094.000000000001" y="507.5" width="346" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-31" value="&lt;b style=&quot;border-color: var(--border-color);&quot;&gt;RG:&lt;/b&gt;&amp;nbsp;XXX" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=0;fontSize=14;fontColor=#000000;fontFamily=Verdana;labelBackgroundColor=none;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1094" y="475" width="310" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-35" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#808080;dashed=1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="275" y="555" width="192" height="190" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-36" value="Public Internet" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=14;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="275" y="555" width="192" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;startArrow=oval;startFill=0;endSize=12;startSize=12;strokeColor=#009900;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="415" y="632" as="sourcePoint" />
            <mxPoint x="705" y="632" as="targetPoint" />
            <Array as="points">
              <mxPoint x="625" y="632" />
              <mxPoint x="625" y="632" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-38" value="Browser" style="html=1;verticalLabelPosition=bottom;align=center;labelBackgroundColor=#ffffff;verticalAlign=top;strokeWidth=2;strokeColor=#0080F0;shadow=0;dashed=0;shape=mxgraph.ios7.icons.user;dashPattern=1 1;fontSize=15;fillColor=#99CCFF;aspect=fixed;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="337.00000000000045" y="607" width="68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-39" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#808080;dashed=1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="275" y="771" width="192" height="190" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-40" value="On-prem / VPN" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=14;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="275" y="771" width="192" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-41" value="Browser" style="html=1;verticalLabelPosition=bottom;align=center;labelBackgroundColor=#ffffff;verticalAlign=top;strokeWidth=2;strokeColor=#0080F0;shadow=0;dashed=0;shape=mxgraph.ios7.icons.user;dashPattern=1 1;fontSize=15;fillColor=#99CCFF;aspect=fixed;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="337.00000000000045" y="823" width="68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-42" value="" style="verticalLabelPosition=bottom;html=1;verticalAlign=top;align=center;strokeColor=none;fillColor=#00BEF2;shape=mxgraph.azure.express_route;pointerEvents=1;dashed=1;strokeWidth=2;fontSize=14;fontColor=#000000;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="496.8900000000001" y="775.29" width="33.33" height="20" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-43" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=1;fontFamily=Verdana;fontSize=17;fontColor=#FF0000;dashed=1;dashPattern=1 1;jumpStyle=none;shadow=0;shape=link;exitX=0.038;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry x="3100" y="79" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="466.894" y="800.4180000000001" as="sourcePoint" />
            <mxPoint x="562.8900000000001" y="800.2899999999995" as="targetPoint" />
            <Array as="points">
              <mxPoint x="468.78999999999974" y="800.3699999999999" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#CC6600;startArrow=oval;startFill=0;endSize=12;startSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="785" y="632" as="sourcePoint" />
            <mxPoint x="872" y="632" as="targetPoint" />
            <Array as="points">
              <mxPoint x="805" y="632" />
              <mxPoint x="805" y="632" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-45" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;startArrow=oval;startFill=0;endSize=12;startSize=12;strokeColor=#CC6600;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="415" y="652" as="sourcePoint" />
            <mxPoint x="705" y="652" as="targetPoint" />
            <Array as="points">
              <mxPoint x="625" y="652" />
              <mxPoint x="625" y="652" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-46" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=oval;startFill=0;strokeWidth=2;strokeColor=#009900;endSize=12;startSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="951" y="607" as="sourcePoint" />
            <mxPoint x="1125" y="697" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1075" y="607" />
              <mxPoint x="1075" y="697" />
              <mxPoint x="1125" y="697" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-47" value="&lt;br style=&quot;font-size: 15px;&quot;&gt;&lt;br style=&quot;font-size: 15px;&quot;&gt;&lt;br style=&quot;font-size: 15px;&quot;&gt;&lt;br style=&quot;font-size: 15px;&quot;&gt;&lt;br style=&quot;font-size: 15px;&quot;&gt;Ingress" style="sketch=0;html=1;dashed=0;whitespace=wrap;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon;prIcon=ing;fontFamily=Verdana;fontSize=15;aspect=fixed;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1121.9999999999995" y="693.7199999999998" width="68" height="65.28" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-48" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#CC6600;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="878" y="710" as="sourcePoint" />
            <mxPoint x="878" y="910" as="targetPoint" />
            <Array as="points">
              <mxPoint x="878" y="910" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-49" value="" style="endArrow=none;html=1;rounded=0;labelBackgroundColor=default;strokeColor=#4D4D4D;strokeWidth=1;fontFamily=Helvetica;fontSize=11;fontColor=default;shape=link;exitX=0.917;exitY=0.005;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.913;entryY=0.996;entryDx=0;entryDy=0;entryPerimeter=0;" parent="RsPSmVlmxrs_U-s3XhF0-1" source="RsPSmVlmxrs_U-s3XhF0-14" target="RsPSmVlmxrs_U-s3XhF0-6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1025" y="825" as="sourcePoint" />
            <mxPoint x="1075" y="775" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-50" value="" style="endArrow=none;html=1;rounded=0;labelBackgroundColor=default;strokeColor=#4D4D4D;strokeWidth=1;fontFamily=Helvetica;fontSize=11;fontColor=default;shape=link;exitX=1;exitY=0.071;exitDx=0;exitDy=0;exitPerimeter=0;entryX=-0.001;entryY=0.023;entryDx=0;entryDy=0;entryPerimeter=0;" parent="RsPSmVlmxrs_U-s3XhF0-1" source="RsPSmVlmxrs_U-s3XhF0-6" target="RsPSmVlmxrs_U-s3XhF0-22" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1135" y="795" as="sourcePoint" />
            <mxPoint x="1110" y="560" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-51" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.005;entryY=0.63;entryDx=0;entryDy=0;entryPerimeter=0;labelBackgroundColor=default;strokeColor=#CC6600;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="951" y="626" as="sourcePoint" />
            <mxPoint x="1123.3399999999997" y="715.8463999999999" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1065" y="626" />
              <mxPoint x="1065" y="716" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-52" value="Azure DevOps" style="aspect=fixed;html=1;points=[];align=center;image;fontSize=15;image=img/lib/azure2/devops/Azure_DevOps.svg;fontFamily=Verdana;fontColor=#333333;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="337.00000000000045" y="1228.44" width="68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-53" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#009900;startArrow=oval;startFill=0;endSize=12;startSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="415" y="845" as="sourcePoint" />
            <mxPoint x="872" y="649" as="targetPoint" />
            <Array as="points">
              <mxPoint x="535" y="845" />
              <mxPoint x="535" y="715" />
              <mxPoint x="805" y="715" />
              <mxPoint x="805" y="649" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-54" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#CC00CC;startArrow=oval;startFill=0;endSize=12;startSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="415" y="867" as="sourcePoint" />
            <mxPoint x="872" y="665" as="targetPoint" />
            <Array as="points">
              <mxPoint x="555" y="867" />
              <mxPoint x="555" y="735" />
              <mxPoint x="815" y="735" />
              <mxPoint x="815" y="665" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-55" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#CC00CC;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="950" y="643" as="sourcePoint" />
            <mxPoint x="1125" y="734" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1055" y="643" />
              <mxPoint x="1055" y="734" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-56" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#CC6600;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="892" y="903" as="sourcePoint" />
            <mxPoint x="892" y="703" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-57" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#333333;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="865" y="695" as="sourcePoint" />
            <mxPoint x="410" y="1260" as="targetPoint" />
            <Array as="points">
              <mxPoint x="865" y="695" />
              <mxPoint x="835" y="695" />
              <mxPoint x="835" y="780" />
              <mxPoint x="590" y="780" />
              <mxPoint x="590" y="1260" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-58" value="Web traffic" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=#FFF2CC;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="477.6199999999999" y="602" width="71.87" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-59" value="Microservice" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=#FFF2CC;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="477.6199999999999" y="652" width="71.87" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-60" value="Web traffic" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=#FFF2CC;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="435" y="814" width="71.87" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-61" value="Microservice" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;labelBackgroundColor=#FFF2CC;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="435" y="866" width="71.87" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-67" value="BLOB SE" style="verticalLabelPosition=bottom;html=1;verticalAlign=top;align=center;strokeColor=none;fillColor=#00BEF2;shape=mxgraph.azure.service_endpoint;fontFamily=Verdana;fontSize=15;aspect=fixed;fontColor=#333333;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1509.8800000000003" y="896" width="68" height="27.2" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-68" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#808080;dashed=1;dashPattern=1 1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="275.11" y="335" width="194.89" height="190" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-69" value="Public Internet" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=14;fontFamily=Verdana;fontColor=#333333;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="269.9999999999992" y="335" width="148.5" height="30" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-70" value="&#xa;&#xa;&#xa;&#xa;&#xa;OKTA" style="shape=image;aspect=fixed;image=data:image/png,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;clipPath=inset(12.66% 30.67% 12.66% 30.33%);fontFamily=Verdana;fontSize=15;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="338.8400000000009" y="396" width="67.42" height="68" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-71" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#CC0000;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="325" y="645" as="sourcePoint" />
            <mxPoint x="345" y="430" as="targetPoint" />
            <Array as="points">
              <mxPoint x="205" y="645" />
              <mxPoint x="205" y="430" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-72" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#CC0000;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=none;startSize=12;endSize=12;endFill=0;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="205" y="625" as="targetPoint" />
            <mxPoint x="325" y="857" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-73" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#CC0000;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="RsPSmVlmxrs_U-s3XhF0-1" target="RsPSmVlmxrs_U-s3XhF0-70" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="923" y="595" as="sourcePoint" />
            <mxPoint x="923" y="375" as="targetPoint" />
            <Array as="points">
              <mxPoint x="923" y="430" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-74" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#00549f;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry width="2750" height="265" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-75" value="Reference Architecture: Web application and microservices&lt;br&gt;Reference Implementation #RI003" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=58;fontColor=#FFFFFF;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="80" y="60" width="2455" height="150" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-76" value="Data Classification:   Confidential or Below" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=41;fontColor=#000000;labelBackgroundColor=#FFFF00;labelBorderColor=#FFFF00;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="2235" y="82.5" width="445" height="105" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-77" value="" style="rounded=0;whiteSpace=wrap;html=1;fillStyle=dashed;dashed=1;strokeWidth=2;strokeColor=#3399FF;gradientColor=none;fillColor=none;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="2146" y="1385.44" width="65" height="35.31" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-78" value="" style="rounded=0;whiteSpace=wrap;html=1;fillStyle=dashed;dashed=1;strokeWidth=2;strokeColor=#B9E0A5;gradientColor=none;fillColor=#B9E0A5;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="2146" y="1330.44" width="65" height="35.31" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-79" value="&lt;div style=&quot;text-align: center; font-size: 24px;&quot;&gt;&lt;b style=&quot;background-color: initial; font-size: 24px;&quot;&gt;&lt;u style=&quot;font-size: 24px;&quot;&gt;Proposed Solution – Workflow&lt;/u&gt;&lt;/b&gt;&lt;/div&gt;&lt;ol style=&quot;font-size: 24px;&quot;&gt;&lt;li style=&quot;font-size: 24px;&quot;&gt;End users access web application and authenticate via Okta&lt;/li&gt;&lt;li style=&quot;font-size: 24px;&quot;&gt;Traffic is routed to Istio Service Proxy that load balances and routes traffic to NGiNX HTTP (web) Servers deployed on containers on AKS&lt;/li&gt;&lt;li style=&quot;font-size: 24px;&quot;&gt;NGiNX returns static content HTML,CSS,&amp;nbsp; JavaScript, images&lt;/li&gt;&lt;li style=&quot;font-size: 24px;&quot;&gt;Browser loads the page, and then via React.JS code makes calls to backend APIs via the API Gateway to get dynamic content&lt;/li&gt;&lt;li style=&quot;font-size: 24px;&quot;&gt;API Gateway checks authorization, and routes requests to Istio Service Proxy&lt;/li&gt;&lt;li style=&quot;font-size: 24px;&quot;&gt;Istio Service Proxy load balances and routes requests to Microservices running on AKS&lt;/li&gt;&lt;li style=&quot;font-size: 24px;&quot;&gt;Microservice executes business logic and accesses data from its data stores&lt;/li&gt;&lt;li style=&quot;font-size: 24px;&quot;&gt;API Response is then sent back through the Service Proxy, API Gateway to the browser&lt;/li&gt;&lt;li style=&quot;font-size: 24px;&quot;&gt;Messages are sent/received to/from other apps via existing Confluent Kafka instances (EIP, S&amp;amp;T, etc.)&lt;/li&gt;&lt;/ol&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=24;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="2098" y="353.44" width="587" height="540" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-80" value="&lt;div style=&quot;text-align: center; font-size: 29px; line-height: 30%;&quot;&gt;&lt;b style=&quot;background-color: initial; font-size: 29px;&quot;&gt;&lt;u style=&quot;font-size: 29px;&quot;&gt;Assumptions&lt;/u&gt;&lt;/b&gt;&lt;/div&gt;&lt;ol style=&quot;font-size: 24px; line-height: 120%;&quot;&gt;&lt;li style=&quot;border-color: var(--border-color);&quot;&gt;New Components – Blob Storage, Azure Key Vault, SQL Database&lt;/li&gt;&lt;li style=&quot;border-color: var(--border-color);&quot;&gt;Existing Components with Configuration changes – DevOps, AKS, Conjur, Kafka, ELK, EIAP ACR, API Management, Application Gateway, Okta&lt;/li&gt;&lt;/ol&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=29;align=left;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="2095" y="1076.75" width="559" height="330" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-81" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="2500" y="1487" width="228" height="54.81" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-82" value="&lt;div style=&quot;font-size: 29px;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;Existing Components&lt;/span&gt;&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=29;align=left;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="2226" y="1378.75" width="331" height="50" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-83" value="&lt;div style=&quot;font-size: 29px;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;New Components&lt;/span&gt;&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=29;align=left;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="2226" y="1323.75" width="279" height="50" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-84" value="" style="rounded=0;whiteSpace=wrap;html=1;fillStyle=dashed;dashed=1;strokeWidth=2;strokeColor=#B9E0A5;gradientColor=none;fillColor=#B9E0A5;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1705" y="715" width="135" height="379.89" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-85" value="EIAP Shared&lt;br&gt;ACR" style="aspect=fixed;html=1;points=[];align=center;image;fontSize=15;image=img/lib/azure2/containers/Container_Registries.svg;fontFamily=Verdana;fontColor=#333333;labelBackgroundColor=default;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1733.39" y="1112.16" width="75.77" height="68" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-86" value="SQL Database" style="aspect=fixed;html=1;points=[];align=center;image;fontSize=15;image=img/lib/azure2/databases/SQL_Database.svg;labelBackgroundColor=default;fontFamily=Verdana;fillColor=none;fontColor=#333333;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1745.7800000000002" y="956.6500000000001" width="50.99" height="68" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-95" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#CC00CC;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="906" y="711" as="sourcePoint" />
            <mxPoint x="906" y="911" as="targetPoint" />
            <Array as="points">
              <mxPoint x="906" y="911" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-96" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#CC00CC;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="921" y="904" as="sourcePoint" />
            <mxPoint x="921" y="704" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="5BfP_TSLXRd9dglMU_Nm-1" value="pep-eiap-prod-suk-infra-02-aks" style="text;whiteSpace=wrap;fontFamily=Verdana;fontSize=9;fontStyle=0;align=left;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1170" y="610" width="81" height="35" as="geometry" />
        </mxCell>
        <mxCell id="5BfP_TSLXRd9dglMU_Nm-13" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#3399FF;dashed=1;dashPattern=1 1;fillColor=none;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1455" y="605" width="175" height="175" as="geometry" />
        </mxCell>
        <mxCell id="5BfP_TSLXRd9dglMU_Nm-14" value="" style="aspect=fixed;html=1;points=[];align=center;image;fontSize=12;image=img/lib/azure2/compute/Kubernetes_Services.svg;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1599.9999999999995" y="585.5" width="51" height="45" as="geometry" />
        </mxCell>
        <mxCell id="5BfP_TSLXRd9dglMU_Nm-15" value="EIAP" style="text;html=1;strokeColor=#3700CC;fillColor=#4C0099;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Verdana;fontSize=14;fontColor=#ffffff;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1472.4999999999995" y="585.5" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="5BfP_TSLXRd9dglMU_Nm-17" value="pep-eiap-prod-suk-global-01-aks" style="text;whiteSpace=wrap;fontFamily=Verdana;fontSize=9;fontStyle=0;align=left;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1470" y="610" width="85" height="35" as="geometry" />
        </mxCell>
        <mxCell id="5BfP_TSLXRd9dglMU_Nm-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;startArrow=oval;startFill=0;endSize=12;startSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1501" y="687.39" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="1435" y="687.39" />
            </Array>
            <mxPoint x="1435" y="687.39" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="5BfP_TSLXRd9dglMU_Nm-18" value="ADO Agent" style="sketch=0;html=1;dashed=0;whitespace=wrap;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];verticalLabelPosition=bottom;align=center;verticalAlign=top;shape=mxgraph.kubernetes.icon;prIcon=svc;aspect=fixed;fontFamily=Verdana;fontSize=15;labelBackgroundColor=default;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1509.8800000000003" y="666.0000000000001" width="68" height="65.28" as="geometry" />
        </mxCell>
        <mxCell id="5BfP_TSLXRd9dglMU_Nm-20" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#3399FF;dashed=1;dashPattern=1 1;fontFamily=Verdana;fillColor=default;strokeWidth=1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1133.75" y="1070.44" width="206.25" height="144.56" as="geometry" />
        </mxCell>
        <mxCell id="d7qkyT9In1pX6ghumVKf-2" value="Kafka" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=15;image=img/lib/azure2/compute/Virtual_Machine.svg;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1199.95" y="1114.625" width="68" height="63.07" as="geometry" />
        </mxCell>
        <mxCell id="d7qkyT9In1pX6ghumVKf-3" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#3399FF;dashed=1;dashPattern=1 1;fontFamily=Verdana;fillColor=default;strokeWidth=1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1445" y="1070" width="210" height="145" as="geometry" />
        </mxCell>
        <mxCell id="d7qkyT9In1pX6ghumVKf-4" value="ACR PE" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=15;image=img/lib/azure2/networking/Private_Endpoint.svg;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1509.8800000000003" y="1111.34" width="68" height="62.32" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-62" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#00CCCC;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;entryX=0.012;entryY=0.669;entryDx=0;entryDy=0;entryPerimeter=0;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1585" y="1155" as="sourcePoint" />
            <mxPoint x="1734.2992400000003" y="1154.652" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1630" y="1155" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-66" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#00CCCC;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1590" y="910" as="sourcePoint" />
            <mxPoint x="1735" y="895" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1640" y="910" />
              <mxPoint x="1640" y="895" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-65" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#00CCCC;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1590" y="991" as="sourcePoint" />
            <mxPoint x="1740" y="991" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1720" y="991" />
              <mxPoint x="1720" y="991" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FEVFjk1wm1-NXaA_mYkI-0" value="pep-eiap-prod-scus-network-01-vnet-paas-pvtlink-01-snet" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Verdana;fontSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1450" y="1070" width="201" height="30" as="geometry" />
        </mxCell>
        <mxCell id="AAy6_aWHXkJGcKGa4CFK-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.85;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;startArrow=oval;startFill=0;strokeWidth=2;endSize=12;startSize=12;strokeColor=#00cccc;exitPerimeter=0;" parent="RsPSmVlmxrs_U-s3XhF0-1" source="RsPSmVlmxrs_U-s3XhF0-88" target="AAy6_aWHXkJGcKGa4CFK-0" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-88" value="" style="rounded=0;whiteSpace=wrap;html=1;fillStyle=dashed;dashed=1;strokeWidth=2;strokeColor=#B9E0A5;gradientColor=none;fillColor=#B9E0A5;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1245" y="620" width="125" height="245" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-90" value="Microservice" style="sketch=0;html=1;dashed=0;whitespace=wrap;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];verticalLabelPosition=bottom;align=center;verticalAlign=top;shape=mxgraph.kubernetes.icon;prIcon=svc;aspect=fixed;fontFamily=Verdana;fontSize=15;labelBackgroundColor=default;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1272.5" y="763.6100000000001" width="68" height="65.28" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-89" value="Web Static&lt;br&gt;Content" style="sketch=0;html=1;dashed=0;whitespace=wrap;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];verticalLabelPosition=bottom;align=center;verticalAlign=top;shape=mxgraph.kubernetes.icon;prIcon=svc;aspect=fixed;fontFamily=Verdana;fontSize=15;labelBackgroundColor=default;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1272.5" y="628.44" width="68" height="65.28" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-32" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.013;entryY=0.694;entryDx=0;entryDy=0;entryPerimeter=0;labelBackgroundColor=default;strokeColor=#00CCCC;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;exitX=0.379;exitY=0.997;exitDx=0;exitDy=0;exitPerimeter=0;" parent="RsPSmVlmxrs_U-s3XhF0-1" source="RsPSmVlmxrs_U-s3XhF0-88" target="d7qkyT9In1pX6ghumVKf-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1292" y="855" as="sourcePoint" />
            <mxPoint x="1478" y="818.2500000000005" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1292" y="885" />
              <mxPoint x="1292" y="1155" />
              <mxPoint x="1273" y="1155" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-34" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#00CCCC;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1325" y="865" as="sourcePoint" />
            <mxPoint x="1506" y="910" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1325" y="910" />
              <mxPoint x="1506" y="910" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-33" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=-0.039;entryY=0.475;entryDx=0;entryDy=0;entryPerimeter=0;labelBackgroundColor=default;strokeColor=#00CCCC;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" target="RsPSmVlmxrs_U-s3XhF0-64" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1310" y="865" as="sourcePoint" />
            <mxPoint x="1488" y="710.7600000000002" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1310" y="990" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-91" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=oval;startFill=0;endSize=12;startSize=12;strokeWidth=2;strokeColor=#009900;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1195" y="720" as="sourcePoint" />
            <mxPoint x="1273" y="660" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1225" y="720" />
              <mxPoint x="1225" y="660" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-92" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.038;entryY=0.634;entryDx=0;entryDy=0;entryPerimeter=0;labelBackgroundColor=default;strokeColor=#CC00CC;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1195" y="755" as="sourcePoint" />
            <mxPoint x="1274.9740000000002" y="801.9975200000001" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1225" y="755" />
              <mxPoint x="1225" y="802" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-93" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#CC0000;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1265" y="818" as="sourcePoint" />
            <mxPoint x="945" y="657" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1045" y="818" />
              <mxPoint x="1045" y="657" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-94" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#CC6600;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;entryX=0.082;entryY=0.322;entryDx=0;entryDy=0;entryPerimeter=0;" parent="RsPSmVlmxrs_U-s3XhF0-1" target="RsPSmVlmxrs_U-s3XhF0-90" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1195" y="737" as="sourcePoint" />
            <mxPoint x="1265" y="785" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1234" y="737" />
              <mxPoint x="1234" y="785" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-87" value="Blob Storage" style="verticalLabelPosition=bottom;html=1;verticalAlign=top;align=center;strokeColor=none;fillColor=#00BEF2;shape=mxgraph.azure.storage_blob;labelBackgroundColor=default;fontFamily=Verdana;fontSize=15;aspect=fixed;fontColor=#333333;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1737.275" y="862.8800000000001" width="68" height="62.45" as="geometry" />
        </mxCell>
        <mxCell id="IrTvPv0Cm_5LxlFjjUan-0" value="Key Vault" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=15;image=img/lib/azure2/security/Key_Vaults.svg;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1738.5" y="759" width="68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="FEVFjk1wm1-NXaA_mYkI-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.002;entryY=0.341;entryDx=0;entryDy=0;entryPerimeter=0;strokeWidth=2;startArrow=oval;startFill=0;endSize=12;startSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" target="RsPSmVlmxrs_U-s3XhF0-85" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1585" y="710" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="1585" y="710" />
              <mxPoint x="1670" y="710" />
              <mxPoint x="1670" y="1135" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-28" style="edgeStyle=orthogonalEdgeStyle;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;labelBackgroundColor=default;strokeColor=#333333;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;startArrow=oval;startFill=0;endArrow=classic;startSize=12;endSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1500" y="710" as="sourcePoint" />
            <mxPoint x="945" y="685" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1445" y="710" />
              <mxPoint x="1445" y="895" />
              <mxPoint x="1025" y="895" />
              <mxPoint x="1025" y="685" />
              <mxPoint x="945" y="685" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="AAy6_aWHXkJGcKGa4CFK-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#00cccc;strokeWidth=2;startArrow=oval;startFill=0;endSize=12;startSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1590" y="829" as="sourcePoint" />
            <mxPoint x="1730" y="795" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1640" y="829" />
              <mxPoint x="1640" y="795" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="AAy6_aWHXkJGcKGa4CFK-0" value="Key Vault SE" style="verticalLabelPosition=bottom;html=1;verticalAlign=top;align=center;strokeColor=none;fillColor=#00BEF2;shape=mxgraph.azure.service_endpoint;fontFamily=Verdana;fontSize=15;aspect=fixed;fontColor=#333333;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1509.8800000000003" y="815.4000000000001" width="68" height="27.2" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-64" value="SQL SE" style="verticalLabelPosition=bottom;html=1;verticalAlign=top;align=center;strokeColor=none;fillColor=#00BEF2;shape=mxgraph.azure.service_endpoint;fontFamily=Verdana;fontSize=15;aspect=fixed;fontColor=#333333;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1509.8800000000003" y="977.05" width="68" height="27.2" as="geometry" />
        </mxCell>
        <mxCell id="dY7sLVWPUoY-mt9tM9YO-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=oval;startFill=0;strokeWidth=2;endSize=12;startSize=12;strokeColor=#007FFF;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1265" y="835" as="sourcePoint" />
            <mxPoint x="945" y="671" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1035" y="835" />
              <mxPoint x="1035" y="671" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="MtVUb3X4Y4yQFOula-di-15" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#B3B3B3;dashed=1;fontFamily=Verdana;fillColor=default;strokeWidth=2;dashPattern=1 1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="596.64" y="1115" width="258.36" height="260" as="geometry" />
        </mxCell>
        <mxCell id="MtVUb3X4Y4yQFOula-di-16" value="&lt;b&gt;Sub:&lt;/b&gt;&amp;nbsp;pep-prod-infratools-01-sub" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=0;fontSize=14;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="604.6399999999999" y="1115" width="307" height="30" as="geometry" />
        </mxCell>
        <mxCell id="dY7sLVWPUoY-mt9tM9YO-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;startArrow=oval;startFill=0;strokeColor=#007FFF;endSize=12;startSize=12;exitX=0.192;exitY=0.994;exitDx=0;exitDy=0;exitPerimeter=0;" parent="RsPSmVlmxrs_U-s3XhF0-1" source="RsPSmVlmxrs_U-s3XhF0-88" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="950" y="699" as="sourcePoint" />
            <mxPoint x="1194" y="1155" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1269" y="990" />
              <mxPoint x="1100" y="990" />
              <mxPoint x="1100" y="1155" />
              <mxPoint x="1194" y="1155" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="MtVUb3X4Y4yQFOula-di-10" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#CCCCCC;fillColor=#FAFAFA;fontFamily=Verdana;strokeWidth=1;dashed=1;dashPattern=1 1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="616.89" y="1151.19" width="214" height="199.56" as="geometry" />
        </mxCell>
        <mxCell id="MtVUb3X4Y4yQFOula-di-11" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#3399FF;dashed=1;dashPattern=1 1;fontFamily=Verdana;fillColor=default;strokeWidth=1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="639.64" y="1181.19" width="166.25" height="144.56" as="geometry" />
        </mxCell>
        <mxCell id="MtVUb3X4Y4yQFOula-di-13" value="&lt;b&gt;VNet:&lt;/b&gt;&amp;nbsp;pep-prod-infratools-01-scus-vnet" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=0;fontSize=14;fontColor=#000000;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="625.39" y="1155.88" width="205.5" height="30" as="geometry" />
        </mxCell>
        <mxCell id="MtVUb3X4Y4yQFOula-di-17" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0.873;entryY=0.991;entryDx=0;entryDy=0;entryPerimeter=0;shape=link;strokeColor=#4D4D4D;" parent="RsPSmVlmxrs_U-s3XhF0-1" source="MtVUb3X4Y4yQFOula-di-13" target="RsPSmVlmxrs_U-s3XhF0-6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1265" y="1135" as="sourcePoint" />
            <mxPoint x="1315" y="1085" as="targetPoint" />
            <Array as="points">
              <mxPoint x="980" y="1163" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="MtVUb3X4Y4yQFOula-di-18" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;endSize=12;startSize=12;strokeColor=#007FFF;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="825" y="755" as="sourcePoint" />
            <mxPoint x="760" y="1240" as="targetPoint" />
            <Array as="points">
              <mxPoint x="825" y="1240" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="MtVUb3X4Y4yQFOula-di-19" value="&#xa;&#xa;&#xa;&#xa;&#xa;ELK" style="shape=image;aspect=fixed;image=data:image/png,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;clipPath=inset(17.33% 63.33% 16% 3.33%);fontFamily=Verdana;fontSize=15;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="688.76" y="1211" width="68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="NXKHTdTzdI8_8Azx5fSz-0" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#808080;dashed=1;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="275.11" y="991.19" width="192" height="190" as="geometry" />
        </mxCell>
        <mxCell id="NXKHTdTzdI8_8Azx5fSz-1" value="Data center" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=14;fontFamily=Verdana;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="275.11" y="991.19" width="192" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NXKHTdTzdI8_8Azx5fSz-2" value="" style="verticalLabelPosition=bottom;html=1;verticalAlign=top;align=center;strokeColor=none;fillColor=#00BEF2;shape=mxgraph.azure.express_route;pointerEvents=1;dashed=1;strokeWidth=2;fontSize=14;fontColor=#000000;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="497.0000000000001" y="995.48" width="33.33" height="20" as="geometry" />
        </mxCell>
        <mxCell id="NXKHTdTzdI8_8Azx5fSz-3" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;strokeColor=#000000;strokeWidth=1;fontFamily=Verdana;fontSize=17;fontColor=#FF0000;dashed=1;dashPattern=1 1;jumpStyle=none;shadow=0;shape=link;exitX=0.038;exitY=1.002;exitDx=0;exitDy=0;exitPerimeter=0;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry x="3100" y="79" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="467.004" y="1020.6080000000002" as="sourcePoint" />
            <mxPoint x="563.0000000000001" y="1020.4799999999996" as="targetPoint" />
            <Array as="points">
              <mxPoint x="468.89999999999975" y="1020.56" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="NXKHTdTzdI8_8Azx5fSz-4" value="" style="shape=image;aspect=fixed;image=data:image/jpeg,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;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="337" y="1052.19" width="68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="NXKHTdTzdI8_8Azx5fSz-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#027ffe;startArrow=oval;startFill=0;endSize=12;startSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="865" y="680" as="sourcePoint" />
            <mxPoint x="410" y="1086" as="targetPoint" />
            <Array as="points">
              <mxPoint x="825" y="680" />
              <mxPoint x="825" y="755" />
              <mxPoint x="575" y="755" />
              <mxPoint x="575" y="1086" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="lrGfHxaCtyptuRs-7v0r-0" value="pep-eiap-prod-scus-network-01-vnet-backend-01-snet" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=12;" parent="RsPSmVlmxrs_U-s3XhF0-1" vertex="1">
          <mxGeometry x="1133.75" y="1070.44" width="206.25" height="35" as="geometry" />
        </mxCell>
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-177" value="Services" parent="RsPSmVlmxrs_U-s3XhF0-0" />
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-178" value="Connectivity" parent="RsPSmVlmxrs_U-s3XhF0-0" />
        <mxCell id="RsPSmVlmxrs_U-s3XhF0-179" value="Legend" parent="RsPSmVlmxrs_U-s3XhF0-0" />
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
