<mxfile host="Electron" modified="2023-08-29T07:34:56.090Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/21.6.8 Chrome/114.0.5735.289 Electron/25.5.0 Safari/537.36" etag="ds6qAJoVzNTULMkcsIig" version="21.6.8" type="device">
  <diagram name="Restricted or below" id="9fVLOCvne9XWaLxJ404U">
    <mxGraphModel dx="649" dy="1950" grid="0" gridSize="5" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2750" pageHeight="1600" background="#FFFFFF" math="0" shadow="0">
      <root>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-0" />
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-1" value="Background" parent="FElwDSOs_kZ1xXXQAgqD-0" />
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-2" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#FFFFFF;fillColor=none;" parent="FElwDSOs_kZ1xXXQAgqD-1" vertex="1">
          <mxGeometry x="2750" width="2750" height="1540" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-3" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#00549f;" parent="FElwDSOs_kZ1xXXQAgqD-1" vertex="1">
          <mxGeometry x="2750" width="2750" height="265" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-4" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.75;exitDx=0;exitDy=0;startArrow=oval;startFill=0;endFill=0;strokeWidth=2;endSize=12;startSize=12;strokeColor=#FF0000;" parent="FElwDSOs_kZ1xXXQAgqD-1" source="FElwDSOs_kZ1xXXQAgqD-23" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="4105" y="1015" as="sourcePoint" />
            <mxPoint x="4145" y="965" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-5" value="" style="endArrow=none;html=1;rounded=0;strokeWidth=2;strokeColor=#FF0000;exitX=1;exitY=0.75;exitDx=0;exitDy=0;endFill=0;endSize=12;startSize=12;startArrow=oval;startFill=0;" parent="FElwDSOs_kZ1xXXQAgqD-1" source="FElwDSOs_kZ1xXXQAgqD-23" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="4157" y="965" as="sourcePoint" />
            <mxPoint x="4145" y="859" as="targetPoint" />
            <Array as="points">
              <mxPoint x="4145" y="965" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-6" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=none;" parent="FElwDSOs_kZ1xXXQAgqD-1" vertex="1">
          <mxGeometry x="3901" y="766" width="461" height="348" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-7" value="Boxes" parent="FElwDSOs_kZ1xXXQAgqD-0" />
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-8" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=3;strokeColor=#0066CC;fillColor=none;dashed=1;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="3301" y="344.69000000000005" width="1480" height="1040" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-9" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=3;strokeColor=#0066CC;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="4201" y="385.85" width="540" height="360.15" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-10" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=3;strokeColor=#0066CC;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="3581" y="387.19000000000005" width="540" height="957.5" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-11" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=3;strokeColor=#0066CC;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="3341" y="387.19000000000005" width="160" height="557.5" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-12" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=3;strokeColor=#0066CC;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="3341" y="986.19" width="160" height="318.5" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-13" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;strokeColor=#0066CC;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="3621" y="1064.54" width="460" height="240.15" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-14" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;strokeColor=#0066CC;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="3621" y="864.69" width="460" height="160" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-15" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;strokeColor=#0066CC;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="3621" y="427.69000000000005" width="460" height="397" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-16" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=3;strokeColor=#0066CC;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="2841" y="385.85" width="320" height="398.84" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-17" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=3;strokeColor=#0066CC;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="2841" y="824.69" width="320" height="240" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-18" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=3;strokeColor=#0066CC;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="2841" y="1104.3600000000001" width="320" height="280.33" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-19" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;strokeColor=#0066CC;fillColor=#66CC00;dashed=1;fontColor=#333333;fillStyle=dashed;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="4241" y="427.69" width="460" height="278.31" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-20" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;strokeColor=#0066CC;fillColor=none;dashed=1;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="3661" y="504.69000000000005" width="260" height="280.5" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-21" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;strokeColor=#0066CC;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="3951" y="504.69000000000005" width="90" height="105" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-23" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;strokeColor=#0066CC;fillColor=none;dashed=1;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="3661" y="904.69" width="260" height="80" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-24" value="&amp;nbsp; &amp;nbsp; Subnet 1" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#0066CC;fillColor=#3399FF;fontFamily=Verdana;fontColor=#333333;fontSize=20;fillStyle=dashed;labelBackgroundColor=default;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="3856.5" y="1139.69" width="180" height="53" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-25" value="&amp;nbsp; &amp;nbsp; Subnet 2" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#0066CC;fillColor=#3399FF;fontFamily=Verdana;fontColor=#333333;fontSize=20;fillStyle=dashed;labelBackgroundColor=default;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="3856.5" y="1212.69" width="180" height="53" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-26" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=3;strokeColor=#0066CC;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="3301" y="1424.69" width="1255" height="80" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-27" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=3;strokeColor=#0066CC;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="4201" y="764" width="540" height="216.63" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-28" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;strokeColor=#0066CC;fillColor=none;dashed=1;fontColor=#333333;fontSize=17;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="4241" y="805.84" width="460" height="139.79" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-29" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=3;strokeColor=#0066CC;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="4201" y="999" width="540" height="345.69" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-30" value="" style="rounded=0;whiteSpace=wrap;html=1;strokeWidth=2;strokeColor=#0066CC;fillColor=none;dashed=1;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="4241" y="1043" width="460" height="259.69" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-31" value="" style="rounded=0;whiteSpace=wrap;html=1;fillStyle=dashed;dashed=1;strokeWidth=2;strokeColor=#0066cc;gradientColor=none;fillColor=none;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="4869" y="1398.69" width="65" height="35.31" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-32" value="" style="rounded=0;whiteSpace=wrap;html=1;fillStyle=dashed;dashed=1;strokeWidth=2;strokeColor=#0066cc;gradientColor=none;fillColor=#66CC00;" parent="FElwDSOs_kZ1xXXQAgqD-7" vertex="1">
          <mxGeometry x="4869" y="1343.69" width="65" height="35.31" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-33" value="Services" parent="FElwDSOs_kZ1xXXQAgqD-0" />
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-34" value="" style="image;sketch=0;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/mscae/ResourceGroup.svg;strokeColor=#3399FF;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="4249" y="436.35000000000014" width="40" height="32" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-35" value="" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/networking/Virtual_Networks.svg;strokeColor=#3399FF;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3626" y="435.25" width="40" height="23.88" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-36" value="" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/networking/Virtual_Networks.svg;strokeColor=#3399FF;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3348" y="396.81000000000006" width="40" height="23.88" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-37" value="" style="shape=image;aspect=fixed;image=data:image/png,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;clipPath=inset(28.5% 58.67% 27% 10.33%);fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3301" y="279" width="60" height="57.4" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-38" value="" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/networking/Virtual_Networks.svg;strokeColor=#3399FF;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3348" y="993.5" width="40" height="23.88" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-39" value="" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/networking/Virtual_Networks.svg;strokeColor=#3399FF;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3626" y="870.25" width="40" height="23.88" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-40" value="" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/networking/Virtual_Networks.svg;strokeColor=#3399FF;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3626" y="1070.25" width="40" height="23.88" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-41" value="DF Blob" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=18;image=img/lib/azure2/general/Blob_Block.svg;fontFamily=Verdana;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="4407" y="1068" width="68" height="54.4" as="geometry" />
        </mxCell>
        <mxCell id="PyIp5_12BELQEx50q_sq-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;startArrow=oval;startFill=0;endSize=12;startSize=12;strokeColor=#6600cc;" edge="1" parent="FElwDSOs_kZ1xXXQAgqD-33">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4496" y="618" as="sourcePoint" />
            <mxPoint x="4595" y="659" as="targetPoint" />
            <Array as="points">
              <mxPoint x="4539" y="618" />
              <mxPoint x="4539" y="659" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-42" value="Azure Data Factory" style="aspect=fixed;html=1;points=[];align=center;image;fontSize=18;image=img/lib/azure2/databases/Data_Factory.svg;fontFamily=Verdana;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="4409.5" y="566.69" width="68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-43" value="Firewall" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=none;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;imageBorder=#FA592C;fontSize=18;fontFamily=Verdana;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3386" y="634.69" width="68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-44" value="DMZ&#xa;Firewall​" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=none;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;imageBorder=#FA592C;fontSize=18;fontFamily=Verdana;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3386" y="1144.69" width="68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-45" value="Sector ADLS" style="sketch=0;aspect=fixed;html=1;points=[];align=center;image;fontSize=18;image=img/lib/mscae/Data_Lake.svg;labelBackgroundColor=none;fontFamily=Verdana;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="4417.66" y="828.63" width="51.68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-46" value="Azure Key Vault" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=15;image=img/lib/azure2/security/Key_Vaults.svg;fontFamily=Verdana;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="4601" y="464.69000000000005" width="42" height="42" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-47" value="Log Analytics" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=15;image=img/lib/azure2/analytics/Log_Analytics_Workspaces.svg;fontFamily=Verdana;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="4601" y="541.9100000000001" width="42" height="42" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-48" value="PE Subnet" style="aspect=fixed;html=1;points=[];align=center;image;fontSize=15;image=img/lib/azure2/networking/Private_Endpoint.svg;fontFamily=Verdana;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3974" y="529.54" width="43.66" height="40" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-49" value="&#xa;&#xa;&#xa;&#xa;&#xa;SHIR" style="shape=image;aspect=fixed;image=data:image/png,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;fontFamily=Verdana;fontSize=18;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3746" y="552.69" width="72.98" height="68" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-50" value="Power Bi Gateway" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=18;image=img/lib/azure2/compute/Virtual_Machine.svg;fontFamily=Verdana;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3750.9799999999996" y="666.6200000000001" width="68" height="63.07" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-51" value="" style="shape=image;aspect=fixed;image=data:image/png,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;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3736" y="917.69" width="169.23" height="54" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-52" value="Databricks" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=none;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;fontSize=18;fontFamily=Verdana;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3725.5" y="1152.69" width="78" height="88" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-53" value="" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/networking/Network_Security_Groups.svg;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3873.5" y="1149.8400000000001" width="30" height="36.45" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-54" value="" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/networking/Network_Security_Groups.svg;imageBorder=none;imageBackground=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3873.5" y="1220.69" width="30" height="36.45" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-55" value="" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/networking/Network_Security_Groups.svg;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3665" y="745.46" width="30" height="36.45" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-57" value="Express Route" style="verticalLabelPosition=bottom;html=1;verticalAlign=top;align=center;strokeColor=none;fillColor=#00BEF2;shape=mxgraph.azure.express_route;pointerEvents=1;fontFamily=Verdana;fontSize=14;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3206" y="374.69000000000017" width="50" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-58" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;imageBackground=default;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3131" y="556.69" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-59" value="&#xa;&#xa;&#xa;&#xa;&#xa;PowerBi" style="shape=image;aspect=fixed;image=data:image/png,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;clipPath=inset(0% 22.67% 0% 24.67%);fontFamily=Verdana;fontSize=18;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="3086" y="1419" width="68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-60" value="PepsiCo Users" style="sketch=0;pointerEvents=1;shadow=0;dashed=0;html=1;strokeColor=none;labelPosition=center;verticalLabelPosition=bottom;verticalAlign=top;outlineConnect=0;align=center;shape=mxgraph.office.users.users;fillColor=#2072B8;fontFamily=Verdana;fontSize=18;fontColor=default;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="2906" y="1429" width="70" height="68" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-61" value="" style="shape=image;aspect=fixed;image=data:image/png,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;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="2841" y="1414" width="59.54" height="20" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-62" value="GCP Cloud" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#0066CC;fillColor=none;fontFamily=Verdana;fontColor=#333333;fontSize=20;fillStyle=auto;labelBackgroundColor=none;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="2881" y="1179" width="180" height="40" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-63" value="SharePoint" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#0066CC;fillColor=none;fontFamily=Verdana;fontColor=#333333;fontSize=20;fillStyle=auto;labelBackgroundColor=none;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="2881" y="1244" width="180" height="40" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-64" value="3&lt;sup&gt;rd&lt;/sup&gt; Party BLOB" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#0066CC;fillColor=none;fontFamily=Verdana;fontColor=#333333;fontSize=20;fillStyle=auto;labelBackgroundColor=none;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="2881" y="1309" width="180" height="40" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-65" value="Oracle" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#0066CC;fillColor=none;fontFamily=Verdana;fontColor=#333333;fontSize=20;fillStyle=auto;labelBackgroundColor=none;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="2861" y="438.77" width="180" height="40" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-66" value="MS SQL" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#0066CC;fillColor=none;fontFamily=Verdana;fontColor=#333333;fontSize=20;fillStyle=auto;labelBackgroundColor=none;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="2861" y="495" width="180" height="40" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-67" value="MySQL" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#0066CC;fillColor=none;fontFamily=Verdana;fontColor=#333333;fontSize=20;fillStyle=auto;labelBackgroundColor=none;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="2861" y="551" width="180" height="40" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-68" value="SFTP Server" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#0066CC;fillColor=none;fontFamily=Verdana;fontColor=#333333;fontSize=20;fillStyle=auto;labelBackgroundColor=none;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="2861" y="607" width="180" height="40" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-69" value="SAP PIRT" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#0066CC;fillColor=none;fontFamily=Verdana;fontColor=#333333;fontSize=20;fillStyle=auto;labelBackgroundColor=none;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="2861" y="663" width="180" height="40" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-70" value="SAP BW" style="rounded=0;whiteSpace=wrap;html=1;strokeColor=#0066CC;fillColor=none;fontFamily=Verdana;fontColor=#333333;fontSize=20;fillStyle=auto;labelBackgroundColor=none;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="2861" y="719" width="180" height="40" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-71" value="DF ADLS" style="sketch=0;aspect=fixed;html=1;points=[];align=center;image;fontSize=18;image=img/lib/mscae/Data_Lake.svg;labelBackgroundColor=none;fontFamily=Verdana;fillColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-33" vertex="1">
          <mxGeometry x="4417.66" y="1193" width="51.68" height="68" as="geometry" />
        </mxCell>
        <mxCell id="PyIp5_12BELQEx50q_sq-0" value="Managed PE" style="aspect=fixed;html=1;points=[];align=center;image;fontSize=15;image=img/lib/azure2/networking/Private_Endpoint.svg;fontFamily=Verdana;fontColor=#333333;" vertex="1" parent="FElwDSOs_kZ1xXXQAgqD-33">
          <mxGeometry x="4601" y="632" width="42" height="38.48" as="geometry" />
        </mxCell>
        <mxCell id="PyIp5_12BELQEx50q_sq-3" value="Sector SQL" style="verticalLabelPosition=bottom;html=1;verticalAlign=top;align=center;strokeColor=none;fillColor=#00BEF2;shape=mxgraph.azure.sql_database;aspect=fixed;fontFamily=Verdana;fontSize=18;" vertex="1" parent="FElwDSOs_kZ1xXXQAgqD-33">
          <mxGeometry x="4578" y="828.63" width="51" height="68" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-72" value="Connectivity" parent="FElwDSOs_kZ1xXXQAgqD-0" />
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-73" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=#333333;strokeWidth=2;startArrow=oval;startFill=0;endSize=12;startSize=12;strokeColor=#3399FF;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3831" y="577.69" as="sourcePoint" />
            <mxPoint x="3942" y="578" as="targetPoint" />
            <Array as="points">
              <mxPoint x="3888" y="578" />
              <mxPoint x="3941" y="578" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-74" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=oval;startFill=0;endSize=12;startSize=12;strokeWidth=2;strokeColor=#6523d2;fontColor=#333333;entryX=0;entryY=0.625;entryDx=0;entryDy=0;entryPerimeter=0;" parent="FElwDSOs_kZ1xXXQAgqD-72" target="FElwDSOs_kZ1xXXQAgqD-41" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4050" y="543" as="sourcePoint" />
            <mxPoint x="4401" y="748.69" as="targetPoint" />
            <Array as="points">
              <mxPoint x="4163" y="543" />
              <mxPoint x="4163" y="1102" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-75" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;startArrow=oval;startFill=0;endSize=12;startSize=12;strokeColor=#6600cc;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4050" y="558" as="sourcePoint" />
            <mxPoint x="4415" y="1211" as="targetPoint" />
            <Array as="points">
              <mxPoint x="4153" y="558" />
              <mxPoint x="4153" y="1211" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-76" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=#333333;dashed=1;endSize=12;startSize=12;strokeWidth=2;strokeColor=#3399FF;startArrow=oval;startFill=0;dashPattern=1 2;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4496" y="600.69" as="sourcePoint" />
            <mxPoint x="4591" y="498" as="targetPoint" />
            <Array as="points">
              <mxPoint x="4531" y="601" />
              <mxPoint x="4531" y="498" />
              <mxPoint x="4591" y="498" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-77" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=oval;startFill=0;endSize=12;startSize=12;dashed=1;strokeColor=#3399FF;strokeWidth=2;dashPattern=1 2;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4496" y="600.69" as="sourcePoint" />
            <mxPoint x="4591" y="563.69" as="targetPoint" />
            <Array as="points">
              <mxPoint x="4531" y="600.69" />
              <mxPoint x="4531" y="563.69" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-78" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#6600cc;startArrow=oval;startFill=0;endSize=12;startSize=12;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4090" y="1229" as="sourcePoint" />
            <mxPoint x="4415" y="1229" as="targetPoint" />
            <Array as="points">
              <mxPoint x="4134" y="1229" />
              <mxPoint x="4134" y="1229" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-79" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;endSize=12;startSize=12;startArrow=oval;startFill=0;dashed=1;dashPattern=1 2;strokeColor=#3399FF;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3831" y="689" as="sourcePoint" />
            <mxPoint x="3901" y="904.69" as="targetPoint" />
            <Array as="points">
              <mxPoint x="3831" y="689" />
              <mxPoint x="3901" y="689" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-80" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;startArrow=classic;startFill=1;endArrow=oval;endFill=0;strokeWidth=2;endSize=12;startSize=12;strokeColor=#3399FF;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3804" y="1196.69" as="sourcePoint" />
            <mxPoint x="3857" y="1166.69" as="targetPoint" />
            <Array as="points">
              <mxPoint x="3841" y="1196.69" />
              <mxPoint x="3841" y="1166.69" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-81" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;endSize=12;startSize=12;strokeColor=#3399FF;startArrow=oval;startFill=0;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" source="FElwDSOs_kZ1xXXQAgqD-25" target="FElwDSOs_kZ1xXXQAgqD-52" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="3841" y="1239.69" />
              <mxPoint x="3841" y="1196.69" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-82" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.117;exitDx=0;exitDy=0;exitPerimeter=0;entryX=-0.003;entryY=0.062;entryDx=0;entryDy=0;entryPerimeter=0;shape=link;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="3501.1899999999996" y="445.7874999999997" as="sourcePoint" />
            <mxPoint x="3619.8099999999995" y="445.674" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-83" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.117;exitDx=0;exitDy=0;exitPerimeter=0;entryX=-0.003;entryY=0.062;entryDx=0;entryDy=0;entryPerimeter=0;shape=link;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="3501" y="882.8900000000001" as="sourcePoint" />
            <mxPoint x="3620" y="882.8900000000001" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-84" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.117;exitDx=0;exitDy=0;exitPerimeter=0;entryX=-0.003;entryY=0.062;entryDx=0;entryDy=0;entryPerimeter=0;shape=link;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="3501" y="1082.9" as="sourcePoint" />
            <mxPoint x="3620" y="1082.9" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-85" value="&lt;div style=&quot;font-size: 14px;&quot;&gt;Peering&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=14;strokeColor=none;align=center;rotation=0;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" vertex="1">
          <mxGeometry x="3516" y="1079.19" width="55" height="25" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-86" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;startArrow=oval;startFill=0;endSize=12;startSize=12;strokeColor=#3399FF;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3741" y="574.69" as="sourcePoint" />
            <mxPoint x="3461" y="651.69" as="targetPoint" />
            <Array as="points">
              <mxPoint x="3541" y="574.69" />
              <mxPoint x="3541" y="651.69" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-87" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#3399FF;endSize=12;startSize=12;startArrow=oval;startFill=0;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3741" y="689.69" as="sourcePoint" />
            <mxPoint x="3461" y="689.69" as="targetPoint" />
            <Array as="points">
              <mxPoint x="3461" y="689.69" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-88" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#3399FF;endSize=12;startSize=12;startArrow=oval;startFill=0;verticalAlign=top;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="3511" y="1454.69" as="sourcePoint" />
            <mxPoint x="3726" y="1454.69" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-89" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#6600CC;endSize=12;startSize=12;startArrow=oval;startFill=0;verticalAlign=top;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="3771" y="1454.69" as="sourcePoint" />
            <mxPoint x="3986" y="1454.69" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-90" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#FF0000;endSize=12;startSize=12;startArrow=oval;startFill=0;verticalAlign=top;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="4031" y="1454.69" as="sourcePoint" />
            <mxPoint x="4246" y="1454.69" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-91" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#000000;endSize=12;startSize=12;startArrow=oval;startFill=0;verticalAlign=top;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="4291" y="1454.69" as="sourcePoint" />
            <mxPoint x="4506" y="1454.69" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-92" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;startArrow=oval;startFill=0;endSize=12;startSize=12;strokeColor=#3399FF;endArrow=classic;endFill=1;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3376" y="689" as="sourcePoint" />
            <mxPoint x="3071" y="1269" as="targetPoint" />
            <Array as="points">
              <mxPoint x="3281" y="689" />
              <mxPoint x="3281" y="1269" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-93" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#3399FF;endSize=12;startSize=12;startArrow=oval;startFill=0;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3376" y="669" as="sourcePoint" />
            <mxPoint x="3171" y="949" as="targetPoint" />
            <Array as="points">
              <mxPoint x="3261" y="669" />
              <mxPoint x="3261" y="949" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-94" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;endSize=12;startSize=12;startArrow=oval;startFill=0;strokeColor=#3399FF;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3376" y="644" as="sourcePoint" />
            <mxPoint x="3201" y="587" as="targetPoint" />
            <Array as="points">
              <mxPoint x="3376" y="644" />
              <mxPoint x="3261" y="644" />
              <mxPoint x="3261" y="587" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-95" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#3399FF;endSize=12;startSize=12;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="3281" y="1206" as="sourcePoint" />
            <mxPoint x="3071" y="1206" as="targetPoint" />
            <Array as="points">
              <mxPoint x="3201" y="1206" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-96" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#3399FF;endSize=12;startSize=12;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="3281" y="1269" as="sourcePoint" />
            <mxPoint x="3071" y="1336" as="targetPoint" />
            <Array as="points">
              <mxPoint x="3281" y="1336" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-97" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#3399FF;endSize=12;startSize=12;startArrow=oval;startFill=0;strokeWidth=2;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3121" y="587" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="3091" y="587" />
              <mxPoint x="3091" y="459" />
            </Array>
            <mxPoint x="3041" y="458.77" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-98" value="" style="endArrow=classic;html=1;rounded=0;endSize=12;startSize=12;strokeWidth=2;strokeColor=#3399FF;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="3091" y="515" as="sourcePoint" />
            <mxPoint x="3041" y="515" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-99" value="" style="endArrow=classic;html=1;rounded=0;endSize=12;startSize=12;strokeWidth=2;strokeColor=#3399FF;entryX=1;entryY=0.546;entryDx=0;entryDy=0;entryPerimeter=0;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="3091" y="573" as="sourcePoint" />
            <mxPoint x="3041" y="572.8400000000001" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-100" style="edgeStyle=none;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;labelBackgroundColor=default;strokeColor=#3399FF;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=#333333;endArrow=classic;startSize=12;endSize=12;startArrow=oval;startFill=0;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3121" y="587" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="3091" y="587" />
              <mxPoint x="3091" y="739" />
            </Array>
            <mxPoint x="3041" y="739" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-101" value="" style="endArrow=classic;html=1;rounded=0;endSize=12;startSize=12;strokeWidth=2;strokeColor=#3399FF;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="3091" y="627" as="sourcePoint" />
            <mxPoint x="3041" y="627" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-102" value="" style="endArrow=classic;html=1;rounded=0;endSize=12;startSize=12;strokeWidth=2;strokeColor=#3399FF;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="3091" y="684" as="sourcePoint" />
            <mxPoint x="3041" y="684" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-103" style="edgeStyle=none;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.981;entryY=0.643;entryDx=0;entryDy=0;entryPerimeter=0;labelBackgroundColor=default;strokeColor=#3399FF;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;endArrow=classic;startSize=12;endSize=12;startArrow=oval;startFill=0;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3376" y="709" as="sourcePoint" />
            <mxPoint x="3153" y="1463" as="targetPoint" />
            <Array as="points">
              <mxPoint x="3291" y="709" />
              <mxPoint x="3291" y="1463" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-104" style="edgeStyle=none;shape=connector;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.649;entryDx=0;entryDy=0;entryPerimeter=0;labelBackgroundColor=default;strokeColor=#3399FF;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=default;endArrow=classic;startSize=12;endSize=12;startArrow=oval;startFill=0;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2986" y="1463" as="sourcePoint" />
            <mxPoint x="3086" y="1463" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-105" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#6523d2;startArrow=oval;startFill=0;strokeWidth=2;endSize=12;startSize=12;fontColor=#333333;entryX=-0.147;entryY=0.714;entryDx=0;entryDy=0;entryPerimeter=0;" parent="FElwDSOs_kZ1xXXQAgqD-72" target="FElwDSOs_kZ1xXXQAgqD-45" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4415" y="1245" as="targetPoint" />
            <Array as="points">
              <mxPoint x="4110" y="1185" />
              <mxPoint x="4110" y="877" />
            </Array>
            <mxPoint x="4091" y="1185" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-106" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=1.006;exitY=0.604;exitDx=0;exitDy=0;exitPerimeter=0;entryX=-0.001;entryY=0.06;entryDx=0;entryDy=0;entryPerimeter=0;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="3163" y="407" as="sourcePoint" />
            <mxPoint x="3300" y="407" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-107" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;startArrow=oval;startFill=0;endSize=12;startSize=12;fontColor=#333333;strokeColor=#6600CC;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4051" y="528" as="sourcePoint" />
            <mxPoint x="4401" y="604.69" as="targetPoint" />
            <Array as="points">
              <mxPoint x="4051" y="528" />
              <mxPoint x="4175" y="528" />
              <mxPoint x="4175" y="605" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-108" value="" style="endArrow=none;html=1;rounded=0;shape=link;entryX=0.915;entryY=1.003;entryDx=0;entryDy=0;entryPerimeter=0;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="4042" y="864.69" as="sourcePoint" />
            <mxPoint x="4042" y="826" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-109" value="" style="endArrow=classic;html=1;rounded=0;labelBackgroundColor=default;strokeColor=#6523d2;strokeWidth=2;fontFamily=Helvetica;fontSize=11;fontColor=#333333;startSize=12;endSize=12;shape=connector;startArrow=oval;startFill=0;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="4050" y="573" as="sourcePoint" />
            <mxPoint x="4411" y="859" as="targetPoint" />
            <Array as="points">
              <mxPoint x="4142" y="573" />
              <mxPoint x="4142" y="860" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="NGCn5jLktkn2RLFbLZjV-0" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;startArrow=oval;startFill=0;strokeWidth=2;endSize=12;startSize=12;strokeColor=#3399fe;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3933" y="945" as="sourcePoint" />
            <mxPoint x="3996" y="610" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NGCn5jLktkn2RLFbLZjV-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#6600cc;startArrow=oval;startFill=0;endSize=12;startSize=12;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4051" y="511" as="sourcePoint" />
            <mxPoint x="4592" y="479" as="targetPoint" />
            <Array as="points">
              <mxPoint x="4175" y="511" />
              <mxPoint x="4175" y="479" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="NGCn5jLktkn2RLFbLZjV-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#6600cc;endSize=12;startSize=12;startArrow=oval;startFill=0;" parent="FElwDSOs_kZ1xXXQAgqD-72" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4090" y="1258" as="sourcePoint" />
            <mxPoint x="4645" y="479" as="targetPoint" />
            <Array as="points">
              <mxPoint x="4152" y="1258" />
              <mxPoint x="4152" y="1322" />
              <mxPoint x="4726" y="1322" />
              <mxPoint x="4726" y="479" />
              <mxPoint x="4645" y="479" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="PyIp5_12BELQEx50q_sq-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;startArrow=oval;startFill=0;endSize=12;startSize=12;strokeColor=#6600cc;" edge="1" parent="FElwDSOs_kZ1xXXQAgqD-72">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="4651" y="658" as="sourcePoint" />
            <mxPoint x="4635" y="863" as="targetPoint" />
            <Array as="points">
              <mxPoint x="4681" y="658" />
              <mxPoint x="4681" y="863" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-110" value="Legend" parent="FElwDSOs_kZ1xXXQAgqD-0" />
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-111" value="&lt;div style=&quot;font-size: 18px;&quot;&gt;Subscription: &lt;span style=&quot;font-weight: normal; font-size: 18px;&quot;&gt;XXX&lt;/span&gt;&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Verdana;fontSize=18;fontStyle=1;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4211" y="389.69000000000005" width="580" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-112" value="&lt;div style=&quot;font-size: 20px;&quot;&gt;Region: &lt;span style=&quot;font-weight: normal; font-size: 20px;&quot;&gt;South Central US&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;font-size: 20px;&quot;&gt;&lt;span style=&quot;font-size: 20px;&quot;&gt;Environment:&lt;/span&gt;&lt;span style=&quot;font-weight: normal; font-size: 20px;&quot;&gt; PROD&lt;/span&gt;&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Verdana;fontSize=20;fontStyle=1;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3378" y="292.70000000000005" width="365" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-113" value="&lt;div style=&quot;font-size: 18px;&quot;&gt;Subscription: &lt;span style=&quot;font-weight: normal; font-size: 18px;&quot;&gt;XXX&lt;/span&gt;&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Verdana;fontSize=18;fontStyle=1;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3591" y="389.69000000000005" width="540" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-114" value="&lt;div style=&quot;font-size: 18px;&quot;&gt;Resource Group: &lt;span style=&quot;font-weight: normal; font-size: 18px;&quot;&gt;XXX&lt;/span&gt;&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Verdana;fontSize=18;fontStyle=1;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4299" y="437.35000000000014" width="460" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-115" value="pep-sharedservice-01-scus-vnet" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=16;strokeColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3343" y="419.19000000000005" width="160" height="35" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-116" value="pep-prod-datahub-02-scus-vnet" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=16;strokeColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3676" y="429.69000000000005" width="405" height="35" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-117" value="pep-network-isolateddmz-scus-01-vnet" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=16;strokeColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3343" y="1015.8800000000001" width="160" height="35" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-118" value="pep-restricted-02-subnet" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=16;strokeColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3671" y="506.07000000000016" width="250" height="35" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-119" value="&lt;div&gt;pep-network-isolated-prod-datahub-scus-01-vnet&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=16;strokeColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3681" y="1064.69" width="400" height="35" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-120" value="PE  TLS : 1.2 TCP : 443" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=17;fontColor=#333333;fillColor=default;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4256" y="549.69" width="115" height="35" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-121" value="PE&amp;nbsp; TLS : 1.2 TCP : 443" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=17;fontColor=#333333;fillColor=default;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4251" y="1049" width="115" height="35" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-122" value="PE&amp;nbsp; TLS : 1.2 TCP : 443" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=17;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4256" y="878.63" width="115" height="35" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-123" value="PE  TLS : 1.2 TCP : 443" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=17;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4257" y="1231" width="115" height="35" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-124" value="&lt;div style=&quot;font-size: 14px;&quot;&gt;Peering&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=14;strokeColor=none;align=center;rotation=0;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4045" y="831.04" width="55" height="25" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-125" value="&lt;div style=&quot;font-size: 14px;&quot;&gt;Peering&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=14;strokeColor=none;align=center;rotation=0;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3516.1899999999996" y="442.3499999999999" width="55" height="25" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-126" value="&lt;div style=&quot;font-size: 14px;&quot;&gt;Peering&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=14;strokeColor=none;align=center;rotation=0;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3516" y="879.1800000000001" width="55" height="25" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-127" value="&lt;div style=&quot;font-size: 20px;&quot;&gt;Legend&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Verdana;fontSize=20;fontStyle=1;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3336" y="1449.69" width="105" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-128" value="&lt;div style=&quot;text-align: center; font-size: 29px;&quot;&gt;&lt;b style=&quot;background-color: initial; font-size: 29px;&quot;&gt;&lt;u style=&quot;font-size: 29px;&quot;&gt;Proposed Solution – Workflow&lt;/u&gt;&lt;/b&gt;&lt;/div&gt;&lt;ol style=&quot;font-size: 29px;&quot;&gt;&lt;li style=&quot;font-size: 29px;&quot;&gt;ADF – SHIR will pull data from internal/external and push to BLOB.&lt;/li&gt;&lt;li style=&quot;font-size: 29px;&quot;&gt;ADF-SHIR will copy data from BLOB to DF ADLS&lt;/li&gt;&lt;li style=&quot;font-size: 29px;&quot;&gt;Data transformation and harmonization will be done by Databricks and Transformed data will be stored in DF ADLS as Bronze and Silver. Aggregated data will be stored in Sector ADLS&lt;/li&gt;&lt;li style=&quot;font-size: 29px;&quot;&gt;Presto SQL Query Engine build views on top of ADLS data.&lt;/li&gt;&lt;li style=&quot;font-size: 29px;&quot;&gt;Reporting layer will directly connect to Presto and dashboards will be created in Power BI.&lt;/li&gt;&lt;/ol&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=29;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4821" y="343.69" width="559" height="540" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-129" value="&lt;div style=&quot;&quot;&gt;&lt;font style=&quot;font-size: 18px;&quot;&gt;User Interaction&lt;/font&gt;&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=20;strokeColor=none;align=center;rotation=0;fontColor=#333333;verticalAlign=top;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4281" y="1458.69" width="225" height="25" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-130" value="&lt;div style=&quot;&quot;&gt;&lt;font style=&quot;font-size: 18px;&quot;&gt;SE Connection&lt;/font&gt;&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=20;strokeColor=none;align=center;rotation=0;fontColor=#333333;verticalAlign=top;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4021" y="1458.69" width="225" height="25" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-131" value="&lt;div style=&quot;&quot;&gt;&lt;font style=&quot;font-size: 18px;&quot;&gt;PE Connection&lt;/font&gt;&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=20;strokeColor=none;align=center;rotation=0;fontColor=#333333;verticalAlign=top;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3761" y="1458.69" width="225" height="25" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-132" value="&lt;div style=&quot;&quot;&gt;&lt;font style=&quot;font-size: 18px;&quot;&gt;System Connection&lt;/font&gt;&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=20;strokeColor=none;align=center;rotation=0;fontColor=#333333;verticalAlign=top;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3501" y="1458.69" width="225" height="25" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-133" value="&lt;div style=&quot;text-align: center; font-size: 29px; line-height: 30%;&quot;&gt;&lt;b style=&quot;background-color: initial; font-size: 29px;&quot;&gt;&lt;u style=&quot;font-size: 29px;&quot;&gt;Assumptions&lt;/u&gt;&lt;/b&gt;&lt;/div&gt;&lt;ol style=&quot;font-size: 29px; line-height: 120%;&quot;&gt;&lt;li style=&quot;font-size: 29px;&quot;&gt;New Components – ADF, Log Analytics, Azure Key Vault, Databricks cluster&lt;/li&gt;&lt;li style=&quot;font-size: 29px;&quot;&gt;Existing Components with Configuration changes – Power BI Gateway, SHIR, Presto and Databricks Workspace, Sector ADLS, DF ADLS, DF Blob&lt;/li&gt;&lt;/ol&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=29;align=left;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4818" y="989" width="559" height="330" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-134" value="&lt;div style=&quot;font-size: 20px;&quot;&gt;PepsiCo On-prem&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Verdana;fontSize=20;fontStyle=1;fontColor=#4D4D4D;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="2841" y="389.19000000000005" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-135" value="&lt;div style=&quot;font-size: 20px;&quot;&gt;3&lt;sup&gt;rd&lt;/sup&gt; Party API&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Verdana;fontSize=20;fontStyle=1;fontColor=#4D4D4D;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="2841" y="828.54" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-136" value="&lt;div style=&quot;font-size: 20px;&quot;&gt;Internal &amp;amp; External data source&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Verdana;fontSize=20;fontStyle=1;fontColor=#4D4D4D;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="2841" y="1118.85" width="320" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-137" value="1" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#333333;fontColor=#FFFFFF;fontFamily=Verdana;fontSize=17;strokeColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3266" y="598.69" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-138" value="1" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#333333;fontColor=#FFFFFF;fontFamily=Verdana;fontSize=17;strokeColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3546" y="579.69" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-139" value="5" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#333333;fontColor=#FFFFFF;fontFamily=Verdana;fontSize=17;strokeColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3546" y="654.69" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-140" value="1" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#333333;fontColor=#FFFFFF;fontFamily=Verdana;fontSize=17;strokeColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4181" y="568.6899999999999" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-141" value="2" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#333333;fontColor=#FFFFFF;fontFamily=Verdana;fontSize=17;strokeColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4216" y="568.6899999999999" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-142" value="1" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#333333;fontColor=#FFFFFF;fontFamily=Verdana;fontSize=17;strokeColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4179" y="1065" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-143" value="2" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#333333;fontColor=#FFFFFF;fontFamily=Verdana;fontSize=17;strokeColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4178" y="1175" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-145" value="3" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#333333;fontColor=#FFFFFF;fontFamily=Verdana;fontSize=17;strokeColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4204" y="884" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-146" value="4" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#333333;fontColor=#FFFFFF;fontFamily=Verdana;fontSize=17;strokeColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4204" y="822" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-147" value="1" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#333333;fontColor=#FFFFFF;fontFamily=Verdana;fontSize=17;strokeColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3226" y="774" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-148" value="1" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#333333;fontColor=#FFFFFF;fontFamily=Verdana;fontSize=17;strokeColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3246" y="1084" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-149" value="Port 443&#xa;TLS 1.2" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=17;fontColor=#4D4D4D;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3183.5" y="1154" width="82.5" height="40.15" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-150" value="Port 443&#xa;TLS 1.2" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=17;fontColor=#4D4D4D;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3183.5" y="1219" width="82.5" height="40.15" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-151" value="Port 443&#xa;TLS 1.2" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=17;fontColor=#4D4D4D;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3183.5" y="1284" width="82.5" height="40.15" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-152" value="REST API&lt;br&gt;Port 443&lt;br&gt;TLS 1.2" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=17;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3174" y="866.6800000000001" width="85" height="50" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-153" value="Oxford Economics | Weather Source | Nielsen | Covid 19 | Public Holidays | Consumer Mobility Index | NASA Power data Access Viewer | Monday.com | servicenow | jira software | upland PowerSteering | GenesysCloud" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=17;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="2856" y="866.6799999999998" width="295" height="177.32" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-154" value="&amp;lt;Port no&amp;gt;" style="text;whiteSpace=wrap;fontSize=11;fontFamily=Verdana;fontColor=#333333;align=center;html=1;spacing=0;labelBackgroundColor=default;textOpacity=80;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3061" y="715.69" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-155" value="&amp;lt;Port no&amp;gt;" style="text;whiteSpace=wrap;fontSize=11;fontFamily=Verdana;fontColor=#333333;align=center;html=1;spacing=0;labelBackgroundColor=default;textOpacity=80;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3061" y="661.69" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-156" value="TCP 22" style="text;whiteSpace=wrap;fontSize=11;fontFamily=Verdana;fontColor=#333333;align=center;html=1;spacing=0;labelBackgroundColor=default;textOpacity=80;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3061" y="603.69" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-157" value="TCP 1433" style="text;whiteSpace=wrap;fontSize=11;fontFamily=Verdana;fontColor=#333333;align=center;html=1;spacing=0;labelBackgroundColor=default;textOpacity=80;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3061" y="491" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-158" value="&amp;lt;Port no&amp;gt;" style="text;whiteSpace=wrap;fontSize=11;fontFamily=Verdana;fontColor=#333333;align=center;html=1;spacing=0;labelBackgroundColor=default;textOpacity=80;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3061" y="435.19000000000005" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-159" value="&amp;lt;Port no&amp;gt;" style="text;whiteSpace=wrap;fontSize=11;fontFamily=Verdana;fontColor=#333333;align=center;html=1;spacing=0;labelBackgroundColor=default;textOpacity=80;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3061" y="549.69" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-160" value="Port 443&#xa;TLS 1.2" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=17;fontColor=#4D4D4D;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3183.5" y="1409.54" width="82.5" height="40.15" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-161" value="Port 443&#xa;TLS 1.2" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=17;fontColor=#4D4D4D;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="2996" y="1409.54" width="82.5" height="40.15" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-162" value="5" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#333333;fontColor=#FFFFFF;fontFamily=Verdana;fontSize=17;strokeColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3256" y="1374" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-163" value="5" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#333333;fontColor=#FFFFFF;fontFamily=Verdana;fontSize=17;strokeColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3871" y="655" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-164" value="Reference Architecture: Business Intelligence with Data Lake&#xa;Reference Implementation #RI001" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=58;fontColor=#FFFFFF;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="2830" y="60" width="2355" height="150" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-165" value="Data Classification:   Restricted or Below" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=41;fontColor=#000000;labelBackgroundColor=#FFFF00;labelBorderColor=#FFFF00;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4985" y="82.5" width="445" height="105" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-166" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="5225" y="1456" width="228" height="54.81" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-167" value="&lt;div&gt;pep-paas-prod-01-scus-vnet&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=16;strokeColor=none;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="3681" y="864.69" width="400" height="35" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-168" value="&lt;div style=&quot;font-size: 29px;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;Existing Components&lt;/span&gt;&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=29;align=left;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4949" y="1392" width="331" height="50" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-169" value="PE&amp;nbsp; TLS : 1.2 TCP : 443" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=17;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4256" y="1163" width="115" height="35" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-170" value="PE&amp;nbsp; TLS : 1.2 TCP : 443" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=17;fontColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4256" y="808.6299999999999" width="115" height="35" as="geometry" />
        </mxCell>
        <mxCell id="FElwDSOs_kZ1xXXQAgqD-171" value="&lt;div style=&quot;font-size: 29px;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;New Components&lt;/span&gt;&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontFamily=Verdana;fontSize=29;align=left;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4949" y="1337" width="279" height="50" as="geometry" />
        </mxCell>
        <mxCell id="NGCn5jLktkn2RLFbLZjV-5" value="3" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#333333;fontColor=#FFFFFF;fontFamily=Verdana;fontSize=17;strokeColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4112" y="1194" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NGCn5jLktkn2RLFbLZjV-6" value="2" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#333333;fontColor=#FFFFFF;fontFamily=Verdana;fontSize=17;strokeColor=#333333;" parent="FElwDSOs_kZ1xXXQAgqD-110" vertex="1">
          <mxGeometry x="4214" y="1065" width="30" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
