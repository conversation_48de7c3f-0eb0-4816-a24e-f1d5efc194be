.flex {
  display: flex;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-even {
  flex: 1 1;
}

.flex-wrap {
  flex-wrap: wrap;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content:space-between;
}

.align-center {
  align-items: center;
}

.mx-auto {
  margin: 0 auto;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.hidden {
  display: none;
}

input.toggle {
  height: 0;
  width: 0;
  overflow: hidden;
  opacity: 0;
  position: absolute;
}

.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

@mixin spin($duration) {
  animation: spin $duration ease infinite;
  @keyframes spin {
    100% {
      transform: rotate(360deg);
    }
  }
}

@mixin fixed {
  position: fixed;
  top: 0;
  bottom: 0;
  overflow-x: hidden;
  overflow-y: auto;
}

@mixin outline {
  outline-style: auto;
  outline-color: currentColor;
  outline-color: -webkit-focus-ring-color;
}
