baseURL                     = 'https://www.pepgenx.dev/'
github_repo                 = "https://dev.azure.com/PepsiCoIT/PepGenX%20documentation%20site"
title                       = 'CloudTechDoc'
copyright                   = 'PepsiCo 2025'
theme                       = "hugo-book"

disablePathToLower          = false
enableGitInfo               = true
BookCommitPath              = 'commit'
enableRobotsTXT             = false

[module]
  [module.hugoVersion]
    extended = true
    min = '0.120.0'

[taxonomies]
   tag = "tags"
   category = "categories"

[outputs]
  home = ["HTML", "RSS", "JSON"]

[frontmatter]
  date = ['date', 'publishdate', 'pubdate', 'published', 'lastmod', 'modified']
  expiryDate = ['expirydate', 'unpublishdate']
  lastmod = [':git', 'lastmod', 'modified', 'date', 'publishdate', 'pubdate', 'published']
  publishDate = ['publishdate', 'pubdate', 'published', 'date']

[markup]
  [markup.goldmark]
    duplicateResourceFiles = false
    [markup.goldmark.extensions]
      definitionList = true
      footnote = true
      linkify = true
      linkifyProtocol = 'https'
      strikethrough = true
      table = true
      taskList = true
      [markup.goldmark.extensions.cjk]
        eastAsianLineBreaks = false
        eastAsianLineBreaksStyle = 'simple'
        enable = false
        escapedSpace = false
      [markup.goldmark.extensions.extras]
        [markup.goldmark.extensions.extras.delete]
          enable = false
        [markup.goldmark.extensions.extras.insert]
          enable = false
        [markup.goldmark.extensions.extras.mark]
          enable = false
        [markup.goldmark.extensions.extras.subscript]
          enable = true
        [markup.goldmark.extensions.extras.superscript]
          enable = true
      [markup.goldmark.extensions.passthrough]
        enable = false
        [markup.goldmark.extensions.passthrough.delimiters]
          block = []
          inline = []
      [markup.goldmark.extensions.typographer]
        apostrophe = '&rsquo;'
        disable = false
        ellipsis = '&hellip;'
        emDash = '&mdash;'
        enDash = '&ndash;'
        leftAngleQuote = '&laquo;'
        leftDoubleQuote = '&ldquo;'
        leftSingleQuote = '&lsquo;'
        rightAngleQuote = '&raquo;'
        rightDoubleQuote = '&rdquo;'
        rightSingleQuote = '&rsquo;'
    [markup.goldmark.parser]
      autoHeadingID = true
      autoHeadingIDType = 'github'
      wrapStandAloneImageWithinParagraph = true
      [markup.goldmark.parser.attribute]
        block = false
        title = true
    [markup.goldmark.renderHooks]
      [markup.goldmark.renderHooks.image]
        enableDefault = false
      [markup.goldmark.renderHooks.link]
        enableDefault = false
    [markup.goldmark.renderer]
      hardWraps = true
      unsafe = false
      xhtml = false