---
weight:                 3
title:                  "Comparative Analysis"
date: 2025-04-15
tags: []
summary: ""
---

# **Comparative Analysis of Agentic AI “Deep Research” Systems**

To navigate the 17 open-source “deep research” systems, we compare how each approaches multi-agent workflows, role assignment, inter-agent communication, user interaction, and data integration. We then synthesize common patterns and best practices for designing such agentic research assistants.

## **Overview of Systems and Architectures**

**Table 1 – Architectural Approaches of Deep Research Systems** (roles, coordination, and data integration):

| **System**                    | **Agent Roles & Structure**                                            | **Workflow Coordination**                                     | **Data/Tools Integration**                                      |
|-------------------------------|-----------------------------------------------------------------------|---------------------------------------------------------------|-----------------------------------------------------------------|
| **LangChain Local Deep Researcher** ([GitHub - langchain-ai/local-deep-researcher: Fully local web research and report writing assistant](https://github.com/langchain-ai/local-deep-researcher#:~:text=Local%20Deep%20Researcher%20is%20a,used%20to%20generate%20the%20summary)) ([GitHub - langchain-ai/local-deep-researcher: Fully local web research and report writing assistant](https://github.com/langchain-ai/local-deep-researcher#:~:text=and%20it%20will%20generate%20a,used%20to%20generate%20the%20summary)) | **Single agent with tools.** One LLM acts as researcher (no separate sub-agents). | **Iterative loop (sequential).** LLM generates a search query, summarizes results, reflects on gaps, then repeats for N cycles ([GitHub - langchain-ai/local-deep-researcher: Fully local web research and report writing assistant](https://github.com/langchain-ai/local-deep-researcher#:~:text=Local%20Deep%20Researcher%20is%20a,used%20to%20generate%20the%20summary)). No parallelism (one step at a time). | **Web search tool + summarizer.** Uses local LLM (via Ollama/LMStudio) and any search API (SerpAPI, etc.) to fetch and scrape web content ([GitHub - langchain-ai/local-deep-researcher: Fully local web research and report writing assistant](https://github.com/langchain-ai/local-deep-researcher#:~:text=Local%20Deep%20Researcher%20is%20a,used%20to%20generate%20the%20summary)). Final output is a Markdown report with sources. |
| **LangChain Open Deep Research** ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=Open%20Deep%20Research%20is%20an,report%20structure%2C%20and%20search%20tools)) ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=The%20multi,researcher%20architecture)) | **Two implementations:** (1) *Workflow mode:* single-agent with structured plan; (2) *Multi‑agent mode:* **Supervisor + multiple Researchers** ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=Open%20Deep%20Research%20is%20an,report%20structure%2C%20and%20search%20tools)) ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,framework%20is%20designed%20to%20support)). | **Workflow mode:** Planner LLM creates outline, then executes sections sequentially with reflection ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,Exa%2C%20ArXiv%2C%20PubMed%2C%20Linkup%2C%20etc)) ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,Exa%2C%20ArXiv%2C%20PubMed%2C%20Linkup%2C%20etc)). **Multi-agent mode:** Supervisor agent plans sections & spawns researcher agents in parallel ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,framework%20is%20designed%20to%20support)) ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,framework%20is%20designed%20to%20support)). Supervisor waits for all to finish, then compiles the final report. | **Pluggable search tools.** Supports many search providers (Tavily, Perplexity, Exa, Arxiv, PubMed, etc.) ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=Available%20search%20tools%3A)). Multi-agent mode currently uses Tavily web search for each researcher ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,search%20tools%20in%20the%20future)). Uses different LLMs for planning vs writing if configured ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=You%20can%20customize%20the%20research,assistant%20workflow%20through%20several%20parameters)). Outputs well-structured reports with citations. |
| **Perplexideez** (Bruno St. John) ([Perplexideez - Self-hosted AI-powered search with SSO, multi-user support, shareable links, and more. : r/LocalLLaMA](https://www.reddit.com/r/LocalLLaMA/comments/1gry1mt/perplexideez_selfhosted_aipowered_search_with_sso/#:~:text=It%27s%20a%20Perplexity%20clone%20that,on%20search%20results%20from%20SearXNG)) ([Perplexideez - Self-hosted AI-powered search with SSO, multi-user support, shareable links, and more. : r/LocalLLaMA](https://www.reddit.com/r/LocalLLaMA/comments/1gry1mt/perplexideez_selfhosted_aipowered_search_with_sso/#:~:text=Quite%20a%20few%20neat%20things%21)) | **Single agent chatbot** (Perplexity.ai clone). No explicit multiple AI roles, but uses separate models per task (configurable) ([GitHub - brunostjohn/perplexideez: Search the web and your self-hosted apps using local AI agents.](https://github.com/brunostjohn/perplexideez#:~:text=Image%3A%20Screenshot)). | **Interactive Q&A loop.** Agent handles user query, calls search API, then generates answer with sources. Conversation persists so user can ask follow-ups, and agent maintains context (chat history) with each turn. | **Web search via SearxNG** metasearch ([Perplexideez - Self-hosted AI-powered search with SSO, multi-user support, shareable links, and more. : r/LocalLLaMA](https://www.reddit.com/r/LocalLLaMA/comments/1gry1mt/perplexideez_selfhosted_aipowered_search_with_sso/#:~:text=It%27s%20a%20Perplexity%20clone%20that,on%20search%20results%20from%20SearXNG)). **Tools:** local or OpenAI LLM for answer synthesis ([Perplexideez - Self-hosted AI-powered search with SSO, multi-user support, shareable links, and more. : r/LocalLLaMA](https://www.reddit.com/r/LocalLLaMA/comments/1gry1mt/perplexideez_selfhosted_aipowered_search_with_sso/#:~:text=It%27s%20a%20Perplexity%20clone%20that,on%20search%20results%20from%20SearXNG)); can use different LLMs for sub-tasks (e.g. smaller model for summarizing, larger for final answer) ([GitHub - brunostjohn/perplexideez: Search the web and your self-hosted apps using local AI agents.](https://github.com/brunostjohn/perplexideez#:~:text=Image%3A%20Screenshot)). UI features: source hover previews ([GitHub - brunostjohn/perplexideez: Search the web and your self-hosted apps using local AI agents.](https://github.com/brunostjohn/perplexideez#:~:text=,come%20from)), auto follow-up question suggestions ([GitHub - brunostjohn/perplexideez: Search the web and your self-hosted apps using local AI agents.](https://github.com/brunostjohn/perplexideez#:~:text=)), favorites and shareable links ([GitHub - brunostjohn/perplexideez: Search the web and your self-hosted apps using local AI agents.](https://github.com/brunostjohn/perplexideez#:~:text=)). |
| **Perplexica** (ItzCrazyKns) ([GitHub - ItzCrazyKns/Perplexica: Perplexica is an AI-powered search engine. It is an Open source alternative to Perplexity AI](https://github.com/ItzCrazyKns/Perplexica#:~:text=,currently%20has%206%20focus%20modes)) ([GitHub - ItzCrazyKns/Perplexica: Perplexica is an AI-powered search engine. It is an Open source alternative to Perplexity AI](https://github.com/ItzCrazyKns/Perplexica#:~:text=,opinions%20related%20to%20the%20query)) | **Single agent chatbot** (Perplexity clone). Internally uses a chain-of-thought prompt with tools, not multiple distinct agents. | **Two modes:** *Normal mode* – single query/answer cycle; *“Copilot” mode* – agent autonomously issues **multiple queries iteratively** to gather more context ([GitHub - ItzCrazyKns/Perplexica: Perplexica is an AI-powered search engine. It is an Open source alternative to Perplexity AI](https://github.com/ItzCrazyKns/Perplexica#:~:text=,tasks%20that%20do%20not%20require)). Copilot mode effectively plans sub-searches (visits top links to find relevant info) ([GitHub - ItzCrazyKns/Perplexica: Perplexica is an AI-powered search engine. It is an Open source alternative to Perplexity AI](https://github.com/ItzCrazyKns/Perplexica#:~:text=,tasks%20that%20do%20not%20require)). | **Web search via SearxNG** ([GitHub - ItzCrazyKns/Perplexica: Perplexica is an AI-powered search engine. It is an Open source alternative to Perplexity AI](https://github.com/ItzCrazyKns/Perplexica#:~:text=just%20using%20the%20context%20by,and%20papers%2C%20ideal%20for%20academic)) (ensures up-to-date info ([GitHub - ItzCrazyKns/Perplexica: Perplexica is an AI-powered search engine. It is an Open source alternative to Perplexity AI](https://github.com/ItzCrazyKns/Perplexica#:~:text=,overhead%20of%20daily%20data%20updates))). **Focus modes:** specialized search tools for academic papers, YouTube, Reddit, WolframAlpha etc. (agent selects the appropriate search API based on query type) ([GitHub - ItzCrazyKns/Perplexica: Perplexica is an AI-powered search engine. It is an Open source alternative to Perplexity AI](https://github.com/ItzCrazyKns/Perplexica#:~:text=,based%20on%20the%20search%20query)). Supports OpenAI, Ollama, Anthropic, etc. for LLMs ([GitHub - ItzCrazyKns/Perplexica: Perplexica is an AI-powered search engine. It is an Open source alternative to Perplexity AI](https://github.com/ItzCrazyKns/Perplexica#:~:text=,wish%20to%20use%20OpenAI%27s%20models)). Answers include citations. |
| **GPT Researcher** (assafelovic) ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=GPT%20Researcher%20is%20an%20open,research%20on%20any%20given%20task)) ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=Architecture)) | **Planner + Execution Agents + Publisher.** The system has distinct phases handled by what can be seen as role-specific agents: a Planner agent breaks the query into sub-questions; multiple **Crawler/Researcher agents** gather info; a Publisher agent compiles the final report ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=Architecture)) ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=The%20core%20idea%20is%20to,findings%20into%20a%20comprehensive%20report)). (Implemented via one orchestrating code loop with the LLM adopting different functions per phase.) | **Structured plan-and-solve loop ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=This%20is%20when%20I%20stumbled,subtasks%20according%20to%20the%20plan)) ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=As%20it%20relates%20to%20research%2C,the%20agent%20concludes%20the%20research)):** The Planner LLM first generates a detailed research plan (outline of questions) ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=Architecture)). Then for each sub-question, parallel worker tasks perform web searches and extract relevant content (executors run concurrently for speed) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=,produce%20a%20final%20comprehensive%20report)) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=,this%20in%20a%20functional%20UI)). Results are aggregated and fed back to the LLM; it decides if more iterations are needed (knowledge gaps) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=parallel%20to%20determine%20page%20usefulness,be%20printed%20in%20the%20output)) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=,report%20will%20be%20printed%20in)). Finally, the Publisher LLM writes a comprehensive report using all collected information ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=The%20core%20idea%20is%20to,findings%20into%20a%20comprehensive%20report)) ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=,into%20a%20final%20research%20report)). | **Web + local files, images.** Uses SERP API for web search and Jina’s web scraper for page content ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=gathered%20all%20the%20necessary%20details,several%20services%20to%20do%20so)) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=,page%20relevance%2C%20and%20extracting%20context)). Supports uploading local documents or images for research ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=,PDF%2C%20Word%2C%20and%20other%20formats)) (it can “smart scrape” images and PDF content). Employs multiple LLM calls: e.g. Claude or GPT-4 for planning and writing, possibly smaller LLMs for extraction. Emphasizes factuality: cites 20+ sources, uses function calling for structured outputs to avoid hallucinations ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,model%20you%20are%20using%20here)) ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,mini%2C%20and%20gpt4.1.%20See%20here)). |
| **OpenBMB ChatDev** ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=,designing%2C%20coding%2C%20testing%20and%20documenting)) ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=%E2%80%A2June%2012%2C%202024%3A%20We%20introduced,structures%20and%20offering%20richer%20solutions)) | **Simulated human team.** Multiple **domain-specific agents** with fixed roles (CEO, CTO, Programmer, Reviewer, Tester, Designer) collaborate as a virtual software company ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=,designing%2C%20coding%2C%20testing%20and%20documenting)). The roles are pre-defined with distinct duties (e.g. CEO defines requirements, Programmer writes code, Tester checks it) ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=intelligent%20agents%20holding%20different%20roles%2C,agent%20organizational)) ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=and%20Role%20settings,initial%20version%20of%20the%20ChatDev)). | **Turn-based roleplay.** Agents engage in a structured dialogue, passing control in a chain: e.g. CEO -> CTO -> Programmer -> Reviewer, etc., each contributing according to role ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=,designing%2C%20coding%2C%20testing%20and%20documenting)). The process is orchestrated by the system following a scripted sequence of “meetings” (design phase, coding phase, testing phase) ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=,designing%2C%20coding%2C%20testing%20and%20documenting)) ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=through%20programming.,studying%20and%20understanding%20collective%20intelligence)). Recent versions support a **graph-based Multi-Agent Network** where agents form a directed acyclic graph for more flexible topologies ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=%E2%80%A2June%2012%2C%202024%3A%20We%20introduced,structures%20and%20offering%20richer%20solutions)) (scaling beyond the linear chain) ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=%E2%80%A2June%2012%2C%202024%3A%20We%20introduced,structures%20and%20offering%20richer%20solutions)). | **Internal knowledge + code execution.** Primarily relies on the agents’ trained knowledge (no web search) to generate software. However, agents have tools: e.g. a code execution sandbox to run/test code. No external internet queries (focused on coding tasks), though design agent might use an image tool for UI design (if included). Interaction with user: the “Manager” agent takes an initial natural language idea from the user (e.g. “build an app to X”) and coordinates the team to produce code ([ChatDev.ai | ai agent](https://chatdev.ai/#:~:text=ChatDev.ai%20,Officer%2C%20Chief%20Technology%20Officer)) ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=,designing%2C%20coding%2C%20testing%20and%20documenting)). (ChatDev showcases multi-agent organization more than info retrieval.) |
| **Jina AI “node-DeepResearch”** ([GitHub - jina-ai/node-DeepResearch: Keep searching, reading webpages, reasoning until it finds the answer (or exceeding the token budget)](https://github.com/jina-ai/node-DeepResearch#:~:text=Keep%20searching%2C%20reading%20webpages%2C%20reasoning,or%20exceeding%20the%20token%20budget)) ([GitHub - jina-ai/node-DeepResearch: Keep searching, reading webpages, reasoning until it finds the answer (or exceeding the token budget)](https://github.com/jina-ai/node-DeepResearch#:~:text=,2%5D%60%2C)) | **Single agent with tool-use (ReAct).** The agent is essentially one LLM that *thinks* and uses tools in a loop. No separate sub-agent personas; reasoning is handled via the LLM’s chain-of-thought which includes calling search/reader tools. | **Recursive reasoning loop.** The LLM outputs `<think>` steps and decides when to call the search tool ([GitHub - jina-ai/node-DeepResearch: Keep searching, reading webpages, reasoning until it finds the answer (or exceeding the token budget)](https://github.com/jina-ai/node-DeepResearch#:~:text=,2%5D%60%2C)). It will keep issuing search queries and reading results until it finds an answer or hits a token limit ([GitHub - jina-ai/node-DeepResearch: Keep searching, reading webpages, reasoning until it finds the answer (or exceeding the token budget)](https://github.com/jina-ai/node-DeepResearch#:~:text=Keep%20searching%2C%20reading%20webpages%2C%20reasoning,or%20exceeding%20the%20token%20budget)). The loop is automated by the code: each `<search>` action triggers the search API, results are fed back, and the LLM decides to either answer or dig deeper. Jina’s implementation includes advanced steps like snippet ranking: it vector-embeds page content and selects the most relevant parts to feed the LLM ([Snippet Selection and URL Ranking in DeepSearch/DeepResearch](https://jinaai.cn/news/snippet-selection-and-url-ranking-in-deepsearch-deepresearch#:~:text=function%20cherryPick%28question%2C%20longContext%2C%20options%29%20,options.numSnippets%29%20return%20longContext)) ([Snippet Selection and URL Ranking in DeepSearch/DeepResearch](https://jinaai.cn/news/snippet-selection-and-url-ranking-in-deepsearch-deepresearch#:~:text=Make%20sure%20you%20call%20Jina,set%20as%20bellow)). | **Web search & web scraping.** Uses Jina’s **Tavily/Reader** API: a combined tool that searches the web and returns raw page text (or Jina’s own embeddings) ([GitHub - jina-ai/node-DeepResearch: Keep searching, reading webpages, reasoning until it finds the answer (or exceeding the token budget)](https://github.com/jina-ai/node-DeepResearch#:~:text=We%20use%20Gemini%20%28latest%20%60gemini,ai)). Also employs Jina Embeddings for **“late chunking”** – splitting long texts and embedding to cherry-pick the most relevant snippets for the LLM ([Snippet Selection and URL Ranking in DeepSearch/DeepResearch](https://jinaai.cn/news/snippet-selection-and-url-ranking-in-deepsearch-deepresearch#:~:text=However%2C%20chunk%20embeddings%20created%20in,To%20further)) ([Snippet Selection and URL Ranking in DeepSearch/DeepResearch](https://jinaai.cn/news/snippet-selection-and-url-ranking-in-deepsearch-deepresearch#:~:text=const%20similarities%20%3D%20chunkEmbeddings.map%28embed%20%3D,questionEmbedding%2C%20embed)). The LLM (Gemini, GPT-4, or local) thus only sees distilled content ([Snippet Selection and URL Ranking in DeepSearch/DeepResearch](https://jinaai.cn/news/snippet-selection-and-url-ranking-in-deepsearch-deepresearch#:~:text=function%20cherryPick%28question%2C%20longContext%2C%20options%29%20,options.numSnippets%29%20return%20longContext)) ([Snippet Selection and URL Ranking in DeepSearch/DeepResearch](https://jinaai.cn/news/snippet-selection-and-url-ranking-in-deepsearch-deepresearch#:~:text=const%20chunksPerSnippet%20%3D%20Math,similarities)). Answers are streamed and include footnote-style citations ([GitHub - jina-ai/node-DeepResearch: Keep searching, reading webpages, reasoning until it finds the answer (or exceeding the token budget)](https://github.com/jina-ai/node-DeepResearch#:~:text=,2%5D%60%2C)) ([GitHub - jina-ai/node-DeepResearch: Keep searching, reading webpages, reasoning until it finds the answer (or exceeding the token budget)](https://github.com/jina-ai/node-DeepResearch#:~:text=questions%20that%20require%20deep%20reasoning,depending%20on%20the%20API%20key)). |
| **Matt Shumer OpenDeepResearcher** ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=This%20notebook%20implements%20an%20AI,several%20services%20to%20do%20so)) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=,produce%20a%20final%20comprehensive%20report)) | **Single orchestrator with concurrent sub-tasks.** No distinct “agents” with names, but the implementation breaks the job into subqueries and page evaluations which happen in parallel threads. Effectively, the LLM is used in different capacities: query brainstormer, result evaluator, and final writer. | **Autonomous research loop** with human-defined limits. The system asks the LLM to generate multiple search queries from the user’s topic (breadth) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=1,to%20four%20distinct%20search%20queries)). It runs all those searches concurrently (async) and fetches content for top links in parallel ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=until%20no%20further%20queries%20are,produce%20a%20final%20comprehensive%20report)) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=,based%20on%20all%20gathered%20context)). The LLM then judges each page’s usefulness and extracts key points (each page is processed by an LLM call, concurrently) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=until%20no%20further%20queries%20are,this%20in%20a%20functional%20UI)) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=,based%20on%20all%20gathered%20context)). After one round, the accumulated “learnings” are fed to the LLM to decide if another round of queries is needed ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=parallel%20to%20determine%20page%20usefulness,be%20printed%20in%20the%20output)) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=analyzed%20by%20the%20LLM%20to,be%20printed%20in%20the%20output)) (depth control). This repeats until no new queries are required. Finally, the LLM produces a comprehensive Markdown report from all gathered info ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=parallel%20to%20determine%20page%20usefulness,be%20printed%20in%20the%20output)) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=,be%20printed%20in%20the%20output)). | **Web search (Google via SerpAPI) + web content via Jina** ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=gathered%20all%20the%20necessary%20details,several%20services%20to%20do%20so)). Uses OpenAI or Anthropic models via OpenRouter API for all LLM functions ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=,page%20relevance%2C%20and%20extracting%20context)). Built to be easily configurable: just a notebook requiring API keys (SerpAPI for search, Jina for scraping) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=Requirements)) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=2.%20Install%20)). It emphasizes simplicity (under 500 LOC) ([GitHub - dzhng/deep-research: An AI-powered research assistant that performs iterative, deep research on any topic by combining search engines, web scraping, and large language models.  The goal of this repo is to provide the simplest implementation of a deep research agent - e.g. an agent that can refine its research direction overtime and deep dive into a topic.](https://github.com/dzhng/deep-research#:~:text=An%20AI,scraping%2C%20and%20large%20language%20models)). The output is a final report with citations and a structured summary of findings. |
| **Nick Scamara open-deep-research** (Firecrawl) ([GitHub - nickscamara/open-deep-research: An open source deep research clone. AI Agent that reasons large amounts of web data extracted with Firecrawl](https://github.com/nickscamara/open-deep-research#:~:text=An%20open%20source%20deep%20research,web%20data%20extracted%20with%20Firecrawl)) ([Building a Clone of OpenAI's Deep Research with TypeScript and Firecrawl](https://www.firecrawl.dev/blog/open-deep-research-explainer#:~:text=relevant%20web%20data.%20,information%20to%20answer%20user%E2%80%99s%20query)) | **Single manager agent with unified tools.** The system operates like a chat assistant that can perform either a simple search or a multi-step deep research. Internally it leverages the **Firecrawl** service which itself handles searching and scraping, so no need for multiple LLM agents. | **Two modes (user-selected):** *“Search”* – one-shot answer (like a normal search engine answer), and *“Deep Research”* – a multi-step autonomous process ([Building a Clone of OpenAI's Deep Research with TypeScript and Firecrawl](https://www.firecrawl.dev/blog/open-deep-research-explainer#:~:text=appropriate%20process%3A%20,to%20generate%20insights%20or%20summaries)). In Deep Research mode, the agent performs an initial web search, scrapes the top results, **analyzes/summarizes the content**, then possibly **iterates**: the LLM planner may identify new questions or missing info and re-launch another round of search->scrape->analyze ([Building a Clone of OpenAI's Deep Research with TypeScript and Firecrawl](https://www.firecrawl.dev/blog/open-deep-research-explainer#:~:text=relevant%20web%20data.%20,information%20to%20answer%20user%E2%80%99s%20query)). This continues until the agent determines it has enough information to answer fully ([Building a Clone of OpenAI's Deep Research with TypeScript and Firecrawl](https://www.firecrawl.dev/blog/open-deep-research-explainer#:~:text=relevant%20web%20data.%20,information%20to%20answer%20user%E2%80%99s%20query)). All steps are orchestrated on the server side (with optional human confirmation of sub-tasks if configured). | **Web search & scrape via Firecrawl API** ([Building a Clone of OpenAI's Deep Research with TypeScript and Firecrawl](https://www.firecrawl.dev/blog/open-deep-research-explainer#:~:text=Web%20Scraping%20%26%20Search%20with,Firecrawl)) ([Building a Clone of OpenAI's Deep Research with TypeScript and Firecrawl](https://www.firecrawl.dev/blog/open-deep-research-explainer#:~:text=match%20at%20L326%20const%20scrapeResult,)). Firecrawl provides `search(query)` and `scrapeUrl(url)` endpoints, so the agent essentially delegates retrieval to that service (which returns page text in Markdown or HTML) ([Building a Clone of OpenAI's Deep Research with TypeScript and Firecrawl](https://www.firecrawl.dev/blog/open-deep-research-explainer#:~:text=One%20of%20the%20key%20pieces,methods)) ([Building a Clone of OpenAI's Deep Research with TypeScript and Firecrawl](https://www.firecrawl.dev/blog/open-deep-research-explainer#:~:text=match%20at%20L326%20const%20scrapeResult,)). LLM (OpenAI GPT-4 by default) is used for reasoning and answering with the retrieved text. Also supports retrieval from **vector DB (Postgres/Redis)** for session memory and uses Vercel Blob for storing conversation history ([Building a Clone of OpenAI's Deep Research with TypeScript and Firecrawl](https://www.firecrawl.dev/blog/open-deep-research-explainer#:~:text=,complex%20process%20involving%20multiple%20steps)) ([Building a Clone of OpenAI's Deep Research with TypeScript and Firecrawl](https://www.firecrawl.dev/blog/open-deep-research-explainer#:~:text=%2A%20Real,Storage%20%26%20Persistence)). The system streams the final answer to the web UI, with real-time updates as it’s generated ([Building a Clone of OpenAI's Deep Research with TypeScript and Firecrawl](https://www.firecrawl.dev/blog/open-deep-research-explainer#:~:text=4)) ([Building a Clone of OpenAI's Deep Research with TypeScript and Firecrawl](https://www.firecrawl.dev/blog/open-deep-research-explainer#:~:text=5)). |
| **HuggingFace SmolAgents** ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=%2B,)) ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=,Visit%20webpage%20tool)) | **Flexible multi-agent or single-agent via code execution.** SmolAgents is a *framework* – developers explicitly define agents (as Python functions or classes) and tools. In examples, a **Manager agent** can delegate to other agents: e.g. a WebSearchAgent with its own tools, etc. ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=%2B,)) ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=Code%20Interpreter%20%20%20,)). Each agent typically uses one LLM backend, thinking by generating Python code that calls tools (the “agent thinks in code”). | **Orchestration by function calls.** In SmolAgents, agents communicate by one agent calling another as if it were a function (the framework wraps agents so they can be invoked as tools). For example, a Manager agent’s plan might literally call `result = web_search_agent(query)` in generated code ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=%2B,)) ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=,Visit%20webpage%20tool)). The WebSearch agent then internally uses its DuckDuckGo and VisitWebpage tools to return an answer. The Manager can also use a Code Interpreter tool for computations in the same way ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=_______________,Visit%20webpage%20tool)) ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=Code%20Interpreter%20%20%20,)). This hierarchical call structure is defined by the developer; the LLM fills in the logic within each agent’s code. All agents run step-by-step under a Python executor that executes any tool invocations the LLM writes. | **Tools as code libraries.** SmolAgents comes with a library of tools (e.g. `DuckDuckGoSearchTool`, `VisitWebpageTool`) ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=Create%20a%20web%20search%20tool)) ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=%40tool%20def%20visit_webpage%28url%3A%20str%29%20,content%20as%20a%20markdown%20string)) and supports custom tools (just Python functions decorated with `@tool`) ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=%40tool%20def%20visit_webpage%28url%3A%20str%29%20,content%20as%20a%20markdown%20string)). The example multi-agent browser builds a tool to fetch webpage content and a search tool ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=Create%20a%20web%20search%20tool)) ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=%40tool%20def%20visit_webpage%28url%3A%20str%29%20,content%20as%20a%20markdown%20string)), then wraps them into a WebSearchAgent. The agent(s) can use any HuggingFace-hosted LLM (the example uses Qwen-32B) ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=login)) ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=%E2%9A%A1%EF%B8%8F%20Our%20agent%20will%20be,easily%20run%20any%20OS%20model)). This framework is model-agnostic. It emphasizes minimal abstractions – the “agents” are essentially code that the LLM writes/executes, which makes adding new data sources (APIs, DB queries, etc.) as simple as providing a Python function. |
| **OpenAGI** (AI Planet) ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=,go%20brr%E2%80%A6%20with%20REACT%20PROMPTING)) ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=This%20release%20brings%20a%20groundbreaking,and%20strict%20instructions%20to%20follow)) | **Admin (manager) + dynamic Workers** ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=,go%20brr%E2%80%A6%20with%20REACT%20PROMPTING)) ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=This%20release%20brings%20a%20groundbreaking,and%20strict%20instructions%20to%20follow)). OpenAGI is a general platform where a central Admin agent can autonomously create multiple **Worker agents** for sub-tasks. Roles can be dynamically determined by the Planner module or preset by the developer. For example, the system might spawn a “Web Research Worker” or a “Code QA Worker” depending on the query. | **Autonomous task decomposition.** A Task Planner (itself LLM-powered) breaks the user’s query into discrete tasks and suggests what worker role each task needs ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=,go%20brr%E2%80%A6%20with%20REACT%20PROMPTING)) ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=This%20release%20brings%20a%20groundbreaking,and%20strict%20instructions%20to%20follow)). The Admin agent then instantiates those worker agents with appropriate instructions ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=This%20release%20brings%20a%20groundbreaking,and%20strict%20instructions%20to%20follow)) ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=need%20for%20manual%20setup,and%20strict%20instructions%20to%20follow)). Workers run in parallel or sequence as needed, each using tools to complete their assignment. They report results back to Admin. The Admin agent monitors progress, possibly reprioritizing or creating new workers if new sub-tasks arise. Once all sub-tasks are done, Admin aggregates the outcomes into a final answer or result. OpenAGI also supports a human-in-the-loop during planning (the Planner can output tasks that a human user approves or edits) ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=and%20other%20descriptions%20to%20define,a%20worker)). | **Highly extensible toolset.** Built-in support for web search (Tavily API) ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=Let%E2%80%99s%20build%20a%20Cricket%20Update,how%20we%20can%20get%20started)), code execution, document writing, YouTube info (via `yt-dlp`), etc. – essentially *any new tool can be integrated* via a “cookbook” template ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=workflow%20to%20involve%20human%20in,task%20created%20by%20the%20Planner)). LLM-agnostic: can plug in OpenAI, local models, etc. In the example, they use **Gemini 1.5** for the LLM and Tavily for search ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=Let%E2%80%99s%20build%20a%20Cricket%20Update,how%20we%20can%20get%20started)). Memory: OpenAGI can maintain chat history and feedback between steps ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=2,task%20created%20by%20the%20Planner)). This system is more of a development framework; it requires some configuration but automates the heavy lifting of spawning/coordinating multiple specialized agents for complex workflows. |
| **OpenAssistantGPT** (openassistantgpt.io) ([OpenAssistantGPT - The complete platform to build your AI chatbot](https://www.openassistantgpt.io/#:~:text=Utilize%20AI%20Agent%20Actions%20to,enabling%20dynamic%20interactions%20and%20responses)) ([OpenAssistantGPT - The complete platform to build your AI chatbot](https://www.openassistantgpt.io/#:~:text=Web%20Search)) | **Single chatbot agent with tool APIs.** This platform does not orchestrate multiple LLM agents; it provides one chat assistant persona which can invoke **actions** (like web search or API calls) as needed. Roles are minimal – essentially system vs. assistant in the prompt (no intermediate “researcher” role separate from the assistant). | **Interactive chat workflow.** The user converses with a single AI assistant. When the assistant needs external info, it triggers a tool function (OpenAssistantGPT supports function calls for web search, data queries, etc.) and incorporates the results into the conversation ([OpenAssistantGPT - The complete platform to build your AI chatbot](https://www.openassistantgpt.io/#:~:text=Utilize%20AI%20Agent%20Actions%20to,enabling%20dynamic%20interactions%20and%20responses)). There isn’t a multi-turn debate between agents; instead it’s a ReAct-style single agent. However, it **iterates internally**: e.g. if the first search doesn’t yield an answer, the assistant may issue another query. These steps are hidden behind the scenes – the user just sees the assistant’s messages. | **Web, files, and APIs.** Has built-in **Web Search** capability ([OpenAssistantGPT - The complete platform to build your AI chatbot](https://www.openassistantgpt.io/#:~:text=Web%20Search)) to fetch live info. Also supports an **“AI Actions”** feature to call arbitrary API endpoints with parameters ([OpenAssistantGPT - The complete platform to build your AI chatbot](https://www.openassistantgpt.io/#:~:text=AI%20Agent%20Actions)) (e.g. query a database or run a calculation) – effectively allowing integration of custom tools. Supports **file attachments**: the user can upload PDFs, CSVs, images, etc., and the assistant will analyze them ([OpenAssistantGPT - The complete platform to build your AI chatbot](https://www.openassistantgpt.io/#:~:text=File%20Attachments)). The system is designed as a SaaS: multi-user, SSO, etc., with a web UI. It focuses on making a single AI chatbot as capable as possible through tool use, rather than splitting tasks among different agents. |
| **AutoGPT (Significant Gravitas)** ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=After%20AutoGPT%20was%20published%2C%20I,the%20future%20of%20online%20research)) ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=Moving%20from%20infinite%20loops%20to,deterministic%20results)) | **Single autonomous agent** (with optional self-critic). By default AutoGPT runs one GPT-based agent (“AI Assistant”) that manages a task list. (Some setups introduced a secondary “Critic” agent to evaluate the assistant’s decisions, but this is not always on by default.) The agent effectively acts as its own manager, generating and executing plans. | **Reflexive loop with memory.** AutoGPT prompts the LLM to think step-by-step: it generates a plan (next action + reasoning + reflection) and a JSON command to execute ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=After%20AutoGPT%20was%20published%2C%20I,the%20future%20of%20online%20research)) ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=Moving%20from%20infinite%20loops%20to,deterministic%20results)). The framework executes the command (e.g. search the web, read a file) and returns the result to the LLM, which then incorporates this new information into the next loop. It continues this **thought→action→result** cycle until the main goal is achieved or a stopping criterion is met ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=Moving%20from%20infinite%20loops%20to,deterministic%20results)) ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=Image)). The agent maintains a list of objectives and completed tasks in a memory file or vector store, which it reviews each iteration to avoid going in circles. (AutoGPT infamously could get stuck in loops; later improvements like “Plan-and-Solve” prompting were introduced to mitigate this ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=The%20first%20step%20in%20solving,time%20frame%2C%20without%20human%20interference)) ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=Example%20inputs%20and%20outputs%20of,Solve%20%28PS%29%20prompting)).) | **Wide toolset and memory.** AutoGPT integrates **web search & web browsing**, local file I/O, code execution, etc., through its command system. It often uses the official **Google search API or Selenium** to fetch web content, and has simple scraping for text. It also uses a **vector memory** (Pinecone or local) to store facts it has learned so it can recall them across iterations. No separate knowledge base search; it relies on internet search and what it can write to its local files. User interaction is minimal: the user gives an initial goal, then the agent runs autonomously, occasionally asking for feedback if not in continuous mode. |
| **GAIR OpenResearcher** ([OpenResearcher: Unleashing AI for Accelerated Scientific Research](https://arxiv.org/html/2408.06941v1#:~:text=Demo%2C%20video%2C%20and%20code%20are,OpenResearcher)) ([OpenResearcher: Unleashing AI for Accelerated Scientific Research](https://ui.adsabs.harvard.edu/abs/2024arXiv240806941Z/abstract#:~:text=OpenResearcher%3A%20Unleashing%20AI%20for%20Accelerated,48550%2FarXiv)) | **Hybrid: single agent with modular subtasks.** OpenResearcher is tailored for scientific literature research. It doesn’t explicitly spawn multiple agent personas; instead it combines several components (like semantic search, paper reading, Q&A) in one pipeline. One can view each component as an “agent function” that the main system invokes. For example, it might have a **Retriever module** and an **Analyzer (LLM)** module. (The associated paper describes it as an LLM orchestrating various domain expert modules.) | **Pipeline + conversational loop.** The user asks a research question. OpenResearcher then: (1) uses semantic search across scientific corpora to find relevant papers, (2) uses an LLM to summarize or extract answers from those papers, possibly (3) asks follow-up questions or refines the search based on initial findings (creating a loop until sufficient confidence). The process is not fully detailed here, but likely it decomposes complex queries into sub-queries (like topics or aspects) and aggregates the findings. Unlike others, it strongly focuses on retrieving from **academic sources** and may maintain a graph of connections between findings (they mention a Graph-based RAG approach). | **Academic databases, custom RAG.** Integrates with scholarly data sources: e.g. **Semantic Scholar/ArXiv APIs or local paper databases**. Likely uses embedding-based **vector search** to find papers related to the query, and possibly a **knowledge graph** to connect concepts across papers (the mention of GraphRAG suggests building a graph of papers/citations or concepts to guide research). The LLM (likely GPT-4 or similar) then answers questions using the retrieved literature. User interaction: probably a chat where the user can refine queries. OpenResearcher outputs not just answers but references to papers (for scientific accuracy). It essentially acts as an AI literature review assistant with a combination of retrieval, analysis, and iterative query refinement. |
| **CopilotKit** (by CopilotKit.ai) ([React UI + elegant infrastructure for AI Copilots, AI chatbots ... - GitHub](https://github.com/CopilotKit/CopilotKit#:~:text=GitHub%20github,CopilotKit%2FCopilotKit)) ([Integrate AI Effortlessly: A Beginner's Guide to Using CopilotKit](https://dev.to/niharikaa/integrate-ai-effortlessly-a-beginners-guide-to-using-copilotkit-1pgg#:~:text=Integrate%20AI%20Effortlessly%3A%20A%20Beginner%27s,It%20provides)) | **Framework for chatbot “copilots”.** It provides a React-based **chat UI and middleware** to integrate LLM agents into apps. The typical agent is a single conversational assistant augmented with tools or domain-specific skills. CopilotKit supports multi-agent setups in that you can configure multiple “skills” (which could be seen as sub-agents) behind one chat interface, but primarily it’s one agent that can tap various back-end capabilities. | **Event-driven interaction.** From the user’s perspective, it’s a normal chat (or form-filling assistant, etc.). Under the hood, CopilotKit routes the conversation to an agent implementation (often using LangChain or similar). For example, a Copilot built for customer support might internally do: query a vector DB for relevant knowledge, then let the LLM compose an answer. Another Copilot might orchestrate a small sequence: fill out a form step by step by asking the user. CopilotKit’s architecture handles the UI events (user message, agent thinking, agent reply) and developers focus on defining how the agent responds (possibly calling multiple tools in sequence). In essence, it’s a **low-code orchestration**: you can define an agent workflow (with or without multiple subcalls) in a config or code, and CopilotKit manages the rest. | **Pluggable data sources (RAG focus).** It’s designed to integrate enterprise data: e.g. connect to a **vector database** of company docs, or an API (CRM, calendar, etc.), and let the agent use that. CopilotKit itself provides “last-mile” pieces: chat UI components, message streaming, etc. ([React UI + elegant infrastructure for AI Copilots, AI chatbots ... - GitHub](https://github.com/CopilotKit/CopilotKit#:~:text=GitHub%20github,CopilotKit%2FCopilotKit)). The intelligence comes from whatever you connect (OpenAI API, HuggingFace model, and any retrieval or tool logic). It supports **function calling** and multi-turn memory out of the box. For multi-agent, one could configure multiple tools (which could even be separate agents exposed as tools) – but that coordination is left to the developer’s design using the kit’s building blocks. Overall, CopilotKit emphasizes **user experience** (smooth chat UI, easy integration) for agentic apps rather than inventing new multi-agent algorithms. |
| **Azure Cosmos DB Multi-Agent** (Banking Workshop) | **Multi-agent with human roles simulation.** This workshop demonstrates multiple AI agents in a banking scenario. Typically, it includes an **“Employee” agent** (e.g. a customer service rep AI) and a **“Customer” agent**, and possibly an **“Analyst” agent** – illustrating how several AI agents can simulate a conversation or workflow (e.g. loan approval) in banking. (Exact roles from the repository: likely an Admin agent, and several Worker agents for tasks like pulling account data, detecting fraud, compliance checks, etc.) | **Scripted multi-agent workflow.** The agents likely communicate via a conversation orchestrated by the scenario logic. For example, a Customer agent asks for a loan; the Employee agent queries a database (via a tool agent) for credit history; a Risk Analyst agent might evaluate the data. The workshop probably uses a turn-by-turn controller to pass messages between these role-playing agents until the workflow (like loan decision) is complete. It also shows how a human can be in the loop – e.g. a human manager could override an agent’s decision. | **Enterprise data integration (Cosmos DB).** A key feature is using **Cosmos DB as a vector store or knowledge source**. Likely one agent is a **Database Tool Agent** that can do similarity search in a Cosmos DB container to retrieve relevant customer info or policy documents. The agents then use that info in their dialogue. Other integrations could include calling Azure functions (e.g. to update an account). Essentially, this workshop highlights connecting **internal business data** to multiple coordinating GPT-4 based agents. The UI might be minimal (perhaps a console or simple web demo), since the focus is on the *architecture* of connecting agents with a cloud database (Cosmos) and showing a realistic banking use-case with multiple AI roles. |

**Key:**
*(LLM = large language model; REACT = reasoning+action prompting method; RAG = retrieval-augmented generation)*

The table above outlines each system’s design. We see a spectrum from **single-agent systems that rely on tool use and iterative self-reflection** (e.g. Jina’s DeepResearch, Perplexity clones) to **explicit multi-agent collaborations with distinct roles** (ChatDev, OpenAGI). Many systems fall in-between: effectively single *LLM* orchestrators, but structured into phases or sub-tasks that mimic multi-agent behavior (LangChain’s planner/executor, GPT Researcher’s planner/workers, OpenResearcher’s modular pipeline).

Next, we delve into cross-cutting themes:

## **1. Multi-Agent vs. Single-Agent Coordination**

Despite the “multi-agent” label, not all systems literally deploy separate LLMs chatting. The common goal is **breaking a complex research task into manageable parts**, whether via multiple agents or one agent using multiple steps. Two dominant patterns emerge:

- **Single Agent with Tools (ReAct paradigm):** Many systems use one principal agent (LLM) that *iteratively plans and acts* in a loop. It generates search queries, calls a search API, reads results, and decides next steps, all within one cognitive thread. Examples: *Local Deep Researcher* and *dzhng’s deep-research* have the LLM itself handle query generation, result analysis, and refinement ([GitHub - langchain-ai/local-deep-researcher: Fully local web research and report writing assistant](https://github.com/langchain-ai/local-deep-researcher#:~:text=Local%20Deep%20Researcher%20is%20a,used%20to%20generate%20the%20summary)) ([GitHub - dzhng/deep-research: An AI-powered research assistant that performs iterative, deep research on any topic by combining search engines, web scraping, and large language models.  The goal of this repo is to provide the simplest implementation of a deep research agent - e.g. an agent that can refine its research direction overtime and deep dive into a topic.](https://github.com/dzhng/deep-research#:~:text=How%20It%20Works)). *AutoGPT* similarly keeps a single agent that continuously self-decomposes tasks ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=This%20is%20when%20I%20stumbled,subtasks%20according%20to%20the%20plan)) ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=As%20it%20relates%20to%20research%2C,Once%20all)). Even the **Perplexity-style QA bots** effectively use one agent: the LLM queries the web and then answers the user in one flow. These systems rely on the *ReAct* pattern (Reason+Act prompting) where the LLM’s prompt contains a pseudo-conversational loop of `Thought -> Action -> Observation -> …` ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=Moving%20from%20infinite%20loops%20to,deterministic%20results)). The advantage is simplicity: no need to manage multiple dialogues. However, without explicit role separation, the agent must “mentally” juggle planning, searching, and writing, which can risk getting stuck or hallucinating if not well-prompted ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=But%20the%20problem%20with%20AutoGPT,never%20actually%20completed%20the%20task)).

- **Explicit Multi-Agent Architectures:** Other systems instantiate **distinct agents for different roles or subtasks**, often organized hierarchically. For instance, *LangChain’s Open Deep Research (multi-agent mode)* spawns one Supervisor agent and several Researcher agents in parallel ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,framework%20is%20designed%20to%20support)). *GPT Researcher* uses a planner agent and multiple executor agents (all implemented with the same underlying LLM but conceptually distinct steps) ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=Architecture)) ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=%2A%20Create%20a%20task,into%20a%20final%20research%20report)). *ChatDev* assigns each AI agent a persona in a software team and has them talk turn-by-turn ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=,designing%2C%20coding%2C%20testing%20and%20documenting)). And **OpenAGI** dynamically creates specialized worker agents depending on the task breakdown (an “autonomous multi-agent” approach) ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=This%20release%20brings%20a%20groundbreaking,and%20strict%20instructions%20to%20follow)) ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=Just%20as%20a%20large%20task,them%20to%20the%20appropriate%20workers)). These architectures explicitly **assign responsibilities to different agent entities**, which can make the problem more tractable: e.g. a dedicated planner agent always thinks at a high level, freeing worker agents to focus on narrow tasks.

**Coordination strategies** vary: Hierarchical delegation is common. A “manager” agent (or manager function) will split the work and collate results. For example, in *OpenAGI*, the Admin agent uses a TaskPlanner to decide “I need one agent to search literature and another to analyze data” and coordinates accordingly ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=This%20release%20brings%20a%20groundbreaking,and%20strict%20instructions%20to%20follow)) ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=Just%20as%20a%20large%20task,them%20to%20the%20appropriate%20workers)). In *LangChain’s multi-agent researcher*, the supervisor agent gives each section outline to a different agent and then waits for them to return their section write-ups. This reduces total time (parallelism) and enforces role separation (each researcher agent only focuses on its section). **Sequential vs. parallel:** Most systems run subtasks sequentially (one after another) unless they explicitly mention parallelism. GPT Researcher and Open Deep Research (multi-agent) are notable for parallelizing subtask agents to speed up research ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,search%20tools%20in%20the%20future)) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=until%20no%20further%20queries%20are,this%20in%20a%20functional%20UI)). Parallelism is a clear *best practice for efficiency* when tasks are independent (e.g. processing separate sections or sources), but it introduces complexity (needing asynchronous orchestration and possibly more API calls simultaneously).

**Autonomy and loop control:** All these systems implement an iterative loop *until some end condition*. A best practice is to have a clear stopping criterion to avoid infinite loops. For instance, GPT Researcher’s planner explicitly checks if further queries are needed or if the agent is sufficiently confident to stop ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=parallel%20to%20determine%20page%20usefulness,be%20printed%20in%20the%20output)). The LangChain workflow version has a fixed number of iterations and queries per section ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,as%20listed%20here)) ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=You%20can%20customize%20the%20research,assistant%20workflow%20through%20several%20parameters)). AutoGPT initially struggled with endless loops; incorporating the *Plan-and-Solve* approach (plan tasks then execute deterministically) was found to improve reliability ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=Moving%20from%20infinite%20loops%20to,deterministic%20results)) ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=Example%20inputs%20and%20outputs%20of,Solve%20%28PS%29%20prompting)). In general, **structured planning (creating a finite to-do list of subtasks)** has emerged as a way to guarantee the process terminates with a result. Many systems (GPT Researcher, OpenAGI, OpenDeepResearch workflow) use a planning step to create a bounded set of tasks ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=This%20is%20when%20I%20stumbled,subtasks%20according%20to%20the%20plan)) ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,Exa%2C%20ArXiv%2C%20PubMed%2C%20Linkup%2C%20etc)). This aligns with the idea from research that LLMs alone are not reliable long-horizon planners ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=You%E2%80%99ve%20probably%20heard%20the%20chatter,do%2C%20and%20do%20very%20well)), so we should break the problem down first.

In summary, a common pattern is: **Plan (decompose) → Execute (gather info) → Synthesize (assemble answer)**. Some do this within one agent (implicitly via chain-of-thought), others split it among multiple agents. Both can work – the multi-agent route tends to be more structured and interpretable (you can see each role’s output), whereas the single-agent route is simpler to implement but requires careful prompting to achieve the same effect. Many modern systems lean toward an *agent-orchestra* approach: one central agent orchestrating subtasks done by itself or by sub-agents. This yields a balance between autonomy and control.

## **2. Role Assignment and Agent Specialization**

When multiple agents are used, developers often assign them **human-inspired roles**. This was seen literally in ChatDev (CEO, CTO, etc.) ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=intelligent%20agents%20holding%20different%20roles%2C,agent%20organizational)) and in research assistants that have a **“Planner” vs “Solver”** dichotomy ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=Architecture)) ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=This%20is%20when%20I%20stumbled,subtasks%20according%20to%20the%20plan)). Common roles across these systems:

- **Manager/Supervisor/Admin:** Oversees the process, breaks the problem into parts, and integrates the answers. E.g. *Supervisor agent* in Open Deep Research ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,researchers%2C%20section%20planning%20for%20supervisors)), *Admin* in OpenAGI ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=,go%20brr%E2%80%A6%20with%20REACT%20PROMPTING)), or the main “User’s Assistant” agent in AutoGPT that delegates to commands. This agent is often the one that interacts with the **end user** or produces the final output.

- **Planner:** Some systems have a distinct planner module that is separate from the manager. For example, OpenAGI’s Planner and Admin are separate – the Planner creates tasks, and the Admin then executes them with workers ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=,go%20brr%E2%80%A6%20with%20REACT%20PROMPTING)) ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=This%20release%20brings%20a%20groundbreaking,and%20strict%20instructions%20to%20follow)). GPT Researcher similarly calls one LLM instance to generate the research questions (plan) and different instances to handle those questions ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=Architecture)) ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=%2A%20Create%20a%20task,into%20a%20final%20research%20report)). Using a **specialized planning agent** (potentially even a different LLM prompt or model) is a best practice to enforce structure. It can use a model optimized for reasoning (as Together’s OpenDeepResearch does by using Qwen for planning) ([Open Deep Research](https://www.together.ai/blog/open-deep-research#:~:text=,we%20use%20together_ai%2Fdeepseek)), and output a clear plan that other agents or steps follow.

- **Researcher/Worker agents:** These do the heavy lifting of retrieving information and analyzing it. In many designs, they are homogeneous (all workers do similar tasks on different subtasks). For example, Open Deep Research’s researcher agents all use the same “search and summarize” toolchain on different report sections ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,framework%20is%20designed%20to%20support)). GPT Researcher’s parallel execution agents each take one question and crawl the web for answers ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=,based%20on%20all%20gathered%20context)). Because these tasks are embarrassingly parallel, these agents don’t need unique personas; they just operate on different input queries. A best practice here is **sandboxing the context**: give each worker only the specific sub-question and not the entire original query (to avoid overlap and ensure focus). This is done in LangChain’s multi-agent: the supervisor passes each section prompt separately to its researcher agent.

- **Specialist domain agents:** Some systems employ different agent types for different kinds of tasks. The clearest example is Together AI’s mixture-of-experts approach: they used a *Summarizer agent (Llama-70B)* to condense web content, a *JSON Extractor agent* to parse outputs, and a *Writer agent (DeepSeek)* for final report writing ([Open Deep Research](https://www.together.ai/blog/open-deep-research#:~:text=,V3)) ([Open Deep Research](https://www.together.ai/blog/open-deep-research#:~:text=content%20from%20web%20pages%20,V3)). Each was actually a different model tuned for that task. This is a powerful idea: **assign roles by capability**. A smaller or faster model can handle simpler tasks (like extracting structured data) while a large, expensive model focuses on the complex task of synthesizing the final answer ([Open Deep Research](https://www.together.ai/blog/open-deep-research#:~:text=model%E2%80%99s%20response%20and%20returning%20JSON%2C,V3)). Similarly, OpenAGI can spawn workers with specific toolsets — e.g. a “Coding Worker” with a code interpreter tool, or a “Web QA Worker” with a search tool. Those workers effectively play specialized roles (even if using the same base LLM) because they have access only to certain tools and prompts. *ChatDev* is another instance of role specialization: each agent has a unique perspective (the Tester agent only evaluates outputs, never generating new content) ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=intelligent%20agents%20holding%20different%20roles%2C,agent%20organizational)) ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=can%20get%20involved%20with%20the,interaction%29%20and)). This specialization reduces the cognitive load on each agent and aligns with the principle of *separation of concerns* in system design.

- **Human role or oversight agents:** A few systems include a pseudo-agent for human feedback. OpenAGI introduced a “Human” mode where after the planner creates tasks, it awaits human validation ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=2,task%20created%20by%20the%20Planner)). In multi-turn settings, a *human-in-the-loop* can be considered an agent (with actual human intelligence) that can approve or adjust the AI’s plan. AutoGPT required user confirmation of actions if not run in continuous mode, effectively making the user a supervisory agent in the loop. This ensures safety and correctness in critical applications. It’s a recommended practice in workflows where the cost of error is high: allow a human agent (or a robust evaluation agent) to review plans or final answers. For example, the Cosmos DB banking scenario likely expects a human supervisor to monitor the agents’ decisions (for compliance).

**Assigning roles and instructions:** Best practices observed include giving each agent a concise description of its role, goals, and the tools it can use. In ChatDev, each role had a system prompt describing its duties (e.g. Tester checks outputs for bugs) ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=intelligent%20agents%20holding%20different%20roles%2C,agent%20organizational)). In OpenAGI, when the planner spawns a new worker, it injects a role-specific instruction (like “You are a Data Collector, use web search to find X”) ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=,go%20brr%E2%80%A6%20with%20REACT%20PROMPTING)) ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=This%20release%20brings%20a%20groundbreaking,and%20strict%20instructions%20to%20follow)). Clearly delineating roles avoids overlap and conflicting actions. Another practice is setting **strict boundaries**: e.g. a researcher agent might be instructed *not* to produce the final answer, only to provide facts and citations. The supervisor agent then knows it must do the final writing. This way, if a sub-agent’s output is insufficient, the manager can decide to launch another iteration or agent.

The use of specialized roles also ties into **model selection**: Together’s approach of picking different LLMs per role is notable ([Open Deep Research](https://www.together.ai/blog/open-deep-research#:~:text=,we%20use%20together_ai%2Fdeepseek)). They treated roles like *planner, summarizer, extractor, writer* as components that could each use the best model for that job (given latency/cost constraints). This is a form of an *ensemble agent system*. Most other projects stick to one model for all agents (for simplicity), but a best practice for advanced implementations is to **mix models** to optimize the overall system (e.g. using open-source local models for retrieval and an API model for final generation, to save costs).

In summary, across these systems, roles are used to impose structure and exploit strengths: planning vs executing, searching vs writing, creative vs critical. Even when multiple roles are implemented by the same LLM instance sequentially, the prompt often explicitly switches context (“Now you are the Planner. Plan the sections… Now you are the Researcher for section 1…”) – this mimics the multi-agent effect. Clearly defining roles (even if virtual) was found to increase determinism and quality ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=Moving%20from%20infinite%20loops%20to,deterministic%20results)) ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=Architecture)). It’s a best practice to separate *what* to do (planning) from *how* to do it (execution), and to separate *finding information* from *synthesizing information*. These maps well to agent roles like Planner, Retriever/Researcher, and Summarizer/Writer.

## **3. Communication and Task-Sharing Between Agents**

In systems with multiple agents, how do agents communicate and share tasks? The designs range from **conversational dialogues** to **implicit messaging via a shared memory or context**:

- **Direct dialog messaging:** Some multi-agent systems have agents explicitly talk to each other in natural language. *ChatDev* is an archetype: the agents communicate via simulated dialogue turns in a chat log ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=,designing%2C%20coding%2C%20testing%20and%20documenting)). Each agent “sees” the conversation history and contributes when it’s their turn. This is a very human-like communication model – essentially, the system prompt is structured to alternate perspectives, and the LLM generates each role’s lines in sequence. Another example is a *Manager agent asking a Worker agent* for results: this can be done by constructing a prompt like “*Manager:* Please find XYZ.\n*Worker:* (does it and replies)” and feeding that to an LLM to get the Worker’s answer. Some frameworks (like LangChain’s agents) simulate tool use in this way, and SmolAgents explicitly allow agent A to call agent B as if it were a function – which is analogous to agent A sending a message/request to agent B and getting a reply. Dialog-based communication is intuitive and often used when multiple agents are implemented within one LLM (the system just alternates personas in the prompt). However, it can be harder to control, as the conversation might go off-track if not tightly guided.

- **Shared blackboard / memory:** Another pattern is that agents don’t message each other directly, but rather write to and read from a **shared memory or context state**. For example, in GPT Researcher, the Planner agent produces a list of questions (this list is stored as a structured object) ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=Architecture)). Each execution agent then reads from that list and writes its findings to a common pool of “learnings” ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=,based%20on%20all%20gathered%20context)) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=2)). The final publisher agent reads all learnings. This is like a blackboard system: agents perform their task and put results on the board for others to use. AutoGPT similarly has a task list that the single agent continually updates (functioning as its own blackboard) ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=The%20first%20step%20in%20solving,time%20frame%2C%20without%20human%20interference)). When multiple independent agents are used, a blackboard approach can be managed by a central orchestrator: e.g. OpenAGI’s Admin could maintain an internal task graph and status for each worker and consolidate their outputs when ready. This approach is powerful for parallel tasks – agents don’t talk to each other directly, but their outputs get merged. A best practice here is to have a **common data format** for agent outputs (e.g. JSON or Markdown sections). Several systems output structured data: GPT Researcher’s workers return JSON with content and citation info, and the final writer consumes that to format the report ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,model%20you%20are%20using%20here)). Using structured intermediates avoids misunderstandings that can happen in free-text messaging.

- **Orchestration via code/function calls:** In frameworks like SmolAgents or CopilotKit, inter-agent communication is handled by the code that links them, not by the agents “speaking” in natural language. For instance, SmolAgents’ Manager agent literally calls `search_agent(query)` – the Worker (search_agent) executes and returns a Python string result, which the Manager’s code then uses ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=,Visit%20webpage%20tool)) ([Orchestrate a multi-agent system ](https://huggingface.co/docs/smolagents/examples/multiagents#:~:text=Code%20Interpreter%20%20%20,)). This is akin to an API call between agents. It’s very efficient and unambiguous (no risk of one agent mis-parsing another’s request, since it’s a direct function call). The trade-off is that it’s less flexible – the roles and call sequence must be pre-defined in the program. However, many systems combine both approaches: e.g. OpenAGI’s Admin might use an LLM Planner to dynamically decide *what* workers to create, but then it programmatically instantiates those worker agents with specific API calls.

- **Shared vector stores or databases:** Some communication is indirect via data stores. For example, one agent might index information into a vector database that another agent later queries. If an agent does some research and stores key points as embeddings, a subsequent agent could retrieve from that DB instead of doing redundant web searches. Cosmos DB example: a “Data agent” might write customer info into the store, then a “Decision agent” just queries that store. Few of the examined systems explicitly use a persistent store between agents (most pass data in memory or via orchestrator code), but as systems scale, this becomes important. A notable mention is AutoGPT’s memory: it saves facts to a vector store and then re-query it in later steps, effectively the agent communicating with its future self (or another instance) through the store.

**Task sharing** often follows the **master-worker pattern**: one agent (master) decides how to split the work, then delegates each chunk to a worker agent, then collects results. This pattern is explicit in LangChain’s multi-agent (“Supervisor assigns sections to researchers”) and OpenAGI (“Planner/Admin assigns tasks to workers”) ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=This%20release%20brings%20a%20groundbreaking,and%20strict%20instructions%20to%20follow)). The communication is typically one-way: Manager -> Worker (instruction) and Worker -> Manager (result). The workers usually do not talk to each other horizontally. If they need to share info (say two researcher agents find related info), it’s done via the manager merging their results. A potential best practice for future systems is to allow some **peer-to-peer agent communication** (e.g. researchers comparing notes before finalizing results), but this can also be simulated by just merging their notes at the manager level.

In conversational multi-agent (like ChatDev), **turn-taking is the method of task sharing**. The conversation itself decides whose turn it is to act on which task. This requires a controller (could be as simple as a round-robin schedule or a rule-based turn order). ChatDev’s sequential roles are essentially a fixed turn order mimicking a software development lifecycle ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=,designing%2C%20coding%2C%20testing%20and%20documenting)). That is easier in scenarios like coding where the process is well-defined. For open-ended research, a more flexible approach is needed (hence the manager/worker style is more prevalent than a free conversational brainstorm among agents, which could go haywire without moderation).

**Memory and context propagation** is critical in multi-step workflows. How do agents remember what others have done? Many systems ensure the *manager agent’s prompt* always contains a summary of what has happened so far or the outputs of prior steps. In LangChain’s workflow mode, after each section is done, the summary is appended to a context that the next section’s generation can see (so the agent doesn’t duplicate content) ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=%2A%20Human,Exa%2C%20ArXiv%2C%20PubMed%2C%20Linkup%2C%20etc)) ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,Exa%2C%20ArXiv%2C%20PubMed%2C%20Linkup%2C%20etc)). GPT Researcher compiles “learnings” from all previous iterations and feeds them to the LLM when asking if more research is needed ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=parallel%20to%20determine%20page%20usefulness,be%20printed%20in%20the%20output)) ([GitHub - mshumer/OpenDeepResearcher](https://github.com/mshumer/OpenDeepResearcher#:~:text=analyzed%20by%20the%20LLM%20to,be%20printed%20in%20the%20output)). That way, the decision to continue is informed by everything found so far. This suggests a best practice: **accumulate a knowledge state and present it to the agent at key decision points**. The knowledge state could be a simple text summary or a structured set of Q&A pairs. Several projects explicitly mention using *reflection and refinement*: after each iteration, have the agent reflect on the current knowledge (often by summarizing it) ([GitHub - langchain-ai/local-deep-researcher: Fully local web research and report writing assistant](https://github.com/langchain-ai/local-deep-researcher#:~:text=hosted%20by%20Ollama%20or%20LMStudio,used%20to%20generate%20the%20summary)) ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=As%20it%20relates%20to%20research%2C,the%20agent%20concludes%20the%20research)). This summary is then used as input for either the same agent or another agent to decide next steps. By communicating via distilled summaries, the system avoids overwhelming agents with raw data and keeps the focus on high-level progress.

In tool-augmented single-agent scenarios, the “communication” is between the agent’s reasoning and the external tools. For example, Jina’s agent communicates with the search tool by outputting `<think> search("query")` and reading back the results ([GitHub - jina-ai/node-DeepResearch: Keep searching, reading webpages, reasoning until it finds the answer (or exceeding the token budget)](https://github.com/jina-ai/node-DeepResearch#:~:text=,2%5D%60%2C)). This is a well-established ReAct loop. The design principle here is to treat tool outputs as agent messages. Indeed, frameworks like LangChain model tools as pseudo-agents that return observations. This messaging abstraction makes adding new tools easy: the agent just sees the tool’s output as a chunk of conversation it can reason about. A best practice from these implementations is to clearly delimit tool outputs (e.g. the `<search/>` tags in Jina’s output ([GitHub - jina-ai/node-DeepResearch: Keep searching, reading webpages, reasoning until it finds the answer (or exceeding the token budget)](https://github.com/jina-ai/node-DeepResearch#:~:text=,2%5D%60%2C)) or the function call/response format in OpenAI function calling). This helps the agent distinguish between its own internal thoughts and external info.

**Error handling in communication:** Some systems incorporate a “critic” or verification step. For example, AutoGPT sometimes had an implicit critic that examined the assistant’s plans. OpenAGI’s human feedback stage is essentially error checking the planner’s tasks by a human ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=2,task%20created%20by%20the%20Planner)). The *Human Intervention* feature can be generalized: one could have an AI critic agent that reviews intermediate decisions (like an automated unit test). While not many of the listed projects explicitly have an AI critic, they often rely on the manager agent to verify worker outputs. E.g. a supervisor might decide a researcher’s result is insufficient and issue another query. In practice, *ensuring communication is accurate* often comes down to how the orchestrator uses the information. If a worker fails, the manager could reassign the task or try a different approach (some AutoGPT variants attempt alternative strategies if stuck). This is an area for best practice: having redundancy or validation. For instance, one could use two agents to research the same question and compare answers (majority vote or consistency check) – though none of the surveyed systems explicitly did that due to cost.

In summary, agent communication is orchestrated rather than emergent in these systems. The **dominant pattern is hierarchical messaging**: a top-level agent breaks tasks into messages/requests to sub-agents (or tools) and they reply with results. The orchestrator (which could itself be an agent or just code) then composes those results. Direct multi-agent dialogue (where agents freely chat to converge on an answer) is less common here, likely because it’s harder to control and reproducibly get a good result without a human moderator. Instead, structures like blackboards, shared memory, and turn-based protocols bring order to the communication. **The best practice** is to make inter-agent communication **as explicit and structured as possible** – either by using structured data (JSON, lists) or well-defined turn sequences, so that each agent knows when to speak and what format of information to produce for others to consume. This avoids confusion and keeps the whole system aligned toward the end goal.

## **4. User Interaction, UI Flow, and Response Aggregation**

The end-user experience varies: some systems function as **one-shot report generators**, while others present an **interactive chat interface** that lets the user steer the research. Key patterns and best practices for user interaction include:

- **Configurable Depth/Breadth by User:** Several systems allow the user to specify how thorough the research should be. *OpenDeepResearcher (Shumer)* takes a “depth” parameter (number of iterations) and “breadth” (queries per iteration) from the user ([GitHub - dzhng/deep-research: An AI-powered research assistant that performs iterative, deep research on any topic by combining search engines, web scraping, and large language models.  The goal of this repo is to provide the simplest implementation of a deep research agent - e.g. an agent that can refine its research direction overtime and deep dive into a topic.](https://github.com/dzhng/deep-research#:~:text=flowchart%20TB%20subgraph%20Input%20Q,Depth%20Parameter%5D%20end)) ([GitHub - dzhng/deep-research: An AI-powered research assistant that performs iterative, deep research on any topic by combining search engines, web scraping, and large language models.  The goal of this repo is to provide the simplest implementation of a deep research agent - e.g. an agent that can refine its research direction overtime and deep dive into a topic.](https://github.com/dzhng/deep-research#:~:text=DP%7Bdepth%20)). This empowers users to trade off speed vs. comprehensiveness. Similarly, LangChain’s UI could allow selecting number of cycles. *Perplexica* offers modes – normal vs. copilot (multi-hop) – which the user selects depending on how in-depth an answer they need ([GitHub - ItzCrazyKns/Perplexica: Perplexica is an AI-powered search engine. It is an Open source alternative to Perplexity AI](https://github.com/ItzCrazyKns/Perplexica#:~:text=,tasks%20that%20do%20not%20require)). **Best practice:** Expose simple controls for research depth, or a mode switch for “quick answer” vs “deep dive.” This manages user expectations about response time and detail.

- **Real-time vs Batch Output:** Some systems (especially those with a final report) operate in batch: the user enters a topic, waits a few minutes, and gets a long Markdown report (e.g. Local Deep Researcher, GPT Researcher CLI). The user’s role during the run is minimal; they just receive the result with all citations compiled. Other systems are fully interactive and stream partial results. *Perplexideez* and *Perplexica* stream the answer sentence by sentence to the UI, showing references as they appear ([GitHub - brunostjohn/perplexideez: Search the web and your self-hosted apps using local AI agents.](https://github.com/brunostjohn/perplexideez#:~:text=,come%20from)). *OpenAssistantGPT* streams chat responses. *CopilotKit* provides chat UIs with streaming. Streaming improves perceived performance and keeps the user engaged, especially for long processes. Many frameworks highlight incremental display (CopilotKit’s React components, LangChain’s callback manager, etc.). For very long processes (multi-minute research), some systems show **progress updates**. GPT Researcher’s Gradio UI could theoretically show the sub-questions it’s working on. In agentic UIs, one best practice is to surface intermediate milestones: e.g. listing the sections being researched or a spinner with “Searching for X…”. *OpenDeepResearch (graph mode)* actually lets the user see the outline (sections) before generation and approve it ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,Exa%2C%20ArXiv%2C%20PubMed%2C%20Linkup%2C%20etc)) ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=%2A%20Human,Exa%2C%20ArXiv%2C%20PubMed%2C%20Linkup%2C%20etc)) – that is a great UX: the user can modify the plan to align with their intent. Having the user confirm the plan (report structure) ensures the result meets their needs ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,Exa%2C%20ArXiv%2C%20PubMed%2C%20Linkup%2C%20etc)) ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=structured%20report%20plan%20%2A%20Human,Exa%2C%20ArXiv%2C%20PubMed%2C%20Linkup%2C%20etc)).

- **User iteration on scope:** After an answer or report is delivered, can the user ask follow-ups or refine? The *chatbot-style systems* (Perplexity clones, OpenAssistantGPT, CopilotKit chat) absolutely allow follow-up questions in the same session. They maintain context from the previous query so the user can say “Go deeper on point 2” or “What about scenario Y?” and the agent will continue the research with that context. That iterative Q&A is a powerful way to do deep research through conversation. In contrast, *report generators* may require a fresh run or manual tweaking of parameters. Some (LangChain workflow) might require running again if you want more depth. A hybrid approach is seen in *OpenAGI’s human-in-loop*: after the planner lists tasks, a user can intervene (“Actually, add a task to check recent news”) before execution ([Autonomous Multi-Agent Architecture | OpenAGI ******* release | by Tarun Jain | AI Planet](https://medium.aiplanet.com/autonomous-multi-agent-architecture-openagi-0-2-9-4-release-170bfa7b5a9b#:~:text=2,task%20created%20by%20the%20Planner)). This is a form of user-driven refinement of scope. **Best practice:** incorporate the user’s feedback *early*, e.g. confirm outline or allow the user to mark certain subtopics as high priority. This results in a more customized output. If interactive chat is available, the user can always dig deeper after the first answer, which is often easier than pre-configuring everything.

- **Prompting and UI for role assignment:** In multi-agent systems that simulate roles, sometimes the user may implicitly adopt a role. For example, in ChatDev, the user provides the initial “idea” and then the AI agents take over. But ChatDev also offered a mode where a human can join as a Reviewer agent to provide suggestions ([GitHub - OpenBMB/ChatDev: Create Customized Software using Natural Language Idea (through LLM-powered Multi-Agent Collaboration)](https://github.com/OpenBMB/ChatDev#:~:text=can%20get%20involved%20with%20the,interaction%29%20and)). In a customer service multi-agent, the user is one of the participants (the Customer). The UI in such cases might show a **multi-party chat interface** (like seeing messages from AI agent A and AI agent B). If the user is only directly chatting with a single persona (like a concierge AI that behind-the-scenes consults worker agents), then the UI just shows one agent face to the user. For example, OpenAGI might present only the Admin agent’s final answer to the user, not the gory details of each worker. Many systems choose to *hide agent complexity from the end user*, presenting a single coherent response. GPT Researcher and OpenDeepResearch produce a single report as if written by one entity, even though internally multiple steps/agents were involved. The citations or footnotes may hint at the process (since many sources are listed), but the user doesn’t see separate “voices.” Conversely, perplexity clones do show the chain of thought partially: they show the list of sources consulted. Perplexideez, for instance, lets you click to see the source text that led to an answer sentence ([GitHub - brunostjohn/perplexideez: Search the web and your self-hosted apps using local AI agents.](https://github.com/brunostjohn/perplexideez#:~:text=,come%20from)). This is a nice transparency feature without exposing the raw reasoning text. 

- **Aggregating responses and citations:** All systems that deliver a **final report or answer aggregate information from multiple sources**. A key design is how to cite those sources. The common approach is **inline citation markers** (e.g. “[1]” footnotes) or hyperlinks. *Local Deep Researcher* outputs a Markdown with numbered references for each fact ([GitHub - langchain-ai/local-deep-researcher: Fully local web research and report writing assistant](https://github.com/langchain-ai/local-deep-researcher#:~:text=Local%20Deep%20Researcher%20is%20a,used%20to%20generate%20the%20summary)). *Nick Scamara’s open-deep-research* presumably does similar (especially since it uses Firecrawl which returns markdown content with links). *Perplexity clones* highlight phrases and link them directly to the source. The consensus best practice is to **attribute each segment of information to its source**, enabling trust and verification ([GitHub - brunostjohn/perplexideez: Search the web and your self-hosted apps using local AI agents.](https://github.com/brunostjohn/perplexideez#:~:text=,come%20from)). This often means the final composing agent needs not only the content from each source but also source identifiers. Many projects handle this by having the search tool return the URL or title along with content, then instructing the final answer agent to append citations (sometimes via a structured output format). Some even had to ensure the model can produce footnotes – LangChain’s open researcher warns that the model must support structured outputs to properly handle citations ([open_deep_research/README.md at main · langchain-ai/open_deep_research · GitHub](https://github.com/langchain-ai/open_deep_research/blob/main/README.md#:~:text=,model%20you%20are%20using%20here)). If the model cannot reliably output citations, one workaround is to post-process the answer and map sentences to sources via an embedding similarity, but that’s less accurate. Most prefer to let the model do it with prompting. 

- **UI elements for multi-agent transparency:** If a user is interested, some UIs show intermediate steps. For instance, AutoGPT’s console output prints each thought, reasoning, and action as it happens – great for debugging or power users. A user-friendly version might hide that by default but allow expanding a “Show reasoning steps” panel. The *Level of detail control* could even be user-configurable (“verbose mode”). The *Cosmos DB banking workshop* might illustrate a UI where you see the conversation between the customer and AI banker, and maybe a sidebar of what backend agent actions (database queries) were triggered, to build user confidence that the answer was based on actual data. In an enterprise setting, showing an audit trail of which sources and internal data were used is important.

- **Multi-turn memory and context with user:** For chat interfaces (Perplexity, OpenAssistantGPT, CopilotKit), maintaining conversation context across turns is crucial so the user can ask follow-ups without restating everything. This requires summarizing or tagging prior content to keep the prompt within token limits**(continuing the answer)**

For example, *CopilotKit* and *OpenAssistantGPT* manage multi-turn context behind the scenes, often by storing conversation history and additional retrieved context outside the immediate prompt (e.g., using a vector store to inject relevant data for each turn). A best practice in these chat scenarios is to implement a **Retrieval-Augmented Generation (RAG)** loop each turn: take the conversation (especially the latest user query), search the knowledge base or web for new info, then have the LLM answer. This keeps answers current and grounded, and it's how many enterprise copilots (CopilotKit included) work. The user might not realize a “multi-agent” workflow occurred, but the assistant essentially acted as a researcher agent on demand.

Finally, **user interface for results**: presenting a long research report needs good formatting. Many systems output in *Markdown with headings, bullets, and tables* (per instructions like those given in this prompt!). LangChain’s Open Deep Research by default produces a well-structured report with an introduction, multiple sections, and conclusion. This makes it easier for the user to digest. If delivered via chat, the assistant might break up the response into multiple messages or use collapsible sections.

## **5. Integration of Data Sources and Tools**

Deep research agents distinguish themselves by using **multiple sources of information**, beyond the LLM’s own knowledge. Key integration patterns observed:

- **Web Search APIs:** All these projects emphasize live web search. Many use APIs like SerpAPI, DuckDuckGo, Google Custom Search or specialized ones like Tavily, Exalead (Exa), etc. *LangChain Open Deep Research* explicitly lists a wide range of supported search tools. The typical flow: the agent calls a search API, gets a list of results (titles, snippets, URLs). Then either the LLM decides which result to open, or (more often) the system automatically fetches the *full text* of top results. Systems vary in how they get the full text:
  - Using an API that returns text directly (Tavily can give raw page content, Firecrawl’s `scrapeUrl` gives Markdown/HTML, Jina’s Reader similarly).
  - Using a headless browser or scraper to open each URL. GPT Researcher uses **Playwright** under the hood to execute JavaScript and get page content (important for dynamic sites). Perplexity clones sometimes integrate something like Searx which may cache content.
  - Using known domain-specific APIs for certain content: e.g. an ArXiv API to get the paper text instead of scraping the PDF. *LangChain* has separate tools for ArXiv and PubMed to directly query those repositories.

  **Best practice:** have a robust web scraping solution that can handle different types of content (HTML, PDFs, possibly requiring JS rendering). Firecrawl and Jina Reader are specialized services tackling this, which is why projects integrate them rather than writing from scratch. Ensuring the agent gets **clean, readable text** is crucial – hence the use of Markdownify in SmolAgents to strip HTML tags.

- **Internal Knowledge Base Search:** Not all projects show this, but CopilotKit and enterprise scenarios (OpenAGI, Cosmos DB example) definitely include vector database or keyword search on internal documents. The workflow is akin to web search but hitting a different API or database. Usually, a retrieval tool is configured to search company docs or a SharePoint, etc. The agent might try internal search first, then web as fallback. *CopilotKit* specifically positions itself as that "last mile" connecting to your data: e.g. you could integrate a Qdrant or Pinecone DB of your PDFs. Many design patterns from web search apply: e.g. multi-step refine (search internal DB for broad topic, then for specifics once initial answers found). The user likely doesn’t see a difference except that some sources cited might be internal docs (or the assistant might say “According to our company policy document…”).

- **Graph-based knowledge integration:** Some advanced ones (OpenResearcher by GAIR) hint at **graph retrieval** where relationships between pieces of info are considered. This is especially useful in research literature: connecting authors, citations, concepts. That system likely uses a custom GraphRAG: first do a broad search, then maybe expand to connected papers via citation graph, etc. The design principle is to not treat retrieval as independent queries but as an expanding frontier of related info (a graph traversal). For a new agentic system, considering graph-based search (for example, querying a knowledge graph like DBpedia or a company’s ontology) could supplement plain text search. But it requires domain-specific design, so it's less common in general-purpose clones.

- **Microsoft 365 / Office integration:** The prompt mentioned Microsoft 365 – e.g. searching emails, documents, etc. None of the above open projects explicitly mention M365, but a robust system could incorporate those via Microsoft Graph API. The architecture might treat it as another search tool (with necessary auth). So an agent might have a “MailSearchTool” or “FilesSearchTool” alongside web search. The challenge is privacy and security, so such integration is usually done in closed enterprise environments, not public open-source (for demo, you might connect to a dummy dataset or require the user’s tokens).

- **Document uploads:** Several systems allow the *user* to upload documents as part of the query. OpenAssistantGPT supports attachments (CSV, images, etc.). GPT Researcher hints at local docs research. Typically, the system will run an OCR or parser to get text from uploads and then either treat it as part of context or index it for searching. A best practice if supporting uploads is to automatically incorporate them: e.g. if the user asks a question and also provides a PDF, the agent should prioritize info from the PDF in the answer. Technically, that means scanning the PDF (embedding or text search within it) for relevant sections to pass to LLM along with question. Many end-user tools do that already (ChatPDF style).

- **Multi-modal integration:** Not heavily covered in text, but Together’s system mentioned using *image and audio generation* alongside text ([Open Deep Research](https://www.together.ai/blog/open-deep-research#:~:text=Together%20AI%E2%80%99s%20Open%20Deep%20Research,available%20through%20TogetherAI%27s%20cloud%20infrastructure)). That’s slightly tangential to “deep research” (which is text-heavy), but one could imagine an agent that also searches for relevant images or charts and includes them in a report. Or an agent that generates an audio summary (podcast) of the report as output, which Together did mention as a feature. Design-wise, these are separate pipelines triggered after the text is done. For example, after writing the report, call ElevenLabs to read it aloud, or DALL-E/Bing Image to illustrate a concept. They enrich the result. The architecture should allow plugging these in – perhaps as optional final-step tools.

- **APIs for dynamic data:** Some agents use specialized APIs if a question demands it. Perplexica had a Wolfram Alpha mode for math. OpenAssistantGPT's actions can query, say, a stock price API for up-to-the-minute data. An agentic system should have a mechanism for *tool selection*: based on the query, decide which tool is best (search engine vs. calculator vs. internal DB, etc.). Perplexica’s “focus modes” are essentially user-driven tool selection. A more automated approach is the LLM itself choosing via a tool-use prompt (like LangChain’s agent that chooses tools with a reasoning process). Many frameworks (LangChain, SmolAgents) have this ability to let the LLM pick tools. It's often rule-based though: e.g. if query contains “calculate” or looks like a math problem, use the calculator tool. Simpler to implement is exposing it to user: e.g. user clicks “academic mode” for scholarly sources. Best practice might be to combine: let the LLM suggest a tool, but if the user explicitly set a mode, respect the user’s choice.

- **Citation management and source linking:** As touched on, integrating sources requires tracking which info came from where. Some design patterns:
  - Attach metadata to text chunks as they move through the system (like each chunk from a page is a tuple (text, source_url)). If using LangChain, the `Document` object has `.metadata` for this. Then ensure the LLM output template uses the metadata to cite.
  - Use reference numbers in the intermediate representation. GPT Researcher’s JSON probably includes the source index; the final generation prompt could look like: “Using the following info: [ {content:..., source:1}, ... ], write report with citations.” So the model can place [1] etc. appropriately.
  - Or have a post-processing step: e.g. in Perplexity clones, after the LLM writes an answer, they likely match sentences to sources via similarity and then insert hyperlinks. However, open-source versions try to have the model do it to reduce complexity.

- **Limiting Hallucinations:** Integration of real sources is the antidote to hallucination, but you must also prompt the model to *only use provided info*. Some systems use *“closed-book” prompting*: e.g. in LangChain’s multi-agent, each researcher agent is tasked with returning a factual section with sources *only from its search results*. The final writer is instructed to rely on those sections. Additionally, some use model functions for extraction to enforce accuracy: Together had a JSON Extractor model to parse an LLM answer and ensure it sticks to a certain format. This is a form of validation. Another technique: ask the model to list its sources first before writing the narrative. Or as OpenAGI did, let the model choose which tools (thus sources) to use, but ultimately those sources are ground truth.

- **Ensuring Recency and Freshness:** Web search ensures up-to-date info. Caching was mentioned by Together: they cached search results for speed but noted to use TTL to avoid stale data. If building a new system, consider storing results for the session (so if two agents search the same query, you don’t double-charge API). But also refresh if the data might have changed (maybe using HTTP headers or TTL). 

- **Data privacy and access control:** Not explicitly discussed in these repos, but crucial for enterprise: ensure the agent only accesses what the user is allowed to see. If multi-agent, each agent should perhaps carry the user’s auth context. A design might involve an “Auth agent” or a context object every tool call checks. The CopilotKit article in the dev.to snippet had a user question about company policy and data, highlighting that enterprises will be cautious. So a best practice: integrate with existing auth (like using the user’s OAuth token when calling SharePoint search, etc.) and never log or leak sensitive data in prompts. Possibly have a “redactor” agent to strip PII before sending stuff to an external LLM API.

- **Tool Integration Frameworks:** Many of these systems leverage existing frameworks for tools: LangChain’s `tool` abstractions, SmolAgents’ simple Python function tools, OpenAGI’s library of actions, etc. A new agentic system should probably use or mimic such a framework for easy extensibility. E.g. define a tool interface, register available tools (web search, internal search, calculator, etc.), then have either static logic or an LLM-based dynamic selection to call them. Most open implementations allow adding a new tool in a few lines (for example, SmolAgents `@tool` decorator or LangChain’s tool kits). This modular design is clearly a best practice so that the system can grow new capabilities without redesigning the core.

To summarize the integration best practices:
- Use **multiple complementary search sources** (general web, specific domains, internal KB) to cover breadth.
- Fetch **full content** of sources with reliable scrapers and parse it into clean text (possibly summarized to relevant snippets).
- Maintain **mapping of content to sources** throughout processing for transparency.
- Possibly use **different LLMs** or modules for different data types (code, math, images).
- Ensure the agent is **guided to stick to source info**, through prompt or function constraints, to prevent hallucination.
- Provide **citations or direct source links** in the final output for user verification.
- For enterprise, integrate with secure data endpoints and abide by auth; this often means running the agent in an environment with access to those resources rather than a public API only.

-----

Having examined all five focus areas (multi-agent coordination, roles, communication, user interaction, data integration), we can now highlight **common patterns and best practices**:

**Common Patterns:**
- **Plan-and-Execute Loop:** Nearly every system implements a loop of planning and refinement ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=Architecture)). This can be internal (single agent thinking) or externalized (planner agent + executors). Planning as a distinct step improves task completeness and mitigates loop traps ([Introducing GPT Researcher - The Future of Online Research — Assaf Elovic](http://www.assafelovic.com/blog/2023/7/25/introducing-gpt-researcher-the-future-of-online-research#:~:text=As%20it%20relates%20to%20research%2C,Once%20all)). *Takeaway:* *Use a structured approach where the system first outlines sub-tasks or sections for the query, then tackles them, and finally synthesizes results.* This improves determinism and coverage.

- **Hierarchical Agent Design:** Many systems create a hierarchy (manager → workers). Even if not multiple LLM instances, the code often has a top-level controller that delegates to sub-functions/agents. *Takeaway:* *Design the agent system as a hierarchy or pipeline of subtasks (planner → researchers → writer). It’s easier to manage than a flat swarm of agents.* Hierarchies ensure information flows logically (top-down goals, bottom-up results).

- **Parallel Information Gathering:** To speed up, when possible they parallelize searching multiple sources or sections. *Takeaway:* *Parallelize independent operations (like fetching different web pages or answering different sub-questions). It significantly reduces total latency of deep research.* Use async I/O or multi-threading as needed.

- **Separation of Concerns (specialized agents):** Roles like planner vs. writer, or searcher vs. coder are separated so each agent/prompt can be optimized for that function ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=Architecture)). *Takeaway:* *If using multiple agents, give each a clear, distinct responsibility and tailor prompts/model choices to that.* E.g., you might use GPT-4 for the final writing but a faster model for repetitive extraction tasks.

- **Memory and Context Management:** All systems maintain context across steps, whether via prompt history, shared variables, or external memory. *Takeaway:* *Implement a memory mechanism so that what’s found in early steps is accessible in later steps.* This could be as simple as concatenating summaries, or as complex as a vector database that agents query when needed. Many use *summaries of findings* as a compact context passed around.

- **User Involvement Points:** Systems either run fully auto or involve the user at key points (like approving a plan or clarifying a query). *Takeaway:* *Where feasible, keep the user in the loop for guiding high-level objectives.* This ensures the output matches user’s intent. For instance, present an outline to the user for feedback (LangChain does this).

- **Structured Outputs with Citations:** Nearly all deep research outputs are structured (with headings, bullet points, or sections) and include citations ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=GPT%20Researcher%20is%20an%20open,research%20on%20any%20given%20task)) ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=The%20agent%20produces%20detailed%2C%20factual%2C,speed%20through%20parallelized%20agent%20work)). *Takeaway:* *Instruct the final agent to produce a well-organized report, not just a blob of text, and to cite sources.* This improves readability and trust.

**Notable Best Practices:**

- **Leverage Existing Services for Search & Scrape:** Instead of reinventing web scraping, use APIs like SerpAPI, an open metasearch (SearxNG), or a specialized content extraction service. Jina’s approach of an all-in-one “Reader” API or Firecrawl’s unified search+scrape API simplifies agent code (the agent doesn’t have to manage two steps). And they handle things like JS-heavy sites. So a best practice: *integrate reliable external tools for information retrieval to focus your agent on reasoning.*

- **Tool/Agent Flexibility:** Systems that allow easy swapping of LLMs or tools are more adaptable. LangChain and SmolAgents emphasize being LLM-agnostic. OpenAGI too is model-agnostic and tool-agnostic with a plugin style. *Takeaway:* *Design your agent system to be modular: you should be able to change the LLM (for cost or performance reasons) and plug in new tools (for new data sources) without rewriting the whole pipeline.* This often means abstracting tool interfaces and using configuration for model choices.

- **Guardrails and Reliability Measures:** Many noted the issues of hallucination and infinite loops. Incorporating guardrails like:
  - setting iteration limits (max depth),
  - using a deterministic task list rather than open-ended loops,
  - validating outputs (like the JSON extractor approach),
  - and fallbacks (if one search API fails or returns nothing, try another).
  Ensuring the system fails gracefully or asks the user for guidance if stuck is important. *Takeaway:* *Add safety stops and alternative strategies. For example, after N attempts with no progress, the agent should either conclude partial info or ask the user for a narrower query.* GPT Researcher specifically tried to address infinite loops by the Plan-and-Solve method.

- **Transparency and User Trust:** Best systems are transparent about sources (citations) and sometimes show intermediate reasoning (optional). *Takeaway:* *Expose the provenance of information and, when appropriate, the thought process. This might be via citations, a log, or an interactive visualization.* ChatDev even provided a visualizer for the multi-agent process – in a research agent context, one could imagine a graph of how the answer was built (section A from source 1, section B from source 2 and 3, etc.). Transparency builds trust, which is crucial if these agents are to be relied upon for factual research.

- **Parallel Human and Agent Workflows:** Some frameworks like OpenAGI allow human input mid-way. A best practice in complex domains is *“human-in-the-loop” for verification or decisions*. E.g., after agents gather data, maybe let the user decide which sections to emphasize in the final report or to provide their own insight that the AI might have missed. This makes it a collaborative tool rather than fully autonomous (which often yields better outcomes in knowledge work).

- **Testing and Evaluation:** Though not directly user-facing, one meta practice is provided by OpenAGI’s benchmarking and ChatDev’s references to papers: they evaluate how well the agent performs (F1 on HotpotQA etc.). This is important for development – ensure your system actually answers correctly and thoroughly. It helps identify which parts of the workflow might need improvement (e.g. maybe the summarizer is losing info, etc.). So, *test your multi-agent system on known questions or benchmarks to tune its performance and correctness*.

Finally, to **synthesize best practices for a new agentic deep research system**:
- Use a **hybrid multi-agent architecture**: e.g., one *Planner/Supervisor agent* to break down the query, multiple *Researcher agents* (or asynchronous tasks) to gather information from various sources in parallel, and a *Synthesizer agent* to compile a final answer ([GitHub - assafelovic/gpt-researcher: LLM based autonomous agent that conducts deep local and web research on any topic and generates a long report with citations.](https://github.com/assafelovic/gpt-researcher#:~:text=Architecture)). This combines the strengths of parallelism and specialization.
- Assign each agent a clear role and *use role-specific prompts/models*: this improves coherence and lets you scale with different models (use smaller, faster models where suitable).
- **Communicate via structured means**: have the supervisor provide a structured work order (outline, questions) to researchers, and have researchers return results in a structured format (e.g. Markdown with bullet points + sources). Minimize free-form chatter to what’s necessary; mostly exchange info in a predictable way the code can merge.
- Provide a **user-friendly UI**: ideally a chat interface where the user can ask a broad question to trigger deep research, get a nicely formatted report as the answer, and then ask follow-ups. Optionally, let advanced users toggle “deep research mode” on or off (like Perplexica’s modes).
- Show **sources/citations clearly**, allow the user to click them to verify. Perhaps include a reference list at the end of the report.
- Integrate **multiple data sources**: web search for general info, domain-specific search if needed (scholarly, etc.), and enterprise knowledge base if relevant. Ensure secure access to private data and do not log it externally.
- Use **iteration and reflection**: after initial answers are gathered, have the system check completeness. If gaps exist (“No info on X was found”), either automatically do another search iteration or prompt the user if they want to broaden the search. This reflective loop is key to thoroughness.
- Optimize for **speed without sacrificing quality**: parallelize wherever possible, cache results per session, and potentially limit scope by default (maybe do 2 iterations by default and only do more if user requests).
- Maintain **extensibility**: design with modular components so new tools or agents can be added easily. For instance, tomorrow if we want to integrate a *Graph database agent* or a *code analysis agent*, it should fit into the framework with minimal changes.

By examining these systems, one sees they collectively treat “deep research” as a problem of *decomposition, parallel information gathering, and iterative refinement*, all while keeping the user in the loop and the answer grounded in sources. Embracing those strategies and the specific patterns noted will help build a robust agentic system for comprehensive research across diverse sources.