---
weight:                 2
title:                  "Deep Research"
date: 2025-04-15
tags: []
summary: ""
---

## Agentic Deep Research

### Overview

The Agentic Deep Research system is a multi-agent research assistant that leverages Retrieval-Augmented Generation and web search to provide answers to user questions. After asking question End Users can interact with the system and provide feedback or commentary throughout multiple research stages. The system breaks down the user's question into sub-tasks, searches for information within knowledge bases and on the internet, and generates complete, citation-supported analytical answer.

### System Architecture Overview

**Architecture Structure**

System components:

- **Orchestrator Service**: entry point for starting research session, streaming results, and providing feedback. Manages multi-agent workflow logic.
- **Agentic Workflow**: research process running as a directed graph of agent nodes. LangGraph manages execution order, parallelism, looping, flow etc.
- **Internal Knowledge Base**: services providing RAG functinality:
	- **Document Ingestion Service**: manages document upload, extraction, chunking, embedding, and internal knowledge base indexes update (through PepGenX).
	- **Document Store**: manages documents uploaded by users (through PepGenX API endpoint, Azure Blob Storage or other solution TBD).
	- **Retrieval Service**: runs hybrid search on internal knowledge base (incl. keyword and vector).
	- **External Search**: runs search using Azure Bing Grounding API (keyword).
- **State Persistence**: manages session state storing and final report storing.
- **Monitoring & Logging**: tracks all activities of each agent (through PepVigil).

### Workflow Summary

When End User submits a question the Orchestrator Service creates a new Research Session with a fresh LangGraph state. Then it starts executing the agent graph step by step streaming outputs (events) to End User. 

**High-level flow**
End User query → Clarify (if needed) → Plan → (Feedback loop) → Research & Sections Writing (in parallel) → Sections Review & Revise (loop) → Assemble Final answer → Return answer

## Interaction Flow

### **Session Initiation**
1. End User initiates research session by sending request (`POST /api/research`) that includes:
    - user question (research topic);
    - additional parameters (e.g., desired depth, format, etc.).
2. Optionally, End User can upload additional documents (`POST /api/docs/upload`). The system indexes the documents into user knowledge base. 
3. Each session is assigned a unique ID to track its state. 
4. Requests are handled asynchronously. The system responds immediately and switches to a Server-Sent Events stream sending data as soon as it becomes available.

### **Outline Generation**

Once the Planner generates initial report outline, the system sends it to End User as an intermediate output informing that their input is required. At this step, the workflow is paused, awaiting human feedback.

### **User Feedback**

End User reviews the outline and provides feedback (e.g. accepted or sugested changes). The feedback is sent back to the system (`POST /api/research/feedback`) with the session ID and the user generated text. The workflow resumes incorporating the feedback. If the user requested modifications, a new outline is generated and streamed back. The cycle repeats until the plan is explicitely approved.

### **Parallel Section Research**

After the outline is approved, system switches to the research and sections writing phase. While working on each section, it streams status updates and partial results for each section. After section is completed system can stream its whole content or keep the results until final text is compiled.

### **Final Answer Rendering**

When all sections of the report are ready, system sends final event containing the entire, compiled report. The report includes all sections from outline along with additional introduction and conclusions. This is the last step in the workflow. The stream is then closed from the server side. On the client side, this event triggers the rendering of LLM answer (complete document with proper markdown formatting, images, citations, etc.).

### **Error Handling**

The system catches all exceptions that are unhandled in the pipeline and converts them into user-friendly error message that is then streamed to End User. Same behaviour is true for invalid or missing End User input (e.g., if they forget to provide a topic), when detected system responds with a message. 

In general system makes sure that End User is never left uncertain about the current status of the system. Every state of the workflow is clearly communicated back to End User e.g. waiting for feedback, performing a search, encountering an error.

## Agent Roles and Responsibilities

Each agent is implemented as a LangGraph class (stateful/modular). The agents communicate indirectly by reading/writing a shared state. 

Key system agents:

| **Agent Role** | **Primary Responsibilities** | **Methods/Tools Used** | **Outputs to State** |
|-----------------|-------------------------------|-------------------------|-----------------------|
| **Chief Editor (Orchestrator)** | Top-level coordinator that **manages the workflow**, triggers agents, and handles conditional logic. | **Orchestration logic via StateGraph.** Delegates tasks to other agents. | **N/A.** Coordinates  state transitions and ensures final, compiled answer is saved in state. |
| **Clarifier Agent** | If the initial user question is unclear to LLM or too broad, it formulates **follow-up clarification questions** to ask the End User. | **LLM prompt:** analyze the End User question and generate follow-up clarification questions. | **Clarified query** or additional input provided by user (e.g. updated research question or constraints). |
| **Planner (Editor Agent)** | Analyzes clarified research question and produces a **structured research plan or outline.** If the query is simple, the plan might be a set of key points; if broad, it would be an outline with multiple sections each having a title and brief description. | **LLM prompt:** generate an outline in a structured format (JSON) that adheres to a pre-defined **Pydantic Sections schema**. | **sections_list** e.g., a list of Section objects with fields like title, description, and a flag if web research is needed for that section. |
| **User Feedback Agent** | Presents the drafts to End User and incorporates user feedback. The agent makes updates according to user decision e.g. approved or changes requested (add/remove sections, adjust emphasis, etc.) | **No LLM:** waits for user input. **LangGraph interrupt()** is used to pause execution until feedback is received. | Updated **sections_list** and a boolean flag indicating approval status. |
| **Researcher Agent** | Focuses on **information retrieval** retrieving information for a given section or subtopic from **internal documents** and **external web sources.** | **Tools:** has access to **WebSearch Tool** and **DocumentRetrieval Tool** (internal knowledge base). May use LLM to generate specific search queries or keywords for each subtopic. Results for each subtopic (web pages text, document excerpts) are aggregated. The agent distills information and summarizes raw results producing a **concise research draft** for the section. | For each section a **section_draft** containing a draft and a list of source citations (with URLs, doc ids etc.). |
| **Reviewer Agent** | Performs a **quality check** of each section draft by identifying issues like: missing information, factual inaccuracies, poor clarity, or not meeting guidelines provided by user in request. Based on the findings it flags draft for revision if it is needed. | **LLM prompt:** critical evaluation of draft against section topic definition from outline and instructions/requirments provided by End User. The output format could include e.g., a JSON with fields **pass: true/false** and **issues: [...]** listing problems. | A **review_notes** object for each section containing a verdict **pass/fail** and a list of improvement suggestions. |
| **Reviser Agent** | Improves section draft based on feedback provided by Reviewer e.g., by correcting facts, adding missing details (also by running additional research), and rewriting. After finishing the new version of draft is send again for reviewed by **the Reviewer**. | **LLM prompt:** udpated draft of a section based on provided feedback. | **Updated section draft** (overwriting current version or adding new version). |
| **Section Writer (optional)** |  Composes a **well-written section** from notes and research. | **LLM prompt:** generate coherent section text with citation. Use notes, chunk of documents and text from web. | **Section text** for each outline section. |
| **Chief Editor (Workflow Controller)** | After all sections are ready (researched and revised), editor **assembles** them into one document and runs **global analysis** to check if any remaining tasks are finished. | **Orchestrator logic:** waits for all parallel section tasks to complete. Has conditional logic managing cases when a section generation fails. | Makes decision wheather to move to **final document.** Compiles a list of all source citations from sections into a global reference list. |
| **Writer Agent (Synthesizer)** | writes **final composed answer** including all section drafts, **introduction and conclusion.** aligning with End User guidelines.  | **LLM prompt:** generate consolidated report including all sections content, and write **introduction** that outlines the sections and **conclusion** that summarizes key findings. | **Final report content** (could be saved in state **final_markdown** or PDF). |
| **Publisher Agent** | Verifies **output formatting** and handles any post-processing tasks before returning the final answer to End User (e.g. merging all parts, inserting a **table of contents** if needed, formatting citations, etc.). | **LLM prompt:** merge all sections into a final report, review and correct format. **PDF and Word document generation logic** is a Python code. | The **final answer** and set of generated document versions. |
| **User Interface Agent** | Manages **human input** (placeholder in the graph). | **N/A.** system pauses for user input. | Updates to session state as provided in **user input**. |

**Note:** If requires the system could include additional agents like **Fact-Checker Agent** to cross-verify facts against sources, or **Citation Formatter Agent** to handle bibliography formatting.

## Workflow and State Management

The Agentic Deep Research system uses LangGraph’s State Machine to manage workflows. Information stored in shared state adhere to predefined **Pydantic data model**. All agents can access and modify shared state. The state persists through the entire research session keeping all information (user query, plan, section contents, etc.).

### **State Structure**

ResearchState class fields (Pydantic dataclass):
- user_query: str – original End User question.
- clarified_query: str – (optional) refined version of the original query after clarification.
- sections_plan: List[Section] – list of sections (each section having fields like e.g. title, description, needs_research).
- section_states: Dict[str, SectionState] – a mapping of section (title or ID) to a SectionState object that holds section’s interim results e.g. draft content, sources, review notes, revision count.
- final_answer: str – final answer to End User question.
- other metadata: writing style, End User provided guidelines or requirements, iteration counters, depth, breadth, etc.

SectionState of a section can also include section_states for subsections with fields like draft_content, sources, feedback, status, etc.

### **Graph Definition**

The research workflow is defined as a directed acyclic graph with possible loops. The graph is initialized as StateGraph(ResearchState) with node for each agent. The nodes are connected in the graph with directed edges. Conditional logic is handled by LangGraph’s command transitions e.g. Reviewer→Reviser→Reviewer loop is a conditional edge that re-invokes ReviserAgent until the ReviewerAgent approves the text.

- **Node1**: ClarifierAgent.run (input: user_query, output: clarified_query).
- **Node2**: PlannerAgent.plan (input: user_query or clarified_query, output: sections_plan).
- **Node3**: HumanAgent.review_plan (input: sections_plan, output: updated sections_plan and/or approval flag).
- **Node4**: Processed parallelly. After the plan is approved, ProcessSection() starts for each section in sections_plan. The subgraph for each section is a StateGraph on SectionState with nodes: ResearcherAgent.gather, ReviewerAgent.review, ReviserAgent.revise (with a loop back to review if needed).
- **Node5**: WriterAgent.synthesize. Running after all section subgraphs are completed (input: all section drafts from state, output: combined draft with intro/conclusion).
- **Node6**: PublisherAgent.format_output (input: combined draft and all citations, output: final_answer formatted).
- **Node7**: HumanAgent.final_review. (Optional) Allows human review of the final answer and request tweaks or confirm completion.

### **State Flow**

During execution agents read information from the state and write results back to the state e.g. Planner reads the user_query and writes the sections_plan, User Feedback reads the sections_plan and user_feedback, then writes an updated sections_plan.

### **Structured Data Exchange**

Agents use structured outputs to minimize errors when parsing responses from LLMs. Prompts instruct LLM to output JSON that adheres to predefined Pydantic models e.g.. *"Respond with a JSON conforming to the Sections schema: {sections: [ {title: …, description: …, needs_research: …}, … ]}".*

### **State Flow example**

End User asks a question: *'What are the latest advancements in soda and chips preservation and packaging?'*

The system goes through the workflow:
1.	**Clarification**: The Clarifier agent may ask additional question like *“Do you want to focus on preservation techniques for soda, packaging sustainability for chips, or all advancements?”*. If the user responds *“Focus on packaging sustainability for chips”* the clarified_query in state is set.
2.	**Planning**: The Planner takes *“advancements in soda and chips preservation and packaging”* and creates report sections e.g.: *[Section1: “Introduction”, Section2: “Innovations in Soda Preservation”, Section3: “Advancements in Chips Packaging Sustainability”, Section4: “Consumer Trends in Soda and Chips”, Section5: “Conclusion”]*. Each section has additional brief description. All sections are marked as needing research except Introduction and Conclusion that will be synthesized from the final content.
3.	**User Feedback on Plan**: The sections are streamed to End User for review and approval. The user might delete “Innovations in Soda Preservation” and add a section “Digital Engagement in Marketing Chips and Soda.” and then approve all the rest. The state is than updated accordingly.
4.	**Parallel Section Processing**: The Orchestrator launches parallel workflows one for each section that was marked for research (Soda, Chips Packaging, Consumer Trends). Each workflow executes agents:
    - **Researcher** agent writes search queries (for internal knowledge base and web), retrieves chunks, texts, documents etc. compiles key points and writes a draft paragraph citing sources.
    - **Reviewer** checks the draft for accuracy and completeness and writes a review notes: *“Draft omitted the new plant-based packaging introduced this year.”*
    - **Reviser** checks reviewer notes and uses research tool to find missing information on the plant-based packaging innovation, than it writes new version of the draft, and outputs a revised draft.
    - **Reviewer** runs veryfication step again, and having no   no issues found submitts _“pass”_ flag. The Chips Packaging section in state is marked completed and final draft is attached.
    - Similar workflows run for other sections. Each section might iterate internally a couple of times until review is marked _"pass"_, or a **maximum allowed iterations** limit is reached.
5.	**Synthesis**: Once all sections are prepared, the Writer agent assembles them into one consistent document checking narrative flow and order of sections. It also writes introduction and conclusion sections, and consolidates list of references from all sections.
6.	**Finalization**: The Publisher agent ensures each section has a Markdown header, the references are numbered and linked properly, and any styling (bold for section titles in a TOC, etc.) is applied. The final_answer in state now stores a complete Markdown text.
7.	**Output**: The Orchestrator streams the final answer to End User. If a Human review node is used, the user can approve the final answer or send feedback asking for small changes, which in turn could trigger a short revision step.

### **Looping**

The system uses the following iterative improvement loops:
- **Plan refinement loop**: after End User provides feedback on the outline, Planner updates the plan and runs the loop again. The process continues until End User is satisfied with the plan.
- **Research deepening loop**: Reviewer may identify some gaps and recommend adding information to a section draft. New search will be triggered to update the draft and the updated draft will be cheked by Reviewer again. This iterative process runs up to a max_search_depth parameter or untill Reviewer approves the section.
- **Human-in-the-loop**: At various steps in the workflow, the End User is presented with key findings, plans, draft versions etc,. and asked to decide what to do. This decision could initiate another round of research or just begin rewriting a section. Than the loop is triggered again.

### **Maintaining Context**

The system doesn't implement long-term memory across End User sessions. Each session starts fresh.The shared state serves as session memory throughout the workflow. All necessary context (user original question, confirmed sections, material found for each section) is read from the state. 

## Prompt Design and Agent Collaboration

Each agent has its own dedicated set of prompts that define its role, provide necessary context, define output format, and guide the agent's actions.

### **System / Role Prompts**

- **Planner**: Task - Creates a detailed outline as an expert editor.  
    *Prompt:* *“You are an expert researcher and editor. Produce a detailed outline for a report on {user_query}. The outline should be in JSON format with sections including title, description, and whether research is needed. Ensure the outline comprehensively covers all key aspects of the question.”*  
    *Output Example:* A sample outline JSON structure illustrating the expected format can be provided for reference.
- **Researcher**: Task - Frames the LLM as an investigative researcher.  
    *Prompt:* *“You are a research assistant specialized in finding information. Gather relevant points on ‘{section_title}’ from provided sources and your knowledge. Use tools to retrieve additional information if necessary. Then compose a draft of this section with sources cited.”*
- **Reviewer**: Task - Sets a critical expert tone.  
    *Prompt:* *“You are a senior analyst reviewing the draft content for accuracy, completeness, and clarity. Compare the content against the question and any guidelines. List any issues or missing pieces. If everything is fine, respond with ‘No issues’ or an empty list.”*  
    *Output Format:* The prompt may explicitly instruct formatting the output in a structured JSON format, e.g., `{pass: bool, issues: []}`.
- **Reviser**: Task - Acts as a skilled editor.  
    *Prompt:* *“You are an expert writer revising content. Improve the following draft according to the reviewer’s feedback. Fix any issues and produce an updated draft that resolves the points.”*  
    *Output Format:* The output could include a structured JSON containing both the updated draft and editor notes.
- **Writer**: Task - Acts as a synthesizer.  
    *Prompt:* *“You are a senior researcher summarizing and concluding a report. Given all the section drafts, write a concise introduction that frames the topic and a conclusion that summarizes the findings. Maintain a {tone} tone throughout. Output in a JSON format with an ‘introduction’ field and a ‘conclusion’ field.”*  
    *Managing Long Reports:* For lengthy outputs, splitting the text into smaller parts can help manage token limits effectively.
- **Human-in-the-loop**: Task - Facilitates user interaction whenever the system might require input.  
    *Execution:* Any content requiring user guidance should be presented as a UI prompt rather than an LLM prompt.

### **Chaining Tools**

The **Researcher** agent uses ReAct technique: 
1. **Tool Selection**: it starts with a prompt explaining available tools (e.g., `SearchWeb`, `SearchDocs`) and their usage format.
2. **Example Workflow**: 
    - Then theagent might reason: *“I should find internal documents on this topic.”*  
    - Action: *SearchDocs["grid-level battery storage latest"]*.  
    - The agent executes the request and processes the retrieved results.
3. **Iterative Refinement**: The process repeats as needed, allowing the LLM to gather sufficient information before producing a final draft.

### **Feedback Loop Prompts**

The **Reviewer** agent performs clarity checks:  
1. **Prompt:** *“Evaluate the above section draft. If it fails to fully address the section topic or omits important aspects, respond with pass=false and propose up to three follow-up research questions that should be answered. If it is thorough, respond with pass=true and include no follow-up questions.”*
2. **Feedback Schema:** When `pass=false` is received, the system loops back to the **Researcher** agent, and uses the proposed questions as new search queries. Iterative research-evaluation process drives much deeper research and improves section quality.

### **User Guidance**

The system incorporates user-provided guidelines into relevant agent prompts to ensure outputs meet End User requirements and maintain consistent style e.g., “use a formal tone”, “include at least 5 sources”, “focus on statistics”.  

### **Token Budget and Chunking**

Because the system handles very log text it must also control context sizes to avoid reaching its limits. To prevent exceeding token limits:
- **Source Limitation per Section**: only the most relevant search results are considered for each section e.g., the top X internal chunks and top X web results. 
- **Text Summarization for Large Inputs**: for large bodies of text (e.g., lengthy research papers), the raw document is summarized into concise key points that retain.  
- **Chunking for Depth**: Follow-up questions requiring additional exploration are split into smaller, sequential steps. 

### **Citations and Attribution**

Each prompt includes instructions to maintain source attributions. Citations are also tracked within the state with mapping to corresponding source URLs or documents.

### **Example Prompts**

#### **Planner**

  __System__: *“You are Planner, an expert at breaking down research questions. Create a detailed outline for a report that fully answers the question. The outline should cover all relevant subtopics. Output only valid JSON following this schema: { "sections": [ { "title": "...", "description": "...", "needs_research": true/false } , ... ] }. Ensure needs_research is true for sections that require external info, and false for those that can be answered from summarizing others (like intro or conclusion).”*
  __User__: *{"query": "What are the latest advancements in renewable energy storage?"}*
  __Assistant__ (expected output):

```json
{
  "sections": [
    { "title": "Introduction", "description": "Overview of renewable energy storage and why advancements are needed", "needs_research": false },
    { "title": "Advancements in Battery Storage", "description": "Latest improvements in battery technology for energy storage (e.g., new chemistries, higher capacities)", "needs_research": true },
    { "title": "Thermal Energy Storage Innovations", "description": "New developments in thermal storage systems like molten salts, heat batteries", "needs_research": true },
    { "title": "Pumped Hydro and Gravity Storage", "description": "Recent projects and improvements in gravitational storage methods", "needs_research": true },
    { "title": "Conclusion", "description": "Implications of these advancements and future outlook", "needs_research": false }
  ]
}
```

This JSON would be than parsed into sections_plan in state.

#### **Reviewer**

  __System__: *“You are Reviewer, a meticulous reviewer. Analyze the section draft below for accuracy and completeness. The user’s original question: ‘{section_question}’. Draft content: {draft_text}. List any factual errors, missing key points, or any part that is unclear. If the draft fully and correctly addresses the topic, respond with ‘No issues’. If not, respond with a JSON: { "pass": false, "issues": ["..."] } where each issue describes a needed improvement. If it is good, respond with { "pass": true, "issues": [] }. Keep issues concise.”*
  __Assistant__ (example output):

```json
{
  "pass": false,
  "issues": [
    "The draft doesn't mention the recent development in lithium-metal batteries announced in 2025.",
    "It provides no data or examples to demonstrate how capacity has improved."
  ]
}
```

This answer tells Orchestrator what are the section that need revision and what to search for or add.

## Information Retrieval and RAG Integration

### **Document Indexing**

When End User uploads documents (e.g., PDFs, Word files, text files, etc.), the system invokes the PepGenX document ingestion service, which performs text extraction, chunking, and indexing. The index is updated so that subsequent queries can retrieve information from newly uploaded documents. The process can run asynchronously but it is necessary to ensure that indexing is completed first because user may ask a question about content of those documents.

### **Hybrid Search Strategy**

Internal knowledge base: the Researcher agent uses hybrid approach to retrieve relevant information from the internal knowledge base:
- Keyword search
- Vector search
- Graph traversal (if implemented)

If user has uploaded a document the system may rely primarily on that document's content and minimize web searches. The system can also detect whether a question seems to directly reference an uploaded document and prioritize that document.

### **Azure Bing Web Search**

External web sources: the Researcher agent writes search queries and calls Bing API endpoint. Than it retrieves top results for each query (can use a configurable limit of pages per query and total web queries per session). For each result the agent extracts raw text from the target web page, downloads shared documents and extracts raw text from it and describes images used on the web page. At the end Researcher either saves original text if the page is highly relevant or summary if the page is less relevant.

### **Combining Internal and External Sources**

By default, Researcher does not prioritize internal documents over web search results but can be instructed to do so. The process typically involves the following steps:
1. Query internal index using keywords.
2. Query internal index using embeddings.
3. Query external sources using keywords.
4. Retrieve top ~n results or results with ~n% similarity from each query type.
5. Rank the retrieved materials (using LLM to select sources that are most likely to answer the End User’s question).
5. Prepare a bundle of all source materials selected after the ranking step.
6. Send the bundle, along with the prompt, to generate the draft. LLM is instructed to label text with the corresponding source.

### **Follow-up Queries (Depth)**

The system supports iterative deepening of research by generating new questions based on initial findings. The Planner starts by analyzing the original question and may decide to break it into sub-questions. If the initial set of results is insufficient (as reported by the Reviewer), the system generates follow-up questions for further research. The Researcher then initiates new queries across both internal and external sources, gathers the answers, and integrates them into the findings. If an entirely new direction emerges during the process agent can propose adding new section.

### **Parallel Section Processing**

Once the research plan is finalized and the outline is approved, the system starts processing sections concurrently e.g. if there are three sections requiring research, the Researcher/Reviewer/Reviser loop will be created 3 times and run in parallel (LangGraph Send(..., parallel=True)). Backend (PepGenX) must assure that high volume of API calls will not be interrupted (LLM requests rate limit/tokens size per minute, vector database, Bing API, etc.).

## API Design and Streaming Interaction

The system provides **RESTful HTTPS API interfaces** that support streaming responses and interactive sessions using **Server-Side Events (SSE)**.

### Endpoints

1. **POST /research** – initiates a new research session. Request body includes user question and optional parameters:
   - `query`: The research question (string).
   - `documents` (optional): A list of IDs or references to already-uploaded documents.
   - `settings` (optional): A JSON object containing settings such as tone, depth (maximum iterations), breadth (maximum parallel queries per iteration), etc. If not provided, defaults from the configuration are used.

   The server responds with an SSE (`Content-Type: text/event-stream`) sending events as the research progresses.

2. **POST /feedback** – is used by end user to provide requested feedback:
   - `session_id`: A session identifier.
   - `user_feedback`: A boolean (approve/decline) or text response depending on the context.
   - Optionally, an agent or stage identifier to specify the target of the feedback.

3. **POST /upload** – is used to upload one or multiple files.

4. **GET /status/{session_id}** – optional to query the status of a session.

### **Event Stream Format**

The system sends SSE events as JSON messages including:
- agent_status – e.g., { "type": "AGENT_STATUS", "agent": "Researcher(Battery Storage)", "message": "Found 3 relevant documents, searching web..." }.
- partial_answer – partial content output.
- ask_feedback – signaling UI to prompt the user.
- final_answer – final compiled answer (streamed in chunks, but at the end also with link to compiled documents).
- error – if any error occurs (an error message).

### **Example SSE Flow** (Pseudo-structured)

```json
data: {"type":"INFO", "message":"Planning research..."}
data: {"type":"OUTLINE", "sections": [ ... ] }
data: {"type":"ASK_FEEDBACK", "message":"Outline ready. Approve or edit?", "sections": [...outline...]}
```
STEP  -- **Server Pauses Waiting** --
STEP  -- End User sends feedback via /feedback endpoint

```json
data: {"type":"INFO", "message":"Outline updated per user feedback. Proceeding with research."}
data: {"type":"INFO", "message":"Launching 3 parallel research tasks for sections."}
data: {"type":"AGENT_STATUS", "agent":"Researcher[Battery Storage]", "message":"Querying internal knowledge base..."}
data: {"type":"AGENT_STATUS", "agent":"Researcher[Thermal Storage]", "message":"Querying internal knowledge base..."}
data: {"type":"AGENT_STATUS", "agent":"Researcher[Pumped Hydro]", "message":"Querying internal knowledge base..."}
data: {"type":"AGENT_STATUS", "agent":"Researcher[Battery Storage]", "message":"Performing web search: 'latest battery storage technology 2025'."}
````
...
```json
data: {"type":"SECTION_DRAFT", "section":"Battery Storage", "content":"Battery storage technologies have seen improvements in energy density... [1] ...", "sources": [{"id":1, "title":"Tesla New Battery 2025", "url":"..."}]}
data: {"type":"AGENT_STATUS", "agent":"Reviewer[Battery Storage]", "message":"Reviewing draft for Battery Storage."}
data: {"type":"REVIEW_NOTES", "section":"Battery Storage", "issues":["Missing info on lithium-metal batteries"] }
data: {"type":"AGENT_STATUS", "agent":"Reviser[Battery Storage]", "message":"Revising draft to address feedback."}
data: {"type":"SECTION_DRAFT", "section":"Battery Storage", "content":"Battery storage technologies... now include lithium-metal battery advances... [1][2] ...", "sources": [ ... updated list ... ]}
data: {"type":"AGENT_STATUS", "agent":"Reviewer[Battery Storage]", "message":"Reviewing draft after revision."}
data: {"type":"REVIEW_PASS", "section":"Battery Storage", "message":"Section draft approved."}
```
STEP  -- Similar events for other sections interleaved
```json
data: {"type":"INFO", "message":"All sections completed. Synthesizing final report."}
data: {"type":"PARTIAL_ANSWER", "content":"# Introduction\nRenewable energy storage has rapidly evolved ..."} 
data: {"type":"PARTIAL_ANSWER", "content":"\n## Advancements in Battery Storage\nOne of the major advancements ... [1] ..."} 
```
STEP  -- The final answer streamed in parts or section by section
```json
data: {"type":"FINAL_ANSWER", "message":"Research complete. Final answer ready with 10 sources."}
```

### **Real-time Feedback Handling**

The system implements a timeout and fallback mechanism:
- If, after a predefined time (e.g., X minutes), the End User does not provide feedback, depending on the configuration system can cancel the session or automatically proceed with a default answer.
- The API can include a field named "timeout" in the ASK_FEEDBACK event, such as "timeout": 300 seconds, to inform the client application about how long it will wait.

### **Final Answer Delivery**

Once the final answer is sent, the server will close the SSE connection. If the client misses part of the response due to a disconnect, it can use the /result/{session_id} endpoint to fetch the final answer. The final answer may remain in the cache for a predefined period.

### **Error Handling**

- All calls to external APIs are wrapped in try/except blocks and will automatically retry few times.
- Minor errors within agents (e.g., a single section failing) will be handled by retrying and notifying the user.
- If a critical error occurs (e.g., an expired API key), the system will stream an error event and terminate.