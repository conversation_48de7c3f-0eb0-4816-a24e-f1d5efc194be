---
weight: 3
title: "Python"
---

# **Python Programming Skills**

### **Introduction**

This section provides essential background knowledge required for coding PepGenX Platform components. A strong foundation in these Python concepts is crucial for your success as a PepGenX developer. Feel free to explore the provided resources and delve deeper into each topic to ensure you're well-equipped to contribute to the PepGenX Platform!

### **Object-oriented programming (OOP)**

1. ["Python Classes and OOP"](https://docs.python.org/3/tutorial/classes.html)
2. ["Understanding Class and Instance Variables"](https://realpython.com/python3-object-oriented-programming/#defining-a-class-in-python)
3. ["Python Object Oriented Programming (OOP) - For Beginners"](https://www.youtube.com/watch?v=JeznW_7DlB0)
4. ["Object-Oriented Programming in Python"](https://python-textbok.readthedocs.io/en/latest/)
5. ["Classes in Python"](https://realpython.com/lessons/classes-python/)
6. ["Inheritance in Python"](https://realpython.com/lessons/inheritance-in-python/)
7. ["Polymorphism in Python"](https://www.geeksforgeeks.org/polymorphism-in-python/)

### **Flow control**

1. ["Python Flow Control"](https://docs.python.org/3/tutorial/controlflow.html)
2. ["Conditional Statements in Python"](https://realpython.com/python-conditional-statements/)

### **Functions**

1. ["Defining Functions"](https://docs.python.org/3/tutorial/controlflow.html#defining-functions)
2. ["Defining Your Own Python Function"](https://realpython.com/defining-your-own-python-function/)
3. ["Functions in Python"](https://realpython.com/lessons/functions-in-python/)

### **Data types**

1. ["Built-in Types Datatypes"](https://docs.python.org/3/library/stdtypes.html)
2. ["Basic Data Types in Python"](https://realpython.com/python-data-types/)

### **Binary data**

1. ["Binary Data Services"](https://docs.python.org/3/library/binary.html)
2. ["Creating a Binary Search in Python"](https://realpython.com/courses/creating-binary-search-python/)

### **Exception handling**

1. ["Errors and Exceptions"](https://docs.python.org/3/tutorial/errors.html)
2. ["Python Exceptions: An Introduction"](https://realpython.com/python-exceptions/)
3. ["Raising and Handling Python Exceptions"](https://realpython.com/courses/raising-handling-exceptions/)

### **JSON**

1. ["json — JSON encoder and decoder"](https://docs.python.org/3/library/json.html)
2. ["Working with JSON Data in Python"](https://realpython.com/python-json/)
3. ["JSONS Offcial"](https://jsons.readthedocs.io/en/latest/)