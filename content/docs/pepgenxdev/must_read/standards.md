---
weight: 5
title: "Standards & Patterns"
---

# **Standards & Patterns**

### **Introduction**

We've established a set of core Python standards and patterns that streamline development, ensure a consistent, high-quality user experience, and contribute to a well-organized and maintainable codebase. Following these guidelines is crucial for creating well-integrated code. We encourage you to explore the provided references.

### **RESTful API**

1. ["RESTful API Tutorial"](https://restfulapi.net/)
2. ["Python and REST APIs: Interacting With Web Services"](https://realpython.com/api-integration-in-python/)
3. ["What is a REST API?"](https://www.youtube.com/watch?v=lsMQRaeKNDk)

### **WebSocket API**

1. ["WebSockets handbook"](https://websocket.org)
2. ["WebSockets Python library"](https://websockets.readthedocs.io/en/stable/)
3. ["How To Build WebSocket Server And Client in Python"](https://piehost.com/websocket/python-websocket)

### **Asynchronous API Calls**

1. ["Async and Await in Python"](https://docs.python.org/3/library/asyncio.html)
2. ["Asyncio Tutorial"](https://realpython.com/lessons/what-asyncio/)

### **JSON Web Token (JWT)**

1. ["JWT.io"](https://jwt.io/)
2. ["PyJWT Documentation"](https://pyjwt.readthedocs.io/en/stable/)
3. ["What Is JWT and Why Should You Use JWT"](https://www.youtube.com/watch?v=7Q17ubqLfaM)
