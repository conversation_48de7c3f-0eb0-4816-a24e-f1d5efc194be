---
weight: 1
title: "Prerequisites"
---

# **Getting Started**

## **Before you begin working with the Platform**

PepGenX Platform is a revolutionary platform designed to make building applications with generative AI and LLMs accessible to everyone. We're building a vibrant developer community where talented individuals like yourself can contribute by coding components that will be the building blocks of the pepgenx ecosystem.

This guide provides an overview of the PepGenX Platform and its core components. Before diving into development, it's essential to understand the fundamental concepts and technologies that underpin the framework.

## **Understanding the Foundation**

The PepGenX Platform is built upon the Kubernetes platform and its architectural model. A solid understanding of Kubernetes' principles, component operations, and implementation will be instrumental in comprehending the architecture of both the PepGenX Platform itself and its individual components.

## **Prerequisites**

### **Kubernetes container orchestration**

1. Familiarity with Kubernetes basic technical concepts.
2. Understanding of Kubernetes components, functionality, their interactions, and implementation options.
3. Knowledge of APIs and configuration management.

**Switch to**: [Kubernetes section]({{< ref "kubernetes" >}})

### **Python Programming Skills**

1. Object-oriented programming (OOP): Familiarity with core OOP concepts like classes, objects, inheritance, and polymorphism.
2. Flow control: Ability to control the execution flow of your code using conditional statements (if/else) and loops (for/while).
3. Functions: Defining and using functions to modularize your code and improve reusability.
4. Data types: Understanding different data types (e.g., integers, strings, booleans) and their appropriate usage.
5. Binary data: Understanding how to work with binary data, including byte manipulation and endianness.
6. Exception handling: The ability to handle errors and exceptions gracefully to prevent program crashes.
7. JSON: Knowledge of JSON format for data exchange between the framework and external systems.

**Switch to**: [Python]({{< ref "python" >}})

### **Python libraries**

1. API web framework - FastAPI: Familiarity with FastAPI for building web APIs.
2. Kubernetes: Understanding of the Kubernetes library for managing and deploying containers.
3. Pydantic: Proficiency with Pydantic for data validation and schema generation in data models.
4. Request, response: Knowledge of handling HTTP requests and responses.
5. Signal: Understanding of signal handling mechanisms for processing software interrupts.

**Switch to**: [Python libraries]({{< ref "libraries" >}})

### **Standards & Patterns**

1. RESTful API: Familiarity with RESTful API design principles.
2. WebSocket API: Understanding of WebSocket API for real-time, two-way communication.
3. Asynchronous API Calls: Ability to implement asynchronous API calls for non-blocking communications.
4. JSON Web Token (JWT): Knowledge of JSON Web Token (JWT) for secure authentication and authorization.

**Switch to**: [Standards & Patterns]({{< ref "standards" >}})

### **Docker Containers**

1. Dockerfile: Understanding of Dockerfile and its role in building Docker images.
2. Docker Images: Familiarity with Docker images as templates defining container configurations.
3. Docker Containers: Knowledge of Docker containers as isolated processes running instances of Docker images.
4. Layers and Caching: Comprehension of Docker's layered approach for building images, including layer caching.
5. Volumes and Bind Mounts: Proficiency with volumes and bind mounts for persisting data beyond container lifecycle.
6. Container Ports: Understanding of container ports and how to expose internal container processes externally.
7. Entrypoint: Knowledge of the entrypoint command executed when a container starts.
8. Container Registry: Familiarity with container registries for storing and distributing Docker images.

**Switch to**: [Docker Containers]({{< ref "docker" >}})