---
weight: 4
title: "Libraries"
---

# **Python libraries**

### **Introduction**

A strong foundation in Python libraries is crucial for your success as PepGenX Platform developer. That's why we've compiled a comprehensive list of essential tools along with valuable learning resources. We highly recommend familiarizing yourself with each library's documentation and exploring the provided tutorials and video resources.

Each library listed below plays a critical role in building robust and efficient Python components for PepGenX Platform.

### **FastAPI**
 
1. Official Documentation: ["FastAPI Documentation"](https://fastapi.tiangolo.com)
2. Online Book: ["FastAPI Handbook – How to Develop, Test, and Deploy APIs"](https://www.freecodecamp.org/news/fastapi-quickstart/)
3. YouTube: ["4 Tips for Building a Production-Ready FastAPI Backend"](https://www.youtube.com/watch?v=XlnmN4BfCxw)
 
 
### **Kubernetes**
 
1. Official Documentation: ["API Overview"](https://kubernetes.io/docs/reference/using-api/)
2. Kubernetes Client Official Python library: ["Python"](https://github.com/kubernetes-client/python/)
 
### **Pydantic**
 
1. Official Documentation: ["Pydantic Documentation"](https://docs.pydantic.dev/latest/)
2. YouTube: ["Pydantic V2 - Full Course - Learn the BEST Library for Data Validation and Parsing"](https://www.youtube.com/watch?v=7aBRk_JP-qY)
3. YouTube: ["Why You Should Use Pydantic in 2024 | Tutorial"](https://www.youtube.com/watch?v=502XOB0u8OY)
 
### **Request, Response**
 
1. Python Requests Library Documentation: ["Requests: HTTP for Humans"](https://docs.python-requests.org/en/latest/)
2. YouTube: ["Requests Library in Python - Beginner Crash Course"](https://www.youtube.com/watch?v=Xi1F2ZMAZ7Q)
 
### **Signal**
 
1. Python Signal Module Documentation: ["signal - Set handlers for asynchronous events"](https://docs.python.org/3/library/signal.html)
2. Tutorial: ["Handling Signals with Python"](https://blog.marco.ninja/notes/technology/python/python-handling-signals/)
3. YouTube: ["Processing & Handling Signals in Python"](https://www.youtube.com/watch?v=758tGjmp7Io)

