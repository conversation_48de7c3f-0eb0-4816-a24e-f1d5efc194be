---
title:                  "Markdown Help"
weight:                 99
---

# Markdown formatting help

### Introduction

Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

{{< columns >}}
### Markdown Quick Help

Learn the basics of standard Markdown formatting, including headings, lists, links, images, and more. This section provides essential syntax examples to help you create well-structured and readable documents. [Browse]({{< ref "markdown" >}}).

<--->

### <PERSON> Markdown Extensions

Discover Hugo-specific Markdown features and shortcodes that extend standard Markdown. This section explains how to use <PERSON>’s unique tools for advanced content formatting and site generation within the Hugo framework. [Browse]({{< ref "hugo" >}}).

{{< /columns >}}

{{< columns >}}
### CommonMark markdown Spec

The CommonMark specification is a standardized, unambiguous syntax for Markdown. It provides a clear and consistent way to write Markdown documents, resolving the inconsistencies and ambiguities present in the original Markdown syntax. [External site](https://spec.commonmark.org/0.31.2/).

<--->

### Markdown format test (Dingus)

Use live testing tool to experiment with Markdown syntax and see the rendered output in real-time. It's a useful resource for testing and validating  Markdown code. [External site](https://spec.commonmark.org/dingus/).

{{< /columns >}}