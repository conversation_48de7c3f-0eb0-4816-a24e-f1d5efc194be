---
weight: 2
title: "Service Bus"
date: 2025-04-15
tags: ["azure", "messaging", "service bus"]
summary: "Azure Service Bus summary"
---

## Cloud Service Classification

**Category of the Service**: Application Integration
**Cloud Provider**: Microsoft Azure
**Website**: [Technical documentation](https://learn.microsoft.com/en-us/azure/service-bus-messaging/)

## Cloud Service Overview

**Name**: Azure Service Bus
**Description**: Azure Service Bus is a fully managed message broker that supports message queues and publish-subscribe topics for asynchronous data integration. It provides features such as transactions, ordering, duplicate detection, and immediate message consistency. Azure Service Bus ensures guaranteed delivery and reliable state transition management.

### SKU Approval Status
  - PepsiCo Approved
    - Basic (Only for DEV)
    - Standard
    - Premium
    
[SKU Selection Decision Tree](#sku-selection-decision-tree)

### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

### Service Lifecycle
  - Release Date: January 2010
  - Planned Decommission Date: No announced decommissioning plans.
  - Decommission Date: Not applicable at this time.

## Usage Guidelines

### Features
  - Messaging Queues and Topics
    Provides queues for point-to-point messaging and topics with subscriptions for publish/subscribe scenarios, enabling one-to-one or one-to-many communication patterns.
  - Reliable Delivery and Ordering
    Ensures at-least-once delivery of messages. Sessions and transactions enable strict ordering (FIFO) and atomic processing of groups of messages.
    - Dead-lettering: Service Bus can automatically move undeliverable or expired messages to a dead-letter queue for later inspection or retries.
    - Scheduled Delivery: Allows messages to be enqueued for delivery at a later time (scheduling them for delayed processing).
    - Message Deferral: Receivers can defer retrieval of certain messages (postpone processing) and later receive them when ready .
    - Duplicate Detection: Automatically removes duplicate messages when the same message is sent multiple times within a user-defined interval.
  - Message Filtering and Routing
    Subscribers to a topic can set up filter rules and actions to ensure they receive only relevant messages from a published stream.
  - Auto-Forwarding
    Queues or subscriptions can automatically forward messages to another entity (queue or topic) within the namespace, chaining message workflows.
  - Auto-Delete
    Queues or topics can be set to auto-delete after a period of inactivity, useful for automatically cleaning up temporary or unused entities .
  - Protocol Support
    Service Bus supports the AMQP 1.0 protocol, which is recommended for efficient, stateful connections, as well as HTTPS/REST. Additionally, Service Bus provides support for the JMS API (Java Message Service), with JMS 2.0 fully supported in the Premium tier.
  - Integration with Azure Ecosystem
    Service Bus can trigger Azure Functions or Logic Apps and send events to Azure Event Grid upon receiving new messages, enabling reactive event handling.

### Sample Use Cases
  - Decoupling Microservices
    Decoupling application components improves reliability, enhances fault tolerance (if the consumer is down, orders accumulate in the queue until it recovers), and allows for independent scaling of the front-end and back-end.
  - Load Leveling and Burst Smoothing
    In scenarios involving spiky traffic, a Service Bus queue serves as a buffer to balance the load. Producers can quickly submit tasks, while consumers process them at a steady rate, preventing overload.
  - Publish/Subscribe Event Distribution
    A single message published to a topic can be delivered to multiple subscribers, each applying its own processing logic. Every system can maintain its own subscription and receive events independently, with the ability to filter only the events that are relevant to it.
  - Workflows and Transactions
    Workflow orchestration can support business processes that involve multiple steps or services. The transaction feature enables the grouping of operations so that they either both commit or both roll back.

### Limitations
  - Basic Tier Constraints
    The Basic SKU supports only queues and does not include features such as transactions, sessions, deferred messages, forwarding to other entities, or duplicate detection. Additionally, it does not offer a geo-disaster recovery feature.
  - Multi-Tenant Throughput Variability
    In the Basic and Standard tiers (a multi-tenant environment), throughput and latency may vary under load. For extremely demanding, low-latency workloads, the Premium tier should be used.
  - Message Size Limits
    In the Basic/Standard tiers, the maximum message size is 256 KB (including both the header and body). In the Premium tier, the broker supports messages of up to 100 MB when using AMQP, or up to 1 MB when using the HTTP API. Messages that exceed these limits will be rejected by the service.
  - Entity and Namespace Quotas
    Each Azure Service Bus namespace can contain a maximum of 10,000 queues and topics combined in the Basic or Standard tier. For the Premium tier, the limit is 1,000 entities per Messaging Unit (MU). This means a Premium namespace with 1 MU allows up to 1,000 queues or topics; with 2 MUs, the limit increases to 2,000 entities, and so on. Additionally, each topic can have a maximum of 2,000 subscriptions in the Standard and Premium tiers (subscriptions per topic).
  - At-Least-Once Delivery
    End-to-end exactly-once delivery is not guaranteed; instead, Service Bus offers duplicate detection features to help eliminate duplicates.
  - Geo-Disaster Recovery Limitations
    The Basic and Standard tiers do not support built-in geo-disaster recovery (geo-DR) capabilities. Geo-disaster recovery is available only in the Premium tier.

### Additional Guardrails
  - TBD

### Used By
  - TBD list & links to some onboardings.

### EA Declaration
  - NOT a declared standard.

## Technical Guidelines

### Best Practices
  - Protocol
    Whenever possible, use the AMQP protocol. AMQP 1.0 maintains a persistent connection and supports features such as batching and prefetching, which significantly enhance throughput and reduce latency compared to the HTTP/REST protocol.
  - Ordering and Session Management
    If strict message ordering is required, session-enabled queues or topics should be used. Ensure that each session is processed by only one consumer at a time, and that a session is completed or closed promptly. Similarly, when using message locks (Peek-Lock mode), the application must complete, defer, or abandon messages within the lock timeout (30 seconds by default) or renew the lock if additional processing time is needed.
  - Error Handling and Dead-Lettering
    Use the dead-letter queue (DLQ) to manage messages that repeatedly fail to process. This ensures the main queue remains unblocked by stuck messages.
  - Security Best Practices
    Restrict access by using Azure AD RBAC instead of widely sharing SAS keys. Periodically rotate SAS keys and disable any unused keys.
  - Partitioning
    Partitioning can enhance throughput capacity; however, certain features (such as transactions) are not supported for partitioned entities in the Standard tier. Consider using the Premium tier and scaling out by utilizing multiple messaging units or multiple namespaces, rather than relying on a single large partitioned entity.

### High Availability & Disaster Recovery
  - In-Region Redundancy
    Azure Service Bus ensures high availability within a region by default. The service is deployed in a cluster that spans multiple fault domains (and, if supported in the region, across Availability Zones). The platform maintains multiple copies of the message store and is capable of seamlessly handling the loss of a node or an Availability Zone while retaining spare capacity.
  - Geo-Disaster Recovery
    The Premium tier of Azure Service Bus offers a Geo-Disaster Recovery (Geo-DR) feature, also known as Namespace Failover. Geo-DR enables the pairing of two namespaces in different regions in an active-standby configuration, using an alias name. The primary namespace continuously replicates metadata (including all entities and rules) to the secondary namespace.

### Backup & Recovery
  - Backup and Restore
    The Service Bus does not offer a native backup/restore feature for messages. Its focus for disaster recovery (DR) lies in redundancy and failover rather than traditional backup solutions.
  - Short-Term Recovery
    In the event of an outage, if the region recovers, Service Bus will come back online with all messages intact (as messages are persisted and replicated within the region’s storage cluster). No user action is necessary.
  - Major Disaster Recovery
    If an entire region is lost for an extended period or permanently, Geo-DR (if configured) can be utilized for recovery. In a Standard Geo-DR (metadata-only) scenario, messages that were in flight on the primary and not yet replicated may be lost. For critical data, a Premium Geo-DR configuration is recommended.

### Access Control & Security Configuration
  - Authentication
    Use Azure Service Bus built-in Azure roles, such as Owner, Data Sender, and Data Receiver. If this is not possible, each Service Bus namespace has SAS policies (shared key credentials) that can be used to sign tokens for client access.
  - Encryption
    All Service Bus communications must use TLS/SSL encryption during transit. The service does not accept unencrypted connections. For encryption at rest, all message data is encrypted on the server side using AES-256 encryption with Microsoft-managed encryption keys. For Restricted data classification, you are required to use Service Bus Premium and enable Customer Managed Keys (CMK) for the namespace. Note that enabling CMK is only possible for new or empty namespaces.

### Network Connectivity Options
  - Public Endpoint 
    NOT ALLOWED: By default, the Service Bus enables public internet access, which must be changed to use either a Private Endpoint or access must be locked down with IP firewall rules to only allow known addresses.
  - Service Endpoints
    NOT AVAILABLE.
  - Private Endpoint
    Required for Restricted data classification. Private endpoint creates a private IP address in PepsiCo VNet, preventing exposure to the public Internet.

### Networking & Security Architecture
  - TBD

### Azure Service Limitations
  - Namespace Quotas
    An Azure Service Bus namespace serves as a container for messaging entities. For Basic and Standard tiers, a namespace can support up to 10,000 total queues and topics combined. In the Premium tier, each Messaging Unit (MU) can support up to 1,000 entities, with the option to use multiple Premium namespaces if needed.
  - Queue/Topic Size
    The standard tier queue/topic has a storage limit of 5 GB for non-partitioned entities, or up to 80 GB for partitioned entities, as partitioned entities can distribute storage across partitions. The premium tier allows storage up to the namespace limit, which is 1 TB per Messaging Unit (MU). This means a queue can effectively have up to 1 TB of storage if enough MUs are allocated. When a queue reaches its maximum size, incoming messages will be rejected until space is freed.
  - Message Size
    For the Standard tier, the maximum message size is 256 KB, while for the Premium tier with AMQP, it can be up to 100 MB. For batch operations, the batch size is limited to 256 KB in the Standard tier and 1 MB in the Premium tier. Both sizes include headers.
  - Concurrent Connections
    Service Bus Standard AMQP connections support up to 5,000 concurrent connections per namespace, while Premium has limits on the number of concurrent connections. Each connection can be either a listener or a sender.
  - Concurrent Operations
    A single queue or topic/subscription supports up to 5,000 concurrent receive or send operations, respectively.
  - Duplicate Detection Window
    If enabled, duplicate detection retains message IDs for a default duration of 1 minute (which can be configured for up to 7 days). Duplicates are detected only if they arrive within the configured time window.

### SKU Features
  - Basic Tier
      - Only supports queue semantics (no topics or subscriptions).
      - Does not support transactions, message sessions, duplicate detection, scheduled messages, or message forwarding.
      - Offers basic peek-lock and dead-lettering mechanisms.
      - No resource isolation or dedicated compute.
      - Maximum message size is 256 KB in Basic and Standard tiers.
      - Namespace allows a maximum of 10,000 entities.
      - No Service Level Agreement (SLA) provided for production use.
  - Standard Tier
      - Queues and Topics
      - Transactions, Deferral, Scheduling, Sessions, Auto-Forwarding.
      - Supports Topics/Subscriptions for Publish/Subscribe (Pub/Sub).
      - Message size can be up to 256 KB.
      - The Standard tier imposes a maximum entity size of 5 GB (unpartitioned) or 80 GB (partitioned), with a total limit of 400 GB as noted.
      - The Geo-DR Standard tier does not include the built-in Geo-Disaster Recovery alias feature.
      - The Standard tier Service Level Agreement (SLA) guarantees 99.9% uptime.
  - Premium Tier
      - Provides resource isolation through Messaging Units (MUs). Each Premium namespace can be allocated 1, 2, 4, 8, or 16 MUs.
      - Supports all Standard features, including queues, topics, transactions, etc.
      - Allows messages of up to 100 MB when using AMQP.
      - Provides Geo-DR with alias-based failover capabilities.
      - Fully supports JMS 2.0 API compliance.
      - No throttling.

### Related Service
  - TBD

## Compliance and Security Guidelines

### Security Baseline InfoSec
  - Information Security Specifications - Azure Service Bus


## SKU Selection Decision Tree

1. Is this usage strictly for development/testing (non-production)?
  Yes → Basic SKU (if queues only and minimal features) may be used (PepsiCo policy: Basic approved DEV only). Continue if production or if more features needed for DEV testing.
  No → Continue to Q2.
2. Do you require only queues (point-to-point), or do you need topics/subscriptions (publish/subscribe)?
  Queues only → Continue to Q3.
  Topics/subscriptions needed → Basic not eligible. Continue to Q3 for Standard or Premium.
3. Do you need any of these advanced broker features?
  Transactions
  Sessions/ordered messaging (FIFO)
  Duplicate detection
  Scheduled delivery
  Message deferral
  Auto-forwarding
  Yes to any → Basic not eligible. Continue to Q4.
  No → Continue to Q4.
4. What is the maximum individual message size required?
  ≤256 KB per message (and via HTTP/AMQP) → Continue to Q5.
  >256 KB, up to 100 MB (via AMQP) or 1 MB (HTTP batch) → Premium required.
  >100 MB → Out of supported range.
5. What data classification will be carried?
  Public, Internal, Confidential → Continue to Q6.
  Restricted → Premium required.
  Must use Private Endpoint
  Enable Customer Managed Key (CMK) for new/empty namespace
6. Do you require private connectivity (Private Endpoint) or Customer Managed Keys (CMK) for encryption?
  Yes, either/both → Premium required.
  No → Continue to Q7.
7. Do you require high or guaranteed throughput, low latency, or non-throttled, dedicated messaging units (resource isolation)?
  E.g., business critical, high message rates, unpredictable load
  Yes → Premium required.
  No → Continue to Q8.
8. Is Geo-Disaster Recovery (geo-DR / cross-region failover) an explicit requirement?
  Yes → Premium required.
  No → Continue to Q9.
9. Will your application need integration with JMS 2.0 (Java Message Service) API?
  Yes → Premium required.
  No → Continue to Q10.
10. Are queues/topics, subscriptions, or storage expected to exceed Standard tier quotas?
  More than 10,000 queues/topics per namespace
  Topics with more than 2,000 subscriptions each
  Queues/topics >5 GB (unpartitioned) / >80 GB (partitioned)
  Storage >400 GB total or require >5,000 concurrent connections
  Yes → Premium required (scale-out via MUs/namespaces).
  No → Standard tier is suitable (unless previous questions escalated to Premium).

## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 7 Apr 2025 (Dariusz Korzun)
