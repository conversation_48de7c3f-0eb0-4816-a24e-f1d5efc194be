---
weight: 2
title: "Container Registry"
date: 2025-04-15
tags: ["azure", "containers", "container registry"]
summary: "Azure Container Registry summary"
---

## Cloud Service Classification

**Category of the Service**: Containers
**Cloud Provider**: Microsoft Azure
**Website**: [Technical documentation](https://learn.microsoft.com/en-us/azure/container-registry/)

## Cloud Service Overview

**Name**: Azure Container Registry (ACR)
**Description**: Azure Container Registry is a fully managed, private Docker registry service designed for storing and managing container images and related artifacts. It supports the open-source Docker Registry 2.0 APIs and OCI (Open Container Initiative) image formats.

### SKU Approval Status
  - PepsiCo Approved
    - Basic Tier
    - Standard Tier
    - Premium Tier

### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

### Service Lifecycle
  - Release Date: March 27, 2017
  - Planned Decommission Date: No announced decommissioning plans.
  - Decommission Date: Not applicable at this time.

## Usage Guidelines

### Features
  - Managed Container Registry
    Fully managed Azure service based on Docker Registry v2 for private container image storage . ACR handles Docker and OCI images as well as other OCI artifact formats (e.g. Helm charts) in a centralized repository .
  - Integration with Azure Ecosystem
    Seamless integration with Azure Kubernetes Service (AKS), Azure App Service, Azure Batch, Azure Service Fabric, and other container orchestration platforms is available.
  - Geo-Replication
    The Premium tier supports geo-replication, allowing a single registry to span multiple Azure regions and function as a globally distributed registry.
  - ACR Tasks (Built-in CI/CD)
    Provides ACR tasks that offload Docker builds to Azure (az acr build).
  - Content Trust (Image Signing)
    The Premium tier supports Docker Content Trust for image tag signing and verification, ensuring that only trusted, signed images are deployed.
  - Webhooks and Event Notifications
    ACR can emit webhooks for certain events, such as image push and delete. The Basic plan supports 2 webhooks, the Standard plan supports 10, and the Premium plan supports up to 500 webhooks.
  - OCI Artifact Repository
    ACR can store OCI-compliant artifacts in the registry, including Helm charts, CNAB bundles, Singularity images, and more.

### Sample Use Cases
  - Deploying containers to Azure Service
    The app running on Azure Services can pull its microservice images from ACR in the same region for fast and secure deployment.
  - Continuous Integration/Continuous Deployment (CI/CD)
    ACR webhooks or ACR Tasks can trigger automated tests and deployments whenever new images are pushed.
  - Helm Chart Repository
    Use a private, access-controlled Azure Container Registry (ACR) to store and version Helm charts for Kubernetes deployments, instead of relying on a public Helm repository.
  - Multi-Region Distribution
    For globally distributed applications or multi-geography teams, enable geo-replication to allow local data pulls in each region for improved performance.

### Limitations
  - Image Size and Storage Constraints
    Each image layer can be up to 200 GB in size, and an image manifest can be up to 4 MB. The storage capacity of a single registry is capped at 40 TB.
  - No Integrated Vulnerability Scanning
    The ACR does not automatically scan images for vulnerabilities upon being pushed.

### Additional Guardrails
  - TBD

### Used By
  - TBD list & links to some onboardings.

### EA Declaration
  - NOT a declared standard.

## Technical Guidelines

### Best Practices
  - Optimize Image Size and Layers
    Keep container images lean to improve pull performance by removing unnecessary files. Use smaller base images when possible, and try to reuse common base layers across images.
  - Implement Image Tagging and Retention Strategy
    Implement a clear tagging scheme and enable retention policies to purge old images. ACR supports the automatic deletion of untagged manifests after a configurable retention period.
  - Secure Access and Disable Admin Account
    The registry's admin account (a single username and password with full privileges) is disabled by default. It is recommended to leave it disabled to enforce individual identities or service principals for access. Grant only the least privilege necessary (e.g., use the AcrPull role for services that only need to pull images, not push).

### High Availability & Disaster Recovery
  - Reliability and Redundancy
    For high availability (HA) within a region, use the Premium SKU that supports Availability Zones.
  - Multi-Region Resilience
    To ensure disaster recovery across regions, enable geo-replication to replicate ACR to multiple chosen regions.
  - Restore Process
    The recovery process involves re-uploading images. If geo-replication is enabled, recovery may utilize another replica or require re-establishing replication after the primary system is restored.

### Backup & Recovery
  - Soft Delete
    Azure Container Registry offers a soft-delete policy (currently in preview) that enables the recovery of deleted artifacts (such as images and tags) within a configurable retention window. ACR retains the data for a specified period, which can be set between 1 and 90 days (the default is 7 days). As of the preview stage, soft-delete is not supported on geo-replicated registries, and deleted artifacts cannot be manually purged—they are automatically purged after the retention period.
  - Retention Policy
    The retention policy can be used to automatically clean up stale data for untagged manifests.
  - Backup Strategy
    There is no native backup or restore functionality for ACR. However, container images can be backed up by regularly replicating them to a secondary registry.

### Access Control & Security Configuration
  - Azure AD Authentication
    Azure Container Registry uses Microsoft Entra ID for authentication by default. It is recommended to use these roles instead of sharing generic credentials.
  - Disable Local Admin User
    Each ACR instance has an optional administrator account that is disabled by default. It is recommended to leave this account disabled in production environments.
  - Repository-Scoped Permissions
    ACR supports repository-scoped tokens, which allow the creation of custom tokens with access restricted to specific repositories and actions (e.g., pull, push, delete). Use repository-scoped tokens to delegate access to external users or to isolate automation tasks for specific images without granting access to the entire registry.
  - Encryption
    All data in ACR (image layers and metadata) is encrypted at rest using Azure Storage encryption with Microsoft-managed keys. For restricted data classification, enable the Premium tier and use a customer-managed key (CMK) for the registry’s storage encryption.

### Network Connectivity Options
  - Public Endpoint
    NOT ALLOWED. By default, Azure Container Registry enables public internet access, which must be changed to use either a Private Endpoint. However, if used, must be locked down with IP firewall rules to only allow known addresses.
  - Service Endpoints
    NOT ALLOWED. This connectivity is less secure than a private link and should not be enabled.
  - Private Endpoint
    Required for all data classification. Private endpoint creates a private IP address in PepsiCo VNet, preventing exposure to the public Internet.
  - Bandwidth and Performance
    Azure Container Registry bandwidth limits vary across the service tiers:
      § Basic SKU: Download bandwidth is limited to 30 Mbps; upload bandwidth is limited to 10 Mbps
      § Standard SKU: Download bandwidth increases to 60 Mbps, while upload bandwidth is set at 20 Mbps
      § Premium SKU: Download bandwidth is limited to 100 Mbps and upload bandwidth of 50 Mbps

### Networking & Security Architecture
  - TBD

### Azure Service Limitations
  - Throughput Throttling
    ACR will throttle clients that exceed certain pull/push rates:
    - Approximately 1,000 read and 100 write operations per minute for Basic.
    - Approximately 3,000 read and 500 write operations per minute for Standard.
    - Approximately 10,000 read and 2,000 write operations per minute for Premium.
  - Resource Limits
    Each registry can contain up to 5,000 repositories. The number of webhooks per registry is limited based on the SKU tier (2/10/500).
  - No Direct Cross-Cloud Replication
    ACR's geo-replication feature cannot natively replicate images to non-Azure environments.

### SKU Features:
  - Basic
    - Includes 10 GB of storage and reduced throughput limits (approximately 30 MB/s blob pull bandwidth)
    - Does not support advanced features such as geo-replication or private endpoints
  - Standard
    - Offers 100 GB of included storage with higher throughput (~60 MB/s)
    - Does not include geo-replication
  - Premium
    - Includes 500 GB of storage with a throughput of approximately 100 MB/s, and supports 10,000 pulls per minute
    - Enables geo-replication
    - Supports high availability (HA) with Availability Zones
    - Provides Private Link connectivity with support for up to 200 private endpoints
    - Allows the use of customer-managed keys for encryption

### Related Service
  - TBD

## Compliance and Security Guidelines

### Security Baseline InfoSec:
  - Information Security Specifications - Azure Container Registry

## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 7 Apr 2025 (Dariusz Korzun)
