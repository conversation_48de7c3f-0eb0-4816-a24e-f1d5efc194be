---
weight: 2
title: "Key Vault"
date: 2025-04-15
tags: ["azure", "security", "key vault"]
summary: "Azure Key Vault summary"
---

## Cloud Service Classification

**Category of the Service**: Security
**Cloud Provider**: Azure
**Website**: [Technical documentation](https://learn.microsoft.com/en-us/azure/key-vault/)

## Cloud Service Overview

**Name**: Azure Key Vault
**Description**: Azure Key Vault helps safeguard cryptographic keys and secrets used by cloud applications and services. It enables secure storage of credentials (e.g., API keys, database connection strings), TLS/SSL certificates, and encryption keys in a centralized and managed vault.

### SKU Approval Status
  - PepsiCo Approved
    - Standard
  - Not Approved
    - Premium
    - Azure Managed HSM
    - Azure Dedicated HSM
    - Azure Payment HSM

### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

### Service Lifecycle
  - Release Date: June 24, 2015
  - Planned Decommission Date: No announced.
  - Decommission Date: Not applicable at this time.

## Usage Guidelines

### Features
  - Secret Management
    Storage and retrieval of secrets (passwords, connection strings).
  - Key Management
    Storage and retrieval of keys for encryption and signing operations.
  - Certificate Management
    Storage and retrieval of SSL/TLS certificates.

### Sample Use Cases
  - Azure Encryption at Rest
    Store customer-managed encryption keys for protecting data at rest across Azure services. Required for Restricted data classification.
  - Data Encryption in Transit
    Store and manage SSL/TLS certificates used in web applications to securre encription in transit. Automate certificate renewal.
  - Key Management for Applications Authentication
    Integrate with application to securely store and manage secrets and access keys for authentication and authorization.
  - Custom Application Encryption
    Integrate with applications to perform encryption, decryption, signing, and verification using keys.

### Limitations
  - Limited number of operations per second (throttling), including key creation and retrieval operations. Exceeding these limits can lead to throttling errors (HTTP status code 429).
  - Backup limitations prevent backing up an entire Azure Key Vault at once. Each key, secret, or certificate must be backed up individually.
  - Administrative operations require vault-level permissions.

### Additional Guardrails
  - TBD

### Used By
  - TBD list & links to some onboardings.

### EA Declaration
  - NOT a declared standard.

## Technical Guidelines

### Best Practices
  - Secret Management
    Implement a strategy for regular rotation of secrets to reduce the risk of exposure if compromised.
  - Backup Strategy
    Plan backup and recovery processes using individual backups for each key, secret, or certificate.
  - Network Security
    Block public Internet access. Configure Azure Key Vault firewall to restrict IP address access using Service Endpoint and Private Endpoint.
  - Monitoring and Auditing
    Enable logging and monitoring through Azure Monitor to track access and usage of secrets, keys, and certificates.

### High Availability & Disaster Recovery
  - Automatic Geo-replication
    Built-in redundancy with automatic replication of keys, secrets, and certificates to a paired region can be enabled.
  - Regional Failover
    Automated failover process to the secondary region. Azure Key Vault might enter a read-only mode. Existing values (keys, secrets, certificates) can be retrieved, but write/update/delete operations are disabled until the primary region is restored.

### Backup & Recovery
  - Backup individual keys, secrets, and certificates.
  - Enable soft delete to recover deleted secrets within a specified time frame.
  - Enable purge protection to prevent accidental or malicious permanent deletion of vault contents.

### Access Control & Security Configuration
  - Use role-based access control (RBAC) to restrict access to the Azure Key Vault.
  - Use Managed Identities for authentication instead of shared access signatures (SAS).
  - Regularly rotate keys and secrets.
  - Enable logging and auditing features to track access and usage.
  - Use separate Azure Key Vaults for different applications or environments. Cross-environment access is not allowed.

### Network Connectivity Options
  - Public Endpoint
    NOT ALLOWED. By default, Azure Key Vault enables public internet access, which must be changed to use either a Service Endpoint or a Private Endpoint. However, if used, must be locked down with IP firewall rules to only allow known addresses.
  - Service Endpoints
    Allowed for Internal & Confidential data classification. Service endpoints route traffic from the VNet over Azure’s backbone network instead of the public Internet.
  - Private Endpoint
    Required for Restricted data classification. Private endpoint creates a private IP address in PepsiCo VNet, preventing exposure to the public Internet.
  - Azure Key Vault Firewall
    Configure IP firewall rules to deny access from the public Internet, allowing access from specific VNets or trusted IP ranges.
  - Managed Identity
    Use System-Assigned Managed Identity for authentication from virtual machines, web apps, or functions.

### Networking & Security Architecture
  - TBD

### Azure Service Limitations
  - Service Limits
    Azure Key Vault has throttling limits, such as a maximum number of transactions per second. Exceeding these limits results in HTTP status code 429 (Too Many Requests).
  - Transaction Volume Constraints
    Azure Key Vault is not optimized for high-throughput scenarios; it is intended for secure storage and retrieval of secrets at deployment time rather than high-volume run-time operations.

### SKU Features:
  - Standard Tier
    - FIPS 140-2 Level 1 validated
    - Multitenant
    - Supports asymmetric and symmetric keys, secrets, and certificates
    - Software-protected keys support encryption-at-rest
    - Supports Service Endpoints
  - Premium Tier
    - FIPS 140-2 Level 3 validated HSM offering with enhanced security
    - Provides a secure hardware boundary managed by Microsoft
    - Supports Private Endpoints

### Related Service
  - TBD

## Compliance and Security Guidelines

### Security Baseline InfoSec:
  - Information Security Specifications - Azure Key Vault

## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.2: 26 Nov 2024 (Dariusz Korzun)
  - v.1: 12 Nov 2024 (Dariusz Korzun)
