---
weight: 2
title: "Cache for Redis"
date: 2025-04-15
tags: ["azure", "database", "redis cache", "cache"]
summary: "Azure Cache for Redis summary"
---

## Cloud Service Classification

**Category of the Service**: Database
**Cloud Provider**: Azure
**Website**: [Technical documentation](https://learn.microsoft.com/en-us/azure/azure-cache-for-redis/)

## Cloud Service Overview

**Name**: Azure Cache for Redis
**Description**: Azure Cache for Redis is in-memory cache based on the open-source Redis. It supports Redis data structures (strings, hashes, lists, sets, etc.) and features like publish/subscribe and Lua scripting.

### SKU Approval Status
  - PepsiCo Approved
    - Basic (Only for DEV)
    - Standard
    - Premium
  - Not Approved
    - Enterprise
    - Enterprise Flash

### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

### Service Lifecycle
  - Release Date: October 7, 2014
  - Planned Decommission Date: No announced.
  - Decommission Date: Not applicable at this time.

## Usage Guidelines

### Features
  - Redis Functionality
    Supports Redis data types (e.g. strings, hashes, lists, sets, sorted sets, bitmaps, and hyperloglogs), Redis features (Pub/Sub messaging, Lua scripting, and keys with time-to-live) and atomic operations on these types.

### Sample Use Cases
  - Data Caching
    Cache database queries or frequently accessed data to reduce database load and latency.
  - Session Store
    Use it as centralized session cache store enabling sticky session data for web application instead of using cookies.
  - Content Caching
    Cache static or semi-static content such as rendered page fragments, menus, or frequently used look-up tables instead of regenerating them or fetching from a database.
  - Message Broker / Pub-Sub
    Enable inter-service communication or real-time updates by publishing events or chat messages to a channel and have subscribers receive them in real time.

### Limitations
  - Unless persistence is enabled (in the Premium tier or higher), data in Azure Cache for Redis is stored only in memory and will be lost in the event of a node failure or restart.
  - Each cache instance is limited by the memory of the selected pricing tier/size. All data (keys + values) plus overhead must fit within the cache’s memory limit, or Redis will evict data (using an eviction policy).
  - Network throughput and client connections per cache limits are based on the tier and size. Hitting these limits can cause connection throttling.
  - Standard and Premium caches use one primary node with replication to a passive secondary. They do not support active-active writes or automatic multi-region replication. 

### Additional Guardrails
  - TBD

### Used By
  - TBD list & links to some onboardings.

### EA Declaration
  - UPDATE

## Technical Guidelines

### Best Practices
  - Choose Tier/SKU
    Select a tier based on your workload needs. The Basic tier is suitable only for development, testing, or prototypes, while the Standard tier is designed for production workloads. The Premium tier is necessary when data persistence, large memory sizes, or clustering (sharding) are required.
  - Data Persistence & Backup
    Plan data backup and recovery based on snapshots (RDB) or append-only file logs (AOF) to Azure Storage.
  - Scaling Strategy
    Scale out versus scale up based on usage patterns. Scale up (increasing the cache size SKU) if you need more memory or slightly higher throughput on a single node. Scale out (sharding with Redis clustering, available in Premium) to distribute the load across multiple nodes. Use clustering when you need to exceed the memory or CPU limits of a single node or to parallelize read/write operations for higher throughput.
  - Eviction and Expiration Policies
    Set TTLs (time-to-live) on cache entries or implement eviction policies to prevent stale data and manage memory. Monitor eviction metrics to ensure that the cache isn’t undersized.
  - Monitoring and Alerting: 
    Use Azure Monitor to track key metrics. Set up alerts for conditions such as memory usage approaching the limit, a sudden increase in eviction count (which indicates the cache is full and discarding data), or high latency detected in operations.

### High Availability & Disaster Recovery
  - In-Region HA
    For mission-critical workloads, deploy Premium tier caches with a Zone Redundant configuration to spread the nodes across separate Availability Zones, thereby improving resiliency against regional failures. The Premium tier’s multi-replica support allows for multiple replicas in different zones, further enhancing fault tolerance.
  - Geo-Disaster Recovery
    Standard and Premium caches are only available in a single region. Plan for manual failover by keeping a second cache in another region and switching the application’s cache endpoint through configuration in the event of primary region downtime. Alternatively, you can use Azure Traffic Manager with caching on both ends.

### Backup & Recovery
  - Snapshot Persistence
    In the Premium tier, enable Redis data persistence (RDB snapshots or AOF logging) if you need the cache state to be recoverable. Configure the snapshot frequency based on how often the data changes and how much data loss you can tolerate.
  - Exporting Data
    Use the Import/Export feature for Azure Cache for Redis (available in the Premium tier) to export cache data to an Azure Storage blob as a backup mechanism. Exports are one-time, on-demand operations that produce an RDB file containing the cache’s contents. The RDB file can be imported into a new cache instance.
  - Automated Data Reload
    Design your system to reload important data into the cache on startup or during a cache miss. This 'warm cache' approach ensures faster recovery in scenarios where the cache has been flushed or replaced.

### Access Control & Security Configuration
  - Authentication
    By default, Azure Cache for Redis instances are secured with an access key. It is recommended to periodically regenerate and rotate both the primary and one secondary key. Azure Cache for Redis also supports Microsoft Entra ID integration for authentication, and using Azure AD authentication is advised for enhanced security.
  - Encryption
    Ensure that encryption in transit is always enabled. For encryption at rest, any persisted data or backups written by Azure Cache for Redis to Azure Storage are encrypted using Azure Storage’s server-side encryption.
  - Logging and Monitoring
    Enable Azure Diagnostic logs for Azure Cache for Redis to record events such as client connections, authentication failures, evictions, and more. Audit both the control plane and the data plane for compliance.

### Network Connectivity Options
  - Public Endpoint
    NOT ALLOWED. By default, Azure Cache for Redis enables public internet access, which must be changed to use either a Private Endpoint. However, if used, must be locked down with IP firewall rules to only allow known addresses.
  - Service Endpoints (Deprecated)
    Private Endpoint should be used instead.
  - Private Endpoint
    This is the preferred method. A Private Endpoint will assign a private IP to the cache within the Azure VNet. All traffic to the cache then occurs entirely within Azure’s backbone network. Private Link is supported on the Basic, Standard, and Premium tiers.

### Networking & Security Architecture
  - TBD

### Azure Service Limitations
  - Lack of Multi-region Auto-Failover
    There is no native multi-region failover capability. This means that if an entire Azure region hosting your cache goes down, you cannot automatically fail over the cache to another region.
  - Data Size per Key
    Redis is not optimized for extremely large payloads associated with a single key. In practice, values should be smaller, ideally less than 100 KB, and certainly under a few MB.
  - Scaling Events
    When performing a scaling operation, the cache may experience a brief interruption or performance degradation while data is being redistributed. Additionally, scaling down is more limited; you cannot automatically revert from a clustered setup back to a single node without data loss. This process requires exporting the data and then reimporting it into a new cache of smaller size.

### SKU Features:
  - Basic
    - Limited to a single node
    - No SLA 
    - No replication
    - No Redis modules
    - Size ranges from 250 MB up to 53 GB 
    - No persistence, clustering, or zone redundancy
    - Maximum of approximately 20k connections
  - Standard
    - Supports two nodes (primary/replica) for high availability in one region
    - 99.9% availability SLA 
    - No replication
    - No Redis modules
    - Size ranges from 250 MB to 53 GB per node
    - No persistence or clustering
    - Maximum of approximately 20k connections
  - Premium
    - Clustering up to 10 nodes
    - Node size node up to 120 GB per node
    - Data persistence (backup to disk)
    - Three replicas per shard (multi-AZ redundancy)
    - Maximum of approximately 40k connections per shard

### Related Service
  - TBD

## Compliance and Security Guidelines

### Security Baseline InfoSec:
  - Information Security Specifications - Azure Cache for Redis

## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 7 Apr 2025 (Dariusz Korzun)
