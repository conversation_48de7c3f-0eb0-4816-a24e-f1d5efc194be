---
weight: 3
title: "Cosmos DB"
date: 2025-04-15
tags: ["azure", "database", "nosql", "cosmos db"]
summary: "Azure Cosmos DB summary"
---

## Cloud Service Classification

**Category of the Service**: Database
**Cloud Provider**: Azure
**Website**: [Technical documentation](https://learn.microsoft.com/en-us/azure/cosmos-db/)

## Cloud Service Overview

**Name**: Azure Cosmos DB
**Description**: Azure Cosmos DB is a fully managed, globally distributed, multi-model NoSQL database that supports various data models including document, key-value, graph, and column-family.

### SKU Approval Status
  - PepsiCo Approved
    - Serverless
    - Provisioned Throughput (Autoscale/Manual)

### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

### Service Lifecycle
  - Release Date: May 10, 2017
  - Planned Decommission Date: No announced.
  - Decommission Date: Not applicable at this time.

## Usage Guidelines

### Features
  - Multi-Model Support
    Cosmos DB natively supports multiple data models, including JSON documents, key-value pairs, graph (Gremlin), and column-family (Cassandra) data, through various APIs such as Core SQL, MongoDB, Cassandra, Gremlin, and the Table API.
  - Global Distribution
    Cosmos DB is globally distributed across any number of Azure regions, offering automatic global replication, configurable consistency, and optional multi-master support for active-active workloads with multi-region writes.
  - Tunable Consistency Levels
    Five consistency levels are available, allowing for fine-grained trade-offs between consistency and performance/availability: Strong, Bounded Staleness, Session, Consistent Prefix, and Eventual.
  - Automatic Indexing
    Cosmos DB is schema-agnostic and, by default, automatically indexes all data without requiring any schema or index management from the user. Indexing policies can be customized or turned off on a per-container basis if needed.
  - Elastic Scalability
    The service is designed to scale horizontally. Throughput (measured in RU/s) can be adjusted up or down on-demand for each container or database. There is effectively no upper limit on storage or throughput. Cosmos DB can partition data across many servers to handle petabytes of storage and millions of requests per second.
  - Change Feed
    Cosmos DB provides a change feed on each container.

### Sample Use Cases
  - Near Real-Time Writes and Queries
    Can be used to ingest and store telemetry from millions of devices. It handles fast writes of sensor data and provides near-real-time query capabilities. IoT devices around the world can write to the nearest region.
  - Global Availability
    Supports globally distributed databases for low-latency systems. Millisecond-level reads/writes and auto-scaling for traffic spikes enhance application backends.
  - Web and Mobile Applications
    Supports applications with a global user base that require high responsiveness. Ideal for storing user-generated content and session data. Schemaless JSON storage and global replication ensure quick access and smooth handling of traffic peaks.

### Limitations
  - The maximum size of a logical partition is 20 GB. It is best practice to choose a partition key with high cardinality to ensure even data distribution and to avoid reaching the 20 GB limit on any single partition. This helps prevent the need for re-partitioning, which occurs once that size is exceeded.
  - Distributed transactions across arbitrary items are not supported. ACID transactions can only operate on items within the same partition key value (i.e., a single logical partition). Multi-item transactions spanning different partitions or containers are not supported.
  - An individual record (document or item) in Cosmos DB has a size limit of 2 MB after JSON serialization. Larger objects, such as images and large text blobs, must be chunked or stored outside of Cosmos DB.
  - A single Cosmos DB container or database can be provisioned with a throughput of up to 1,000,000 RU/s (this is a soft limit).
  - An Azure account has a soft limit of 500 containers (or databases) per account.
  - JOINs across containers are not supported, and there is no concept of server-side foreign keys or multi-document join constraints. Cross-partition queries may incur higher latency and resource unit (RU) charges.

### Additional Guardrails
  - TBD

### Used By
  - TBD list & links to some onboardings.

### EA Declaration
  - NOT a declared standard.

## Technical Guidelines

### Best Practices
  - Effective Partitioning
    Avoid hot partitions that concentrate a disproportionate amount of traffic on a single partition, as this can throttle that partition and waste RUs. A well-chosen partition key should have high cardinality and group data that is often accessed together, minimizing cross-partition queries.
  - Indexing Strategy
    Customize the indexing policy to index only what you need. If certain large JSON subtrees or fields are not queried, exclude them from indexing to save on RU costs associated with writes. Conversely, ensure that any fields you filter or sort on are indexed.
  - Throughput Provisioning
    For steady or mission-critical workloads, use provisioned throughput on containers (or databases) to guarantee resources. If the workload experiences periodic spikes, enable autoscale; this feature will adjust RUs up to a maximum and down to a minimum, allowing you to pay for a baseline while automatically managing surges.
  - Use Appropriate Consistency
    Strong consistency provides the highest level of data integrity but comes with increased latency and is only available for single-region writes. Session consistency offers predictable consistency for each user or session, along with enhanced performance. Consistent prefix or eventual consistency maximizes throughput and availability. Both strong consistency and bounded staleness incur higher latency and resource unit (RU) costs, particularly in multi-region setups.
  - Lean Queries and Projections
    Avoid using SELECT *. Projecting only the necessary fields reduces the amount of data that Cosmos DB has to load and send, thereby saving Request Units (RUs). Prefer targeted queries with a filter on the partition key. If you must query across partitions, include additional filters to narrow the scope, or use parallel querying from the client to speed up results. Avoid unbounded cross-partition scans on large containers, as they can be slow and costly.
  - Batch Operations
    Group multiple operations into a single one when possible. Using batches can drastically reduce network calls and ensure atomicity for those grouped operations.
  - Enable TTL for Data Lifecycle
    Utilize the Time-to-Live (TTL) feature for containers. TTL will automatically delete items older than the specified interval, helping to manage storage bloat and costs. For archiving needs beyond TTL, consider periodically exporting data to more cost-effective storage solutions (e.g., Azure Blob or Data Lake) and then allowing TTL to purge the data from Cosmos DB.
  - Monitor and Optimize
    Utilize Azure Monitor metrics and diagnostic logs. Key metrics include RU/s consumption, 429 errors (indicating throttling), CPU/Memory usage (for client-side, if using integrated cache or gateway), and replication latency. Set up alerts for RU consumption approaching provisioned limits or for unusual spikes in latency.

### High Availability & Disaster Recovery
  - Multi-Region Deployment
    Deploy Cosmos DB in at least two regions to guard against regional outages. Designate one region as the write region (primary) and the others as read regions, or enable multi-region writes for active-active scenarios. In single-write configurations, Service-Managed Failover can be enabled; if the primary region goes down, Cosmos DB will automatically promote a secondary region to primary without manual intervention. By using at least one read replica, you ensure that the database remains available for read operations (and can be quickly written to after failover) during regional incidents.
  - Data Durability and RPO
    In a single-region account, a catastrophic region loss could result in data loss if backups are not in place. In multi-region accounts, Cosmos DB’s cross-region replication ensures durability. The Recovery Point Objective (RPO)—which refers to the maximum acceptable amount of data loss measured in time—depends on the consistency level. With Strong consistency or bounded-staleness and sufficient replication, Cosmos DB can achieve an RPO of 0 (indicating zero data loss), even if a region suddenly disappears. For weaker consistency levels, such as Session or Eventual, the asynchronous replication might lead to an RPO that ranges from seconds to minutes. Microsoft documents an RPO of less than 15 minutes for session, consistent-prefix, and eventual consistency in a two-region setup.

### Backup & Recovery
  - Automatic Backups
    Azure Cosmos DB provides automated backups of data, taking snapshot backups at regular intervals (at least every hour). The two most recent snapshots are maintained, and older ones are overwritten. Cosmos DB also offers Continuous Backup (Point-in-Time Restore), which, when enabled, continuously backs up data in each region with approximately 100-second granularity. Continuous backup allows for restoration to any point in time within the retention period (up to 30 days).

### Access Control & Security Configuration
  - Azure AD Authentication
    Use Azure Entra ID for authenticating and authorizing access to Cosmos DB resources at the database account, database, or container level. It is recommended to disable key-based authentication or, at the very least, avoid using the primary key in applications when Azure AD is in use.
  - Keys and Key Rotation
    If primary keys are used, rotate them regularly. Integrate key rotation into your secret management practices. Azure Key Vault can be utilized to store the keys and trigger rotation reminders.
  - Encryption at Rest
    All data in Cosmos DB is encrypted at rest by default using Microsoft-managed keys. For data classified as Restricted, you should use customer-managed keys (CMK). Enabling CMK may impact throughput, as each request involves a key unwrap.
  - Encryption in Transit
    Ensure that all direct REST calls use HTTPS, as there is no option for unencrypted traffic—all communications are always encrypted in transit. For added protection, enforce TLS 1.2 at the client runtime.

### Network Connectivity Options
  - Public Endpoint 
    NOT ALLOWED: By default, the Cosmos DB enables public internet access, which must be changed to use either a Service Endpoint or a Private Endpoint. However, if used, must be locked down with IP firewall rules to only allow known addresses.
  - Service Endpoint
    Allowed for Internal & Confidential data classification. Service endpoints route traffic from the VNet over Azure’s backbone network instead of the public Internet.
  - Private Endpoint
    Required for Restricted data classification. Private endpoint creates a private IP address in PepsiCo VNet, preventing exposure to the public Internet.
  - Network Performance
    Enabling Availability Zones for Cosmos in a region (for resiliency) has no adverse network effect for the client, but comes with a slight cost increase (1.25x RU charge for single-region accounts) .

### Networking & Security Architecture
  - TBD

### Azure Service Limitations
  - Resource Limits
    Each database with shared throughput can support up to 25 containers.
  - Throughput and Storage
    A single logical partition is limited to 20 GB of data, and a single container can initially scale to 1 million RU/s. Unpredictable workloads on serverless may hit the cap if they suddenly require more than 5,000 RU/s without the data needed to scale partitions.
  - Multi-Region Write Constraints
    For multi-region writes (multi-master), all regions must use the same consistency level. Conflict resolution is automatic (last-write-wins by default), but a custom conflict resolver can be configured if needed.
  - Backup Restore
    If Continuous Backup is not used, point-in-time restore is not available. In-place restoration must be performed to a new account. This means that to execute a point-in-time “rollback,” restoration must be done to a new account, followed by swapping the connection in the app. The Cassandra API does not support continuous backup.

### SKU Features:
  - Provisioned Throughput
    - Fixed number of RUs per second
    - Manual scaling up or down at any time (with a minimum of 1 RU/s per 1 GB of storage on the container as a floor)
    - Autoscaling on provisioned throughput, with a configured maximum RU/s
    - Supports all Cosmos DB features
    - Hourly billing based on highest provisioned RU/s
  - Serverless
    - No provisioned Request Units per second (RU/s)
    - Billing for consumed Request Units (RUs)
    - Limited to a single region (no multi-region replication)
    - Cap of 5,000 Request Units per second (RU/s) per container
    - No reserved capacity
    - No performance SLA
  - API Choices
    - Core (SQL) API: (default) offering SQL-like query language over JSON data, stored procedures in JavaScript. All features of Cosmos DB are available in this API.
    - MongoDB API: Cosmos DB emulates a MongoDB server endpoint. MongoDB drivers to talk to Cosmos DB. Not all Cosmos features (like multi-region writes with strong consistency) are available, and certain Mongo-specific features (e.g., MongoDB Aggregation pipeline stages or MongoDB-specific data types) might have limitations. It does support MongoDB transactions.
    - Cassandra API: Cosmos DB appears as a Cassandra cluster. Cassandra Query Language (CQL) can be used to interact with Cosmos DB. There are some differences (table schema definitions, omitted features like TTL per query and some system tables). 
    - Gremlin (Graph) API: Enables graph data model, using Gremlin syntax to traverse. The graph is stored in Cosmos DB containers as vertices and edges (internally as JSON documents).
    - Table API: Compatible wtih Azure Table Storage. Table API doesn’t support all Cosmos DB features (for example, no automatic secondary indexes beyond the partition and row keys like Azure Table).

### Related Service
  - TBD

## Compliance and Security Guidelines

### Security Baseline InfoSec:
  - Information Security Specifications - Azure Cosmos DB

## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 7 Apr 2025 (Dariusz Korzun)
