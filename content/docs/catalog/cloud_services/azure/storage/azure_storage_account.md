---
weight: 2
title: "Storage Account"
date: 2025-04-15
tags: ["azure", "storage", "storage account"]
summary: "Azure Storage Account summary"
---

## Cloud Service Classification

**Category of the Service**: Security
**Cloud Provider**: Azure
**Website**: [Technical documentation](https://learn.microsoft.com/en-us/azure/storage/blobs/)

## Cloud Service Overview

**Name**: Azure Storage Account
**Description**; An Azure Storage Account is used to store large amounts of both unstructured and structured data, providing access to various storage services such as Blob Storage, File Shares, Queues, and Tables. Data in a storage account can be accessed via HTTP or HTTPS from anywhere, and by default, all data is encrypted at rest.

### SKU Approval Status
  - PepsiCo Approved
    - Standard general-purpose v2
    - Premium block blobs
    - Premium file shares
    - Premium page blobs
  - Not Approved
    - TBD

### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

### Service Lifecycle
  - Release Date: February 2010
  - Planned Decommission Date: No announced decommissioning plans.
  - Decommission Date: Not applicable at this time.

## Usage Guidelines

### Features
  - Multiple Storage Services
    A single storage account can host Blob Containers (used for object/blob storage), File Shares (providing managed NAS access via SMB/NFS), Queues (cloud-based message queues), and Tables (a NoSQL key-attribute store).
  - Scalability
    Supports storing petabytes of data and millions of objects. By default, each account can scale up to 5 PiB of capacity, with a virtually unlimited number of blobs, files, or messages.
  - Redundancy & Durability
    Offers configurable data replication options to safeguard against hardware or regional failures, including Locally Redundant, Zone-Redundant, Geo-Redundant, and Read-Access Geo-Redundant (Geo-Redundant with secondary read access).
  - Data Lake Integration
    Blob storage in GPv2 accounts can be enabled with Azure Data Lake Storage Gen2 (Hierarchical Namespace) to support big data analytics scenarios.

### Sample Use Cases
  - Unstructure Data Storage
    Storage Account can store unstructured data such as images, videos, documents, backups, and logs in a cost-effective and scalable manner. Blobs are ideal for backup and archive scenarios (using cool or archive tiers) and for big data lakes (using Data Lake Gen2) for analysis.
  - File Storage
    Azure Files can serve as a replacement for or a complement to file servers and NAS devices, enabling file sharing across multiple virtual machines (VMs) or applications in the cloud. Azure Files also supports syncing frequently accessed files to local Windows servers, providing cached access for improved performance. NFS file shares allow Linux-based workloads and containerized environments to leverage Azure Files as persistent shared storage.
  - Messaging
    Queue Storage can be utilized to decouple components in cloud applications when asynchronous processing is necessary.
  - NoSQL data store
    Azure Table Storage can be used to store large amounts of structured, schemaless NoSQL data. It functions as a key-value/attribute store, allowing data to be retrieved based on its key.

### Limitations
  - Messaging
    Azure Queues are straightforward FIFO message queues that do not natively support publish-subscribe mechanisms or advanced filtering capabilities.
  - NoSQL data store 
    Table Storage has limited query capabilities, with no support for joins or secondary indexes beyond primary keys. It functions as a basic key-attribute store.
  - Feature Limitations
    Features vary depending on the account type. A Premium storage account is hot-only and does not support the blob cool or archive tiers. Certain redundancy options, such as GZRS, are only available with Premium GPv2 accounts. Additionally, Azure Files does not support RA-GRS or RA-GZRS. It is recommended to consult the Microsoft Azure Storage Account documentation to verify the features available for each type of Storage Account.
  - Immutability vs. Mutability
    While Blob storage can store massive files (up to ~190 TiB for a single block blob with the latest API ), mutable random-write access is only via page blobs (max 8 TiB each ). 
  - File Size
    Azure Files currently supports files up to 4 TiB.
  - Account Change
    Once created, the type of a storage account cannot be changed. Achieving this requires creating a new account and copying the data to it.
  - Protocols
    Azure Storage supports the SMB protocol exclusively through Azure Files. The NFS protocol can be used with both Azure Files (NFS 4.1) and Blob Storage (NFS 3.0).

### Additional Guardrails
  - TBD

### Used By
  - TBD list & links to some onboardings.

### EA Declaration
  - NOT a declared standard.

## Technical Guidelines

### Best Practices
  - Choose the Right Account Type
    Select GPv2 for most scenarios. Switch to Premium Block Blob Storage only when you require very low latency or high IOPS for object storage, or to Premium File Storage for latency-sensitive enterprise applications or file shares that demand high throughput (e.g., for SAP).
  - Design for Scale and Performance
    To optimize performance when using Azure Table, design the PartitionKey in a way that minimizes the likelihood of too many entities in a single partition being accessed simultaneously. For Azure Blob Storage, distribute blobs across multiple containers to better balance the load. Extremely high request rates targeting the same partition (e.g., the same blob or the same table partition) can result in throttling (HTTP 503 errors).
  - Optimize for Cost
    Store infrequently used data in the Cool tier, which offers lower storage costs but higher access costs, or in the Archive tier, which provides very low storage costs for archival purposes but requires rehydration to access offline data. Use Lifecycle Management rules to automatically transition blobs to cooler tiers or to delete old versions and snapshots.
  - Security
    Use Managed Identities for Azure VMs or App Services to access storage without embedding keys. If not possible use Shared Key. Rotate the keys regularly, but no less frequently than every 12 months.
  - High Availability Design
    Leverage RA-GRS/RA-GZRS to provide read access to data located in the secondary region if the primary region becomes unavailable. Then, use account failover to promote the secondary region to the primary region.

### High Availability & Disaster Recovery
  - Redundancy
    When creating a storage account, select a redundancy option based on your durability requirements:
    - LRS (Locally Redundant Storage): Maintains three synchronous copies of the data within a single Azure datacenter.
    - ZRS (Zone-Redundant Storage): Stores three copies of the data across different availability zones in the same region for higher availability.
    - GRS (Geo-Redundant Storage): Combines LRS with asynchronous replication to a secondary region. The data in the secondary region is not accessible unless Microsoft initiates a failover or it is performed manually.
    - RA-GRS (Read-Access Geo-Redundant Storage): Similar to GRS but provides read-only access to the secondary endpoint.
    - GZRS (Geo-Zone-Redundant Storage): Combines ZRS with GRS by synchronously writing data to multiple zones in the primary region, and asynchronously replicating it to a secondary region (using LRS in the secondary region).
    - RA-GZRS (Read-Access Geo-Zone-Redundant Storage): The read-access version of GZRS, allowing read access to the secondary region.
  - Failover Process
    After a failover, the secondary replica becomes the new primary, and the DNS for the account will be updated to point to the new region

### Backup & Recovery
  - Blob Data Protection
    Utilize Blob soft delete to retain deleted blobs in a recoverable state for X days. Enable Blob versioning to automatically save previous versions of blobs when they are overwritten. Activate point-in-time restores to restore containers to a specific point in time.
  - File Share Backup
    Azure Files supports snapshots – point-in-time, read-only copies of the entire file share. They allow the restoration of individual files or entire shares to previous snapshot versions.
  - Retention Policies
    Implement policies that safeguard critical data, ensuring it is not deleted before a specified period. Lifecycle rules can be utilized to automatically delete files after X days.

###  Access Control & Security Configuration
  - Authentication & Authorization
    Microsoft Entra ID-based access for storage is preferred. If Entra ID cannot be used, implement SAS tokens for delegated access, but always set an expiration date for the SAS. Avoid using the storage account key.
  - Encryption
    All Azure Storage data is encrypted at rest by default using AES-256. For data classified as Restricted, Customer-Managed Keys (CMK) must be enabled.
  - Logging & Auditing
    To ensure proper handling of restricted data classification, enable Azure Storage Logging to record operations at the blob, file, queue, and table levels.
  - Configuration Hardening
    Disable Shared Key access if it is not needed, and disable cross-tenant replication unless it is necessary.

### Network Connectivity Options
  - Public Endpoint
    NOT ALLOWED. By default, Storage Account enables public Internet access for blob, file, queue, table. This connectivity must be changed to use Service Endpoint or Private Endpoint.
  - Service Endpoints
    Allowed for Internal & Confidential data classification. Service endpoints route traffic from the VNet over Azure’s backbone network instead of the public Internet.
  - Private Endpoint
    Required for Restricted data classification. Private endpoint creates a private IP address in PepsiCo VNet, preventing exposure to the public Internet.
  - Azure Key Vault Firewall
    Configure IP firewall rules to deny access from the public Internet, allowing access from specific VNets or trusted IP ranges.

### Networking & Security Architecture
  - TBD

### Azure Service Limitations
  - Capacity
    A standard storage account (GPv2) supports 5 PB (5,242 TB) of data by default, encompassing all services within the account.
  - Throughput and IOPS
    - Request Rate: For a standard GPv2 account, the maximum request rate is 20,000 requests per second, which includes both read and write operations across all services.
    - Bandwidth Limit: In most regions, ingress is limited to up to 25 Gbps, while egress is limited to up to 50 Gbps for a GPv2 account.
  - Object Limits
    There is no limit to the number of blobs, files, queue messages, or table entities in a storage account (practically constrained by capacity). However, per-object limits do exist. For example, a queue message can be a maximum of 64 KB in size, while a single Azure Table entity (row) can be up to 1 MB. Additionally, a table entity can have a maximum of 255 properties (columns).
  - Azure AD Integration Limits
    The evaluation of Azure RBAC is performed on a per-request basis. Therefore, extremely high request rates may require proper caching or the use of SAS tokens to ensure optimal performance. Additionally, there is a limit of up to 2,000 Azure AD role assignments per storage account.

### SKU Features:
  - General Purpose v2 (GPv2)
    - Supports all blob types: block blobs, append blobs, page blobs, ADLS Gen2, Files (SMB/NFS), Queues, and Tables.
    - Offers both Standard (HDD) and Premium performance tiers.
    - Supports all redundancy options: LRS, ZRS, GRS, RA-GRS, GZRS, RA-GZRS, as well as blob access tiers (hot, cool, archive).
    - Includes features such as object replication, immutable storage, and private endpoints.
  - General Purpose v1 (GPv1)
    - Legacy.
    - Supports blobs, files, queues, and tables.
    - Does not support blob tiering (cool/archive tiers) or newer enhancements.
    - Does not support private endpoints.
  - Block Blob Storage (Premium)
    - A specialized premium account type designed specifically for Block Blobs and Append Blobs, often referred to as "Premium Blob Storage."
    - Utilizes SSD storage for enhanced performance.
    - Supports LRS (Locally Redundant Storage) and ZRS (Zone Redundant Storage) replication.
    - Geo-redundancy is currently not available for premium blobs.
    - Does not support tables, queues, or file storage.
    - Supports Azure Data Lake Gen2, including the hierarchical namespace feature.
  - FileStorage (Premium Azure Files)
    - Supports both SMB 3.1.1 and NFS 4.1 protocols.
    - Stores data on SSDs.
    - Supports LRS and ZRS replication.
    - Cannot contain blobs, tables, or queues – only file shares.
  - Premium Page Blobs
    - Used for premium unmanaged Azure VM disks (page blobs).
    - Supports only page blobs (does not support block blobs, append blobs, files, queues, or tables).
    - Supports page blobs up to 8 TiB.
    - Supports LRS (Locally Redundant Storage) and ZRS (Zone Redundant Storage).
  - Blob Storage (legacy, hot/cool)
    - Supports only the Blob service (does not include files, queues, or tables).
    - Replaced by GPv2.

### Related Service
  - TBD

## Compliance and Security Guidelines

### Security Baseline InfoSec:
  - Information Security Specifications - Azure Storage Account

### Ownership and Version Control

## Service Architect
  - <NAME_EMAIL>

## Version Control
  - v.1: 14 Apr 2025 (Dariusz Korzun)
