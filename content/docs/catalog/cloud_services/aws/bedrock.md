---
weight: 24
title: "Bedrock"
date: 2025-08-13
tags: []
summary: ""
---

## Cloud Service Classification

* **Category of the Service:** AI/ML
* **Cloud Provider:** AWS

## Cloud Service Overview

* **Name:** Amazon Bedrock

* **Description:** Amazon Bedrock is a fully managed AWS service for generative AI that provides access to a choice of leading foundation models (FMs) through a unified API. It offers a broad set of capabilities to simplify the development of AI applications with security, privacy, and responsible AI built in. With Amazon Bedrock, users can experiment with top-tier FMs, privately customize models using their own data (e.g. via fine-tuning and retrieval-augmented generation), and create managed AI agents that perform complex tasks using enterprise data and APIs – all without having to manage infrastructure. The service is **serverless**, so integration into applications via familiar AWS tools is quick and secure, and customer data is protected (Bedrock does not use your content to train its models).

### SKU Approval Status:
  - PepsiCo Approved
    - TBD
  - Not Approved:
    - TBD 
### Allowed PepsiCo Data Classification:
  - Public  
  - Internal  
  - Confidential  
  - Restricted  

### Service Lifecycle  
- **Release Date:** August 22, 2025 
- **Planned Decommission Date:** No announced decommissioning plans.  
- **Decommission Date:** Not applicable at this time.

## Usage Guidelines

### Features:

  * **Foundation Model Hub**: Access high-performing foundation models from AWS and third-party providers via a single API, enabling flexibility and easy switching between models.
  * **Model Customization**: Customize models with your data using no-code fine-tuning or Retrieval-Augmented Generation (RAG) to improve domain-specific performance.
  * **Knowledge Bases (RAG)**: Extend model knowledge with proprietary data through retrieval-augmented generation, useful for tasks like question-answering on company documents.
  * **Managed AI Agents**: Fully managed framework for multi-step tasks, enabling developers to build assistants without custom orchestration logic.
  * **Guardrails and Responsible AI**: Built-in tools for content filtering and compliance, reducing harmful content and preserving privacy by not using your data for training.

### Sample Use Cases:

  * **Content Generation**: Create text content like marketing copy or product descriptions from prompts.
  * **Knowledge Q&A**: Answer questions by synthesizing information from large document corpora.
  * **Image Creation**: Generate images from text descriptions for design or marketing.
  * **Personalized Recommendations**: Provide intelligent product or content recommendations based on user profiles.
  * **Document Summarization**: Summarize lengthy documents into concise insights.

### Limitations:

  * **Service Quotas and Throttling**: Usage quotas may throttle requests; high-volume applications should request increases.
  * **Preview Features**: Some features in public preview may change and are not recommended for production.
  * **Generative AI Accuracy**: Outputs may contain inaccuracies; human review is essential for critical use cases.

### Additional Guardrails:
  * TBD

### Used By:
  * TBD list & links to some onboardings.

### EA Declaration:
  * NOT a declared standard.

## Technical Guidelines

### Best Practices:

  * **Model Selection and Evaluation** Experiment with different models to find the best fit for your task and re-evaluate as new versions become available.
  * **Secure Data Handling** Encrypt data in transit and at rest, use IAM policies for access control, and avoid hard-coding sensitive data in prompts.
  * **Enable Guardrails** Use Bedrock’s content filters and define custom policies to block inappropriate content.
  * **Cost Optimization** Use batch mode for cost efficiency and provisioned throughput for steady high-volume workloads.
  * **Monitoring and Logging** Track usage with CloudWatch and log API calls with CloudTrail for auditing and debugging.

### High Availability & Disaster Recovery:

  * **In-Region Redundancy** Bedrock ensures high availability with multi-AZ deployment and a 99.9% SLA.
  * **Multi-Region Resilience** Deploy in secondary regions for disaster recovery and use cross-region inference for resilience.

### Backup & Recovery:

  * **Data Persistence** Retain original data to recreate custom models or knowledge indexes if needed.
  * **Infrastructure as Code** Use CloudFormation or SDKs to script Bedrock configurations for rapid recovery.
  * **Logging and Output Storage** – Archive requests and responses in S3 or CloudWatch Logs for record-keeping and auditing.

### Access Control & Security Configuration:

  * **IAM Policies** Use least-privilege IAM policies to control Bedrock access and avoid static credentials.
  * **Data Encryption** Encrypt all data with TLS and AWS KMS, and ensure compliance with data standards.
  * **Network Isolation** Use PrivateLink to keep Bedrock API calls within AWS’s network.
  * **Monitoring & Auditing** Enable CloudTrail and Config rules to track API calls and ensure compliance.
  * **Guardrails & Policies** Define custom rules to block disallowed content and monitor outputs for compliance.

### Network Connectivity Options:

  * **Public Endpoint (Internet)** Not allowed for sensitive data; use secure channels if necessary.
  * **AWS PrivateLink (VPC Endpoint)** Required for Confidential data, ensuring private communication within AWS.
  * **Network Architecture** Minimize latency by placing services in the same region and implement egress controls.

### Networking & Security Architecture:
  * TBD

### AWS Service Limitations:

  * **Single-Region Scope** Bedrock is region-specific; multi-region resilience requires manual setup.
  * **Context and Size Limits** Models have input/output size limits; split large inputs if needed.
  * **Customization Support** Not all models support fine-tuning; use prompt engineering or RAG for customization.
  * **Model Lifecycle** Test outputs when models are updated to ensure compatibility.
  * **No On-Premises Option** Bedrock is cloud-only; consider alternatives for offline requirements.

### SKU Features:

  * **On-Demand Inference:** Pay-as-you-go for variable workloads with burst scaling.
  * **Batch Processing:** Cost-efficient for large-scale jobs, reducing costs by ~50%.
  * **Provisioned Throughput:** Reserved capacity for consistent high-volume usage with discounted pricing.

### Related Service:

  * TBD

## Compliance and Security Guidelines

* **Security Baseline – InfoSec:**
  * Information Security Specifications – Bedrock

## Ownership and Version Control

* **Service Architect:** Abdul Moyeed (<EMAIL>)
* **Version Control:**

  * *v.1:* 22 Aug 2025 (Abdul Moyeed)