---
weight: 24
title: "Bedrock"
date: 2025-08-13
tags: []
summary: ""
---

## Cloud Service Classification

* **Category of the Service:** AI/ML
* **Cloud Provider:** AWS

## Cloud Service Overview

* **Name:** Amazon Bedrock

* **Description:** Amazon Bedrock is a fully managed AWS service for generative AI that provides access to a choice of leading foundation models (FMs) through a unified API. It offers a broad set of capabilities to simplify the development of AI applications with security, privacy, and responsible AI built in. With Amazon Bedrock, users can experiment with top-tier FMs, privately customize models using their own data (e.g. via fine-tuning and retrieval-augmented generation), and create managed AI agents that perform complex tasks using enterprise data and APIs – all without having to manage infrastructure. The service is **serverless**, so integration into applications via familiar AWS tools is quick and secure, and customer data is protected (Bedrock does not use your content to train its models).

* **SKU Approval Status:**

  * **PepsiCo Approved:** Amazon Bedrock (Foundation Model service) – *Approved for use with proper controls*
  * **Not Approved:** *No alternative SKUs (N/A)*

* **Allowed PepsiCo Data Classification:**

  * Public
  * Internal
  * Confidential
  * Restricted

## Service Lifecycle

* **Release Date:** April 13, 2023 (service announced); GA on September 28, 2023
* **Planned Decommission Date:** No announced decommissioning plans.
* **Decommission Date:** Not applicable at this time.

## Usage Guidelines

* **Features:**

  * *Foundation Model Hub* – Provides access to a wide selection of high-performing foundation models from AWS and third-party AI providers (e.g. AI21, Anthropic, Cohere, Stability AI, Meta) via a single API. This model choice flexibility allows future-proofing and easy switching between model providers as new versions become available.
  * *Model Customization* – Allows private customization of models with your own data. You can fine-tune selected FMs or use Retrieval-Augmented Generation to inject your proprietary knowledge, improving model performance on domain-specific tasks. Fine-tuning is a no-code process in Bedrock’s console (selecting training data in S3 and adjusting parameters), resulting in a custom model tailored to your needs.
  * *Knowledge Bases (RAG)* – Built-in support for retrieval-augmented generation: you can create **Knowledge Bases** by uploading or connecting to enterprise data sources, enabling models to fetch relevant information during inference. This extends the model’s knowledge with your up-to-date proprietary data without altering the model, useful for use cases like question-answering on company documents.
  * *Managed AI Agents* – Offers a fully managed **Agents** framework to handle multi-step tasks. Amazon Bedrock Agents can break down user requests, call APIs or other services, and incorporate knowledge base queries to fulfill complex tasks (e.g. booking travel, processing forms, managing content workflows) autonomously. This capability lets developers build powerful conversational or task-oriented assistants without writing their own orchestration logic.
  * *Guardrails and Responsible AI* – Provides built-in **guardrails** to enforce content filtering and policy compliance. Bedrock’s automated safety checks can block or redact inappropriate content and reduce model hallucinations (AWS reports up to 88% of harmful content can be blocked and hallucinations reduced by \~75% using these tools). This helps ensure generated outputs meet corporate standards and ethical guidelines. Bedrock also never uses your prompts or data to train the base models, preserving privacy.

* **Sample Use Cases:**

  * *Content Generation* – Automatically generate original text content such as short stories, marketing copy, social media posts, or product descriptions based on prompts. For example, a marketing team can use Bedrock to create draft ad copy or blog posts given a few keywords or instructions.
  * *Knowledge Q\&A* – Use Bedrock to answer natural language questions by searching and synthesizing information from large document corpora. This can power an enterprise FAQ chatbot or research assistant that reads internal knowledge bases and provides detailed answers.
  * *Image Creation* – Generate realistic or artistic images from text descriptions. Bedrock’s support for text-to-image models (e.g. Stable Diffusion) enables use cases like creating design mockups, concept art, or personalized marketing visuals on the fly from a prompt.
  * *Personalized Recommendations* – Provide more contextual and intelligent product or content recommendations. Bedrock can interpret user profiles and queries to suggest items with relevance beyond simple keyword matching (e.g. recommending products that match a shopper’s preferences and past purchases). Retailers can enhance customer search results and recommendations using generative AI for better relevance.
  * *Document Summarization* – Summarize long text documents (articles, reports, books, etc.) into concise summaries. This helps users quickly grasp the key points without reading full documents. For instance, an analyst could input a lengthy report and Bedrock would return a short executive summary highlighting the main insights.

* **Limitations:**

  * *Service Quotas and Throttling* – Amazon Bedrock enforces default **quotas** on usage per account (such as maximum requests per minute and tokens per minute/day). If these limits are exceeded, requests will be throttled (HTTP 429 errors). High-volume applications must be designed to stay within these limits or request quota increases. (By default, new accounts have lower daily token limits until they build usage history.)
  * *Preview Features* – Some Bedrock capabilities may initially launch in **public preview** and are subject to change. For example, the “Latency Optimized Inference” mode is currently a preview feature. Preview features are not recommended for production use until they reach general availability, as their performance and interfaces might change.
  * *Generative AI Accuracy* – Responses from foundation models may occasionally contain inaccurate information or undesired content (“hallucinations” or biases). While Bedrock’s guardrails filter many issues, they do not eliminate them completely. It’s important to thoroughly test outputs and implement human review for high-stakes use cases. Generated content should not be blindly trusted for critical decisions without verification.

* **Additional Guardrails:**
  TBD

* **Used By:**
  *TBD –* (To be updated with list of PepsiCo use cases or onboarded projects.)

* **EA Declaration:**
  *NOT* a declared standard (service is not yet an official PepsiCo standard offering).

## Technical Guidelines

* **Best Practices:**

  * *Model Selection and Evaluation* – Choose the foundation model that best fits your task by comparing outputs and performance across available models. Amazon Bedrock makes it easy to experiment with different FMs in the console playground and via a single API, so you can evaluate which model yields the best results for your use case. Regularly re-evaluate models as new versions or providers become available to maintain optimal performance.
  * *Secure Data Handling* – Leverage Bedrock’s features to keep sensitive data secure. All data sent to Amazon Bedrock is encrypted in transit and at rest by default, and you can use AWS KMS to manage your own encryption keys for any data used in model customization. Use IAM policies to strictly control access to Bedrock (only allow necessary identities to invoke models or create agents). **Avoid hard-coding sensitive data** in prompts; instead reference secure data sources via knowledge bases or use AWS Secrets Manager for any keys/tokens an agent might need.
  * *Enable Guardrails* – Always utilize Amazon Bedrock Guardrails for applications that produce user-facing content. Enable built-in content filters or define custom guardrail policies to block profanity, PII, or other disallowed content in model outputs. Additionally, implement business-specific validation on responses where applicable (for example, ensure no confidential data is being inadvertently revealed in outputs). Guardrails help reduce inappropriate content and hallucinations, but continue to monitor output quality and refine prompts or rules as needed.
  * *Cost Optimization* – Manage usage patterns to control costs. Use **On-Demand** mode for development or low-volume workloads and **Batch** mode for large-scale offline processing (batch jobs can reduce inference cost by \~50% for supported models). For steady high-volume production workloads, consider **Provisioned Throughput** to get discounted pricing in exchange for reserved capacity. In addition, take advantage of Bedrock’s optimization features: for instance, using prompt caching or distilled models can significantly lower costs (distilled models run up to 5× faster and at 75% lower cost for only minor accuracy loss). Designing your application to reuse results (caching frequent queries) and choosing smaller models when absolute accuracy isn’t required are practical ways to keep generative AI affordable.
  * *Monitoring and Logging* – Implement comprehensive monitoring for Bedrock usage. Use Amazon CloudWatch to track metrics such as request counts, latency, and token consumption. Set up CloudWatch Alarms for unusual spikes or approaching quota limits (e.g. high error rates or token usage nearing the daily cap). Enable AWS CloudTrail for Bedrock to log all API calls for auditing – this will record who is invoking models or modifying configurations. For debugging or analysis, you can configure Bedrock to store request/response payloads to an S3 bucket or CloudWatch Logs. Regularly review logs to detect any misuse or anomalies and to gather insights on how users are interacting with the AI (which can inform prompt improvements).

* **High Availability & Disaster Recovery:**

  * *In-Region Redundancy* – Amazon Bedrock is a fully managed service deployed across multiple AWS Availability Zones for high availability. AWS manages the underlying infrastructure to eliminate single points of failure within a region. The service comes with an SLA of 99.9% uptime for each supported region, meaning AWS commits to a high level of availability (with service credits offered if availability drops below that threshold). There is no user-visible concept of Bedrock “instances” to make highly available – you simply call the regional API endpoint, and AWS ensures the requests are served even if one AZ has issues.
  * *Multi-Region Resilience* – **Amazon Bedrock is region-specific** and does not automatically fail over to another region in an outage. For disaster recovery planning, deploy your application in a secondary AWS region as a backup and be prepared to switch Bedrock API calls to that region if the primary region becomes unavailable. Amazon Bedrock supports **cross-region inference** in on-demand mode, which allows you to route requests to an alternate region’s infrastructure for higher throughput or resilience when needed. Utilize this capability to balance traffic or as part of a manual failover strategy. All persistent data (e.g., fine-tuned models or knowledge bases) would need to be recreated or reconfigured in the DR region ahead of time. Ensure your architecture includes proper DNS routing or configuration management to redirect Bedrock API usage to the backup region during an event.

* **Backup & Recovery:**

  * *Data Persistence and Recreate Strategy* – Amazon Bedrock itself is largely stateless (it hosts foundation models which AWS maintains, and your **custom artifacts** like fine-tuned models or knowledge indexes are stored within the service). There is no native “backup” feature for these artifacts, so it’s critical to **retain the original data** that was used to create them. Always keep backups of your training datasets, prompt templates, and source documents in a durable storage like Amazon S3. In an extreme scenario (e.g., accidental deletion or corruption of a custom model), you can use these originals to re-fine-tune a new model or rebuild a knowledge index.
  * *Infrastructure as Code* – Treat your Amazon Bedrock configurations as code to simplify recovery. Use AWS CloudFormation or the AWS SDK to script the creation of Bedrock resources (such as setting up knowledge bases, agents, and custom model endpoints). By having these configurations in code repositories, you can rapidly redeploy Bedrock setups in a new environment or region if needed, ensuring consistency and reducing manual steps in a recovery situation.
  * *Logging and Output Storage* – As a best practice, configure Amazon Bedrock to deliver a copy of requests and generated responses to an S3 bucket or CloudWatch Logs for record-keeping. This archive of interactions can serve as a backup for the *outputs* of the service. In case of an unexpected outage or the need to audit past outputs, you have a history of what was generated. Storing prompts and responses also means that if you need to migrate to a different solution, you have example inputs/outputs to retrain or fine-tune new models.

* **Access Control & Security Configuration:**

  * *Identity & Access Management (IAM)* – Use AWS IAM to tightly control access to Amazon Bedrock. Create specific IAM policies for Bedrock actions, limiting which AWS principals (users, roles) can invoke model inference or manage Bedrock features. For instance, an application EC2 instance might be granted permission only to call the `bedrock:InvokeModel` action on specific models. By employing least-privilege principles, you reduce risk of abuse. **Identity-based policies** can also restrict usage by conditions (e.g., allow Bedrock calls only from particular VPCs or only during business hours). Avoid using root credentials or static long-lived access keys to call Bedrock; instead, use AWS roles (with short-term credentials or instance profiles) for stronger security.
  * *Data Encryption* – All data handled by Bedrock is encrypted by default. Ensure that encryption is end-to-end: use TLS for all connections to the Bedrock API (required by AWS), and leverage **AWS KMS** for any at-rest encryption needs. For example, if you upload documents for a knowledge base or provide training data from S3, use KMS-encrypted S3 buckets. Amazon Bedrock allows use of customer-managed KMS keys to encrypt content used in model customization, giving you full control over access to that data. Verify that any content temporarily stored or logged in your workflow (prompts, outputs) is likewise encrypted and handled per your data classification standards.
  * *Network Isolation* – **Use PrivateLink/VPC Endpoints for Bedrock.** Configure an AWS PrivateLink interface endpoint for Amazon Bedrock in your VPC so that all Bedrock API calls remain within AWS’s network. This prevents any traffic from traversing the public internet, which is essential when working with Confidential or Restricted data. With an interface VPC endpoint in place, your applications in the VPC can call Bedrock’s APIs via private IP addresses, and you can apply VPC security group rules to control which resources can communicate with that endpoint. (If direct internet access must be used for Bedrock due to a unique scenario, ensure that egress is locked down – e.g., limit outbound access only to Bedrock’s API domains and use AWS WAF or proxies to monitor requests.)
  * *Monitoring & Auditing* – Enable detailed logging to meet compliance requirements. AWS CloudTrail should be turned on to record all Bedrock-related API calls (who invoked a model, who created or modified an agent, etc.) for audit trails. Use AWS Config rules or other monitoring to ensure the Bedrock PrivateLink endpoint and KMS encryption remain in place (any configuration drift could open a security hole). Regularly review CloudTrail logs and Bedrock CloudWatch metrics for signs of misuse or anomalies – for example, a sudden spike in requests could indicate an automation gone awry or a security issue. Incorporate Bedrock usage into your SIEM (Security Information and Event Management) so that security teams are alerted to unusual patterns (such as an account making Bedrock requests outside of expected hours or IP ranges).
  * *Bedrock Guardrails & Policies* – Take advantage of Bedrock’s guardrails to enforce InfoSec policies on AI output. Define **custom guardrail rules** if needed to block content that violates your organization’s guidelines (for instance, you can create a block list of terms or patterns that should never appear in outputs). Bedrock’s managed safety features can be configured to detect and prevent the release of sensitive data or toxic content. Integrate these guardrails with your application logic: for example, if Bedrock’s response indicates it was modified or blocked by a guardrail, have your application handle that gracefully (perhaps by logging an incident or reverting to a safe fallback response). By actively tuning and monitoring guardrails, you maintain control over the AI’s behavior in production.

* **Network Connectivity Options:**

  * *Public Endpoint (Internet)* – **NOT ALLOWED** for production use with sensitive or internal data. By default, Amazon Bedrock’s API endpoints are reachable via the public AWS API endpoints over HTTPS. In the PepsiCo environment, calling Bedrock directly over the internet is discouraged. If Bedrock must be accessed from an environment without a direct AWS network presence, you should still route through secure channels (such as a VPN into AWS or proxy) rather than exposing Bedrock usage openly. **Any exception to use the public endpoint** (e.g., for Public data classification tests) must include IP filtering and logging to ensure only authorized sources can communicate.
  * *AWS PrivateLink (VPC Endpoint)* – **Required** for Confidential and Restricted data, and strongly recommended for all use cases. Configure an AWS PrivateLink interface endpoint to connect to Amazon Bedrock within your AWS Virtual Private Cloud. This provides a private, controlled communication path to Bedrock. All Bedrock traffic stays on the AWS network, reducing exposure. Using PrivateLink also enables integration with VPC Service Controls and monitoring of traffic via VPC Flow Logs. In this setup, your applications do not need internet access at all to use Bedrock – they use the endpoint’s private address. Ensure the endpoint is placed in the appropriate subnets and that your security groups allow your application instances to reach it.
  * *Network Architecture Considerations* – Place Bedrock-consuming services (e.g., application servers or Lambda functions) in the same AWS region as the Bedrock endpoint to minimize latency. If you operate in a multi-account setup, you can share the Bedrock PrivateLink endpoint to other accounts using AWS RAM (Resource Access Manager) to avoid duplicating endpoints. Also, implement egress controls: for instance, an AWS NAT Gateway or firewall that only allows outbound traffic to Bedrock’s official AWS endpoint URLs, preventing any accidental data exfiltration to unknown endpoints. All connectivity should be over HTTPS (TLS 1.2+).

* **Networking & Security Architecture:**
  TBD

* **AWS Service Limitations:**

  * *Single-Region Scope* – Amazon Bedrock is confined to individual AWS regions. There is no built-in cross-region replication or failover for the service; if a region is down, Bedrock in that region is unavailable. Applications needing global resilience must handle invoking Bedrock in an alternate region manually (and have any necessary model customizations deployed there ahead of time). While cross-region inference is supported to help distribute load, it is not a substitute for a multi-region DR setup.
  * *Context and Size Limits* – Foundation models have limits on input prompt length and output size (measured in tokens). Extremely large inputs may not be fully processed in one request. For example, Amazon Titan models support up to \~8,000 tokens of context for embeddings, and other models vary (some newer LLMs support much larger contexts, e.g. 100k tokens, but those may have reduced availability or higher cost). If you need to process very long documents, you may have to split them into chunks or use summarization techniques first. Likewise, the API has payload size limits – sending overly large payloads or images may result in errors. Always consult the latest Bedrock documentation for the specific limits of each model and plan your usage within those bounds.
  * *Customization Support* – Not all models in Bedrock can be fine-tuned or customized. Currently, **Custom Model Import** allows bringing your own model (or one trained elsewhere) into Bedrock, but only for certain model families (e.g. Llama 2/3, Mistral, Flan-T5 architectures). Many third-party foundation models available through Bedrock are offered as-is for inference and do not support parameter fine-tuning on Bedrock. This means for those models you rely solely on prompt engineering and RAG for customization. If your use case requires a fully custom-trained model beyond what Bedrock supports, you may need to use Amazon SageMaker or another ML service.
  * *Model Lifecycle and Updates* – When using third-party models via Bedrock, be aware that model providers may release new versions or deprecate older ones. AWS handles the deployment of updated model versions on Bedrock and strives to make upgrades seamless (the unified API means you often can switch to a new version with minimal code changes). However, **changes in model behavior** with new versions could affect your application. It’s a best practice to test your application outputs whenever a model is updated. Additionally, some models labeled “beta” or “experimental” might be replaced or removed if the provider changes strategy. Keep an eye on AWS announcements for Bedrock to stay informed of model additions or removals.
  * *No On-Premises Option* – Amazon Bedrock is only available as a cloud service on AWS. There is no on-prem or private cloud deployment of Amazon Bedrock foundation models. Organizations with strict offline requirements will need to consider alternate solutions (such as running open-source models on SageMaker or EC2 instances). Using Bedrock requires connectivity to AWS, so network architecture should account for this (e.g., ensuring low-latency connection from on-prem environments to the nearest AWS region if needed). This limitation means vendor lock-in to some degree – although you can always export your data and switch to other model hosting solutions, you cannot export the proprietary foundation model itself from Bedrock.

* **SKU Features:**

  * **On-Demand Inference:** Pay-as-you-go usage with no long-term commitment. In on-demand mode, you are charged per input token and per output token for text generation models (and per image for image generation). This model provides full elasticity – you incur costs only when you invoke the service. It’s ideal for development, testing, or variable workloads. On-demand also supports *burst scaling* across regions (cross-region inference) transparently to handle traffic spikes, charged at the region’s rates.
  * **Batch Processing:** Cost-efficient mode for large-scale or offline jobs. You can submit a batch of prompts in a single job (for example, a file in S3 containing 10,000 prompts) and Bedrock will process them asynchronously, writing the outputs to an S3 location. Batch jobs can achieve roughly **50% lower cost** compared to on-demand per request for supported model types, making it economical for processing high volumes of data where latency is not interactive. This mode is useful for nightly processing or bulk content generation.
  * **Provisioned Throughput:** Dedicated, pre-allocated capacity for consistent high-volume usage. With provisioned throughput, you commit to a certain throughput (tokens per second) for a specific model in a region, and AWS allocates capacity to you to meet this demand. In return, you get a **discounted pricing** relative to on-demand and more predictable latency, even during peak times. This option suits production scenarios with steady traffic or SLA requirements. Provisioned throughput ensures your requests won’t be throttled below the level you reserved. (For example, if you have an app that constantly generates text, you might provision a certain number of tokens/minute to guarantee performance and cost stability.)
    *(Aside from the above pricing models, Amazon Bedrock also offers features like **Latency Optimized Inference** (currently in preview) for certain models, which can improve response times by running on specialized AWS Inferentia/Trainium hardware – this is an add-on setting rather than a separate SKU.)*

* **Related Service:**
  TBD *(e.g., Amazon SageMaker, which provides custom model training/hosting outside of Bedrock – to be detailed in future)*

## Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * Information Security Specifications – *Amazon Bedrock* (Refer to PepsiCo InfoSec baseline document for Amazon Bedrock, outlining required security configurations and controls in detail.)

## Ownership and Version Control

* **Service Architect:** Dariusz Korzun – *[<EMAIL>](mailto:<EMAIL>)*
* **Version Control:**

  * *v.1:* 13 Aug 2025 (Dariusz Korzun)
