---
weight: 22
title: "Elasticache for Redis"
date: 2025-08-13
tags: ["aws", "database"]
summary: "Amazon ElastiCache for Redis"
---

# Amazon ElastiCache for Redis

## Cloud Service Classification

**Category:** Database (In-Memory Cache)  
**Cloud Provider:** AWS
**Website**: [Technical documentation](https://docs.aws.amazon.com/elasticache/)

## Cloud Service Overview

**Name:** Amazon ElastiCache for Redis  
**Description:** Fully managed, in-memory Redis-compatible cache and data store. Delivers microsecond latency, supports Redis data structures, clustering, multi-AZ, encryption, and automated management. Ideal for caching, session stores, leaderboards, analytics, and real-time workloads.

## SKU Approval Status

 - PepsiCo Approved: 
   Provisioned clusters (current-gen nodes, single-node or replication group, clustering, multi-AZ)
 - Not Approved: 
   ElastiCache for Redis Serverless, outdated node families/engine versions

## Allowed PepsiCo Data Classification

 - Public
 - Internal
 - Confidential
 - Restricted (with required security controls)

## Service Lifecycle

 - Release Date: September 4, 2013
 - Planned Decommission: None

## Usage Guidelines

### Features

  - Redis-compatible, sub-ms latency, in-memory cache
  - Clustering (up to 15 shards), multi-AZ, automatic failover
  - Snapshots for backup/restore, S3 export
  - Encryption in transit (TLS) and at rest (KMS)
  - Redis AUTH, ACLs, IAM authentication
  - Fully managed: patching, scaling, monitoring, failover
  - Global Datastore for cross-region replication

### Sample Use Cases

  - Database query caching
  - Session stores
  - Content/page caching
  - Leaderboards
  - Pub/Sub messaging
  - Distributed locking, counters
  - Real-time analytics

### Limitations

  - In-memory, not durable by default (data loss possible on failure)
  - Asynchronous replication (possible data loss on failover)
  - Single-threaded command execution per shard
  - Node memory limits; cluster for large datasets
  - Restricted Redis commands; no custom modules
  - No active-active multi-region writes

### Additional Guardrails

  - TBD

### Used By

  - TBD

### EA Declaration

  - NOT a declared standard

## Technical Guidelines

### Best Practices

  - Choose node type/size for workload; use memory-optimized for large datasets
  - Use cluster mode for large/partitioned datasets; replicas for HA/read scaling
  - Enable automatic snapshots; test restores
  - Set TTLs/eviction policies to manage memory
  - Use persistent connections and connection pools
  - Monitor with CloudWatch; set alarms for memory, CPU, evictions, replication lag
  - Always deploy in private subnets, restrict security groups
  - Enable encryption in transit/at rest, use strong AUTH tokens, rotate regularly
  - Use Redis ACLs for least-privilege access

### High Availability & Disaster Recovery

  - Deploy with Multi-AZ and replicas for 99.99% SLA
  - Global Datastore for cross-region DR (manual promotion)
  - Regularly test failover and restore procedures
  - Use snapshots for backup/restore; copy to S3 for cross-region DR

### Backup & Recovery

  - Enable daily automatic snapshots; take manual snapshots before changes
  - Restore creates new cluster; update app endpoints as needed
  - Copy/export snapshots to S3 for DR/retention
  - Test restores regularly

### Access Control & Security Configuration

  - Deploy in VPC, private subnets; restrict access via security groups
  - Enable TLS for all connections; enforce at-rest encryption (KMS/CMK)
  - Use Redis AUTH and rotate tokens; enable IAM authentication if supported
  - Use Redis ACLs for user-level permissions
  - Enable CloudTrail for auditing API actions

### Network Connectivity Options

  - No public access; only private endpoints in VPC
  - Use VPC peering/Transit Gateway for cross-VPC access
  - On-premises access via VPN/Direct Connect
  - Use cluster/config endpoints for client connections

### Networking & Security Architecture

- TBD

### AWS Service Limitations

  - Max 300 nodes/account/region (soft limit)
  - Max 90 nodes/cluster (cluster mode)
  - Node memory limits; data tiering on R6gd nodes
  - No Redis modules; restricted commands
  - No PITR (point-in-time recovery); only snapshot-based
  - No automatic scaling (except serverless, not approved)
  - Global Datastore: up to 2 replica regions, manual promotion

### SKU Features

  - General Purpose (M5/M6g), Memory Optimized (R5/R6g), Compute/Network Optimized (C7g/C7gn)
  - Burstable (T3/T4g) for dev/test only
  - Data tiering (R6gd) for large/cold datasets
  - Multi-AZ, clustering, Global Datastore, encryption, ACLs

### Related Service

- TBD

### Alternatives

  - Amazon MemoryDB for Redis (durable, multi-AZ)
  - ElastiCache for Memcached (simple cache)
  - Self-managed Redis on EC2
  - DynamoDB DAX, CloudFront, other AWS caches

# Compliance and Security Guidelines

### Security Baseline InfoSec

  - Follow PepsiCo InfoSec baseline for ElastiCache for Redis

# Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 7 Apr 2025 (Kalyan Battiprolu)
