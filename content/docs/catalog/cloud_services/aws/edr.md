---
weight: 6
title: "EDR"
date: 2025-07-22
tags: [AWS Elastic Disaster Recovery Service, AWS EDR, AWS DRS, Disaster Recovery]
summary: ""
---

# AWS Elastic Disaster Recovery Service (AWS DRS)

## Cloud Service Classification

* **Category of the Service:** Disaster Recovery
* **Cloud Provider:** AWS
* **Website**: [Technical documentation](https://docs.aws.amazon.com/drs/latest/userguide/what-is-drs.html)

## Cloud Service Overview

* **Name:** AWS Elastic Disaster Recovery (AWS DRS)

* **Description:**  *AWS Elastic Disaster Recovery enables continuous replication to a **staging area**, non-disruptive testing, readiness monitoring, quick failover to AWS, and failback to the primary site.*

* **SKU Approval Status:**

  * **PepsiCo Approved:** AWS Elastic Disaster Recovery Service (all standard usage in supported PepsiCo approved regions)
  * **Not Approved:** *None (no separate SKU tiers for this service)*

* **Allowed PepsiCo Data Classification:** Public, Internal, Confidential, **Restricted** requiring encryption, network isolation, access controls, and private connectivity for secure replication and storage.

## Service Lifecycle

* **Release Date:** November 17, 2021 (General Availability). AWS DRS was introduced as the next-generation disaster recovery service, built upon technology from CloudEndure DR.
* **Planned Decommission Date:** No end-of-life or decommission date has been announced by AWS.
* **Decommission Date:** Not applicable at this time.

## Usage Guidelines

* **Features:**

  * **Continuous Block-Level Replication:** AWS DRS uses an agent to **securely replicate** disk volumes at the block level to Amazon EBS staging volumes, enabling near-real-time sync with seconds-level RPO.
  * **Point-In-Time Recovery:** AWS DRS regularly creates incremental **Recovery Point snapshots** of replicated volumes, retained up to 365 days per policy, enabling recovery to earlier states as needed.
  * **Automated Conversion & Orchestration:** AWS DRS automates converting source machines to AWS-native format, injecting drivers and configuring boot settings for EC2, while orchestrating multi-server launches with customizable settings.
  * **Non-Disruptive Testing:** AWS DRS enables **DR drills** by launching test instances without interrupting replication or impacting the source, allowing safe, regular disaster recovery testing.
  * **Post-Launch Automation:** AWS DRS offers **Post-Launch Actions** to run custom scripts or Systems Manager automations on recovered instances, automating post-failover configurations and validations.
  * **Network Configuration Replication:** AWS DRS replicates AWS network configurations—VPC components like subnets, security groups, and route tables—to the recovery region, ensuring consistent and secure DR environments.
  * **Flexible Recovery Options:** Users can launch Recovery instances as new AWS instances or recover into existing stopped EC2 instances to preserve IDs, ENIs, and metadata for compliance or standby use.
  * **Broad Platform Support:** AWS DRS supports replication of Windows and Linux servers (physical, VMware, Hyper-V, other clouds) plus enterprise apps, enabling cross-AZ and cross-Region AWS-to-AWS recovery.
  * **Scalability & Elasticity:** AWS DRS scales to protect thousands of servers using lightweight, scalable replication instances with pay-as-you-go pricing and no long-term commitments, minimizing costs until failover or drills.
  * **Failback Support:** AWS DRS enables integrated failback by replicating changes from AWS back to the primary environment, including a **Mass Failback Client** for VMware to streamline large-scale restores with minimal downtime.

* **Sample Use Cases:**

  * **On-Premises Datacenter Outage:** Use AWS DRS to continuously replicate critical on-premises apps to AWS for rapid recovery, ensuring business continuity with seconds-level RPO and minutes-level RTO.
  * **Ransomware or Cyberattack Recovery:** AWS DRS enables point-in-time recovery before ransomware or malware attacks, allowing clean restoration without ransom payment and supports ransomware drills for preparedness.
  * **Cross-Region Disaster Recovery for AWS Workloads:** AWS DRS increases cloud-native app resilience by replicating AWS resources across regions, enabling rapid failover during regional disruptions to meet high availability and compliance requirements.
  * **Cloud-to-Cloud Migration/DR:** AWS DRS enables continuous replication and rapid failover of workloads from other clouds (Azure, GCP) to AWS, supporting disaster recovery and seamless migration with minimal downtime.
  * **Regional Availability Zone Failure (Pilot Light Scenario):** AWS DRS enables recovery from an Availability Zone failure within the same region by launching instances in a pre-set alternate AZ, supporting pilot light disaster recovery strategies.
  * **Enterprise IT Resilience & Testing:** AWS DRS supports continuous testing by enabling on-demand, isolated copies of production systems for drills and updates without impacting production, improving recovery confidence and compliance.

* **Limitations:**

  * **Maximum Protected Servers per Account:** AWS DRS supports up to 300 active source servers per account per region, scalable to thousands using multiple accounts or regions, with some quotas adjustable via AWS support requests.
  * **Volume and Storage Limits:** Each source server can replicate up to 60 volumes (max 16 TiB each); larger setups require bigger replication instances.
  * **Operating System Support Constraints:** AWS DRS requires installing its agent on supported modern Windows and Linux OS (x86_64), excluding legacy *(end-of-life)*, 32-bit, or uncommon platforms, with deprecated support for Windows Server 2003 and specific filesystem limitations.
  * **Data Consistency Considerations:** AWS DRS captures block-level crash-consistent snapshots by default without application awareness, so achieving application consistency requires additional replication methods (as per PepsiCo approved standards).
  * **No Instant Automatic Failover:** AWS DRS enables fast recovery via manual or scripted failover but *does not support automatic or zero-downtime failover*, requiring environment setup and runbook execution for disaster response.
  * **Networking and Connectivity Requirements:** Continuous AWS DRS replication requires reliable, high-speed network connectivity; slow or unstable links increase replication lag and RPO, with recovery catching up once connectivity restores.
  * **Resource Usage During Recovery:** During recovery or drills, running AWS recovery instances incur standard EC2 and EBS charges, which should be budgeted; replication billing continues, and the original source isn’t updated **unless** AWS instances are reconfigured as sources.
  * **Snapshot Retention Risks:** AWS DRS’s point-in-time recovery depends on retained snapshots—if deleted or purged, earlier recovery points are lost. Hence, configure it as per **Pepsico retention policy based on data classification**.

* **Used By:** *TBD*
  *(List of PepsiCo projects or teams currently utilizing AWS Elastic Disaster Recovery, if any, along with references or links to their implementation summaries.)*

* **EA Declaration:** NOT a declared standard.
  *(This service is currently not formally declared as a PepsiCo Enterprise Architecture standard. It may be used on a case-by-case basis, but it is not yet broadly adopted as a standard pattern for disaster recovery. Architecture review and approval are required for each implementation.)*

## Technical Guidelines

* **Best Practices:**

  * **Establish a Detailed DR Plan:** Prepare and regularly update a detailed Disaster Recovery Runbook outlining roles, failover steps, system startup order, user rerouting, and post-failback data reconciliation.
  * **Perform Regular DR Drills:** Regularly conduct non-disruptive AWS DRS disaster recovery drills to test failover and failback, document issues, refine processes, and clean up test resources to control costs.
  * **Right-Size Launch Settings & Resource Mapping:** Preconfigure AWS DRS Launch Settings for each source server—selecting appropriate instance types, subnets, security groups, and IAM roles—to ensure smooth, coordinated failover without last-minute adjustments.
  * **Leverage Infrastructure as Code for Networks:** After a successful drill, export AWS DR infrastructure to CloudFormation or Terraform code for consistent, cost-effective, and rapid environment provisioning.
  * **Monitor Replication Health Continuously:** Monitor AWS DRS health via the console and CloudWatch metrics, setting alerts for replication lag or stalled servers, and automate notifications or remediation to ensure up-to-date recovery readiness.
  * **Optimize Network Throughput & Costs:** AWS DRS compresses and deduplicates data by default to reduce bandwidth; use Direct Connect or VPN for large transfers, configure **bandwidth throttling** schedules, and optimize costs with gp3 EBS volumes and snapshot lifecycle policies.
  * **Secure and Streamline Agent Installation:** Restrict AWS DRS agent installation by using a dedicated IAM role with scoped permissions, automate deployment via build processes or Systems Manager, and verify replication health in the DRS console.
  * **Plan for DNS and Endpoint Failover:** Define and test in the recovery plan on user traffic and integrations switch to AWS during failover — using Route 53 DNS failover, load balancers, or VPNs—and automate these steps within runbooks.
  * **Enable Termination Protection in Actual Failovers:** Enable termination protection on recovery EC2 instances during failover to prevent accidental shutdowns, documenting this step in failover procedures.
  * **Avoid Premature Cleanup/Disconnect:** In disaster recovery, keep source servers connected in AWS DRS until no earlier snapshots are needed, as disconnecting deletes all replication resources and snapshots.
  * **Re-Protect After Migrating or Failover:** For production workloads on AWS, set up DR protection for those instances—reinstall the agent or use cross-region replication—to avoid a single point of failure by chaining DR sites across regions.

* **High Availability, Backup & Disaster Recovery:**

  * **Cross-Region Resilience:** To protect against region outages, configure AWS DRS to replicate sources to a secondary AWS region or chain failover regions, or at least maintain off-site backups with cross-region copies for critical systems.  
  * **Failover and Failback Workflow Resiliency:** Ensure failover support services like Route 53, VPNs, and Direct Connect are highly available and redundant to eliminate single points of failure in the disaster recovery path.
  * **Recovery Site Preparedness:** Maintain pilot light resources and updated AMIs in the DR region, ensure sufficient service quotas and EC2 capacity for full failover, and keep the AWS replication agent AMI ready to minimize downtime during failover and failback.
  * **Retention of Recovery Points:** Set AWS DRS **Point-In-Time snapshot retention** (default 7 days, up to 365) to balance recovery needs and costs, ensuring compliance and protection against unnoticed logical errors.
  * **Integration with AWS Backup and AWS Storage Services:** Use AWS Backup for long-term data retention beyond AWS DRS’s short-term recovery, combining DRS for fast recovery with periodic full backups and for databases, use native/Rubrik with S3 for backup and archival needs.
  * **Automated Recovery Testing:** Regularly test full restorations from backups—like spinning up EC2 instances or restoring EBS snapshots in isolated environments—to ensure backup viability alongside AWS DRS drills.
  * **Failback Planning and Data Re-Synchronization:** Stop application/database instances to ensure full sync, reverse replication to on-premises/source, validate restored servers, backup AWS instances beforehand, and use AWS Failback Client for bulk VMware restores to minimize data loss.
  * **Cleanup After Recovery:** After a DR event or drill, clean up AWS by terminating recovery instances, disconnecting and deleting unused DRS sources and replication volumes, retaining necessary logs, and revoking temporary access to restore normal operations and control costs.

* **Access Control & Security Configuration:**

  * **Identity and Access Management (IAM):** Enforce least-privilege IAM roles with MFA for AWS DRS agents and users, limiting permissions to necessary API actions and restricting recovery or replication control to authorized administrators only.
  * **Agent Installation Permissions:** Restrict AWS DRS agent installation by assigning the managed installation policy to a dedicated role, monitor deployments via CloudTrail, and alert on any unauthorized installation attempts.
  * **Network Security (Replication Traffic):** Configure replication server security groups to allow inbound TCP traffic only from the source networks or VPN endpoints, restricting access by IP whether using public or private IP modes.
  * **Encryption and Data Protection:** AWS DRS encrypts data in transit with TLS by default; enable EBS encryption for staging volumes and snapshots using customer-managed CMKs for sensitive data, ensuring encryption settings are configured before replication begins.
  * **Post-Launch Security Hardening:** After AWS DRS recovery, apply security patches, disable unwanted services, enforce firewall rules, and automate hardening or vulnerability scans via post-launch actions to ensure recovered instances are secure.
  * **Logging and Monitoring:** Enable CloudTrail to audit AWS DRS actions, use CloudWatch Logs and EventBridge for monitoring and alerts, and secure logs with tamper-evident storage to ensure compliance and prompt incident response.
  * **Isolation of DR Environment:** Deploy the DR staging area in a separate VPC or account isolated from other workloads, restrict access to DR admins, and enforce security best practices using Security Hub for robust protection. *(AWS Config is not used as per PepsiCo Foundations Standards).*
  * **DDoS and Network Protection:** Protect public replication endpoints and failover resources with AWS Shield (seek Infosec approval if necessary) and AWS WAF to guard against DDoS attacks during critical recovery periods.

* **Network Connectivity Options:**

  * **Public Internet (Default):** For **Confidential or Restricted data**, AWS DRS **must not use** public internet replication without encryption and prior approval; always restrict inbound access to known source IPs and prefer private connectivity like VPN or Direct Connect to secure ongoing replication.
  * **VPN or Direct Connect (Private Connectivity):** **Preferred method for on-premises to AWS replication.** AWS DRS supports **private IP replication** over Site-to-Site VPN or Direct Connect, ensuring data traffic stays within secure tunnels and allowing replication servers to reside in private subnets, which is required for Restricted data and enhances performance and security.
  * **Cross Region Replication with AWS Cloud WAN:** Enables secure, private, and simplified cross-region AWS DRS replication over AWS’s global backbone without manual peering, while still incurring standard cross-region data transfer costs.
  * **AWS PrivateLink (Interface Endpoints):** *Not applicable.* AWS DRS currently does not offer a PrivateLink endpoint service for the DRS API or replication data plane.
  * **Networking Security Architecture:** The DR environment should replicate production network segmentation. No unnecessary internet exposure, and **pre-created VPC endpoints** for essential services to ensure secure, monitored failover operations.
  *(Further detailed networking and security architecture for the DR solution is organization-specific and is marked TBD.)*

* **Networking & Security Architecture:** *TBD*
  *(This section will include diagrams or descriptions of the specific networking architecture patterns recommended for AWS DRS at PepsiCo – e.g., how the DR VPC and AWS Cloud WAN is set up, security zones, etc. To be developed in collaboration with the network and security teams.)*

* **AWS Service Limitations:**

  * **No Native Multi-Region Failover Automation:** Requires manual recovery initiation in a preconfigured secondary region for cross-region failover, with no automatic or one-click failover; thorough planning and testing are essential to update endpoints and DNS (Route 53) records accordingly.
  * **Consistency vs. Continuity Trade-off:** Provides crash-consistent replication but does not guarantee transactional consistency across distributed applications; to mitigate, implement application-level coordination or quiescing as this requires manual effort beyond DRS’s capabilities.
  * **Lack of Built-in Application Awareness:** While DRS can bring up infrastructure, it doesn’t natively reconfigure applications.
  * **Scaling During Recovery Not Automatic:** AWS DRS launches a fixed number of instances as configured and does not auto-scale recovered environments; to handle fluctuating loads, users must manually adjust instance counts or integrate with auto-scaling post-recovery.
  * **Costs for Drills and Extended Outages:** AWS DRS has no built-in cost caps—monitor costs carefully, especially during large drills, by tagging launched instances and tracking spend with cost management tools to avoid unexpected expenses.
  * **Limited CloudFormation Support:** AWS DRS configurations must be managed manually via console, CLI, or API. No native CloudFormation support. Use scripting with DRS APIs for automation if needed.
  * **AWS Outposts Support:** AWS DRS supports AWS Outposts as sources or recovery targets **(since 2023)**, with recovery into Outposts limited to the Rack form factor; DR targets for Outposts sources are still AWS regions, making it suitable for hybrid cloud DR scenarios.
  * **Agent Overhead:** The AWS DRS replication agent uses minimal CPU and memory. But, test its impact on latency-sensitive or legacy systems—especially Windows and verifying in staging environments.

* **SKU Features:**
  *(Single-tier service with all features included by default, using a pay-as-you-go pricing model without SKU distinctions..)*

  * **Single Service, Pay-as-You-Go:** Offers a single edition with full features and charges hourly per replicating source server plus staging and recovery AWS resource costs, with no upfront fees or pricing tiers.
  * **Staging Resources (Always On):** Continuously incurs costs for a small replication EC2 instance (t3.small by default) and EBS volumes matching source disk sizes, offering cost savings over full servers but requiring scale-based budgeting.
  * **Recovery Resources (On Demand):** Recovery instance launch incurs standard EC2 and EBS charges only during tests or DR events, with no extra DRS fees; costs revert to staging resource baseline after termination.
  * **Included Features:** Includes all features like point-in-time snapshots and post-launch actions by default, with no premium fees; only extended snapshot retention may increase indirect storage costs.
  * **Support Plan Consideration:** Enterprise Support plan for production use of DRS to ensure 24/7 assistance, which is a separate cost not included in DRS pricing.
  * **No License Fees:** AWS DRS agents are free with no separate license fees.

* **Related Service:** *TBD*
  * EC2 and EBS: Replication Instance
  * Cloud WAN, VPC
  * CloudTrail, CloudWatch
  
  *(This section will list services related to AWS DRS. Potential related services include AWS Application Migration Service (MGN) – which is a similar service focused on migrations rather than ongoing DR – and AWS Backup – which complements DRS for data backup. Also CloudEndure (preceding technology) could be mentioned. To be filled out with context of how they relate in PepsiCo environment.)*

* **Alternatives:**

  * **Traditional Backup & Restore:** Periodic backups using Rubrik, AWS Backup or Storage Gateway offer lower-cost disaster recovery with higher RTO and RPO, suitable for non-critical systems.
  * **Pilot Light Architecture:** Maintains minimal critical infrastructure (like replicated databases) ready to scale and deploy full apps via automation during disasters; AWS DRS automates much of this data replication and recovery, but pilot light can rely on native DB replication and IaC for quicker, manual failover.
  * **Warm Standby:** Runs a scaled-down, continuously replicated secondary environment serving light workloads, enabling low RTO and RPO with higher costs due to always-on resources, supported by services like Aurora Global Database and DynamoDB Global Tables.
  * **Multi-Site Active/Active:** Runs applications concurrently across regions for near-zero RTO/RPO using multi-master data replication and traffic routing, offering seamless failover at higher complexity and cost.

## Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * *Information Security Specifications – AWS Elastic Disaster Recovery:* The **PepsiCo InfoSec baseline for AWS Elastic Disaster Recovery** mandates strict compliance with data encryption (in transit and at rest), robust access controls, network isolation, continuous security testing, and adherence to regulations like GDPR to ensure secure handling of sensitive data during disaster recovery.
  * **Shared Responsibility Model:** AWS manages security of the DRS cloud platform, while PepsiCo is responsible for secure and compliant use of DRS—configuring access, data locations, and controls per InfoSec guidelines under the shared responsibility model.

## Ownership and Version Control

* **Service Architect:** Ramakrishna Ramaraju – Cloud Architecture, PepsiCo ([<EMAIL>](mailto:<EMAIL>))
* **Version Control:**

  * v1.0 – 04 Aug 2025: *Initial draft prepared for AWS Elastic Disaster Recovery Service catalog entry (Ramakrishna Ramaraju).*
