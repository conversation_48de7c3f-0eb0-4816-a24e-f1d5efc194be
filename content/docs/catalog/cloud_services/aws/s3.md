---
weight: 4
title: "S3"
date: 2025-07-22
tags: []
summary: ""
---

# **Amazon S3 (Simple Storage Service)**
## **Cloud Service Classification**
* **Category:** Storage  
* **Provider:** Amazon Web Services (AWS)  
## **Cloud Service Overview**
* **Name:** Amazon Simple Storage Service (Amazon S3)  
* **Description:** Amazon S3 is a **scalable object storage service** for storing and retrieving any amount of data from anywhere on the web. It ensures **industry-leading durability** (99.999999999%) and **high availability** (99.99% by default) via redundancy across multiple facilities. S3 offers **unlimited capacity**, high performance, and **granular security controls** for a wide range of use cases, including data lakes, analytics, application storage, and backups. Organizations benefit from **cost-effective storage classes** and comprehensive features for data organization and access control.  

* **SKU Approval Status:**

  * **PepsiCo Approved:** *TBD*
  * **Not Approved:** *TBD*
  * *S3 Standard* – TBD
  * *S3 Intelligent-Tiering* – TBD
  * *S3 Standard-IA* – TBD
  * *S3 One Zone-IA* – TBD
  * *S3 Glacier Instant Retrieval* – TBD
  * *S3 Glacier Flexible Retrieval* – TBD
  * *S3 Glacier Deep Archive* – TBD
  * *S3 Express One Zone* – TBD
  * *S3 on Outposts* – TBD

* **Allowed PepsiCo Data Classification:**

  * Public
  * Internal
  * Confidential
  * Restricted

## **Service Lifecycle**
* **Release Date:** March 14, 2006  
* **Planned Decommission:** None announced  
## **Usage Guidelines**
* **Features:**
  * **Durable & Scalable Storage:** Supports massive scalability by redundantly storing objects across three Availability Zones (Standard class).  
  * **Flexible Storage Classes:** Includes Standard, IA classes, Intelligent-Tiering, and archival options (e.g., Glacier) for tailored cost-efficiency.  
  * **Security and Access Management:** Robust security with **default server-side encryption (AES-256)** and support for AWS Key Management Service (SSE-KMS). Access is tightly controlled via policies, and features like **S3 Block Public Access** prevent accidental exposure. Auditing is supported via CloudTrail and access logs.  
  * **Data Management Features:** Features include **Versioning**, **Object Lock (WORM)**, **tagging** for lifecycle management, **Cross-Region Replication**, and efficient multipart uploads for large files.  
  * **Event-Driven Integrations:** Integrates with AWS Lambda, SNS, SQS, and supports **S3 Object Lambda** for on-the-fly data processing. Static website hosting and **CloudFront CDN** enhance performance and delivery.  
* **Sample Use Cases:**  
  * **Data Lakes & Analytics:** Use S3 as a central repository for raw and processed data at scale, integrating with analytics tools like Amazon Athena.  
  * **Backup & Disaster Recovery:** Ideal for nightly database backups or long-term archives using cheaper Glacier tiers. CRR ensures resiliency.  
  * **Archiving and Compliance Storage:** Transition old or infrequent data to Glacier tiers for significantly reduced costs. Use Object Lock to meet regulatory requirements.  
  * **Application File Storage & Content Distribution:** Store user uploads, media assets, or exported data; deliver global content efficiently with CloudFront CDN.  
* **Limitations:**  
  * **Object Size Limits:** Up to 5 TB per object; multipart upload required for files > 5 GB.  
  * **Cross-Region Consistency:** CRR is asynchronous; unreplicated data may be lost in regional failure scenarios.  
  * **Minimum Duration Fees:** Storage classes like Glacier impose minimum age charges (90-180 days) and retrieval fees.  
  * **Throughput Throttling at Extreme Scale:** Loads on single prefixes may trigger temporary 503-level throttling; distribute traffic wisely.  
  * **Operations Atomicity:** S3 handles updates atomically per object but doesn’t support multi-object transactions or inbuilt querying beyond prefix/tags.  
* **Additional Guardrails:** TBD  
* **Used By:** TBD  
* **EA Declaration:** Not a declared standard  
---
## **Technical Guidelines**
* **Best Practices:**
  * **Secure Configuration:** Default public access should remain blocked; manage access via policies over ACLs for precise control.  
  * **Encryption & Data Protection:** Ensure HTTPS-only connections and for sensitive or Confidential/Restricted data, use **Server-Side Encryption with AWS KMS** (SSE-KMS) so that you have control over the encryption keys and can set up key rotation policies. SSE-KMS also allows enabling of features like separate permissions for decrypting data. If applicable, enable **Dual-layer encryption (DSSE-KMS)**.  
  * **Versioning and Object Lifecycle:** Enable **Versioning** for critical data and automate transitions to cheaper tiers using lifecycle policies.  
  * **Monitoring and Auditing:** Activate **CloudTrail Data Events**, **CloudWatch Alarms**, and **access logging** for tracking and compliance.  
  * **Cost Optimization:** Use Intelligent-Tiering for unpredictable access patterns and compress data before uploads.  
  * **Performance Optimization:** Distribute workload across key prefixes; use CDN or caching for rapid small-object access.  
* **High Availability & Disaster Recovery:**  
  * **Regional Redundancy:** Standard redundancy across AZs ensures availability within a region.  
  * **Cross-Region Replication:** Asynchronously replicate backups to alternate regions for disaster recovery.  
  * **Multi-Region Access Points:** Simplify global access and failover; pair with CRR for seamless operations.  
  * **Backup and External Recovery:** Periodically back up S3 data using AWS Backup or other tools to protect against regional failures, accidental deletions, malicious actions, and compromised account scenarios. Define **Recovery Time Objectives (RTO)** and **Recovery Point Objectives (RPO)** to decide between standard **Cross-Region Replication (CRR)** or additional mechanisms like synchronous replication. Routinely test your disaster recovery (DR) plan by simulating failover or verifying backup integrity.  
* **Backup & Recovery:**  
  * **Versioning and Restoration:** Recover older object versions from delete markers or corrupted files. Enable MFA Delete for added protection.  
  * **S3 Object Lock:** Set WORM policies on buckets to avoid accidental deletions or comply with regulatory demands.  
  * **AWS Backup Integration:** Use AWS Backup to efficiently manage S3 backups at scale. It supports immutable, **point-in-time** snapshots (requires versioning) stored in a centralized backup vault. Configure continuous or scheduled periodic backups, with cross-account and cross-region copies to ensure recoverability during account compromise or regional failures. AWS Backup enables restoring entire buckets or specific objects to a point in time, making it ideal for critical data protection (e.g., databases or essential documents).  
  * **Retention:** Automate lifecycle policies and periodically test restore procedures for reliability.  
* **Access Control & Security:**  
  * **Identity & Access Management (IAM):** Use IAM roles and policies instead of hard-coding credentials for S3 access. For EC2 or Lambda, assign an IAM Role with necessary permissions rather than AWS access keys to leverage temporary credentials. Apply least privilege by creating fine-grained policies (e.g., s3:GetObject for specific buckets or prefixes). Regularly audit roles/users for overly broad permissions, such as wildcards, and refine as needed.  
  * **Bucket Policies and ACLs:** Rely on Bucket Policies, restricting access by VPC or IP conditions instead of ACLs.  
  * **Public Access Block:** Enforce organization-wide Block Public Access; expose limited content via CloudFront if required.  
  * **Encryption Standards:** Leverage **SSE-KMS** for sensitive data, enforce TLS connections, and monitor KMS throttling limits.  
  * **Private Connectivity:** Enable **VPC Gateway Endpoints** for secure S3 access across AWS backbone; use Interface Endpoints only for specific needs.  
* **Network Connectivity Options:**  
  * **Public Internet:** Only allowed for **Public-classified data**, subject to approval and rigorous security reviews.  
  * **VPC Gateway Endpoints (Preferred):** Route S3 traffic privately for Internal, Confidential, or Restricted data without Internet exposure.  
  * **Interface Endpoints:** Use for cross-region workflows or fine-grained API-specific restrictions.  
  * **Access Points:** Simplify bucket sharing or multi-tenant scenarios with granular controls per consumer.  
* **Service Limitations:**
  * **Bucket Quotas:** Each AWS account supports up to **10,000 buckets** by default. Request quota increases as needed.  
  * **Object & Request Limits:** Max object size: 5 TB; throughput scales dynamically with distributed prefixes. S3 operations maintain strong consistency.  
  * **Feature Constraints:** Certain advanced features (e.g., Object Lock) require configuration at bucket creation. Multipart uploads capped at 10,000 parts.  
* **SKU Features & Use Cases:**
  * **Standard Class:** High durability and availability for frequently accessed data.  
  * **Intelligent-Tiering:** Automatically optimizes costs for unpredictable access patterns.  
  * **Express One Zone:** Single-AZ storage for ultra-low latency workloads.  
  * **IA Classes & Glacier:** Tailored for infrequent access or archives based on SLA and retrieval timing.  
  * **Outposts:** Local S3 storage for compliant, low-latency environments.  
---
## **Compliance and Security Guidelines**  
* Refer to InfoSec baseline for mandatory controls such as encryption, approvals, and monitoring.
---
## **Ownership and Version Control**  
* **Service Architect:** Dariusz Korzun (**<EMAIL>**)  
* **Version Control:** v.1: 22 Jul 2025 (Dariusz Korzun)
