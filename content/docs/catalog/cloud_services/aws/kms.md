---
weight: 8
title: "KMS"
date: 2025-07-22
tags: []
summary: ""
---

# AWS Key Management Service (AWS KMS)

## Cloud Service Classification

* **Category of the Service:** Security
* **Cloud Provider:** AWS

## Cloud Service Overview

* **Name:** AWS Key Management Service (AWS KMS)

* **Description:**
  AWS Key Management Service (KMS) is a managed service providing centralized control and HSM-backed security for cryptographic keys to encrypt data across AWS services and applications.

* **SKU Approval Status:**

  * **PepsiCo Approved:**

    * AWS Key Management Service (AWS KMS)

  * **Not Approved:**

    * AWS CloudHSM
    * AWS Payment Cryptography

* **Allowed PepsiCo Data Classification:**
  Public; Internal; Confidential; Restricted

## Service Lifecycle

* **Release Date:** November 12, 2014
* **Planned Decommission Date:** No announced decommissioning plans.
* **Decommission Date:** Not applicable at this time.

## Usage Guidelines

* **Features:**

  * **Centralized Key Management** – AWS KMS manages cryptographic keys, including creation, rotation, access control, and secure storage in HSMs, supporting symmetric and asymmetric keys for encryption and signing.
  * **Integration with AWS Services** – AWS KMS integrates with services like S3, EBS, and RDS, enabling data encryption using KMS-managed keys with envelope encryption for efficient and secure key management.
  * **Secure HSM‑Backed Storage** – AWS KMS secures keys with FIPS 140-3 Level 3 HSMs, ensuring key material never leaves the HSMs in plaintext and cryptographic operations remain fully protected.
  * **Logging and Auditing** – AWS KMS records all key usage in CloudTrail, enabling tracking, detection of unauthorized access, and compliance reporting with detailed logs of encryption and decryption operations.
  * **Automatic Key Rotation** – AWS KMS supports automatic annual rotation for symmetric customer-managed keys and AWS-managed keys, while asymmetric, HMAC, and imported keys require manual rotation to meet compliance and limit data exposure.

* **Sample Use Cases:**

  * **Encrypting Data at Rest in AWS** – Use KMS customer-managed keys to encrypt AWS service data, ensuring sensitive or regulated data is accessible only to your organization's principals.
  * **Application‑Level Encryption (Envelope Encryption)** – Use KMS to generate and manage encrypted data keys, enabling applications to locally encrypt/decrypt sensitive data while offloading key management to KMS for security and efficiency.
  * **Digital Signing and Verification** – KMS generates asymmetric key pairs for secure digital signing, keeping private keys in HSMs and allowing applications to use APIs for authorized signing and verification.
  * **Bring Your Own Key (BYOK)** – Import key material from on-prem HSMs or external systems into AWS KMS to enable cloud encryption while maintaining an external root of trust for compliance.

* **Limitations:**

  * **Data Size Limit for Encryption** – AWS KMS directly encrypts payloads up to 4 KB; use envelope encryption with data keys for efficient handling of large or high-volume data.
  * **Throttling and API Limits** – KMS enforces per-account/Region request quotas, with limits on encrypt/decrypt operations and keys, requiring backoff, batching, or caching to avoid throttling.
  * **Key Material Export Restriction** – Customer-managed KMS keys cannot be exported, ensuring security, but deleted keys are irretrievably lost, so use aliases or rotation instead of immediate deletion for important encrypted data.
  * **Service Scope** – KMS is regional service, with keys tied to specific regions unless using multi-Region keys, and it doesn't support specialized cryptography like payment processing, which requires AWS CloudHSM or Payment Cryptography services.
  * **Not a General Secrets Store** – KMS manages cryptographic keys, not user secrets or certificates; use AWS Secrets Manager or Parameter Store for secrets and AWS Certificate Manager for certificates.

* **Additional Guardrails:** TBD

* **Used By:** TBD (list & links to some onboardings)

* **EA Declaration:** AWS KMS is a declared standard service for cryptographic key management in the enterprise.

## Technical Guidelines

* **Best Practices:**

  * **Use Customer‑Managed Keys for Sensitive Data** – Create customer-managed KMS keys for sensitive data to control policies, rotation, and lifecycle, and segment keys by application or domain to enforce least privilege.
  * **Least Privilege Access Control** – Enforce strict KMS key and IAM policies, separating admin roles from usage roles, avoiding wildcards, and requiring MFA for sensitive operations.
  * **Enable CloudTrail and Monitoring** – Enable CloudTrail to log KMS API calls, set alerts for unusual activity, and review usage logs and AWS Config rules to detect unauthorized key actions.
  * **Key Rotation and Lifecycle Management** – Enable automatic rotation for symmetric keys or manually rotate unsupported keys, using aliases to simplify updates, and carefully re-encrypt data before scheduling key deletions.
  * **Multi‑Account and Multi‑Region Strategy** – Choose centralized or decentralized key management based on needs, carefully configure cross-account key access, and use multi-Region keys for disaster recovery, testing decryption in backup regions.
  * **Use VPC Endpoints for KMS** – Access KMS via PrivateLink to keep API calls within AWS’s network, enhance security, and attach endpoint policies for additional access control

* **High Availability & Disaster Recovery:**

  * **Regional Redundancy** – KMS ensures 11 nines durability and high availability within a region using redundant HSMs across multiple AZs, backed by a regional API endpoint and SLA.
  * **Multi‑Region Strategy for DR** – Use KMS multi-Region keys to create interoperable replicas for disaster recovery, enabling failover to another region, though your DR plan must manage switching regions manually.
  * **Key State and Backup Considerations** – KMS keys cannot be exported, so rely on its durability, and for imported key material, retain secure backups; use Infrastructure-as-Code to recreate key configurations if needed.

* **Backup & Recovery:**

  * **Scheduling Deletion (Soft Delete)** – KMS keys use scheduled deletion with a 7-30 day waiting period; set the maximum 30-day window for critical keys as a grace period to cancel accidental deletions. Regularly audit deletion schedules via CloudTrail to prevent unintended key removal.
  * **Recovery from Accidental Key Deletion** –  Deletion can be canceled during the waiting period, but deleted keys are irrecoverable; enforce strict approval processes and consider multi-layer encryption for vital data in highly regulated cases.
  * **Alternate Key Recovery Strategies** – Prevent key loss by using external key managers or CloudHSM alongside KMS, introducing key rotation to avoid reliance on a single key for critical data.

* **Access Control & Security Configuration:**

  * **Key Policies and IAM Integration** – Use KMS key policies with IAM to enforce access control, limiting administrative actions to security groups and applying least privilege for encryption/decryption roles. Regularly review policies for unnecessary wildcards or broad grants, as key policies override IAM permissions.
  * **Separation of Duties** – Separate roles for managing keys and using keys to reduce risks of unauthorized deletion or alteration, and use SCPs to restrict sensitive actions outside break-glass accounts.
  * **Encryption Context and Additional Controls** – Utilize encryption context metadata to bind ciphertexts to specific contexts, enhancing security by preventing misuse; custom applications should adopt this feature via the AWS SDK.
  * **Logging and Alerting** – Forward CloudTrail logs to a SIEM to monitor sensitive KMS events, automate responses for critical changes, and use AWS Config rules to enforce key rotation and policy compliance.
  * **Compliance and Key Management** – Use customer-managed keys for compliance-critical data, document key-to-data mappings, and leverage AWS certifications (via Artifact) to ensure KMS meets regulatory standards like GDPR, PCI, and FedRAMP.

## Network Connectivity Options

* **Public Endpoint (AWS KMS Regional API)** – Direct internet access to KMS public endpoints is disallowed for sensitive data; if required for testing, tightly control egress with allow-listed IPs or filtered NAT gateways.
* **VPC Interface Endpoint (AWS PrivateLink)** – Use KMS VPC endpoints to ensure private access for sensitive workloads, enabling calls via AWS backbone and restricting usage to organization-specific policies.
* **Cross‑Region Access** – Use KMS multi-Region keys for encryption across regions instead of cross-region API calls; if required, use private AWS backbones to avoid internet exposure.
* **On‑Premises Access** – Route on-prem or third-party access to KMS through VPN, Direct Connect, or AWS proxies, disallowing direct internet calls and closely monitoring traffic through controlled channels.

## Networking & Security Architecture

TBD

## AWS Service Limitations

* **No Key Export or External Use** – KMS keys cannot be extracted for external use; all encryption/decryption must occur via KMS APIs, with external integrations using data keys or considering CloudHSM for offline key needs.
* **Envelope Encryption for Large Data** – KMS supports direct encryption only for payloads up to 4 KB, requiring envelope encryption for larger data, which is best managed using AWS guidelines or the Encryption SDK.
* **Latency and Throughput** – KMS is not designed for ultra-low latency, high-frequency encryption; reduce calls with caching or batching, and use HSMs for high TPS encryption needs.
* **Integration Gaps** – KMS focuses on key management, not bulk cryptographic tasks; use complementary services like AWS Secrets Manager, CloudHSM, or Payment Cryptography for specialized needs.

## SKU Features

* **AWS Key Management Service (AWS KMS)** – AWS KMS is a managed key management service with FIPS 140-3 Level 3 HSM-backed security, supporting symmetric keys, asymmetric key pairs, and HMAC keys. It integrates with over 100 AWS services for encryption, logs all usage to CloudTrail, and scales automatically; costs include $1 per key per month and $0.03 per 10,000 API calls. Default limits like keys per account or request rates can be increased, making it ideal for secure and auditable encryption across AWS.
* **AWS CloudHSM (Not Approved)** – AWS CloudHSM provides dedicated single-tenant HSM clusters in your VPC for exclusive control of hardware-level encryption keys. It supports PKCS#11, JCE, and OpenSSL interfaces, allowing custom cryptographic operations and key export for on-prem or proprietary use cases, but lacks native AWS integrations and requires customer-managed clustering and backups. Due to higher costs, complexity, and regulatory requirements, it is not approved in our environment, where AWS KMS remains the standard solution.
* **AWS Payment Cryptography (Not Approved)** – AWS Payment Cryptography is a managed service for payment card industry workflows, offering PCI-compliant cryptographic operations like PIN block translation, CVV generation, and EMV validation. It uses PCI-certified HSM hardware and separate APIs for key management and cryptographic functions, providing elastic scalability and low latency for payment processors and banks. This niche service is not approved in our organization due to its specialized scope and unique compliance requirements.

## Related Service

TBD

## Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * Information Security Specifications – AWS Key Management Service (AWS KMS)

## Ownership and Version Control

* **Service Architect:** Dariusz Korzun ([<EMAIL>](mailto:<EMAIL>))
* **Version Control:** v.1: 22 Jul 2025 (Dariusz Korzun)
