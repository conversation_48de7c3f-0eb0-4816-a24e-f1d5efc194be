---
weight: 23
title: "SageMaker"
date: 2025-08-13
tags: []
summary: ""
---

## Cloud Service Classification

* **Category of the Service:** Artificial Intelligence / Machine Learning
* **Cloud Provider:** Amazon AWS

## Cloud Service Overview

* **Name:** Amazon SageMaker
* **Description:** Amazon SageMaker is a fully managed machine learning platform that enables data scientists and developers to **build, train, and deploy** ML models at scale. It provides an integrated environment (SageMaker Studio IDE and managed Jupyter notebook instances) for the entire ML workflow, offering managed notebooks, distributed training with built-in algorithms or custom containers, automated hyperparameter tuning, and one-click model deployment to secure, scalable endpoints.

### SKU Approval Status:
  - PepsiCo Approved
    - AWS Secrets Manager (standard service offering)  
  - Not Approved:
    - None (AWS Secrets Manager has no tiered SKUs; all features are available by default)  
### Allowed PepsiCo Data Classification:
  - Public  
  - Internal  
  - Confidential  
  - Restricted  

### Service Lifecycle  
- **Release Date:** August 22, 2025
- **Planned Decommission Date:** No announced decommissioning plans.  
- **Decommission Date:** Not applicable at this time.  

## Usage Guidelines

### Features:

  * **Integrated Notebook Environments:** Fully-managed Jupyter environments for ML development, including SageMaker Studio (web-based IDE) and Notebook Instances (managed EC2 instances). Both come pre-configured with popular ML frameworks (TensorFlow, PyTorch, SciKit-Learn) to simplify setup.
  * **Automated Model Development:** AutoML and built-in algorithms streamline model building. **SageMaker Autopilot** explores algorithms and pipelines to train models, while built-in algorithms (e.g., XGBoost, linear regression) and hyperparameter tuning optimize performance.
  * **Deployment & Inference Options:** Simplifies deploying models to production with one-click deployment to **real-time inference endpoints** or **Batch Transform** for offline predictions. **Serverless Inference** scales down to zero for cost efficiency. Auto-scaling and GPU support are included.
  * **MLOps & Lifecycle Management:** Integrated tools like **SageMaker Pipelines** for CI/CD, **Model Registry** for versioning, and **Model Monitor** for tracking data drift ensure governance and continuous improvement.
  * **Supporting Tools & Ecosystem:** Tools like **SageMaker Clarify** (bias detection), **Debugger** (training insights), **Data Wrangler** (data preparation), and integrations with pre-trained models (e.g., HuggingFace) make SageMaker a comprehensive ML platform.

### Sample Use Cases:

  * **Predictive Analytics and Recommendation Systems:** Build models for predicting outcomes like product demand or customer churn. SageMaker supports real-time APIs for personalized recommendations or predictive maintenance.
  * **Computer Vision (Image/Video Analysis):** Train deep learning models for tasks like image classification or object detection. Examples include automated quality control or video surveillance analytics.
  * **Natural Language Processing and Text Analytics:** Develop NLP models for tasks like sentiment analysis or chatbot intelligence. SageMaker supports fine-tuning large language models and analyzing text-heavy datasets.
  * **Automated Model Retraining & MLOps:** Automate workflows for regular model retraining using **SageMaker Pipelines**. Common in industries like finance or retail, where data updates frequently.

### Limitations:

  * **Service Quotas:** SageMaker imposes default resource limits (e.g., 8 notebook instances, 4 training/hosting instances per region). These can be increased upon request but may restrict large-scale usage initially.
  * **Single-Region Deployments:** SageMaker lacks native cross-region failover. Multi-region resiliency requires manual deployment and failover mechanisms.
  * **Network Isolation and Internet Access:** Default configurations may access AWS services over the internet. Proper setup (e.g., VPC integration, PrivateLink) is required for secure, compliant usage.
  * **Complexity and Learning Curve:** SageMaker’s comprehensive features can be challenging for new teams. Small projects may find it overly complex.
  * **AWS Lock-In and Portability:** Solutions are tightly coupled with AWS services, making migration to other platforms challenging. Use open standards to mitigate lock-in.
  * **Cost Management Considerations:** Idle resources (e.g., notebook instances, endpoints) incur ongoing charges. Governance is needed to avoid cost inefficiencies.

### Additional Guardrails:
  * TBD

### Used By:
  * TBD

### EA Declaration:
  * TBD

## Technical Guidelines

### Best Practices:

  * **Environment Setup and Isolation:** Use **SageMaker Studio** in **VPC Only mode** to disable direct internet access and route traffic through private subnets. For **Notebook Instances**, launch them inside a VPC with `DirectInternetAccess` set to “Disabled” to ensure secure network paths.
  * **Data Management and Preparation:** Store training data in **Amazon S3** with proper versioning. Use **SageMaker Data Wrangler** for feature engineering or **Processing Jobs** for scalable data transformations. Maintain data lineage with experiment tracking.
  * **Optimizing Training Jobs:** Match instance types to workloads (e.g., GPUs for deep learning). Use **Spot Instances** for cost savings and **Debugger rules** to catch training issues early. Monitor **CloudWatch metrics** to optimize resource usage.
  * **Model Deployment and Testing:** Enable **auto-scaling** for real-time endpoints and deploy at least two instances across AZs for high availability. Use shadow deployments or A/B testing for gradual model updates. Implement **end-to-end monitoring** with CloudWatch Alarms and **Model Monitor**.
  * **Cost Efficiency:** Use smaller instance types for development and **Serverless Inference** for low-traffic deployments. Leverage **multi-model endpoints** to host multiple models on a single instance. Regularly review billing metrics to identify idle resources.
  * **Security and Access Controls:** Apply least privilege for IAM roles and encrypt data with **AWS KMS**. Enable **network isolation** for sensitive workloads and maintain audit logs with **CloudTrail**. Use **Model Registry** for artifact management.

### High Availability & Disaster Recovery:

  * **In-Region High Availability:** Deploy multiple instances across AWS Availability Zones to ensure resilience. For critical endpoints, maintain at least 2 instances with autoscaling enabled. Regularly back up important notebooks (e.g., via Git) to prevent data loss.
  * **Cross-Region DR (Disaster Recovery):** Design a manual DR strategy by deploying models in a secondary region. Use infrastructure-as-code (e.g., CloudFormation) to replicate deployments. Replicate data in S3 using cross-region replication and periodically test the DR setup.
  * **Backup & Recovery:** Use AWS Backup or EFS snapshots for Studio artifacts and version S3 data to prevent loss. Store trained models in **SageMaker Model Registry** or a code repository. Enable **CloudWatch Logs** for training and inference jobs to aid recovery and troubleshooting.

### Access Control & Security Configuration:

  * **Identity and Access Management (IAM):** Use fine-grained IAM roles to limit SageMaker permissions (e.g., access to specific S3 buckets or KMS keys). Avoid overly-broad roles like `AmazonSageMakerFullAccess`. Use IAM condition keys (e.g., `sagemaker:VolumeKmsKey`) to enforce security settings.
  * **Encryption:** Encrypt all data at rest using **AWS KMS**. Use customer-managed keys for sensitive data and ensure encryption for S3 buckets, EBS volumes, and inter-container traffic during training. Apply strong key management practices.
  * **Network Configuration:** Disable public network access for SageMaker resources. Use **VPC-only mode** for Studio and private subnets for Notebook Instances and endpoints. Leverage **AWS PrivateLink** for API calls and set up VPC Endpoints for S3, ECR, and other services.
  * **Monitoring and Auditing:** Enable **AWS CloudTrail** to log SageMaker operations and use **AWS Config rules** or **Security Hub** to monitor compliance. Set up CloudWatch Alarms for suspicious activity and enable **data capture** on endpoints for debugging and audits.

### Network Connectivity Options:

  * **Public Internet Access:** **Not Allowed** for production or sensitive workloads. SageMaker Studio and Notebook Instances must restrict internet access. Use controlled NAT gateways or VPC proxies for necessary internet access. All usage should rely on private connectivity (VPC, PrivateLink).
  * **AWS PrivateLink (Interface VPC Endpoints):** **Required/Preferred** for connecting to SageMaker APIs within AWS. Use PrivateLink for Studio, Runtime Inference API calls, and SDK/CLI calls to ensure traffic stays within the AWS backbone. Enable private DNS for seamless internal endpoint resolution.
  * **VPC Subnets (Private Subnets):** Launch SageMaker compute instances in **private subnets** with no direct internet route. Configure subnets with VPC Endpoints or a NAT gateway for required AWS service access, ensuring trusted network boundaries.
  * **On-Prem Connectivity:** Use **AWS Direct Connect or VPN** for on-premises access to SageMaker, enabling secure hybrid connectivity without exposing endpoints to the public internet.
  * **Network Architecture:** Deploy SageMaker endpoints in **dedicated private subnets** with necessary Interface Endpoints and a NAT (if outbound internet is needed). Use Security Groups to control access and ensure all traffic to AWS services goes through VPC Endpoints.

### Networking & Security Architecture:
  * A reference architecture diagram or description can be provided here showing SageMaker in a VPC, with PrivateLink endpoints, NAT gateways, etc., aligned with PepsiCo’s network security standards.

### AWS Service Limitations:

  * **Quotas and Limits:** SageMaker has default limits, such as 8 notebook instances and 4 training/hosting instances per region. Monitor Service Quotas and request increases for production needs.
  * **Model Size and Artifact Limits:** Large models may face startup delays or memory limits. SageMaker supports large models via DJL containers but requires awareness of instance disk/memory constraints.
  * **Lack of Native GPU Multi-tenancy:** SageMaker hosts one model per container per instance by default. Multi-Model Endpoints are suitable for infrequent use cases but may require additional instances for simultaneous hosting.
  * **Cold Start Latency for Serverless:** Serverless Inference endpoints have cold-start delays and payload size limits. Use batch transform for large inputs.
  * **Feature Gaps:** SageMaker lacks built-in automated model lineage across services and data version control. Debugging may require manual log analysis in CloudWatch.

### SKU Features:
  * TBD

### Related Service:
  * TBD

### Alternatives:
  * TBD

## Compliance and Security Guidelines

* **Security Baseline – InfoSec:**
  * Information Security Specifications – SageMaker

## Ownership and Version Control

* **Service Architect:** Abdul Moyeed (<EMAIL>)
* **Version Control:**

  * *v.1:* 22 Aug 2025 (Abdul Moyeed)
