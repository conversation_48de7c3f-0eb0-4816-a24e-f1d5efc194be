---
weight: 23
title: "Sagemaker"
date: 2025-08-13
tags: []
summary: ""
---

## Cloud Service Classification

* **Category of the Service:** Artificial Intelligence / Machine Learning
* **Cloud Provider:** Amazon AWS

## Cloud Service Overview

* **Name:** Amazon SageMaker
* **Description:** Amazon SageMaker is a fully managed machine learning platform that enables data scientists and developers to **build, train, and deploy** ML models at scale. It provides an integrated environment (SageMaker Studio IDE and managed Jupyter notebook instances) for the entire ML workflow, offering managed notebooks, distributed training with built-in algorithms or custom containers, automated hyperparameter tuning, and one-click model deployment to secure, scalable endpoints.
* **SKU Approval Status:**

  * *PepsiCo Approved:* All core SageMaker services and features are approved (including SageMaker Studio, Notebook Instances, Training Jobs, Batch Transform for batch inference, Real-Time Endpoints for deployment, Autopilot AutoML, Data Wrangler, Feature Store, Model Monitor, etc.), **provided that required security configurations are in place** (see Usage and Security guidelines).
  * *Not Approved:* Use of **SageMaker Ground Truth Plus** (the AWS-managed external labeling workforce) is **not approved** for PepsiCo confidential or restricted data, due to data residency and privacy considerations (only **internal or approved third-party** labeling options should be used for sensitive data).
* **Allowed PepsiCo Data Classification:** **Public, Internal, Confidential, Restricted** (All data classifications are allowed **with proper controls**. *Restricted* data usage requires strict adherence to guardrails such as VPC-only networking, encryption of data at rest and in transit, and private endpoints as described below.)

## Service Lifecycle

* **Release Date:** *November 29, 2017* – Amazon SageMaker was launched at AWS re\:Invent 2017.
* **Planned Decommission Date:** *No announced decommission date.* Amazon SageMaker continues to receive new feature updates (e.g., “SageMaker AI” enhancements announced at re\:Invent 2024) and remains a core AWS service with no end-of-life plans publicized.
* **Decommission Date:** *Not applicable at this time.* (SageMaker is an actively supported service with ongoing development.)

## Usage Guidelines

* **Features:**

  * **Integrated Notebook Environments:** Provides fully-managed Jupyter environments for ML development. SageMaker Studio is a unified, web-based IDE that integrates notebooks, code editing, and AWS tools in one interface. It supports collaboration and includes built-in integrations (e.g. Git, experiment tracking). For lighter use or isolated jobs, SageMaker also offers Notebook Instances (managed EC2 instances pre-loaded with ML libraries) for developing and running notebooks. Both environments come pre-configured with popular ML frameworks (TensorFlow, PyTorch, SciKit-Learn, etc.) to accelerate setup.
  * **Automated Model Development:** Includes AutoML and built-in algorithms to streamline model building. **SageMaker Autopilot** automatically explores different algorithms and feature-processing pipelines to train models, allowing users to generate high-quality classification or regression models without deep ML expertise. The platform also provides many **built-in algorithms** (e.g. XGBoost, linear regression, image classification, etc.) and **automated hyperparameter tuning** capabilities to efficiently find optimal model parameters. These features reduce the heavy lifting of manual model development and tuning.
  * **Deployment & Inference Options:** Simplifies deploying trained models to production. SageMaker supports one-click deployment of models to **real-time inference endpoints**, which are fully managed HTTPS APIs that automatically scale across multiple AWS Availability Zones for high availability. It also offers **Batch Transform** for offline/batch predictions on large datasets, which is useful when you don’t need a persistent endpoint. For use cases with intermittent or unpredictable traffic, SageMaker provides **Serverless Inference** endpoints that automatically scale down to zero to save cost (no instance management required, within concurrency limits). In all cases, the service handles provisioning and managing the underlying compute (including GPUs for deep learning models) and enables setting auto-scaling policies.
  * **MLOps & Lifecycle Management:** Amazon SageMaker includes integrated MLOps tools to manage the ML lifecycle. **SageMaker Pipelines** allows building repeatable, CI/CD pipelines for ML – from data preparation to training to deployment – enabling automation and traceability in ML workflows. A **Model Registry** is provided to version models, track metadata, and manage approval status before deployment to production. SageMaker’s **Model Monitor** continuously tracks deployed models for data drift or performance degradation, automatically alerting or triggering retraining if drift is detected. These features help implement governance and continuous improvement for production ML solutions (e.g., retraining models periodically or when data changes).
  * **Supporting Tools & Ecosystem:** SageMaker offers a broad range of supplemental tools to assist in building responsible, high-quality models. For example, **SageMaker Clarify** can be used to detect bias in datasets and explain the importance of features in a model’s predictions, improving transparency for AI outcomes. **SageMaker Debugger** provides real-time insights during training (capturing metrics and detecting issues like vanishing gradients). For data preparation, **SageMaker Data Wrangler** offers a visual interface to load, transform, and analyze data prior to modeling. Additionally, SageMaker includes **Ground Truth** for data labeling (with the option to use automated labeling and/or human labelers), and integrates with Amazon’s catalog of pre-trained AI Services and open-source models (through **SageMaker JumpStart** and **HuggingFace** integrations) to jump-start solutions. Together, these features make SageMaker a comprehensive end-to-end platform for ML development.

* **Sample Use Cases:**

  * **Predictive Analytics and Recommendation Systems:** SageMaker is commonly used to build models that predict business outcomes or user behaviors. For example, data science teams can train a model to predict product demand or customer churn using SageMaker’s training jobs, then deploy it as an API for real-time predictions. The service’s automation and scalability support use cases like personalized recommendations on e-commerce platforms or predictive maintenance in manufacturing. (At Care.com, SageMaker Pipelines are used to build end-to-end ML workflows that deliver *real-time caregiver recommendations* to customers, improving decision making.)
  * **Computer Vision (Image/Video Analysis):** SageMaker supports training deep learning models for image classification, object detection, and video analytics. A typical use case is automated quality control or image-based inspection – e.g., a food production company can train a convolutional neural network to classify products on an assembly line. **Tyson Foods** uses SageMaker to train and frequently retrain image recognition models that identify products requiring label verification on production lines, leveraging SageMaker’s pipelines so that even non-developers can update models with new images. Similarly, SageMaker can be used for video surveillance analytics, visual defect detection, or any scenario involving large-scale image data processing.
  * **Natural Language Processing and Text Analytics:** Organizations use SageMaker to develop NLP models for tasks like document classification, sentiment analysis, chatbot intelligence, or search optimization. SageMaker provides the compute and tools to fine-tune large language models or train custom NLP models on text data. For instance, an enterprise could use SageMaker to analyze and extract insights from thousands of documents or support a question-answering system. **Lexitas (legal services)** leveraged SageMaker with foundation models to enable semantic search through thousands of pages of legal transcripts, allowing lawyers to quickly find answers in deposition documents. This demonstrates SageMaker’s applicability in text-heavy domains (legal, healthcare records, customer service logs) to build NLP solutions at scale.
  * **Automated Model Retraining & MLOps:** SageMaker is useful for scenarios where models need regular retraining as new data arrives. Using SageMaker Pipelines and scheduling, companies can set up automated workflows to periodically retrain models (for example, retraining a sales forecast model monthly as new sales data comes in, or continuously retraining a fraud detection model as new transactions are collected). SageMaker’s integration with CI/CD and model registry helps implement MLOps best practices – promoting updated models through dev/test/prod stages with approvals. This is commonly used in industries like finance or retail, where data is continuously updated and models must stay current. (Enterprises like 3M have highlighted that SageMaker’s data preparation, feature store, and pipeline capabilities help them **scale out ML to many products and retraining cycles faster**, speeding time-to-market for new models.)

* **Limitations:**

  * **Service Quotas:** By default, SageMaker imposes certain resource limits per AWS account. For example, an account is initially limited to **8 running notebook instances** and **4 training or hosting instances** per region. Similarly, the default maximum concurrent training jobs, endpoints, or parallel hyperparameter tuning jobs may be constrained (e.g., hyperparameter tuning can run 10 training jobs in parallel by default). These quotas can be increased by requesting limit increases, but out-of-the-box they can restrict very large-scale usage. It’s important to plan for these limits or request adjustments in advance for production workloads.
  * **Single-Region Deployments:** SageMaker does not provide native cross-region replication or failover for models. All training jobs and deployed endpoints run in a specific AWS region chosen by the user. In the event of a region-wide outage, there is **no automatic failover** of SageMaker endpoints to another region – achieving multi-region resiliency requires manual deployment of models to secondary regions and implementing your own failover mechanism (e.g., DNS routing). This is a limitation for disaster recovery planning, as high availability can be achieved within a region (across AZs) but not across regions without additional effort.
  * **Network Isolation and Internet Access:** While SageMaker can be configured for network isolation, by default many components will access AWS services over the internet. Care must be taken to use VPC integration and VPC Endpoints (PrivateLink) for services like S3, since training jobs or notebooks could otherwise attempt to download data or docker images via the public internet. SageMaker does support an “internet-free” mode for notebooks/Studio and network isolation for training jobs, but if not enabled, this could pose security and compliance limitations (especially for Restricted data). In essence, **secure setup requires additional configuration** – this is not a functional limitation of SageMaker per se, but a consideration that the onus is on the user to properly configure networking (VPC-only, no direct internet) to meet corporate security requirements.
  * **Complexity and Learning Curve:** Amazon SageMaker’s comprehensive nature means there are many components and configurations to learn (Studio, jobs, endpoints, IAM roles, etc.). New teams may face a steep learning curve to use it optimally. For small simple projects, SageMaker might feel heavyweight – for example, deploying a simple script might involve understanding containers or IAM roles. This is a subjective limitation: while not a “feature gap,” it means using SageMaker effectively requires skilled users or training, and there may be a overhead in managing the various moving parts.
  * **AWS Lock-In and Portability:** SageMaker is designed to work within the AWS ecosystem (S3 for data storage, AWS IAM for auth, CloudWatch for logs, etc.). Consequently, solutions built on SageMaker are tightly coupled with AWS services. There is no on-premises or multi-cloud version of SageMaker. If an organization needed to move a workload off AWS, they would have to manually port models and code to a different environment (e.g., containerize the model to run on Kubernetes). While model artifacts (e.g., the trained model files) can always be exported from SageMaker and reused elsewhere, the managed convenience and integrations of SageMaker do not directly transfer off AWS. This lack of portability can be seen as a limitation if future cloud strategy changes, and it underscores the need to design with open standards (containers, standard ML frameworks) to mitigate lock-in.
  * **Cost Management Considerations:** SageMaker’s ease of provisioning resources can lead to cost inefficiencies if not managed. For example, leaving **idle notebook instances or inference endpoints** running will incur ongoing charges. There is no built-in cost cap or automatic shutdown for idle resources (aside from user-set auto-stop scripts on notebooks). This means users must implement governance – e.g., stop resources when not in use, use auto-scaling policies for endpoints, prefer spot instances for training when appropriate – to avoid excessive costs. While not a limitation of capability, this is a practical limitation: without careful management, costs can scale up unexpectedly.

* **Additional Guardrails:** *TBD* – (Any additional internal guardrails or policies specific to PepsiCo’s use of SageMaker should be defined here. For example, requirements for tagging SageMaker resources, mandatory use of certain network configurations, enabling specific logging/auditing settings, etc.)

* **Used By:** *TBD* – (List of PepsiCo teams or projects using SageMaker, with links to onboarding documents or case studies if available.)

* **EA Declaration:** *NOT a declared standard.* (Amazon SageMaker is not yet an official “Enterprise Architecture declared standard” service; it may be used with appropriate approvals but is not globally standardized at this time.)

## Technical Guidelines

* **Best Practices:**

  * **Environment Setup and Isolation:** Use **SageMaker Studio** for collaborative development, but ensure you configure it in **VPC Only mode** for enterprise use – this disables direct internet access and routes all Studio traffic through your private VPC subnets. Leverage IAM Identity Center (formerly SSO) integration for Studio to manage user access centrally, and disable the default “root” or general user in notebooks (SageMaker notebooks should not run with root access by default). When using **Notebook Instances**, always launch them inside a VPC and **disable direct internet access** (set `DirectInternetAccess` to “Disabled”) to prevent accidental exposure. This ensures all network calls (e.g., to S3 or Docker repos) go through controlled network paths (VPC endpoints or NAT as needed).
  * **Data Management and Preparation:** Store all training and validation data in **Amazon S3** and **organize data** using a proper prefix structure for versioning (e.g., `/project/version/date/…`). Take advantage of **SageMaker Data Wrangler** for data preparation tasks – it can simplify feature engineering and directly export prepared datasets or processing jobs into pipelines. If doing custom data processing, consider using **SageMaker Processing Jobs** or AWS Glue instead of embedding heavy data prep in notebooks, to have scalable, reproducible data transformation steps. Maintain data versioning and lineage by using SageMaker’s experiment tracking or by keeping a record of S3 data snapshot references used for each training run (this will aid in reproducibility and audit).
  * **Optimizing Training Jobs:** Choose instance types that match the workload – e.g., use GPU instances (such as `ml.p3` or `ml.p4` families) for deep learning, and CPU instances for lighter algorithms. Use **Spot Instances** for training whenever feasible (for non-time-critical training jobs) to significantly reduce cost – SageMaker can manage spot interruptions and resume training from checkpoints if configured. Utilize **distributed training** options provided by SageMaker (built-in data parallelism or model parallelism features) when working with large datasets or models, but only as needed (start with single-machine training and scale out if the training time is too long). Always **monitor CloudWatch metrics** during training – key metrics like GPU utilization, CPU utilization, disk I/O, and throughput can indicate if the chosen instance type is underused (and a smaller instance could suffice) or overburdened (and a larger instance or distributed setup is needed). Implement SageMaker **Debugger rules** to catch issues during training (e.g., poor convergence or NaN losses) early.
  * **Model Deployment and Testing:** For real-time endpoints, enable **auto-scaling** on deployed models to handle variable load – define scaling policies based on metrics like CPU utilization or invocation count to automatically adjust instance count. Always deploy at least **two instances across different AZs** for production endpoints to ensure high availability (SageMaker will distribute endpoint instances across Availability Zones by default when you deploy >1 instance). Test models using shadow deployments or A/B testing: SageMaker allows you to send a small percentage of traffic to a new model version by deploying a **production variant** to the same endpoint – use this to gradually test new model versions against the current one in production. Avoid changing or updating a model’s files in S3 without redeploying; instead, use SageMaker’s deployment mechanisms (or create a new endpoint) to update models – modifying model artifacts in place can lead to undefined behavior on running endpoints. Finally, implement **end-to-end monitoring**: use CloudWatch Alarms for errors or latency spikes on endpoints, and SageMaker Model Monitor to track data quality – for example, set up constraints and alerts if input data deviates from the training data schema.
  * **Cost Efficiency:** Utilize **SageMaker instance types wisely** – for development, use smaller instance types or the new **SageMaker Studio Lab** (free tier) for initial exploration. Shut down notebook instances or Studio kernels when not in use (auto-stop idle notebooks after a timeout). For deployment, consider if **Serverless Inference** is suitable (low traffic, intermittent requests) to avoid paying for idle time – serverless endpoints automatically scale down to 0, though note the current concurrency limits (e.g., max 10 concurrent requests by default). Use **multi-model endpoints** if you need to host many small models – this feature allows loading multiple models onto a single instance on demand, saving cost by sharing infrastructure (ideal for scenarios like one model per tenant or dozens of tiny models). During training, prefer built-in optimizations: for example, the **SageMaker Training Compiler** can accelerate deep learning training on GPUs, and using **Pipe Mode** data ingestion can speed up training for large datasets by streaming from S3. Leverage spot training for cost savings and **Amazon SageMaker Savings Plans** or reserved capacity for long-running workloads to reduce cost. Regularly review CloudWatch billing metrics or Cost Explorer to identify any idle SageMaker resources.
  * **Security and Access Controls:** Apply the principle of least privilege for SageMaker IAM roles. The **execution role** attached to notebook instances or training jobs should only allow the minimum required actions (for example, access to specific S3 buckets, specific AWS KMS keys, etc.). Use **AWS Key Management Service (KMS)** customer-managed keys to encrypt data at rest: ensure S3 buckets used by SageMaker have default encryption with KMS, and encrypt notebook instance volumes or Studio EFS volumes with KMS. **Network isolation**: for sensitive workloads, enable network isolation (the `EnableNetworkIsolation` flag) on training jobs/inference jobs – this prevents the container from making any outbound network calls, adding an extra layer of security for confidential code or data. Access to SageMaker should be federated through AWS SSO/AD rather than long-term IAM users – maintain audit logs with AWS CloudTrail for all SageMaker API calls (e.g., who started a training job or who deleted a model). Finally, ensure **artifact retention and cleanup**: models are output to S3 – implement lifecycle policies or retention plans for these artifacts (especially if they contain sensitive data like model weights derived from sensitive datasets), and use SageMaker’s **Model Registry** to catalog and manage approved models rather than ad hoc model storage.

* **High Availability & Disaster Recovery:**

  * **In-Region High Availability:** SageMaker leverages AWS Availability Zones to keep services resilient. For production model endpoints, deploy multiple instances and let SageMaker automatically spread them across at least two AZs. This way, if one AZ goes down, your endpoint will still be served from the other AZs. It’s **recommended to always have a minimum of 2 instances for critical endpoints** (and configure the endpoint’s autoscaling minimum capacity to 2) to achieve \~99.95% availability. SageMaker endpoints perform health checks and will replace unhealthy instances automatically. For Studio and notebooks, the backing services (EFS for Studio storage, etc.) are multi-AZ or durable, but you should also **regularly backup important notebooks** (e.g., via Git) to ensure work isn’t lost.
  * **Cross-Region DR (Disaster Recovery):** As noted, SageMaker does not auto-failover across regions, but you can design a manual DR strategy. If your application requires a DR plan, consider deploying the trained model in a secondary region as well. You can use infrastructure-as-code (CloudFormation/Terraform or SageMaker Project CI/CD pipelines) to **duplicate model deployments in a second region**. Data (in S3) can be replicated using cross-region replication so that training data and model artifacts are available in the DR region. In a failure scenario, you’d update DNS (or Route 53 with health checks) to point to the standby endpoint in the secondary region. **Note:** Client applications would need to be able to connect to the endpoint in the new region, and any associated resources (like feature store or data sources) must be available there as well. Test the DR deployment periodically. For SageMaker Studio, AWS now allows launching Studio domains in specific regions – there isn’t an active-active Studio, but you could maintain a backup of critical notebooks in a repository to re-launch in another region if needed. Overall, implement DR by treating SageMaker deployments as code: retrain or replicate models in alternate regions and have a playbook for cutover.
  * **Backup & Recovery:** SageMaker itself manages certain backups (for example, model artifacts are in S3 which has 11 9’s durability by design). However, you should plan backups for **SageMaker Studio artifacts** (which are stored on Amazon EFS in your account). Use AWS Backup or EFS backup capabilities to take periodic snapshots of the Studio home directories, especially if they contain important code or data. For Notebook Instances, if you store data on the notebook’s attached EBS volume, consider creating snapshots of those volumes (or, better, move persistent data to S3 so that notebooks are stateless). **Model artifacts** and training data in S3 should be versioned – enable S3 Versioning on buckets so that you don’t lose information if something is overwritten or deleted. Additionally, consider storing trained model artifacts in **SageMaker Model Registry** or a code repository (for model code) as an additional form of backup. For compliance, enable **SageMaker experiment tracking** to log parameters, data sources, and metrics of training runs – this metadata acts as a backup of the “recipe” to reproduce models if needed. If a model or endpoint is accidentally deleted, having the training data, scripts, and logged parameters means you can recreate it. Finally, **enable CloudWatch Logs** for training and inference jobs (SageMaker can stream logs to CloudWatch) – this provides a backup of the training output (useful for troubleshooting recovery scenarios). By combining data backups, artifact versioning, and logged pipelines, you can recover from failures or revert to prior versions of models if necessary.

* **Access Control & Security Configuration:**

  * **Identity and Access Management (IAM):** Use fine-grained IAM roles for SageMaker. **Avoid using overly-broad roles** like `AmazonSageMakerFullAccess` in production. Instead, craft IAM policies that allow SageMaker to do only what is needed (e.g., allow read/write to specific S3 buckets, access to specific ECR repositories for custom images, KMS decrypt on specific keys). Attach these as the **execution role** for notebooks, training, and deployment. Additionally, control which IAM principals (users/groups) can perform SageMaker actions – for instance, not all developers should be able to create or delete endpoints or notebooks in all accounts. Utilize IAM condition keys specific to SageMaker, such as `sagemaker:VolumeKmsKey` or `sagemaker:NetworkIsolation`, to enforce security settings – AWS allows requiring that all SageMaker jobs use encryption keys and network isolation via IAM policy conditions. This ensures security configurations aren’t skipped.
  * **Encryption:** Ensure all data at rest is encrypted. By default, SageMaker notebooks and endpoints encrypt storage volumes with AWS-managed keys; for sensitive data, use **customer-managed KMS keys**. Encrypt S3 buckets that store datasets and model artifacts with KMS keys (and restrict who can access those keys). If using SageMaker Feature Store or endpoint data capture, those outputs land in S3 as well – similarly encrypt them. SageMaker also supports **encryption of inter-container traffic** on training jobs (if using multiple distributed nodes) – enable that if your compliance requires (it ensures training traffic between machines is encrypted). For **SageMaker Canvas** (if used for no-code ML with sensitive data), there are options to encrypt the application data with KMS. In short, apply encryption at all possible points (S3, EBS, EFS, etc.), and use strong key management practices for the KMS keys.
  * **Network Configuration:** **Do not allow public network access** to SageMaker resources in a production setting. That means: for Notebook Instances, set them up with **no public IP** and in private subnets; for Studio, use VPC-only mode; for inference endpoints, if they must be invoked from on-prem or a VPC, put them **inside a VPC** (attach the endpoint to subnets and security groups). Note that a SageMaker endpoint with VPC configuration will only be reachable from within that VPC (or via AWS Direct Connect/VPN from on-prem), which is desirable for internal APIs. Use **AWS PrivateLink (Interface VPC Endpoints)** for SageMaker API and runtime: create interface endpoints for `aws.sagemaker.*` and `aws.sagemaker-runtime.*` so that your applications or other AWS services can call SageMaker internally without crossing the public Internet. This is especially important for invoking endpoints from AWS Lambda or EC2 in your VPC – with PrivateLink, the inference calls stay on AWS’s private network. Additionally, set up VPC Endpoints for S3, ECR, CloudWatch, etc., so that SageMaker jobs (which often pull containers and data) don’t need internet access. All SageMaker traffic (both control plane and data plane) can be confined to your private network with these configurations. Also employ **security groups** to restrict access: for example, the security group on a VPC-attached endpoint could be configured to allow inbound traffic only from your application servers’ security group.
  * **Monitoring and Auditing:** Enable AWS CloudTrail for all SageMaker operations – this will log who created or modified SageMaker resources (notebooks, models, endpoints) and when. This is key for audit purposes. Use **AWS Config rules** or **Security Hub** to continuously check compliance of SageMaker resources. AWS provides managed rules such as ensuring notebooks are in a VPC, no direct internet, and no root access on notebooks. Enable these Security Hub **controls for SageMaker** to get alerts if someone launches a notebook with internet access or an endpoint with a single instance (there’s a control that checks for HA by requiring more than one instance in production). Log CloudWatch metrics and set up alarms for suspicious activity – e.g., an unusual spike in endpoint invocations might indicate misuse. Also consider enabling **data capture** on endpoints and **AWS CloudWatch Logs** for inference to record predictions (for debugging and audit of model outputs – useful in regulated industries to reproduce what was predicted). From a process standpoint, treat SageMaker environments as production-critical: implement peer reviews for notebook code, use source control for all code (and even infrastructure-as-code for deploying SageMaker models via CloudFormation or Terraform). This ensures there’s an audit trail for changes to ML code and infrastructure.

* **Network Connectivity Options:**

  * **Public Internet Access:** **Not Allowed** for any production or sensitive workloads. By default, SageMaker Studio and Notebook Instances can have internet access, and endpoints are invoked via a public AWS API endpoint; however, in the PepsiCo environment this **must be restricted**. Do not use SageMaker resources openly over the internet. If internet access is required for, say, downloading public packages during training, route it through a controlled NAT gateway or VPC proxy – direct internet connectivity should be turned off. In summary, all SageMaker usage should be through private connectivity (VPC, PrivateLink).
  * **AWS PrivateLink (Interface VPC Endpoints):** **Required/Preferred** for connecting to SageMaker APIs from within AWS. PrivateLink endpoints should be used for **Studio** (so the web interface runs locally), for **Runtime Inference API** calls, and for any SDK/CLI calls to SageMaker from AWS compute. This ensures API calls do not traverse the public internet and are instead communicated via the AWS backbone network. When configuring interface endpoints, enable the private DNS option so that `*.sagemaker.amazonaws.com` names resolve to your VPC endpoint addresses. This way, your code doesn’t need to change URLs – it will automatically hit the internal endpoints.
  * **VPC Subnets (Private Subnets):** Launch all SageMaker compute instances in **private subnets** of your VPC (with no direct route to internet). For Notebook Instances, set **SubnetId** and no public IP; for Studio, specify subnets during domain creation (and choose “VPC only”); for training jobs and endpoints, provide a **VpcConfig** with your subnet IDs and security group IDs. Ensure these subnets have network access to required AWS services either via VPC Endpoints or a NAT gateway in a **single egress point**. This setup keeps SageMaker processing within trusted network boundaries.
  * **On-Prem Connectivity:** If you need to access SageMaker from on-premises (for example, to query a model endpoint from a data center or to allow Studio users to reach an on-prem database), use **AWS Direct Connect or VPN** into the AWS VPC. Through Direct Connect, on-prem systems can call SageMaker endpoints via the PrivateLink endpoint as if it’s on the same network. This avoids exposing endpoints to the public internet while still enabling hybrid connectivity.
  * **Network Architecture:** In practice, the recommended architecture is: deploy SageMaker endpoints in a **dedicated private subnet** (or subnets) with only the necessary Interface Endpoints and a NAT (if outbound internet is absolutely needed for specific tasks). Use Security Groups to strictly control which sources can connect to the endpoints (for example, only an application load balancer or specific EC2 instances). For notebooks, similarly isolate them and consider enabling **Network ACLs** or additional firewalling if needed to limit egress traffic (only to AWS service endpoints). All traffic between SageMaker and other AWS services (S3, ECR, etc.) should go through VPC Endpoints – not through the internet. This effectively mirrors the Azure guidance (no public endpoints, use private connectivity) in the AWS context.

* **Networking & Security Architecture:** *TBD* (A reference architecture diagram or description can be provided here showing SageMaker in a VPC, with PrivateLink endpoints, NAT gateways, etc., aligned with PepsiCo’s network security standards. It should illustrate how data flows from corporate networks to SageMaker and back, all within secure endpoints.)

* **AWS Service Limitations:**

  * **Quotas and Limits:** SageMaker comes with numerous service limits. Key ones include: maximum **number of notebook instances** (default 8 per region), maximum **instances for training or inference** (default total of 4 each), maximum **parallel hyperparameter tuning jobs** (10 parallel by default), and limits on certain instance types (some GPU instance families are initially set to 0 and require requesting a limit increase to use). Also, the newer **Serverless Inference** feature has limits on concurrency (each account gets a certain throughput limit, e.g. 10 concurrent requests across all serverless endpoints by default). These limitations mean that out-of-the-box, large teams might quickly hit caps – one should proactively monitor Service Quotas and request increases for production needs.
  * **Model Size and Artifact Limits:** There are practical limits on model artifact sizes – while S3 can store very large objects, deploying extremely large models (tens of GBs) may lead to longer startup times or hitting memory limits on instances. Models larger than what can fit in one instance’s memory cannot be served without partitioning (except using inference pipelines or partitioning model across multiple containers manually, which is complex). SageMaker’s new features like **LLM support (e.g., through DJL containers)** partially address large models, but users should be aware of instance disk/memory limits. Additionally, SageMaker Processing jobs have a 16 TB limit on **Amazon EBS volume** size attached – which is plenty in most cases, but heavy ETL might approach this.
  * **Lack of Native GPU Multi-tenancy:** If you have many small models to serve, SageMaker by default will put one model per container per instance. There’s a **Multi-Model Endpoint** feature to host multiple models on one instance, but it’s best for many infrequently-used models (it loads/unloads them on demand). If you have a scenario of hosting say 100 tiny models all needing to be loaded simultaneously, SageMaker would require enough instances to handle them – it can’t pack too many active models on one instance unless memory allows. This is a limitation compared to some self-managed solutions that allow more custom sharing of GPU/CPU.
  * **Cold Start Latency for Serverless:** The Serverless Inference endpoints in SageMaker have a cold-start delay (typically a few seconds) when scaling from zero. This may not be suitable for very low-latency requirements. Additionally, **maximum payload size** for serverless or even for real-time endpoints via the AWS API is capped (payloads above a certain size, e.g. 6 MB for invoke-endpoint requests, might not be supported) – large inputs should use batch transform or be pre-processed.
  * **Feature Gaps:** SageMaker is a broad service, but there are a few things it does not do natively, which could be considered limitations. For example, **no built-in automated model lineage across services** (though SageMaker Model Registry and Lineage tracking address this for SageMaker jobs, they won’t automatically track models trained outside SageMaker). SageMaker doesn’t inherently perform **data version control** – that is left to the user via S3 or external tools. Also, while SageMaker manages a lot of infrastructure, debugging issues can sometimes be challenging (e.g., if a training job fails, you may need to sift through CloudWatch logs – there’s no integrated UI for log analytics). Understanding these gaps can help in planning complementary tools or processes.

* **SKU Features:** *(N/A – Amazon SageMaker is a single service with usage-based pricing rather than distinct SKU tiers. Relevant feature variations (like instance types, on-demand vs. serverless, etc.) are covered in other sections.)*
  *(For reference, SageMaker’s capabilities can be considered in different modes: e.g., **On-Demand Instances** for training/inference where you choose the instance type and pay per hour; **Serverless Inference** which abstracts instances; and **SageMaker Canvas** for no-code model building. However, these are not “SKUs” with separate tiers, but rather options within the service.)*

* **Related Service:** *TBD* – (List any related or complementary services. For example, Amazon SageMaker works alongside **AWS Glue/Athena** for data processing, or you might compare it to **Azure Machine Learning** as an alternative in Azure. This section can enumerate services that integrate or serve as alternatives, to provide context.)

* **Alternatives:** *TBD* – (Identify alternatives to SageMaker, such as other cloud ML platforms or on-prem solutions. E.g., **Azure ML Studio, Google Vertex AI** for other clouds, or **Kubernetes with Kubeflow** for a DIY approach. Also mention if using AWS native AI services (Rekognition, Comprehend, etc.) could be an alternative when a fully custom model isn’t needed. This helps decision-makers consider when SageMaker is the right choice versus another tool.)

## Compliance and Security Guidelines

* **Security Baseline – InfoSec:** All usage of Amazon SageMaker must conform to PepsiCo’s cloud security baseline and controls for AI/ML services. **Information Security Specifications – Amazon SageMaker** should be followed, which include ensuring that SageMaker is deployed in a secure network configuration (no public access, VPC only), that **notebooks have no direct internet access and run in a custom VPC**, and that encryption is enabled for data at rest and in transit. Key baseline controls include: disabling root access on notebook instances, requiring IAM authentication for all API access, restricting SageMaker usage to approved AWS accounts/VPCs, and monitoring SageMaker resources via Security Hub and GuardDuty. Any data used in SageMaker, especially Confidential or Restricted data, must be handled according to PepsiCo data protection standards (e.g., PII encryption, access logging). Regular audits should be conducted to verify compliance with these controls. (Refer to the “PepsiCo InfoSec Cloud Security Baseline – Amazon SageMaker” document for detailed requirements and checklists.)

*(The AWS Security Hub provides automated controls for SageMaker that align with best practices. For example, it checks that Internet access is disabled on notebooks, notebooks are in a VPC, multiple endpoint instances are provisioned, etc. These should be enabled as part of the compliance monitoring for SageMaker.)*

## Ownership and Version Control

* **Service Architect:** Dariusz Korzun – *Global Cloud Service Architect* ([<EMAIL>](mailto:<EMAIL>))
* **Version Control:**

  * *v.1:* 13 Aug 2025 – Initial draft for Amazon SageMaker catalog entry (Dariusz Korzun)
