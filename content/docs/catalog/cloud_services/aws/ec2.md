---
weight: 3
title: "EC2"
date: 2025-07-22
tags: [compute, virtual servers, instances, AWS EC2, Amazon EC2, EC2]
summary: ""
---

# Amazon EC2

## Cloud Service Classification

* **Category of the Service:** Compute (Infrastructure as a Service – Virtual Servers)
* **Cloud Provider:** Amazon Web Services (AWS)
* **Website**: [Technical documentation](https://docs.aws.amazon.com/ec2/)  

## Cloud Service Overview

* **Name:** Amazon Elastic Compute Cloud (Amazon EC2)
* **Description:** Amazon EC2 offers scalable, on-demand virtual servers with flexible configurations and the AWS Nitro System for enhanced security and performance.
* **SKU Approval Status:**

  * **PepsiCo Approved:** On-Demand Instances, Savings Plans (Compute and Instance) and Reserved Instances (for 1 or 3-year consistent usage) and On-Demand Capacity Reservations (for flexible durations in a specific Availability Zone).
  * **Not Approved:** *Following instance families are not approved:*
    - General Purpose: T-family (Burstable Instances).
    - Memory Optimized: U-family (Ultra Large Memory).
    - Accelerated Computing: G-family.
    - Storage Optimized: Instances of I, D and H family.
    - HPC Optimized: Hpc Series instances.

  (All standard EC2 purchase options **except spot** are available; usage is subject to company guidelines for cost and workload suitability).

* **Allowed PepsiCo Data Classification:**

  * Public
  * Internal
  * Confidential
  * Restricted
    *(All data classifications allowed on EC2, but **Restricted** data requires strict controls like no public exposure, encryption, and network isolation.)*

## Service Lifecycle

* **Release Date:** EC2 became generally available with SLA on October 23, 2008.
* **Planned Decommission Date:** Not announced.
* **Decommission Date:** Not applicable at this time.

## Usage Guidelines

* **Features:**

  * **Elastic Compute Cloud (EC2) Instances:** Launch and terminate instances with flexibility of cloud automation (API-driven) functioning like on-premises servers.
  * **Instance Variety:** Broad range of types, sizes and processors (Intel, AMD, AWS Graviton).
  * **Flexible Storage Options:** Integrates with EBS for block storage and instance store for temporary storage.
  * **Networking & Security:** Run in VPCs with Security Groups, key pairs for login, and the AWS Nitro System for enhanced protection.
  * **Integration with AWS Ecosystem:** Compatible with Auto-scaling, CloudWatch, and managed via Console, CLI, SDKs, CloudFormation or Terraform.

* **Sample Use Cases:**

  * **Web & Enterprise Application Hosting:** Scalable apps with auto-scaling and multi-AZ deployment.
  * **High-Performance Computing (HPC):** Compute-intensive workloads with Elastic Fabric Adapter (EFA) for low-latency networking.
  * **Machine Learning & Big Data Analytics:** GPU/memory-optimized clusters for training, data analytics with upto 400 Gbps networking.
  * **Development and Testing Environments:** Rapidly provision on-demand dev/test environments, eliminating on-prem hardware.

* **Limitations:**

  * **No Implicit High Availability:** Requires multi-AZ deployment for High availability (HA).
  * **Resource Quotas:** AWS enforces strict instance quotas per account/region, can be increased by request; exceeding quotas causes instance launch failures.
  * **Ephemeral Instance Storage:** Instance store **data is lost** on stop/terminate/hibernate
  * **No Automatic Backups:** Manual setup required using EBS snapshots, AWS Backup, or Rubrik.
  * **Specialized Configurations:** Advanced networking (10+ Gbps throughput, EFA) demands specific instance types that aren’t always available in all regions.

* **Additional Guardrails:** *TBD*
  *(Enterprise-specific guardrails, such as mandatory tagging, required approval for certain instance sizes, or cost budget alerts, to be defined.)*

* **Used By:** *TBD*
  *(List of some internal applications or teams using EC2, with links to onboarding documentation – to be provided.)*

* **EA Declaration:** NOT a declared standard.
  *(EC2 is broadly used as a foundational service; however, it may not be explicitly declared as a “standard” since it underpins various solutions.)*

## Technical Guidelines

* **Best Practices:**

  * **Security Hardening & Configuration:** Use IAM roles, restrict remote admin access, encrypt data at rest and in transit, and monitor with CloudTrail. Enhance protection with Amazon Inspector and AWS Security Hub. Run EC2 Inspector Assessments for vulnerability.
  * **Instance and Resource Management:** Right-size EC2 instances, tag resources for governance, monitor quotas, and use AWS Trusted Advisor for optimization.
  * **Storage Configuration:** Segregate OS and app data on separate encrypted EBS volumes and ensure instance store–using applications tolerate data loss.
  * **High Availability Design:** Design apps across multiple *(minimum two)* Availability Zones (AZs) with Elastic Load Balancing and replicate data for multi-region Disaster Recovery.
  * **Monitoring and Logging:** Enable detailed EC2 monitoring with CloudWatch, audit with CloudTrail, enforce config rules, and use GuardDuty for threat detection and logging.
  * **Performance & Cost Optimization:** Enhance cost-efficiency by using Optimize CPU, newer or Graviton instances, auto-scaling with smaller units, Reserved Instances and/or Savings Plans.

* **High Availability & Disaster Recovery:**

  * **In-Region High Availability:** Deploy EC2 across multiple Availability Zones *(minimum two)* with Elastic Load Balancing and Auto Scaling for high availability, and use a multi-region strategy with PepsiCo-approved AWS regions for critical apps.
  * **Backup and Restore:** Periodically backup EC2 data with consistent EBS snapshots for reliable multi-AZ recovery. Standard backup of EBS data must be performed using Rubrik. Ensure updated AMIs are stored in S3.
  * **Multi-Region Disaster Recovery:** Implement cross-region disaster recovery using active-active or active-passive setups in PepsiCo-approved regions, replicate snapshots and AMIs regularly, use AWS DRS, database replication, Route 53 failover, and test failover periodically.

* **Backup & Recovery:**

  * **EBS Volume Snapshots:** Perform regular incremental EBS snapshots during volume use. Take backups using Rubrik, and enable fast restore for quicker recovery.
  * **AMI (Amazon Machine Image) Creation:** Create and update AMIs regularly to capture OS and software states for fast recovery, cloning, and cross-region disaster recovery.
  * **Database and App Backups:** For databases or stateful EC2 apps, use application-level backups (Rubrik, DB dumps, native tools) with EBS snapshots, store in S3, and replicate across regions for durability.
  * **Recovery Drills and Automated Recovery Features:** Regularly test EC2 backup restores and Auto Scaling, enable Auto-Recovery, termination protection, and EBS Recycle Bin to ensure recovery readiness and prevent data loss.

* **Access Control & Security Configuration:**

  * **Identity and Access Management (IAM):** Use IAM roles (instance profiles) to securely grant EC2 instances permissions to access AWS services.
  * **Secure Remote Access:** Use SSH keys for Linux and strong credentials for Windows EC2, disable passwords, prefer Systems Manager Session Manager, and restrict SSH/RDP with MFA, bastion hosts, or VPN.
  * **Network Security:** Strictly limit inbound traffic with Security Groups and Network ACLs, place sensitive EC2 in private subnets with NAT or VPC endpoints, and monitor via VPC Flow Logs for anomalies.
  * **Encryption:** Ensure that data **encryption at rest and in transit** is enabled for all EC2 instances.
  * **Logging and Monitoring:** Enable CloudTrail and AWS Config for EC2 audit and compliance, and enhance security with Amazon Inspector, anti-malware, time sync, and centralized logging.

* **Network Connectivity Options:**

  * **Public Internet Access:** Requires approval.
  * **Private Subnets and VPN:** Host applications in private subnets without internet access, using VPN or Direct Connect, and access via bastion hosts or Systems Manager for secure isolation.
  * **AWS Direct Connect:** Use AWS Direct Connect with Transit Gateway for secure, low-latency private links between on-prem data centers and AWS.
  * **Transit Gateway:** Use AWS Transit Gateway to connect EC2 instances across VPCs, configuring security groups and route tables to permit cross-VPC traffic.
  * **AWS Cloud WAN:** *TBD*
  * **VPC Endpoints and PrivateLink:** Use VPC Endpoints for private EC2 access to AWS services and AWS PrivateLink for secure connectivity to third-party SaaS without internet exposure, vital for *Restricted data*.
  * **Network Appliances and NAT Gateways:** Enhance network security by routing EC2 traffic through virtual appliances for inspection and using NAT Gateways for secure outbound internet from private subnets without inbound exposure.
    
    *(Note: All network connectivity for **Restricted** data EC2 instances should be through private channels. Any exception requiring public access must go through approval and have proper compensating controls like IP whitelisting, multi-factor auth, etc.)*

* **Networking & Security Architecture:** *TBD*
  *(Diagram and description of a typical network/security architecture for EC2 deployments – e.g., showing VPC, subnets, route tables, firewalls, etc., to be provided by enterprise architecture.)*

* **AWS Service Limitations:**

  * **Instance Limits:** Monitor AWS vCPU and EC2 resource limits per account and Region, requesting increases before scaling or large deployments.
  * **Networking Limits:** Plan network design within ENI and security group attachment and rule limits; for high-scale needs, distribute load or request AWS limit increases.
  * **Email (SMTP) Throttling:** EC2 **blocks** *outbound traffic on port 25 by default* to prevent spam; to send email via SMTP, request removal or use alternatives like Amazon SES or other ports.
  * **Lack of Live Migration:** EC2 lacks live migration; AWS may reboot or stop/start instances for maintenance, so design critical workloads with redundancy for availability.
  * **Old Instance Deprecation:** Migrate from deprecated EC2 instance types and validate newer ones regularly to stay current with AWS advancements as older types are retired.

* **SKU Features:** *EC2 Instance Families and Capabilities*

  * **General Purpose Instances:** Balanced mix of CPU, memory, and networking resources to support diverse workloads.
  * **Compute Optimized:** High CPU performance per dollar, ideal for compute-bound tasks.
  * **Memory Optimized:** Large memory allotments for memory-bound workloads such as high performance databases.
  * **Accelerated Computing (GPU/FPGA):** GPU, FPGA, Trainium, and Inferentia instances for specialized workloads like ML training, inferencing, video rendering, and hardware acceleration
  * **Storage Optimized:** High IOPS or throughput with local NVMe SSD or HDD storage for databases and big data.
  * **HPC Optimized:** Designed for large-scale High Performance Computing on AWS, featuring *Elastic Fabric Adapter (EFA)* for ultra-low latency and high-throughput internode communication.
    
    *(When choosing an EC2 instance, select the family that best matches the workload profile to maximize performance and cost efficiency. Each family has multiple sizes (e.g., large, xlarge, etc.) to scale resources as needed.)*

* **Related Services:** Amazon Auto-scaling, Elastic Load Balancer, CloudWatch, Amazon ECS, Amazon EKS for containers.

* **Alternatives:**

  * **AWS Fargate / Container Services:** AWS Fargate offers serverless containers without managing infrastructure for microservices and containerized workloads. Amazon ECS/EKS use EC2 and Fargate.
  * **AWS Lambda (Serverless):** Lambda runs code serverlessly on demand, scaling automatically without managing servers, ideal for stateless, short-duration functions replacing always-on EC2 instances.
  * **Managed AWS Services:** Managed services like Amazon RDS, EMR, and ElastiCache can replace EC2-hosted software, reducing operational overhead by automating tasks like backups and patching.
  * **On-Premises or Hybrid (AWS Outposts):** AWS Outposts extends EC2 to on-premises data centers for a cloud-consistent, hybrid deployment managed by AWS, eliminating separate virtualization stacks. Ideal for data residency and low-latency needs.

## Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * *Information Security Specifications – Amazon EC2* (Refer to PepsiCo’s internal InfoSec baseline document for AWS EC2, which covers required security configurations, hardening standards, and compliance controls for EC2 instances.)
  * *Compliance Certifications:* Amazon EC2 meets standards like **PCI DSS Level 1, SOC 1/2, ISO 27001, FedRAMP, and HIPAA**, with customers responsible for compliant use and can access audit reports via AWS Artifact.

*(Ensure all EC2 deployments follow PepsiCo’s InfoSec policies – including hardened AMIs, disabling unnecessary services, regular patching, least privilege IAM roles, network segmentation, and monitoring – to maintain compliance and security posture.)*

## Ownership and Version Control

* **Service Architect:** Ramakrishna Ramaraju ([<EMAIL>](mailto:<EMAIL>))
* **Version Control:**

  * **v.1:** 30 Jul 2025 – Initial draft prepared for Amazon EC2 service catalog item (Ramakrishna Ramaraju)
