---
weight: 19
title: "DocumentDB"
date: 2025-08-13
tags: []
summary: ""
---

# Amazon DocumentDB (with MongoDB compatibility)

## Cloud Service Classification

**Category:** NoSQL Document Database  
**Cloud Provider:** AWS  
**Website:** [Technical documentation](https://docs.aws.amazon.com/documentdb/)

## Cloud Service Overview

**Name:** Amazon DocumentDB (with MongoDB compatibility)  
**Description:** Fully managed, MongoDB-compatible document database for JSON data. SSD-backed, 6× replication across 3 AZs, auto-scales up to 128 TiB per cluster. Decoupled compute/storage, high availability (99.99%), multi-AZ failover, up to 15 read replicas. ACID transactions, strong consistency, encryption at rest (KMS) and in transit (TLS), VPC isolation, IAM integration, RBAC, audit logging. Compliance: HIPAA, PCI DSS, ISO, SOC, FedRAMP.

### SKU Approval Status
  - PepsiCo Approved
    - Instance-based clusters (Standard & I/O-Optimized) – prod workloads (R6g, R5)
    - Burstable (t3/t4g) – dev/test only
    - Elastic Clusters – under evaluation
  - Not Approved: None (all standard offerings allowed)

### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

### Service Lifecycle
  - Release Date: Jan 9, 2019
  - Planned Decommission: None

## Usage Guidelines

### Features
  - MongoDB API compatible (3.6, 4.0, 5.0)
  - SSD-backed, auto-scales to 128 TiB/cluster
  - Multi-AZ, up to 15 read replicas
  - ACID transactions, strong consistency
  - PITR backups (35 days), manual snapshots
  - Encryption at rest (KMS) and in transit (TLS)
  - VPC isolation, IAM, RBAC, audit logging

### Sample Use Cases
  - JSON document storage, content management
  - Catalogs, user profiles, IoT, analytics
  - MongoDB migration/compatibility

### Limitations
  - No multi-master writes (single primary per cluster)
  - No native full-text search/analytics (use OpenSearch/Athena)
  - Max document size: 16 MB; max cluster: 128 TiB, 16 nodes
  - No server-side JS/stored procs; limited change streams/BI tool support

### Additional Guardrails
  - TBD

### Used By
  - TBD

### EA Declaration
  - NOT a declared standard

## Technical Guidelines

### Best Practices
  - Deploy 2+ instances across AZs for HA
  - Monitor CPU, memory, storage, connections
  - Use cluster endpoint; test failover
  - Set backup retention; test restores
  - Use private subnets, security groups, IAM, RBAC
  - Enable encryption at rest (KMS) and in transit (TLS)
  - Use Secrets Manager for credentials
  - No public internet for prod; use VPC peering/PrivateLink/VPN

### High Availability & Disaster Recovery
  - 6× replicated storage across 3 AZs; auto failover
  - At least one replica in a different AZ for HA
  - Global Clusters for cross-region DR (read-only secondaries)
  - Backups are region-specific; copy snapshots for DR

### Backup & Recovery
  - PITR backups (35 days), manual snapshots
  - Restores create new cluster; test restores regularly
  - Backups/snapshots encrypted if source is encrypted

### Access Control & Security Configuration
  - Use IAM for management API; least privilege
  - RBAC for DB access; rotate credentials with Secrets Manager
  - Encryption at rest (KMS) and in transit (TLS) always enabled
  - Enable audit logging to CloudWatch

### Network Connectivity Options
  - No public internet for prod
  - Use VPC peering, Transit Gateway, or PrivateLink
  - On-premises via VPN/Direct Connect
  - Open port 27017 only to required sources

### Networking & Security Architecture
  - TBD

### AWS Service Limitations
  - Single-region, single-primary writes
  - Max 16 nodes/128 TiB per cluster; 100,000 collections; 1,000 users
  - Max document size: 16 MB; index key: 2048 bytes
  - No server-side JS/stored procs; limited aggregation/joins
  - Restore from backup can be slow for large clusters

### SKU Features
  - Instance-based: Standard & I/O-Optimized (no I/O charge, higher base cost)
  - Elastic Clusters: Managed sharding, up to 4 PiB, millions of ops/sec
  - All clusters: 6× replicated storage, multi-AZ, PITR, encryption, monitoring

### Related Service
  - TBD

### Alternatives
  - TBD

# Compliance and Security Guidelines

### Security Baseline InfoSec:
  - Follow PepsiCo InfoSec baseline for DocumentDB

# Ownership and Version Control

### Cloud Solutions  Architect
  - <NAME_EMAIL>

### Version Control
  - v1.0: 12 Aug 2025 (Kalyan Battiprolu)
