---
weight: 14
title: "RDS"
date: 2025-08-01
tags: ["aws", "database"]
summary: "Relational Database Service"
---

# Amazon Relational Database Service (RDS)

## Cloud Service Classification

**Category:** Database
**Cloud Provider:** AWS
**Website**: [Technical documentation](https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/Welcome.html/)

## Cloud Service Overview

**Name:** Amazon Relational Database Service (RDS)
**Description:** Fully managed relational database service supporting MySQL, PostgreSQL, Oracle, and SQL Server. Automates setup,  scaling, patching, backups, and recovery. High availability (Multi-AZ), security best practices, and cost-efficient, resizable capacity. Developers use familiar engines/tools without infrastructure management.

### SKU Approval Status

  - PepsiCo Approved: MySQL, PostgreSQL, Oracle, SQL Server (Multi-AZ recommended for production)
  - Not Approved: MariaDB, Aurora, RDS Custom, RDS on Outposts (special review required)
### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

## Service Lifecycle
 - Release Date: October 26, 2009
 - Planned Decommission: None announced

## Usage Guidelines

### Features
  - Managed database engines: MySQL, PostgreSQL, Oracle, SQL Server
  - Automated provisioning, patching, backups, and recovery
  - Automated backups (point-in-time recovery, manual snapshots)
  - Multi-AZ high availability (synchronous standby, auto failover)
  - Read replicas for scaling (MySQL, PostgreSQL, MariaDB, Oracle)
  - On-demand scaling (compute/storage), storage auto-scaling
  - Security: Encryption at rest (KMS), in transit (SSL/TLS), VPC isolation, IAM integration
  - Monitoring: CloudWatch metrics, Enhanced Monitoring, Performance Insights
  - Compliance: HIPAA, SOC, ISO, FedRAMP

### Sample Use Cases
  - Web/mobile app backend, SaaS platforms
  - Enterprise database migration (Oracle, SQL Server)
  - Microservices/dev/test environments
  - Read-heavy analytics (read replicas)
  - Disaster recovery (cross-region replicas/snapshots)

### Limitations
  - No OS/superuser access; restricted admin privileges
  - Some native engine features unsupported (e.g., Oracle RAC, SQL Server SSAS)
  - Service quotas: 40 DB instances/region, 5-15 read replicas/primary, max storage per instance (64 TiB/16 TiB)
  - Performance: Network-attached storage, latency overhead for multi-AZ writes
  - AWS controls patching/failover; plan for maintenance windows

### Additional Guardrails
  - TBD

### Additional Guardrails
  - TBD
### EA Declaration
  - NOT a declared standard

# Technical Guidelines

### Best Practices 
  - Enable Multi-AZ for Production 
    Always configure production-grade databases with Multi-AZ deployment for high availability and automatic failover. This ensures that an up-to-date standby is available in a different AZ, minimizing downtime in case of infrastructure failure. Multi-AZ deployments also ensure maintenance operations (like backups or instance class changes) are done with minimal impact (by using the standby). AWS provides a higher service SLA (uptime commitment) for Multi-AZ instances (99.95% availability), reflecting the significant resilience gain.  
  - Monitor Resource Utilization and Scale Proactively
    Utilize Amazon CloudWatch metrics and alarms to keep track of DB instance health and capacity (CPU, memory, storage space, I/O throughput, replication lag, etc.). Set thresholds to warn if you are approaching limits (e.g. <20% free storage, high CPU for sustained periods, etc.). RDS allows scaling up instance size and storage, but these operations should be done ahead of demand. It’s recommended to **scale up** capacity when usage patterns indicate growth, with some headroom rather than running at the brink of capacity. Similarly, ensure you have enough memory so that the working set fits in memory – if CloudWatch shows consistently high Read IOPS, it might indicate the instance needs more RAM for caching. Proactive scaling avoids performance degradation and maintains availability.  
  - Optimize Backup and Maintenance Windows 
    Enable automated backups (with an appropriate retention period) on all RDS instances – this is critical for recovery. Configure the **backup window** to a period of low database activity (off-peak hours). During the backup window (for single-AZ deployments) the I/O on the primary may be briefly suspended, so choosing a time with minimal traffic reduces impact. With Multi-AZ, backups are taken from the standby to avoid impacting the primary workload. Also schedule the **maintenance window** (for patching and minor upgrades) to an off-peak time; while you can defer patches, it’s best to apply security and engine updates regularly. Maintain awareness of engine version lifecycle – plan upgrades for when your database engine versions approach end-of-support to stay compliant and supported (RDS can do minor version upgrades automatically if enabled).  
  - Use Appropriate Storage and IOPS
    Choose the storage type that matches your I/O requirements. General Purpose (gp2/gp3) SSD is cost-effective for most use cases, but if you have a very IO-intensive workload (large transaction rates or batch processing), consider **Provisioned IOPS (io1/io2)** storage to allocate sufficient IOPS. Ensure that the provisioned IOPS and instance class can support your workload’s throughput – insufficient IOPS can manifest as increased latency and will slow recovery in failover scenarios. If using gp3, you can tune the IOPS and throughput independent of size. Also, watch the Amazon CloudWatch **WriteIOPS/ReadIOPS** and **Freeable Memory** metrics: if you see prolonged high write IOPS or writes queued, you might need to increase IOPS or move to a larger instance class optimized for I/O.  
  - Connection Management
    Use connection pooling in your applications or employ Amazon RDS Proxy for workloads (such as serverless apps or PHP/Python apps) that open many short-lived connections. RDS Proxy is a managed connection pool that can improve efficiency by reducing the overhead on the database from frequent connect/disconnect, and it helps with fast failovers by preserving connections. It also integrates with AWS Secrets Manager and IAM for secure authentication, removing the need to embed credentials in application code. This is especially useful for Lambda-based applications or microservices that scale out and could otherwise exhaust the database’s connection limit. By maintaining a steady pool of connections, RDS Proxy can also reduce connection churn and mitigate reaching max connections.  
  - Set Conservative DNS Cache TTL 
    If your application or environment does any DNS caching for the RDS endpoint, ensure the DNS TTL is set low (Amazon RDS endpoints are DNS names). This is important because on failover (Multi-AZ) or endpoint change, the underlying IP address of the RDS instance will change. AWS recommends using a TTL of 30 seconds or less for DNS caching on the client side. Many language runtime DNS resolvers honor TTL; double-check this in your application stack. By respecting a short TTL, your application will more quickly reconnect to the new primary instance after a failover, minimizing downtime.  
  - Test Failover and Recovery Procedures
    Periodically test the RDS failover process to ensure your application can handle it. You can simulate a Multi-AZ failover by using the **Reboot with failover** option on the RDS console (in a non-production environment) to force the standby to become primary. Observe how long the failover takes and ensure your application reconnect logic properly retries and continues working after the DNS update. Also test restoring from backups/snapshots in a dev environment – verify that you can successfully restore data to a point in time and that the restored instance works as expected. These drills will validate that your backup retention is adequate and give confidence in recovery times for compliance (Recovery Time Objective, Recovery Point Objective).  

### High Availability & Disaster Recovery  
  - Multi-AZ Within Region
    Leverage Amazon RDS Multi-AZ deployments for high availability. In a Multi-AZ setup, RDS keeps a synchronous secondary in a different Availability Zone ready to take over if the primary fails. Failover happens automatically on events like instance crashes, AZ outages, or loss of network connectivity on the primary, typically completing in under a minute (applications simply reconnect to the same endpoint which now points to the new primary). Multi-AZ also greatly reduces downtime for planned maintenance – e.g. during patching or instance class scaling, RDS will update the standby first, then perform a controlled failover, limiting outage to the failover time. It’s important to design the application for transient connection interruptions and use of retries to smoothly handle the failover. With Multi-AZ, you can expect **99.95%** or higher availability per AWS’s SLA.  
  - Cross-Region Disaster Recovery
    RDS does not currently offer an automatic cross-region failover for the standard engines, so DR across regions requires planning. For engines that support read replicas (MySQL, PostgreSQL, MariaDB, Oracle), you can set up a **cross-region read replica** to keep a near-real-time copy of your database in another AWS region. This replica uses asynchronous replication, so it can lag seconds or more behind the primary. In a DR scenario (region-wide outage), you would manually promote the read replica in the secondary region to be the new standalone primary. This provides a recovery point with typically minimal data loss (dependent on replication lag) and a recovery time mostly dependent on how quickly DNS/clients can be repointed. Note that for Amazon RDS for Oracle, AWS supports an “Oracle Replica” feature (using Active Data Guard under the hood) for cross-region replication, available with BYOL licensing. RDS for SQL Server currently does not support cross-region replicas, so DR would involve restoring from backups or using AWS Database Migration Service for ongoing replication.  
  - Cross-Region Backups
    As an alternative or complement to live replicas, enable cross-region automated backups if supported (currently available for Oracle and SQL Server). When this feature is enabled, RDS will automatically copy snapshots and transaction logs from the primary region to a secondary region on a scheduled basis. This allows you to perform a point-in-time restore in another region, improving your Recovery Point Objective. For engines where cross-region automated backup isn’t native, you can script periodic copy of RDS snapshots to another region. In a disaster, you would restore the latest snapshot in the DR region and update application configurations to point to the new region’s endpoint. This approach typically has a higher RPO (e.g. last 24 hours if daily snapshots) but can be simpler and more cost-effective than maintaining a running replica.  
  - Backup Recovery in DR
    If using cross-region snapshots, test the restoration process in the target region to estimate how long it takes to spin up a new instance from snapshot (for a large database, this could be an hour or more – knowing this helps set expectations for RTO). Consider storing infrastructure-as-code (CloudFormation/Terraform) templates for redeploying the database and related resources in the DR region so you can quickly instantiate networking, security groups, parameter groups, and the restored RDS instance consistently.  
  - On-Premises DR / Hybrid
    In hybrid setups, you might use Amazon RDS as the DR target for an on-prem primary database or vice versa. For example, a production Oracle database on-prem could ship archived logs to an RDS Oracle instance via Data Guard (if licensing permits) or via AWS DMS. Ensure network connectivity (VPN/Direct Connect) and security are configured to allow continuous replication in such cases. While not a typical scenario, it’s possible to include RDS in on-prem DR plans, but careful attention is needed to engine versions and character sets to ensure compatibility during failover.  
  - Geo-Distributed and Multi-Region Architectures
    If your application requires multi-region active-active capabilities or globally distributed reads with minimal latency, consider using Amazon Aurora (a separate service) which offers Aurora Global Database and multi-master features beyond standard RDS. For standard RDS engines, an active-active multi-region setup at the database level isn’t supported (you would have one region active and the other on warm standby). Thus, at the application tier, you might need to implement a routing layer or use separate databases per region with data synchronization strategies if truly active-active is needed, acknowledging increased complexity.  

### Backup & Recovery  
  - Automated Backups
    Ensure automated backups are turned on for every RDS instance (by default this is enabled with a 7-day retention, but verify and adjust as needed). Automated backups allow point-in-time recovery (PITR) by combining daily snapshots and transaction log backups. You can configure the retention period up to 35 days. With PITR, you can restore your database to any second in the retention window, up until the last few minutes before a failure (RDS keeps the transaction logs such that the latest restore point is usually within ~5 minutes of current time). In the event of accidental data deletion or corruption, this feature is extremely valuable.  
  - Manual Snapshots
    In addition to automated backups, take manual snapshots at important points (e.g., before a major schema migration or application release). Manual DB snapshots are retained until you delete them, giving you an indefinite backup beyond the 35-day automated window. Snapshots are stored in S3 and are incremental (efficiently stored). You can restore an RDS instance from a snapshot at any time, creating a new instance with the data as of the snapshot. This can be used for cloning databases for testing or for long-term archiving of a database state (e.g., for regulatory retention).  
  - Snapshot Management
    Implement a strategy for snapshot management and cross-region copies if needed. For critical data, you might periodically copy snapshots to another region to protect against regional disasters (manual or using automated backup copy features). These snapshots in another region can be used to restore the DB in that region if the primary region is unavailable. There are AWS Lambda and Backup solutions that can automate snapshot copying and retention policies.  
  - Restore Testing
    Regularly test the restoration process from backups. For example, at least quarterly, perform a test restore of an automated backup to validate that the backups are recoverable and to measure how long the restore takes for your database size. This also validates that your team knows the steps to restore (via AWS Console or CLI) under pressure.  
  - Transaction Logs and Binlogs
    Be aware of how RDS handles transaction logs. For MySQL/MariaDB, the binary logs (binlogs) can be retained (there’s a setting for how many days to retain them) which facilitates point-in-time restore and can be used for your own auditing or replication needs. For PostgreSQL, the WAL segments are managed automatically. Ensure you don’t inadvertently disable backups or purge logs needed for PITR. In Oracle, archived redo logs are used similarly. RDS manages these under the covers, but if you’re doing any custom setup (like logical replication slots in PostgreSQL or using Oracle Data Pump for exports), coordinate with backups so as not to impact performance or consume excessive storage from retained logs.  
  - Retention and Purging
    Set an appropriate retention period balancing business requirements and cost. Longer retention (e.g., 35 days) gives more safety but consumes more storage for backup (note: backup storage up to the size of the database is included at no additional charge, but beyond that you incur costs). Monitor your “Total Backup Storage” in AWS billing – if you have large manual snapshots or long retention, this could grow. Delete snapshots that are no longer needed (after ensuring they’re not the only copy of some needed state). Also consider enabling the “Delete Protection” option on production RDS instances – this prevents accidental deletion of the instance (you must disable the protection to delete), thereby reducing the chance of losing a database without having taken a snapshot.  
  - Recovery Procedure
    Document the step-by-step procedure to recover a database, whether from an automated backup (point-in-time restore) or from a snapshot. Note that a restore from backup creates a **new** DB instance – it does not overwrite the existing one. So your runbook might involve restoring to a new instance, then redirecting applications or promoting that instance to replace the old. For PITR, you will specify a date/time to recover to, and AWS will create a new instance with a new endpoint. If doing a full DR, you might restore the snapshot in another region and update application configurations to point to the new region’s endpoint. Having this documented and scripted if possible will greatly speed up your response in an actual disaster.  

### Access Control & Security Configuration 
  - Network Isolation (VPC)
    Deploy RDS databases in private subnets of your Amazon VPC so they are not directly reachable from the public internet. By default, when you create an RDS instance you should set “Publicly Accessible = No” for production instances, which ensures the DB instance only has a private IP and can only be reached from within the VPC (e.g. from EC2 application servers, or through a VPN/Direct Connect from on-prem). Use VPC security groups to tightly control inbound access to the database – for example, allow only application server instances or specific IP ranges to connect to the DB’s port. This minimizes the attack surface.  
  - Encryption
    Always enable **encryption at rest** for RDS instances that store sensitive data (and as a general best practice for all data). RDS uses AWS KMS for key management: you can choose an AWS-managed key or a customer-managed CMK to encrypt the storage of the database. When encryption is enabled, all data on disk, snapshots, and backups are encrypted. Note that you must enable this at creation time (you cannot enable encryption on an existing unencrypted instance without creating a new encrypted copy). Also enforce **encryption in transit** by requiring SSL/TLS for database connections. RDS provides an SSL certificate for each DB instance; you can download AWS’s root CA and verify clients. For PostgreSQL and MySQL, you can enforce SSL by parameter (rds.force_ssl=1). For SQL Server and Oracle, clients should be configured to use SSL. This ensures data is encrypted on the network and guards against eavesdropping.  
  - IAM for Resource Access
    Use AWS Identity and Access Management (IAM) to control who (which IAM users/roles) can perform RDS actions (like creating or modifying instances, taking snapshots, etc.). Follow least privilege – e.g., the team responsible for databases can be granted permission to modify RDS instances, while other teams might only get read-only monitoring access. Do not use root AWS credentials for administrative tasks – create individual IAM users or assume roles for that. IAM allows you to audit and tightly govern any changes to the RDS infrastructure.  
  - Database Authentication and Authorization
    For the database login itself, avoid using the master/admin account for regular application access. Create specific database users with least privileges needed for the application. RDS for MySQL and PostgreSQL can integrate with IAM authentication – this allows you to connect to the database using an IAM token (and credentials via IAM roles) instead of a native password, which can simplify credential management. If IAM DB auth is used, ensure your application obtains fresh tokens and that the latency of token generation is acceptable. For Oracle and SQL Server, you can integrate RDS with AWS Managed Microsoft AD to use Windows Authentication (Kerberos) for database logins. This is useful to centrally manage users via Active Directory. If using AD integration, ensure the RDS instance is joined to the domain and that you manage the rotation of the service account password (AWS does this for managed AD).  
  - Secrets Management
    Store database credentials (for the master user or any service accounts) in AWS Secrets Manager rather than in plaintext in code or config files. Secrets Manager can automatically rotate credentials for RDS databases with a Lambda function integration. Enabling rotation (for example, every 90 days) helps comply with security best practices. When using Secrets Manager, your applications can pull the latest credentials at runtime. Make sure to update connection strings to use the secret reference or an API call to fetch the secret.  
  - Logging and Auditing
    Enable database logs and integrate them with AWS CloudWatch Logs for analysis and retention. In RDS, you can choose to publish logs like general logs, slow query logs (MySQL/PostgreSQL), audit logs (Oracle, SQL Server) to CloudWatch. For example, enable MySQL slow query log to identify inefficient queries. For Oracle and SQL Server, consider enabling Fine-Grained Auditing or Extended Events, respectively, to track security-relevant actions, and have those logs uploaded. Additionally, AWS offers **Database Activity Streams** for RDS (currently for Oracle and SQL Server engines) which provides a near-real-time stream of audit events to security monitoring tools (integrating with Kinesis streams). If your InfoSec requirements are high, enabling Database Activity Streams can ensure all SELECT/DDL/DML activities are recorded and securely stored outside the DB (note: this may incur additional costs and slight overhead).  
  - Security Patches
    RDS will apply critical security patches to the database engine as part of its managed service (during the scheduled maintenance window). It’s important to stay on relatively current engine versions to receive these patches. Track the RDS engine version in use and review AWS security bulletins or RDS release notes for any vulnerability fixes. If RDS schedules a mandatory upgrade (this can happen if an engine version is deprecated or has a severe security issue), plan and test accordingly.  
  - Data Masking and Access Control
    Within the database, implement role-based access for users. Use read-only accounts for read-only applications or reporting, and separate accounts for writing data. If applicable, use column-level encryption or data masking for sensitive fields at the application level. While not an RDS-specific feature, it complements RDS security by limiting exposure in case credentials leak.  
  - Firewall and Connectivity
    Double-check that database security groups do not unintentionally allow broad access. For example, avoid rules that allow access from “0.0.0.0/0” (anywhere) to the database port. A common best practice is to allow access only from application server security group(s). When allowing on-prem access via VPN, restrict to specific on-prem IP ranges. Use AWS Security Hub and Config rules which have checks for RDS, such as whether the RDS instance is publicly accessible or not. These can alert you if, for instance, someone inadvertently made an instance publicly accessible or left the security group too open.  

### Network Connectivity Options 
  - Public Endpoint
    NOT ALLOWED: for production or sensitive databases. By default, RDS can allocate a public IP if “Publicly Accessible” is set to yes, but this should be avoided for PepsiCo use cases with Confidential/Restricted data. A public endpoint would mean the database is reachable from the internet, increasing exposure. If a public endpoint must be used (for example, in a development environment or for a specific use-case), it must be tightly restricted: ensure the security group only allows connections from specific IP ranges (e.g., corporate IPs or VPN egress ranges) and always require SSL. In general, treat public accessibility as a last resort – connecting through a bastion host or VPN is preferred.  
  - VPC Private Subnets
    The recommended deployment is to place RDS instances in **private subnets** of your VPC. In this configuration, the DB instance gets a private IP and is only reachable within the VPC (or via network links to the VPC). Application servers in the same VPC (or peered VPCs) can connect directly. For on-premises connectivity, you would use an AWS VPN or Direct Connect to the VPC. Private subnet deployment greatly reduces risk, as no traffic from the open internet can reach the database.  
  - VPC Peering / Transit Gateway
    If your application servers reside in a different VPC (for example, a separate VPC for microservices or a central VPC for database services), you can use VPC Peering or AWS Transit Gateway to enable connectivity. VPC Peering establishes a direct network route between VPCs (must handle non-overlapping IP ranges). Transit Gateway can connect multiple VPCs and on-prem networks in a hub-and-spoke manner. Ensure security groups on RDS allow the traffic from those peered VPC CIDRs or specific security groups. Note that security group references across VPCs are not possible unless using the same Transit Gateway and inbound rules with source as CIDR may be needed.  
  - AWS PrivateLink for RDS
    AWS PrivateLink (Interface VPC Endpoints) is typically used to access AWS services without crossing the internet. In the case of RDS data plane, since your RDS is already in your VPC, PrivateLink isn’t applicable to connecting to your own RDS instance. (There is a feature called **RDS Data API** for Aurora Serverless which uses a PrivateLink endpoint, but that’s outside the scope of standard RDS engines.) However, note that **RDS Proxy** – if used – is deployed as an ENI in your VPC, so it’s already private. The RDS **control plane** (API calls to manage RDS) can be accessed via an AWS Interface Endpoint for “rds.amazonaws.com” if you need to ensure those API calls stay within AWS network (for compliance). This is more of a management consideration.  
  - On-Premises Access
    To connect on-prem systems or developer machines to an RDS in a private subnet, use a **Site-to-Site VPN** or **AWS Direct Connect**. Through these, your on-prem network is extended into the VPC, and the RDS private IP can be accessed as if it were on an internal network. Alternatively, AWS Client VPN or a bastion host can be used for individual developer access. Avoid opening database ports to the public; instead, require users to VPN in, then connect.  
  - Security Group Configuration
    Define inbound rules to only allow the necessary port (e.g., TCP 3306 for MySQL, 5432 for PostgreSQL, 1521 for Oracle, 1433 for SQL Server) from known sources. Use security group references when possible (e.g., allow the app servers’ security group). Deny all others. There is no need for outbound rules on the DB security group in most cases (the default outbound “allow all” is fine, RDS will egress for patches/updates as needed). If using Active Directory integration, ensure the RDS instance can reach the AD domain controllers.  
  - Network Monitoring
    Consider enabling VPC Flow Logs for database subnets to record network traffic metadata. Although not inspecting payload, flow logs can be useful to detect any unusual IPs trying to connect or for auditing connections (source IPs, timestamps). Also, AWS GuardDuty RDS Protection can be enabled (for Aurora engines currently) to detect anomalous login attempts; for non-Aurora, GuardDuty will still alert on suspicious network patterns at the EC2 infrastructure level.  

### Networking & Security Architecture
  - TBD

### Amazon RDS Service Limitations  
  - Instance and Storage Limits
    An RDS DB instance has a maximum storage limit depending on engine (as noted, 64 TiB for MySQL, PostgreSQL, MariaDB, Oracle; 16 TiB for SQL Server). If an application requires more than this, sharding or using Aurora (which can go to 128 TiB) might be necessary. Also, each instance has an upper limit on IOPS based on storage type (for gp2 it’s tied to volume size, for gp3 you can provision up to 16,000 IOPS per volume, and io1/io2 up to 256,000 IOPS with largest instances). There are also **maximum throughput** limits per instance (for example, certain instance classes can achieve only a certain MB/s to storage). These are documented in AWS’s instance specs and should be considered for very high throughput systems.  
  - Connection Limits
    The maximum number of concurrent database connections is governed by the **DB engine and instance class**. For example, a small instance may only allow a few dozen to a few hundred connections (MySQL on db.t3.micro ~60 connections), whereas a large instance can allow thousands. Hitting the max connections results in errors for additional connection attempts. This can be mitigated by proper connection pooling. The specific formulas for connection limits vary (MySQL’s `max_connections` is tuned based on memory and parameter group setting; PostgreSQL has a default typically 100, but can be increased at cost of memory; Oracle and SQL have their own limits per edition). Plan for these limits by using RDS Proxy or application pools as described in Best Practices.  
  - Read Replicas & Replication
    Standard RDS (non-Aurora) read replicas use asynchronous replication. This means there is **replication lag** to consider – under heavy write load, replicas can fall seconds or more behind. They are not guaranteed to be in sync at all times. Also, **automatic failover is only for Multi-AZ**; read replicas do **not** auto-promote on primary failure. Promotion of a read replica is a manual decision (or needs a custom automated solution) in a disaster. This is a limitation compared to some on-prem replication or Aurora, where replicas can be nearly synchronous or auto-failover. Additionally, for MySQL/PostgreSQL, RDS allows up to 5 replicas by default (15 with approval) – if more read scaling is needed beyond that, or if an active-active multi-region is needed, the limitation would necessitate considering Aurora or redesigning the architecture (e.g., using database clustering at application level).  
  - Lack of Multi-Master 
    RDS (for the engines in question) does not support multi-master writes. There is only one writable primary in a given DB instance or cluster. If an application requires a multi-master (multi-writer) setup for write availability or geo-distribution, RDS cannot provide that (Aurora Multi-Master is an option for certain use cases, but again that’s Aurora). Thus, applications need to be designed for single-master with failover. This is typically fine for most OLTP apps, but worth noting as a design constraint.  
  - Feature Parity and Extensions
    RDS supports many (but not all) plugins/extensions of the databases. For example, in PostgreSQL, RDS supports a long list of the common extensions (PostGIS, pg_cron, pg_partman, etc.), but if your application requires a PostgreSQL extension that RDS doesn’t support, you cannot install it yourself (no superuser access). One recent improvement is **Trusted Language Extensions (TLE) for PostgreSQL** on RDS, which allows safe deployment of custom extensions in a controlled manner – but this is still evolving. In SQL Server, RDS does not support installing custom CLR assemblies in UNSAFE mode, and in Oracle you cannot load custom C libraries or agents. Essentially, anything that would require OS access or could interfere with the managed nature is not allowed. If such features are absolutely required, the alternative is to use RDS Custom (which gives you more access at the cost of some management) or run the database on EC2.  
  - Old Version Support and Deprecation
    AWS periodically deprecates older engine versions on RDS. When a version is deprecated, you’ll be given a timeframe to upgrade before AWS auto-upgrades it. This is a limitation in the sense that you can’t stay on an out-of-support version for extended periods if AWS decides to retire it. In on-prem, some companies run older versions longer (with or without vendor support), but in RDS you must plan to upgrade when AWS schedules it. Generally this is good (for security), but be mindful of testing your applications with newer versions in time.  
  - No RAC or Shared Disk Clustering
    As mentioned, Oracle RAC is not supported. Microsoft SQL Server failover clustering (the multi-instance cluster using shared storage) is also not supported – instead RDS uses SQL Always On AG under the hood for Multi-AZ. If you have a requirement for a clustered database with shared disk, RDS cannot meet that. Likewise, if you rely on Oracle’s advanced features like RAC, or certain edition-specific features that require sys privileges, those would be limitations.  
  - Integration Limitations
    Certain integrations that require special agents or access might not work on RDS. For instance, you cannot install third-party backup agents, monitoring agents, or data replication tools on the RDS host. You must use what RDS provides (or run such tools externally). For example, Oracle Recovery Manager (RMAN) backups to S3 are not directly available, you rely on RDS snapshots. If using a tool like Oracle GoldenGate, it can be used for replication *to* RDS (with some configuration) but not *from* RDS unless using the RDS-provided CDC extracts or logical replication. Always verify compatibility of any peripheral tools with RDS’s managed environment.  

### SKU Features 
  - Burstable Classes
    These are cost-effective instances designed for dev/test and light workloads. They provide baseline CPU performance with the ability to burst CPU usage when needed, governed by a credit system. **Use Case**: Development or QA environments, or small microservices databases, where consistent high CPU is not required. *Guardrail*: In production, burstable instances are generally not recommended for sustained workloads since if the CPU needs are constant, they could be throttled when credits run out. (Within PepsiCo, treat T-class instances as for non-prod or very small prod instances only.)  
  - General-Purpose Classes 
    These instance families provide a balanced mix of CPU, memory, and network resources for a broad range of applications. They are the “standard” class for most production databases. For example, db.m5.large has 2 vCPU and 8 GiB RAM – suitable for moderate workloads. Newer generations (m6g, m7g with Graviton CPUs, or m7i with Intel) offer improved price-performance. General-purpose instances are a good default choice, offering stable performance for many OLTP workloads.  
  - Memory-Optimized Classes
    These instance types offer a higher memory-to-CPU ratio, ideal for memory-intensive workloads like large in-memory caches or analytics, and they typically also have higher network throughput. **R5/R6g** instances deliver ~5-8x more memory per vCPU than general-purpose and are ideal when your working set is large (e.g., large buffer pools for a database). **X-class** instances (x1e, x2idn/x2iedn, etc.) provide extremely large memory (up to 4 TB) and are suited for in-memory databases or decision support systems. **Z1d** is a special memory-optimized class that also provides very high single-threaded CPU clock speed – useful for workloads with expensive per-core licensing (like Oracle or SQL Server Enterprise) because you get strong performance per vCPU. *Use Case*: Choose memory-optimized classes for production workloads that are bottlenecked by memory or that benefit from caching the entire dataset in RAM.  
  - Compute-Optimized Classes
    These have higher vCPU count relative to memory and might be considered if you have CPU-intensive stored procedures or computations in the database that don’t need as much memory. They are not a primary choice unless a specific CPU-bound scenario is identified.  
  - Storage Options
    RDS provides three storage types for most engines – **General Purpose SSD (gp2/gp3)** for cost-effective balanced performance, **Provisioned IOPS (io1/io2)** for high consistent I/O performance, and previously **magnetic** (standard) which is legacy and not recommended. Most workloads should start with gp3 as it allows tuning of IOPS and throughput independent of size. If the workload demands >16,000 IOPS or very low latency, io1/io2 with provisioned IOPS up to 256k and multi-AZ synchronous replication might be needed. Use of io2 Block Express on RDS (for extremely high performance needs) is available in some regions – this can give higher durability and IOPS but at higher cost. For SQL Server, also note max storage is 16 TiB on RDS due to underlying limitations, regardless of SSD type. *Best Practice*: Use General Purpose (gp3) for most use cases; monitor I/O latency and if average queue > 0 or read/write latency grows, consider increasing IOPS or moving to provisioned IOPS storage.  
  - Multi-AZ Deployment Options
    The standard Multi-AZ (one standby) is available for all engines. Newer “Multi-AZ DB Cluster” deployment with **two readable standbys** is available for MySQL, PostgreSQL, and Oracle, which uses three AZs and can provide up to 2 additional read endpoints and faster failover (typically under 35 seconds failover and lower replica lag). This is considered an enhanced SKU – it’s essentially a cluster of 3 nodes managed by RDS. If ultra-high availability is required and the engine supports it, this Multi-AZ cluster option might be used (subject to approval as it may cost more).  
  - Licensing Considerations
    We avoid the details of licensing SKUs here as per guidance.  
 

### Related Service
  - TBD

### Related Service 
  - Amazon Aurora
    A cloud-optimized relational database service from AWS that is compatible with MySQL and PostgreSQL. Aurora is often seen as an upscale alternative to RDS for those engines, offering higher performance (5x+ throughput of MySQL on same hardware), auto-scaling storage up to 128 TiB, faster replication (up to 15 read replicas with sub-second lag), and global database capabilities with cross-region replication under a second. If an application requires features like writer failover in under 30 seconds, reader auto-scaling, or multi-region replication with minimal lag, Aurora might be a better fit. However, Aurora has its own cost considerations and is a distinct service; standard RDS is simpler and may be cheaper for small instances.  
  - Self-Managed on EC2
    Instead of using RDS, one could install and manage the database on an EC2 virtual machine (or on-premises). This gives full control (including OS access, custom configurations, and support for features RDS doesn’t allow). For example, if an organization needs Oracle RAC for high availability or needs to run an older database version that RDS doesn’t support, they might opt for managing Oracle on EC2. The trade-off is significantly more management effort and risk of human error (patching, backups, clustering all become your responsibility). Generally, RDS is preferred unless there is a compelling reason for self-managing (unsupported feature or extreme customization needs).  
  - Other AWS Database Services
    Depending on use case, a non-relational database might sometimes be a better choice. For instance, **Amazon DynamoDB** (a fully managed NoSQL key-value and document database) can be an alternative if the data access pattern is simple lookups or if the application needs massive scale-out with near-infinite throughput (e.g., for user session storage, caching, or IoT data). DynamoDB offers global distribution and zero admin, but it’s not SQL – so this is only an alternative if eventual consistency or non-relational model is acceptable. Another service, **Amazon Redshift**, is a petabyte-scale cloud data warehouse; if the primary need is complex analytical queries across large datasets (OLAP workloads), offloading those to Redshift may be better than straining an OLTP database with analytics.  
  - Hybrid or Multi-Cloud
    In scenarios where applications are spread across cloud providers, one might consider using cloud-agnostic approaches. For example, running MySQL or PostgreSQL in containers (e.g., on Kubernetes) or using a service like Cloud SQL (GCP) or Azure Database for PostgreSQL if integration in those clouds is tighter. However, these are generally not needed unless there is a multi-cloud requirement. Within AWS, RDS/Aurora is the recommended relational solution. If the requirement is to avoid vendor lock-in, an alternative is to use open-source databases on EC2 or containers so you can easily migrate, but that re-introduces management overhead.  
  - In-Memory Data Stores
    For use cases where the main requirement is microsecond latency or millions of operations per second on simple key-value access, an in-memory store like Amazon ElastiCache (Redis or Memcached) might complement RDS. It’s not a replacement for durable relational storage, but can alleviate load on RDS by caching frequent reads. Designing with a caching layer can be an alternative approach to scaling reads instead of adding many replicas to RDS.  

## Compliance and Security Guidelines

### Security Baseline InfoSec:  
  - Information Security Specifications – Amazon RDS

# Ownership and Version Control

### Cloud Solutions Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 7 Apr 2025 (Kalyan Battiprolu)
