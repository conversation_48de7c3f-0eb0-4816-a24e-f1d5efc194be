---
weight: 17
title: "SNS"
date: 2025-08-07
tags: []
summary: ""
---

# Cloud Service Classification

* **Category of the Service:** Messaging
* **Cloud Provider:** Amazon Web Services (AWS)

# Cloud Service Overview

* **Name:** Amazon Simple Notification Service (SNS)

* **Description:** Amazon SNS is a highly available, fully managed pub/sub messaging service for both application-to-application (A2A) and application-to-person (A2P) communications. It enables decoupling of microservices and distributed systems by providing topics for high-throughput, push-based, many-to-many messaging. Publishers send messages to an SNS topic, which then fans out the messages to multiple subscribing endpoints in parallel, including AWS services (such as SQS queues, Lambda functions, or HTTP/S webhooks) and user notification endpoints (such as SMS text messages, mobile push notifications, or email). This flexible, event-driven model allows applications to scale and communicate asynchronously without direct dependencies.

* **SKU Approval Status:**

  * **PepsiCo Approved:**

    * Standard Topics (general-purpose SNS topics with high throughput, best-effort ordering)
    * FIFO Topics (First-In-First-Out topics for strict ordering and deduplicated delivery)
  * **Not Approved:**

    * *(None)*

* **Allowed PepsiCo Data Classification:**

  * Public
  * Internal
  * Confidential
  * Restricted

# Service Lifecycle

* **Release Date:** April 7, 2010
* **Planned Decommission Date:** No announced decommissioning plans.
* **Decommission Date:** Not applicable at this time.

# Usage Guidelines

* **Features:**

  * **Managed Pub/Sub Topics (Fan-Out):** Amazon SNS provides *topics* as managed endpoints for publish/subscribe messaging. A message published to a topic is automatically delivered to all subscribers of that topic, enabling *fan-out* to multiple services or users simultaneously. This allows one-to-many broadcast of events and decoupling of producers and consumers in a distributed system.
  * **Multiple Endpoint Types (A2A & A2P Delivery):** SNS supports delivery to a wide range of subscriber types. For application-to-application integration, SNS can push messages to AWS services like SQS queues, AWS Lambda functions, and HTTPS endpoints. For application-to-person notifications, it can send out messages via SMS text messages, mobile push notifications (to Apple/Google push services), and email. This multi-protocol capability means a single message can reach both internal systems and end-users as needed.
  * **Message Filtering and Attributes:** SNS allows the use of message attributes and subscription filter policies to tailor which messages each subscriber receives. Publishers can tag messages with attributes, and subscribers can define filter policies so they only get notifications matching certain criteria. This built-in message filtering offloads logic from subscribers and prevents unnecessary traffic by ensuring each subscriber is only sent relevant messages.
  * **FIFO Topics (Ordered, Exactly-Once Delivery):** SNS offers FIFO topic types for workloads that require strict message ordering and deduplication. FIFO topics preserve the order in which messages are published and ensure each message is delivered *exactly once* to subscribers (duplicates are eliminated within a 5-minute interval). This is ideal for use cases like financial transactions or inventory updates that cannot tolerate out-of-order or duplicate messages. FIFO topics support message grouping (using message group IDs) to order related sequences of messages independently. (Note: FIFO topics currently support delivery to Amazon SQS FIFO queues as subscribers, for ordered processing downstream.)
  * **High Durability and Retry Mechanisms:** Amazon SNS is designed for high durability. It stores published messages across multiple Availability Zones within a region to protect against infrastructure failure. The service implements automatic **retry policies** for message delivery – if a subscriber endpoint (e.g. an HTTPS webhook or email server) is unreachable, SNS will retry delivery for a configurable period. For certain protocols, **delivery status logging** can be enabled to Amazon CloudWatch for monitoring successes or failures. In addition, SNS supports configuring **Dead-Letter Queues (DLQs)** using Amazon SQS – undeliverable messages (those that exhaust all retry attempts) can be routed to an SQS queue for later analysis or reprocessing.

* **Sample Use Cases:**

  * **Decoupled Microservice Communication:** Use SNS topics to publish events that need to be consumed by multiple microservices in parallel. For example, an e-commerce application can publish an “Order Placed” event to an SNS topic, which fans out to inventory services, billing systems, and notification services simultaneously, ensuring all systems react to the event in near real-time. This fan-out pattern via SNS makes the architecture more scalable and fault-tolerant (each component receives the message independently).
  * **Application and System Alerts:** SNS is often used for alerting and monitoring. Cloud services like Amazon CloudWatch Alarms can trigger SNS notifications to send critical alerts. For instance, if an application server’s CPU is high or a business transaction fails, an SNS topic can broadcast an alert to on-call engineers via multiple channels – such as an email to a distribution list, an SMS text message for urgent attention, and even invoke a Lambda function to create an incident ticket.
  * **User Notifications (Mobile, SMS, Email):** Applications leverage SNS to deliver messages to end-users on various channels. For example, a mobile app can use SNS to send push notifications to Apple Push Notification service or Google Firebase for all users of the app (via SNS mobile push integration), or a backend system can send transactional SMS messages (using SNS’s SMS capability) for multi-factor authentication codes, and send emails for account alerts. SNS manages the complexities of interfacing with telecom providers and email delivery, allowing developers to reach users globally with minimal effort.
  * **Ordered Event Processing for Critical Data:** In scenarios like financial systems, stock trading, or inventory management, the order of events is critical. SNS FIFO topics can be used so that events are published in order and delivered to exactly-once processing workflows. For example, a stock trading platform can publish trade events to a FIFO topic to ensure that downstream accounting services process transactions in the exact sequence they occurred. By subscribing an SQS FIFO queue (or a chain of them) to the topic, the system gains a durable, ordered buffer for these events, and a processing service can consume from the queue knowing the order is preserved. This pattern ensures consistency and correctness in processing sequences of events that must be strictly ordered.

* **Limitations:**

  * **Standard Topics – No Ordering Guarantees & Possible Duplicates:** Amazon SNS *Standard* topics provide an at-least-once delivery model. This means messages might occasionally be delivered out of order or more than once to subscribers. Applications using standard topics should be designed to handle potential duplicate messages and unordered delivery (e.g., by implementing idempotent processing and using message timestamps or sequence numbers if ordering matters). If strict ordering or exactly-once delivery is required, use SNS FIFO topics instead.
  * **FIFO Topics – Throughput and Subscription Limits:** FIFO topics trade off some performance and scalability for ordering guarantees. They have a **lower throughput** limit of up to 3,000 messages per second (or 10 MB per second) per topic by default, and a maximum of 300 messages per second *per message group* (when using message grouping). Also, each FIFO topic supports at most 100 subscriptions (compared to millions for standard topics). This makes FIFO topics unsuitable for extremely high fan-out scenarios. Ensure your workload’s throughput and fan-out requirements fit within these limits, or consider standard topics if higher scalability is needed.
  * **Message Size Limits:** SNS imposes a maximum message payload size of **256 KB** for a single publish action. If an application needs to send larger payloads, it must use workarounds such as the **SNS Extended Client Library**, which stores the payload in Amazon S3 and publishes only a reference to it (supporting payloads up to 2 GB). This adds complexity and potential latency for large messages. In general, avoid sending extremely large messages through SNS; instead send a reference (e.g., an S3 object key) when possible.
  * **No Built-in Message Persistence for Standard Topics:** SNS is a push-based transit system and does not persist messages once delivered (standard topics do not archive messages). If a subscriber is unavailable and misses messages (beyond retry attempts), those messages are not stored by SNS (unless a dead-letter queue is configured). This means SNS alone is not a substitute for durable message storage or long-term event logging. For **guaranteed processing**, it’s a best practice to use an Amazon SQS queue as a subscriber to SNS (so that messages are queued durably for consumption). Note that SNS FIFO topics do support message archiving for up to 365 days, but this is not available for standard topics.
  * **Region Scope and Disaster Recovery:** SNS topics are region-scoped resources; there is **no native cross-region replication** of topics. While SNS can deliver messages to some cross-region targets (for example, you can subscribe an SQS queue in another region to an SNS topic), there is no automatic failover of an SNS topic to another region. In practice, achieving cross-region redundancy requires manually publishing duplicate messages to a topic in a secondary region or using higher-level AWS services (like EventBridge global endpoints) to route events. This limitation means architecture designs must account for region failures if globally distributed notifications are required.
  * **Protocol-Specific Constraints:** Some SNS delivery protocols have their own limitations. For instance, email deliveries via SNS are throttled to **10 messages per second** per account, and SMS deliveries are throttled to **20 messages per second**. Large-scale email or SMS campaigns may require spreading messages over time or using dedicated services (like Amazon SES for email) for higher throughput. Additionally, SMS messaging incurs spending limits by default for new AWS accounts (a monthly cap on spending) which must be raised via support ticket for production use. Also, mobile push notifications via SNS are subject to the limits and reliability of third-party push services (APNS, FCM); delivery is not guaranteed by AWS if the device is offline or the push service fails to deliver.

* **Additional Guardrails:**
  *TBD*

* **Used By:**
  *TBD (list of internal applications using SNS)*

* **EA Declaration:**
  *NOT a declared standard.* (SNS is available for use but has not been declared an official standard service by Enterprise Architecture)

# Technical Guidelines

* **Best Practices:**

  * **Appropriate Topic Type & Idempotent Processing:** Choose the SNS topic type that suits your workload. Use **Standard topics** for most use cases that require high throughput and can tolerate eventual consistency (possible duplicates or out-of-order messages). Reserve **FIFO topics** for cases needing strict ordering and exactly-once delivery, and plan for the lower throughput and subscriber limits in those cases. All subscriber applications should be designed for **idempotency** and resiliency, especially with standard topics – for example, use unique message IDs or content de-duplication on the consumer side so that processing a duplicate message has no adverse effect.
  * **Leverage Message Attributes and Filtering:** Structure your published messages to include relevant **message attributes**, and define **filter policies** on subscriptions so that each consumer only receives the messages it needs. This avoids “broadcasting” everything to all subscribers and then filtering in the consumer logic, thereby reducing unnecessary traffic and processing. For instance, if an SNS topic is used for multiple event types, use an attribute like “eventType” on messages, and subscribers can filter on this attribute (e.g., only receive `eventType = OrderShipped`). This practice keeps architectures efficient and costs down by minimizing superfluous messages.
  * **Integrate SQS or Persistence for Critical Data:** For workflows that require guaranteed delivery or delayed processing, integrate **Amazon SQS** queues with SNS. A common pattern is an SNS topic fanning out to one or multiple SQS queues which act as durable buffers for subscribers. This ensures that even if a subscriber service is down, the messages are stored in the queue for later processing. Additionally, consider subscribing a Kinesis Data Firehose stream (which can load into S3, Redshift, etc.) to SNS for audit logging of all messages. By having an archive of messages, you can replay or analyze past notifications if needed.
  * **Monitor and Scale Proactively:** Make use of AWS CloudWatch metrics and logs to monitor SNS usage. Track metrics such as NumberOfMessagesPublished, NumberOfNotificationsDelivered, and NumberOfNotificationsFailed for each topic. Enable **delivery status logging** for protocols that support it (e.g., HTTP/S, Lambda) to CloudWatch Logs, so you are alerted to any delivery failures. Also monitor SMS usage logs if using SMS. Set up alarms for unusual drops in delivery success or spikes in failure rates. **Plan for capacity** by observing usage against AWS SNS quotas – if your application is approaching the default publish rate limits (e.g., nearing thousands of messages per second), request a quota increase in advance. For extremely high throughput systems, consider sharding messages across multiple topics (though usually not needed, as AWS can increase SNS throughput limits on request).
  * **Security and Least Privilege:** Treat SNS topics as sensitive resources and secure them following AWS best practices. Ensure topics are not inadvertently made public – **do not allow anonymous publish/subscribe** access. Use IAM policies and topic policies to grant access to specific principals (e.g., allow only your applications or AWS services to publish, and only needed accounts or roles to subscribe). Implement the principle of least privilege by granting only the necessary SNS actions (Publish, Subscribe, etc.) on specific topics to each role/application. *Never* embed AWS secret keys in client apps for SNS access; instead use IAM roles (for EC2, ECS, Lambda, etc.) or Amazon Cognito federated identities for mobile apps to obtain temporary credentials. By limiting who can interact with topics, you reduce the risk of data exfiltration or unwanted messages.

* **High Availability & Disaster Recovery:**

  * **In-Region Redundancy:** Amazon SNS is architected to be highly available within an AWS region. Messages published to SNS are stored durably across multiple **Availability Zones** (data centers) in that region by default. This multi-AZ replication means that even if one data center fails, SNS can continue to operate using copies of the data in other AZs, providing fault tolerance without any user intervention. SNS abstracts this redundancy, so from the user perspective, publishing and delivery continue normally even during an AZ outage.
  * **Cross-Region Resilience:** Because SNS topics are regional, achieving disaster recovery across regions requires architectural planning. There is **no automatic failover** for SNS to another region. If you have a critical messaging system, consider **active-active or active-passive approaches** across regions. For example, you might deploy a duplicate SNS topic in a second region and have critical events published to both regions (active-active fan-out). Subscribers in the secondary region could stay idle until needed, or you could use a routing mechanism to choose which region’s messages to consume. Alternatively, use AWS services like **Amazon EventBridge Global Endpoints** or custom scripts to detect a regional SNS failure and redirect notifications to a backup topic in another region. Note that SNS does support **cross-region delivery** to certain endpoints – e.g., an SNS topic can have an SQS queue in a different region as a subscriber – which can be leveraged to forward events to another region. However, if the entire region hosting the SNS topic is down, messages would not be accepted into that SNS. Therefore, a multi-region publication strategy (or higher-level event bus) is essential for true DR.
  * **Failover and Testing:** Regularly **test your DR setup** for SNS. If using multi-region publishing, simulate failure of the primary region to ensure subscribers in the secondary region receive the messages (you may need to manually flip endpoints or process messages from the backup queue). If using a backup SNS topic, ensure applications can switch to it (possibly via DNS-based endpoints or configuration flags) quickly. Also test the scenario of an unavailable subscriber and recovery via DLQ in the primary region. Through periodic drills, you can validate that messages will not be lost and notifications will still reach their destination during regional outages or major incidents.

* **Backup & Recovery:**

  * **Message Archiving (FIFO Topics):** For use cases requiring message replay or audit, SNS FIFO topics support an optional **message archiving** feature. When enabled, SNS will retain a copy of each message published to a FIFO topic in an immutable storage tier for up to **365 days**. Archived messages can later be **replayed** to the topic’s subscribers (e.g., to recover from an outage or reprocess events) in the original publish order. This feature is useful for long-term retention of critical events, but note that it is **only available for FIFO topics** (standard topics do not support automatic archiving/replay). If using this feature, periodically review the retention period and set appropriate access controls, as the archived data may contain sensitive information.
  * **Dead-Letter Queues for Undelivered Messages:** To facilitate recovery from transient failures, configure a **Dead-Letter Queue (DLQ)** for SNS subscriptions. By attaching an SQS queue as a DLQ to an SNS subscription, any message that fails to deliver to that subscriber (after all retry attempts are exhausted) will be routed to the DLQ. Operators can then inspect the DLQ to see which messages were not delivered and take appropriate action (reprocess them, debug the subscriber endpoint, etc.). This mechanism prevents silent loss of notifications. It’s a best practice to use DLQs especially for critical subscribers (e.g., an order processing system) so that no data is lost even if the endpoint is down or misconfigured.
  * **External Backups via Firehose or Lambda:** Since SNS (standard topics) doesn’t permanently store messages, if you require a persistent log of all messages for compliance or backup, you can set up an **archival subscriber**. For instance, subscribe an AWS Lambda function or a Kinesis Data Firehose delivery stream to the topic; this subscriber can then write every incoming message to a durable store like Amazon S3 or a database. By doing so, you maintain an external backup of all notifications. This approach can complement the SNS FIFO built-in archive (or replace it for standard topics). Ensure such backup subscribers are well-monitored so they do not fall behind or get throttled.
  * **Recovery Procedures:** In the event that messages need to be replayed (e.g., after fixing a downstream system that missed some events), utilize the available mechanisms: for FIFO topics, use the **SNS replay** feature to retransmit archived messages to subscribers for a given time range; for standard topics (which lack replay), retrieve messages from your DLQ or S3 backup and re-publish them to the topic or directly to the endpoint. Automate this process where possible for efficiency and to reduce errors. Also, test the end-to-end recovery path periodically – for example, intentionally send a test message to a DLQ and then ensure your process can successfully handle and reprocess it – so that you are confident the backup and restore strategy works when needed.

* **Access Control & Security Configuration:**

  * **Topic Policies & IAM:** Secure SNS topics with fine-grained policies. By default, new SNS topics are private to your AWS account. Use **SNS topic policies** to explicitly allow cross-account access only to trusted accounts or to specific AWS services that need to publish/subscribe. Avoid using wildcard principals (`*`) in topic policies. For example, if another AWS account or an external system (via AWS IAM roles) needs to subscribe, grant only that account permission in the topic policy. Internally, prefer IAM roles and policies over hard-coded credentials: an EC2 instance or Lambda function publishing to SNS should do so under an IAM role that has `sns:Publish` permission to the specific topic, rather than embedding AWS keys. Regularly audit topic policies and IAM roles for SNS to ensure they follow least privilege and no overly broad access has been given.
  * **Encryption at Rest (SSE):** Enable **server-side encryption** for SNS topics, especially those that handle sensitive or confidential data. SNS supports integration with AWS Key Management Service (KMS) to encrypt all messages at rest using a customer-managed KMS key. When SSE is enabled, SNS will encrypt message data before storing it (and decrypt when delivering to subscribers). Use a KMS CMK that is managed by your organization (for Restricted data classification, a customer-managed key is recommended over AWS managed keys). Ensure the KMS key policy allows the SNS service to use the key and that appropriate administrators are given access to manage the key. Encryption at rest adds a layer of protection in case SNS message storage is ever compromised, and may be required for compliance with internal InfoSec policies.
  * **Encryption in Transit:** Always enforce encryption for data in transit to and from SNS. All AWS SDKs and console communications with SNS use HTTPS by default. Do not publish messages over unsecured HTTP – if a client must use the HTTP endpoint (not recommended), enforce a policy condition to deny any non-HTTPS requests. Likewise, for subscriber endpoints, prefer HTTPS endpoints for webhooks and ensure clients (like mobile apps using mobile push) use TLS. If an SNS topic is encrypted with KMS, AWS **requires** that publications use HTTPS to maintain security. You can add an explicit IAM policy condition on the topic (`aws:SecureTransport` must be true) to ensure only encrypted transport is used. This guards against man-in-the-middle attacks and eavesdropping on notifications.
  * **VPC Endpoints (PrivateLink):** For internal applications running within AWS, utilize **Amazon VPC Endpoints** for SNS to keep traffic off the public internet. By creating an SNS Interface VPC Endpoint in your VPC, your EC2 instances or containers can publish to SNS entirely within AWS’s private network. You can also restrict your SNS topic to only accept traffic from your VPC endpoint by using conditions in the topic policy (e.g., allowing only if the source VPC or source VPCE is a certain ID). This ensures that even if credentials were compromised, an attacker from outside your network couldn’t easily exploit the topic. Using PrivateLink is **required for Restricted data** use cases, as it prevents any payload from traversing the public internet. Configure VPC endpoint policies if needed to further limit which SNS topics can be accessed via the endpoint.
  * **Audit and Logging:** Enable AWS CloudTrail for your account to log all SNS API calls. CloudTrail logs will record who created or deleted topics, who published messages (with subject lines, but not full message content), who subscribed or unsubscribed endpoints, etc. This is crucial for security audits and investigating any unauthorized access or misuse of SNS. In addition, turn on **logging of delivery status** for protocols that support it (HTTP/S, Lambda, SMS, email) – for example, SNS can log to CloudWatch Logs whether an SMS was successfully delivered or if an email bounced. Use these logs to detect misconfigurations or issues (such as endpoints that consistently fail to receive messages, which could indicate a broken consumer or a permissions issue). By actively monitoring these logs and integrating with alerting systems, you can quickly respond to security or delivery issues (for instance, if an unknown principal publishes to a topic, or if a flood of messages is detected unexpectedly).

* **Network Connectivity Options:**

  * **Public Endpoint:** *Not Allowed (for internal use).* By default, SNS is accessed via its public AWS endpoint (`sns.<region>.amazonaws.com`), which requires internet connectivity or an AWS public API endpoint access. In the PepsiCo environment, using SNS over the public endpoint is **strongly discouraged** for any internal or sensitive messaging. If absolutely necessary (for example, sending notifications to end users’ phones or emails inherently goes out to the public networks), the SNS topic should be locked down with strict IAM policies (no public publish permissions) and the publishing clients should reside in secure networks. All communications must be over HTTPS. Essentially, direct public internet access to SNS should be avoided for backend integrations – leverage PrivateLink instead for those.
  * **Service Endpoints:** *Not Applicable (AWS uses PrivateLink).* AWS does not use the same concept of “service endpoints” as Azure Service Endpoints. In AWS, the analog for private service access is **AWS PrivateLink** (VPC Interface Endpoints). There is no separate “service endpoint” mode for SNS aside from the default public endpoint and the PrivateLink option.
  * **Private Endpoint (VPC Interface Endpoint):** **Allowed & Recommended.** AWS PrivateLink support for SNS allows you to create a private endpoint for SNS in your VPC. When using a VPC Endpoint, all SNS API calls (e.g., Publish, Subscribe) travel through the AWS backbone network and do not traverse the public internet. This is required for **Restricted** data classification and strongly recommended for Internal/Confidential data. By using a private endpoint, you can also integrate on-premises systems via Direct Connect or VPN to publish to SNS without opening internet access. Always enable **private DNS** for the endpoint, so that `sns.<region>.amazonaws.com` resolves to the private IP within the VPC. Additionally, use **endpoint policies** to restrict which SNS topics can be accessed, and configure SNS topic policies to only allow traffic via your VPC endpoint (enhancing security).
  * **Network Access Control:** SNS does not use IP-based firewall rules for its topics (access is managed via IAM/topic policies rather than IP allowlists). Therefore, controlling network access is achieved by the above means (PrivateLink endpoints and routing). Ensure that any clients publishing to SNS from an AWS VPC route their traffic through either the VPC Endpoint (if enabled) or a secure NAT/gateway. For subscribers that are HTTP endpoints running on-premises, consider using AWS VPN or tunneling solutions so that they can reach SNS endpoints without exposing the endpoint publicly. In summary, prefer private network paths for SNS communication wherever possible, and avoid open internet exposure for corporate systems interacting with SNS.

* **Networking & Security Architecture:**
  *TBD (Reference architecture diagram and further network/security integration details to be provided.)*

* **AWS Service Limitations:**

  * **Message Payload Limits:** Each SNS message payload can be up to **256 KB** in size. This includes the message body and any message attributes. If an application needs to send larger data, it must use strategies like the SNS Extended Client (which offloads payloads to S3). The maximum payload that can be offloaded via this method is 2 GB. Exceeding the 256 KB limit on direct publishes will result in an error. Design your messages to be compact – send only necessary data, or send references (IDs, URLs) to larger data stored elsewhere.
  * **Throughput and Throttling:** SNS is designed for high throughput, but there are default soft limits in place. In major regions (e.g., us-east-1), an AWS account can publish around **30,000 messages per second** to SNS by default across all topics. Other regions have lower defaults (e.g., 1,500 or 9,000 per second). These limits can be increased by AWS on request. For **FIFO topics**, the maximum throughput per topic is **3,000 messages per second or 10 MB/s** (whichever limit is hit first). Also, FIFO topics enforce a limit of **300 messages per second per message group** for ordered processing. If publishers exceed these rates, they will receive throttling errors (HTTP 429) until the rate falls below the limit or the limit is raised. To avoid throttling, use exponential backoff in your publish code and monitor CloudWatch metrics for throttled publishes.
  * **Resource Limits (Topics and Subscriptions):** AWS accounts can create a very large number of SNS topics and subscriptions, but there are some hard limits. An account can have **100,000 Standard topics** and each standard topic can support up to **12,500,000 (12.5 million) subscriptions**. In contrast, FIFO topics are limited to **1,000 topics per account**, with each FIFO topic allowing at most **100 subscriptions**. Keep these limits in mind when planning your topic hierarchy or multi-tenant architectures. If you foresee needing more than 100k topics (which is uncommon), you would need to request an increase or re-evaluate the design. Similarly, the 100 subscription limit on FIFO topics means you cannot fan out a FIFO message to more than 100 endpoints directly – if more fan-out is needed with ordering, consider chaining topics or using SQS to distribute to additional consumers.
  * **Delivery Semantics – Standard vs FIFO:** With Standard SNS topics, delivery is **at-least-once** and *best-effort ordering*. This means every message will be delivered to each subscriber at least one time, but occasionally a subscriber may receive duplicate deliveries of the same message, and the order of messages is not guaranteed to match the publish order if the messages were published very close in time. Developers should account for this in subscriber logic (e.g., using message IDs or content to detect duplicates). By contrast, SNS FIFO topics provide **exactly-once** delivery and preserve the publish order for each message group. They achieve this by eliminating duplicates (via a deduplication ID or content hashing) and using sequential ordering. The trade-off is that FIFO topics have the throughput and subscriber limits discussed above. Essentially, choose Standard vs FIFO depending on whether message ordering/duplication or scalability is the priority in your use case.
  * **External Notifications Constraints:** When using SNS for external notifications (A2P use cases), be aware of additional limits and regulations. **Email** notifications are subject to AWS Simple Email Service constraints since SNS uses SES under the hood for email – there’s a cap of 10 emails per second as mentioned, and emails might be paused if too many bounces or complaints occur. **SMS** notifications have multi-faceted limits: aside from the 20 TPS default throughput, each SMS message has a size limit (160 GSM characters per segment; longer messages are split and concatenated and incur higher costs). SMS deliveries also depend on country-specific telecom regulations – some countries have daily caps or require sender IDs/shortcodes. **Mobile Push** notifications (APNS, FCM, etc.) have their own delivery reliability factors; SNS will attempt delivery but cannot guarantee the mobile device receives it (as it relies on Apple/Google push services). Also, SNS mobile push has limits on endpoints per application (typically in the millions, which is usually sufficient). For voice notifications (if using SNS with AWS Pinpoint or such), there are call rate limits as well. Always consult AWS SNS documentation for the latest on these limits if your application relies heavily on A2P messaging.

* **SKU Features:**

  * **Standard Topics:**

    * Virtually *unlimited* throughput (able to handle very high publish rates; default soft limit \~30k msgs/sec in some regions, scalable beyond with approval).
    * Delivers messages on a **best-effort** basis – may occasionally deliver messages out of order or duplicate a message (at-least-once delivery).
    * Supports **multiple subscriber types** for fan-out: can deliver to SQS queues, Lambda functions, HTTP/S endpoints, email, SMS, mobile push, etc., all from the same topic.
    * Designed for broad fan-out: each Standard topic can have up to 12.5 million subscriptions and supports 100,000 topics per account, enabling massive scale publish/subscribe architectures.
    * Use cases: general event dissemination, broadcasting notifications, decoupling microservices where ultra-high throughput and wide fan-out is needed and the application can tolerate eventual consistency in delivery.
  * **FIFO Topics:**

    * **Strict ordering** of messages – each message is delivered in the exact order it was published (per message group). Preserves sequence for critical workflows.
    * **Exactly-once** message delivery – duplicates are prevented by SNS (using a deduplication window of 5 minutes) so each subscriber receives each message only one time.
    * Throughput is **limited** to 3,000 messages per second or 10 MB/s per topic (whichever limit is hit first). Also, within a given message group, processing is single-threaded up to 300 msg/sec due to ordering. If higher throughput is required, it can be achieved by using multiple message groups in parallel (while still keeping order within each group).
    * Supports a subset of subscriber types: currently, **Amazon SQS FIFO queues** are the primary supported subscriber for FIFO topics. (SNS FIFO -> SQS FIFO is a common pattern to ensure end-to-end ordering with durability). Other services like Lambda may not support FIFO SNS directly as of now.
    * Resource limits: up to 1,000 FIFO topics per account and each FIFO topic can have at most 100 subscriptions, reflecting a more controlled fan-out.
    * Use cases: scenarios like financial transactions, stock price updates, manufacturing or supply chain events, where the sequence of events must be preserved and duplicates cannot be tolerated. The design ensures accuracy and consistency at the expense of some scalability.

* **Related Service:**
  *TBD (e.g., Amazon SQS, Amazon EventBridge as complementary or alternative services)*

# Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * Information Security Specifications – **Amazon SNS** (ensure alignment with PepsiCo InfoSec baseline for cloud messaging services, including data encryption, access controls, monitoring, and incident response procedures specific to SNS)

# Ownership and Version Control

* **Service Architect:** Dariusz Korzun ([<EMAIL>](mailto:<EMAIL>))
* **Version Control:**

  * v.1: 7 Aug 2025 (Dariusz Korzun)
