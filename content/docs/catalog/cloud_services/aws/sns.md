---
weight: 17
title: "SNS"
date: 2025-08-07
tags: []
summary: ""
---

# Cloud Service Classification

* **Category of the Service:** Messaging
* **Cloud Provider:** Amazon Web Services (AWS)

# Cloud Service Overview

* **Name:** Amazon Simple Notification Service (SNS)

* **Description:** Amazon SNS is a fully managed pub/sub messaging service that enables high-throughput, push-based messaging for both application and user notifications, allowing microservices and distributed systems to communicate asynchronously and at scale by decoupling publishers from multiple subscribing endpoints.

* **SKU Approval Status:**

  * **PepsiCo Approved:**

    * Standard Topics (general-purpose SNS topics with high throughput, best-effort ordering)
    * FIFO Topics (First-In-First-Out topics for strict ordering and deduplicated delivery)
  * **Not Approved:**

    * *(None)*

* **Allowed PepsiCo Data Classification:**

  * Public
  * Internal
  * Confidential
  * Restricted

# Service Lifecycle

* **Release Date:** April 7, 2010
* **Planned Decommission Date:** No announced decommissioning plans.
* **Decommission Date:** Not applicable at this time.

# Usage Guidelines

* **Features:**

  * **Managed Pub/Sub Topics (Fan-Out):** SNS topics enable automatic broadcasting of published messages to all subscribers, supporting one-to-many communication that decouples producers from consumers.
  * **Multiple Endpoint Types (A2A & A2P Delivery):** SNS delivers messages to various endpoint types—including AWS services and user notification channels—enabling integration with both internal systems and end-users.
  * **Message Filtering and Attributes:** SNS supports message attributes and subscription filters, allowing subscribers to receive only the messages relevant to their defined criteria.
  * **FIFO Topics (Ordered, Exactly-Once Delivery):** FIFO topics maintain strict message ordering and deliver each published message exactly once, ensuring reliability for sensitive workflows.
  * **High Durability and Retry Mechanisms:** SNS ensures high message durability with multi-AZ storage, automatic delivery retries, monitoring via CloudWatch, and dead-letter queues for undeliverable messages.

* **Sample Use Cases:**

  * **Decoupled Microservice Communication:** SNS topics let microservices receive events in parallel—for example, broadcasting an “Order Placed” event to inventory, billing, and notification systems for real-time, scalable processing.
  * **Application and System Alerts:** SNS enables CloudWatch and other services to send critical alerts via multiple channels (email, SMS, Lambda, etc.), keeping teams informed and automating responses to incidents.
  * **User Notifications (Mobile, SMS, Email):** SNS delivers user notifications through push, SMS, and email channels, handling provider integrations so developers can reach users globally.
  * **Ordered Event Processing for Critical Data:** SNS FIFO topics ensure that important events (like transactions) are delivered and processed in exact order with exactly-once guarantees for consistency.

* **Limitations:**

  * **Standard Topics – No Ordering Guarantees & Possible Duplicates:** Standard SNS topics use an at-least-once delivery model, so messages may be delivered out of order or more than once, requiring consumers to handle duplicates and unordered delivery.
  * **FIFO Topics – Throughput and Subscription Limits:** FIFO topics provide strict ordering and exactly-once delivery but have lower throughput and support fewer subscriptions compared to standard topics.
  * **Message Size Limits:** SNS messages are limited to 256 KB per publish, with larger payloads requiring use of the Extended Client Library (storing content in S3) which adds complexity and latency.
  * **No Built-in Message Persistence for Standard Topics:** Standard SNS topics do not persist delivered messages or archive missed ones, so for durable delivery use SQS as a subscriber or configure dead-letter queues.
  * **Region Scope and Disaster Recovery:** SNS topics are region-specific and lack native cross-region replication or automatic failover, so global redundancy must be managed separately.
  * **Protocol-Specific Constraints:** SNS delivery protocols have specific throughput limits (e.g., 10 emails/sec, 20 SMS/sec) and dependencies on third-party services, which can require alternate solutions for large-scale or highly reliable messaging.

* **Additional Guardrails:**
  *TBD*

* **Used By:**
  *TBD (list of internal applications using SNS)*

* **EA Declaration:**
  *NOT a declared standard.* (SNS is available for use but has not been declared an official standard service by Enterprise Architecture)

# Technical Guidelines

* **Best Practices:**

  * **Appropriate Topic Type & Idempotent Processing:** Select the SNS topic type (standard or FIFO) based on your ordering and throughput needs, and ensure all subscribers are idempotent to handle potential duplicates or reordering.
  * **Leverage Message Attributes and Filtering:** Include relevant message attributes in published messages and use subscription filter policies so consumers only receive the necessary messages, minimizing traffic and cost.
  * **Integrate SQS or Persistence for Critical Data:** Combine SNS with SQS queues or Kinesis Firehose for guaranteed delivery, buffering, and audit logging of critical notifications to provide durability and replay options.
  * **Monitor and Scale Proactively:** Continuously track SNS usage and delivery metrics in CloudWatch, enable delivery status logging, and proactively plan for capacity or quota increases as usage grows.
  * **Security and Least Privilege:** Secure SNS topics with restrictive IAM policies, never allow anonymous access, and follow least privilege principles by granting only required permissions to specific roles or applications.

* **High Availability & Disaster Recovery:**

  * **In-Region Redundancy:** SNS stores messages durably across multiple Availability Zones within a region, ensuring high availability and fault tolerance by transparently continuing operation during data center outages.
  * **Cross-Region Resilience:** Achieving disaster recovery across regions requires deploying SNS topics and subscribers in multiple regions, since SNS offers no automatic cross-region failover and topics are region-scoped.
  * **Failover and Testing:** Regularly test your multi-region or backup SNS setup to verify messages are delivered as expected during simulated regional failures and subscriber recoveries.

* **Backup & Recovery:**

  * **Message Archiving (FIFO Topics):** SNS FIFO topics can retain and replay archived messages for up to 365 days, supporting audit and recovery needs.
  * **Dead-Letter Queues for Undelivered Messages:** Set up DLQs for SNS subscriptions to capture and recover undelivered messages after retries are exhausted.
  * **External Backups via Firehose or Lambda:** Use Lambda or Kinesis Firehose subscribers to store copies of SNS messages to S3 or databases for persistent backup.
  * **Recovery Procedures:** For recovery, replay messages from FIFO topic archives, DLQs, or S3 backups and regularly test these processes to ensure they work in practice.

* **Access Control & Security Configuration:**

  * **Topic Policies & IAM:** Restrict SNS access using precise topic policies and IAM roles, avoiding wildcards and auditing permissions regularly to enforce least privilege.
  * **Encryption at Rest (SSE):** Enable server-side encryption with AWS KMS for SNS topics to protect sensitive data at rest and ensure compliance with security policies.
  * **Encryption in Transit:** Mandate HTTPS for all SNS communications by enforcing policy conditions, protecting against interception of messages in transit.
  * **VPC Endpoints (PrivateLink):** Use SNS VPC endpoints to keep internal application traffic within AWS's private network and restrict SNS topic access to trusted VPC sources.
  * **Audit and Logging:** Enable CloudTrail and delivery status logging for SNS to monitor API calls, track access, and quickly detect or investigate security and delivery issues.

* **Network Connectivity Options:**

  * **Public Endpoint:** Avoid using the default public SNS endpoint for internal messaging, and if necessary, enforce strict IAM controls and HTTPS for any external notifications.
  * **Service Endpoints:** AWS does not provide Azure-style service endpoints for SNS—use PrivateLink for private connectivity instead.
  * **Private Endpoint (VPC Interface Endpoint):** **Allowed & Recommended.** Use and prefer SNS VPC endpoints (PrivateLink) for secure private network access, enabling private DNS and restricting topic access via endpoint and topic policies.
  * **Network Access Control:** SNS access is controlled by IAM and topic policies rather than IP allowlists, so ensure internal clients use VPC endpoints or secure network paths, avoiding public internet exposure whenever possible.

* **Networking & Security Architecture:**
  *TBD (Reference architecture diagram and further network/security integration details to be provided.)*

* **AWS Service Limitations:**

  * **Message Payload Limits:** Each SNS message is limited to 256 KB, requiring use of S3 payload offloading for larger messages (up to 2 GB) and best practice is to keep messages compact.
  * **Throughput and Throttling:** SNS supports up to 30,000 messages per second per account for standard topics (lower in some regions), with lower throughputs for FIFO topics and per-message-group limits; exceeding these rates results in throttling errors until rates are reduced.
  * **Resource Limits (Topics and Subscriptions):** Accounts can create up to 100,000 standard topics and 12.5 million subscriptions per topic, while FIFO topics are limited to 1,000 per account and 100 subscriptions each.
  * **Delivery Semantics – Standard vs FIFO:** Standard topics offer at-least-once delivery and possible duplicates or reordering, while FIFO topics provide exactly-once, in-order delivery, with lower throughput and fan-out limits.
  * **External Notifications Constraints:** SNS external notifications (email, SMS, mobile push) are subject to additional provider and regulatory limits, including per-second caps, message size restrictions, and region/country-specific constraints.
* **SKU Features:**

  * **Standard Topics:**

    * Virtually *unlimited* throughput (able to handle very high publish rates; default soft limit \~30k msgs/sec in some regions, scalable beyond with approval).
    * Delivers messages on a **best-effort** basis – may occasionally deliver messages out of order or duplicate a message (at-least-once delivery).
    * Supports **multiple subscriber types** for fan-out: can deliver to SQS queues, Lambda functions, HTTP/S endpoints, email, SMS, mobile push, etc., all from the same topic.
    * Designed for broad fan-out: each Standard topic can have up to 12.5 million subscriptions and supports 100,000 topics per account, enabling massive scale publish/subscribe architectures.
    * Use cases: general event dissemination, broadcasting notifications, decoupling microservices where ultra-high throughput and wide fan-out is needed and the application can tolerate eventual consistency in delivery.
  * **FIFO Topics:**

    * **Strict ordering** of messages – each message is delivered in the exact order it was published (per message group). Preserves sequence for critical workflows.
    * **Exactly-once** message delivery – duplicates are prevented by SNS (using a deduplication window of 5 minutes) so each subscriber receives each message only one time.
    * Throughput is **limited** to 3,000 messages per second or 10 MB/s per topic (whichever limit is hit first). Also, within a given message group, processing is single-threaded up to 300 msg/sec due to ordering. If higher throughput is required, it can be achieved by using multiple message groups in parallel (while still keeping order within each group).
    * Supports a subset of subscriber types: currently, **Amazon SQS FIFO queues** are the primary supported subscriber for FIFO topics. (SNS FIFO -> SQS FIFO is a common pattern to ensure end-to-end ordering with durability). Other services like Lambda may not support FIFO SNS directly as of now.
    * Resource limits: up to 1,000 FIFO topics per account and each FIFO topic can have at most 100 subscriptions, reflecting a more controlled fan-out.
    * Use cases: scenarios like financial transactions, stock price updates, manufacturing or supply chain events, where the sequence of events must be preserved and duplicates cannot be tolerated. The design ensures accuracy and consistency at the expense of some scalability.

* **Related Service:**
  *TBD (e.g., Amazon SQS, Amazon EventBridge as complementary or alternative services)*

# Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * Information Security Specifications – **Amazon SNS** (ensure alignment with PepsiCo InfoSec baseline for cloud messaging services, including data encryption, access controls, monitoring, and incident response procedures specific to SNS)

# Ownership and Version Control

* **Service Architect:** Dariusz Korzun ([<EMAIL>](mailto:<EMAIL>))
* **Version Control:**

  * v.1: 7 Aug 2025 (Dariusz Korzun)
  * v.2: 8 Aug 2025 (Rakesh Ponnada)
