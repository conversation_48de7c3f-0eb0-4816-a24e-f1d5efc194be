---
weight: 18
title: "Lambda"
date: 2025-08-07
tags: [AWS Lambda, Serverless, Functions-as-a-Service, Event-Driven Execution]
summary: ""
---

## Cloud Service Classification

* **Category of the Service:** Compute (Serverless)
* **Cloud Provider:** AWS
* **Website**: [Technical documentation](https://docs.aws.amazon.com/lambda/)
## Cloud Service Overview

* **Name:** AWS Lambda
* **Description:** AWS Lambda is a serverless compute service that runs code without provisioning or managing servers, automatically handling resource provisioning, maintenance, patching, and scaling. It executes code only in response to events—triggered by services such as Amazon S3, DynamoDB, or API Gateway—so you pay only for compute time consumed, enabling event-driven architectures without managing infrastructure.
* **SKU Approval Status:**

  * **PepsiCo Approved:** AWS Lambda (standard on-demand functions; all runtime architectures and features are allowed)
  * **Not Approved:** None (no disallowed SKUs – AWS Lambda has no alternate tiers beyond configuration settings)
* **Allowed PepsiCo Data Classification:**

  * Public
  * Internal
  * Confidential
  * Restricted

## Service Lifecycle

* **Release Date:** November 13, 2014 (initial launch)
* **Planned Decommission Date:** No announced decommissioning plans.
* **Decommission Date:** Not applicable at this time.

## Usage Guidelines

* **Features:**

  * **Event-Driven Execution:** Runs code in response to events from other AWS services or custom triggers—such as S3 object uploads, DynamoDB table updates, SNS messages, API Gateway requests, or scheduled timers—thereby enabling reactive, event-driven workflows.
  * **Automatic Scaling:** Automatically scales to handle incoming events without pre-provisioning capacity, within service limits, thereby ensuring consistent performance under bursty or unpredictable workloads.
  * **Multiple Runtime Support:** Supports multiple programming languages and custom runtimes, allows using existing code and libraries without modification, and enables packaging functions as container images for deployment with familiar tooling.
  * **Fully Managed Service:** AWS Lambda runs on highly available, multi-AZ infrastructure with built-in redundancy and no maintenance windows; integrates logging and monitoring via Amazon CloudWatch; and removes the need to manage servers or scaling policies.

* **Sample Use Cases:**

  * **Web & Mobile Backends:** By integrating AWS Lambda with Amazon API Gateway, users can build RESTful APIs or backend microservices without managing servers.
  * **File Processing:** Use AWS Lambda to automatically process files and data streams in real time (as response to an event such as generating thumbnail when an image is uploaded to S3).
  * **Data Transformation & ETL:** Use AWS Lambda for lightweight ETL (Extract, Transform, Load) — filtering, aggregating, and enriching data on the fly—by responding to events from DynamoDB tables or Kinesis streams and updating other systems or pushing processed records to data stores.
  * **Scheduled Tasks & Automation:** Use AWS Lambda with Amazon EventBridge (CloudWatch Events) to replace cron jobs and scheduled scripts for tasks and periodic maintenance, without requiring a persistent server.
  * **Serverless Event-driven Applications:** Integrate AWS Lambda with Amazon SNS and EventBridge to handle messaging and system events, enabling automatic remediation (e.g., shutting down idle resources or responding to CloudWatch alarms) and reactive IoT workflows.

* **Limitations:**

  * **Execution Time Limit:** AWS Lambda invocations are limited to 15 minutes (900 seconds); workloads requiring longer or continuous execution should be re-architected or run on services such as Amazon EC2 or AWS Batch.
  * **Stateless and Ephemeral:** AWS Lambda functions are stateless and run in ephemeral environments with up to 10 GB of memory and 512 MB of `/tmp`; there is no guaranteed affinity or state reuse across invocations, so persistent in-memory state or long-lived connections require external storage (databases, EFS) or other services.
  * **Startup Latency:** AWS Lambda can incur cold-start latency—especially with infrequent invocations, large packages, or certain runtimes—mitigable via Provisioned Concurrency or keeping instances warm.
  * **Resource Limits:** AWS Lambda has resource and rate limits: deployment packages 50 MB zipped (direct upload) or 250 MB uncompressed, container images up to 10 GB, environment variables capped at 4 KB, and a default per‑region concurrency of 1,000; exceeding concurrency throttles invocations (429 for synchronous; asynchronous retried or dropped after retries), and it **lacks GPU or specialized hardware, excluding GPU‑accelerated or certain high‑performance workloads**.

* **Additional Guardrails:**

  * **TBD** (to be defined – e.g., internal policies or constraints specific to enterprise use of Lambda)

* **Used By:**

  * **TBD** (will include list of teams or projects using AWS Lambda)

* **EA Declaration:** NOT a declared standard (usage of AWS Lambda is not yet formally declared as a standard by Enterprise Architecture).

## Technical Guidelines

* **Best Practices:**

  * **Design for Statelessness:** Write stateless Lambda handlers; persist data externally, and use environment reuse for performance (e.g., caching configuration, reusing connections) without relying on it for state.
  * **Optimize Function Configuration:** Right-size memory and timeouts through testing: more memory increases CPU and can shorten runtime; load test to find the optimal memory and set timeouts (up to 15 minutes) to catch hung processes.
  * **Use Environment Variables & Configuration Management:** Externalize configuration (e.g., S3 bucket names, DB connection strings) into environment variables or AWS Systems Manager Parameter Store or Secrets Manager to avoid hard-coding and manage sensitive data securely; encrypt environment variables with AWS KMS for sensitive values.
  * **Idempotent and Resilient Logic:** Implement idempotency and proper error handling—especially for event-driven workloads that may retry on failure—handle duplicate events to avoid unintended side effects, use built-in retries for asynchronous events, and consider DLQs for events that cannot be processed after retries.
  * **Least Privilege IAM:** Apply least privilege to the Lambda execution role and IAM policies by restricting permissions to only required resources and actions (e.g., specific S3 buckets or DynamoDB tables) to minimize blast radius, and enable AWS CloudTrail and CloudWatch Logs to monitor executions and API calls for auditing and security visibility.

* **High Availability & Disaster Recovery:**

  * **High Availability (In-Region):** AWS Lambda provides in‑region high availability by running functions across multiple Availability Zones with built‑in replication and fault tolerance. It has no planned maintenance windows or downtime and requires no user action for resiliency; if an AZ experiences issues, Lambda automatically routes executions to healthy facilities to ensure continuous operation.
  * **Disaster Recovery (Multi-Region):** AWS Lambda functions are region-specific; to handle a regional outage, deploy critical functions to a backup region and route traffic via Route 53, AWS Global Accelerator, or application logic. Design cross‑region state synchronization and event routing at the application level (e.g., cross‑region event streams or backups). Lambda does not provide cross‑region failover, but combining it with S3 cross‑region replication or EventBridge can recreate events in a secondary region; regularly test failover for disaster recovery.

* **Backup & Recovery:**

  * **Code and Configuration Backup:** Keep Lambda code in source control (e.g., Git) or as versioned artifacts in S3 or a container registry to enable redeployments and rollbacks, and define Lambda resources with infrastructure-as-code (CloudFormation/Terraform) so they can be re-created after accidental deletion or corruption.
  * **Versioning and Deployment:** Use Lambda versioning with aliases (e.g., “prod,” “dev”): publish a new version with each deployment, and roll back by repointing aliases to a previous stable version, providing a safety net and rapid recovery from code issues.
  * **Persistent Data Considerations:** Lambda does not retain data between invocations; when writing to external systems (databases, S3, EFS), ensure they have their own backup and recovery (e.g., database backups, S3 versioning), and if using Amazon EFS with Lambda, regularly back up the file system via AWS Backup or EFS backup features.
  * **Post-Failure Recovery:** Implement monitoring and alerts with CloudWatch Alarms or X-Ray to detect function errors and timeouts, and for asynchronous invocations configure a Dead Letter Queue or on-failure target (e.g., an SNS topic) so failed events can be reviewed, retried, or handled manually, ensuring no data-triggered event is lost without visibility.

* **Access Control & Security Configuration:**

  * **IAM Security:** Control Lambda access with IAM so only authorized users and services can create, update, or invoke functions. Use fine-grained IAM policies (e.g., team-scoped), assign a minimal-permission execution role for other AWS resources, and regularly review and rotate any credentials or keys if used (Lambda typically uses roles, not long-lived keys).
  * **Networking and VPC:** By default, Lambda runs in an AWS‑managed environment with public internet access. As per PepsiCo standards, for enhanced security and internal resource access, run functions in a VPC (private subnets, security groups) without internet access unless routed (e.g., via a NAT Gateway or AWS PrivateLink endpoints), and **use VPC Interface Endpoints** (AWS PrivateLink) for services like S3 and SQS to keep traffic on the AWS internal network.
  * **Data Encryption:** Lambda automatically encrypts function code at rest (stored in S3); encrypt sensitive data at rest and in transit, use AWS KMS for secret environment variables, and prefer services that enforce encryption (e.g., ensure S3 buckets have default encryption).
  * **Function Security Features:** Use Lambda Code Signing to require trusted, signed deployments and block unapproved code; enable AWS X-Ray for tracing and anomaly detection; and keep runtimes updated—AWS patches managed runtimes, but custom runtimes and container images must pull the latest dependency patches.
  * **Monitoring & Auditing:** Enable CloudWatch Logs to capture Lambda stdout/stderr; configure CloudWatch or third‑party alerts for errors, throttling, and performance; use AWS CloudTrail to audit Lambda API calls (create, update, delete); and include Lambda in regular security reviews and threat modeling, especially for sensitive data or critical business logic.

* **Network Connectivity Options:**

  * **Public Access (Internet-facing):** *Not Allowed by Default.* AWS Lambda has no direct public endpoint unless fronted by API Gateway, an Application Load Balancer, or a Lambda Function URL. Do not expose functions handling internal data directly to the internet; if public access is required, **use an approved gateway/service with authentication and authorization** (e.g., API Gateway or ALB with OIDC), and avoid Lambda Function URLs for sensitive internal apps or use them only with strict IAM authorization.
  * **VPC Private Connectivity:** *Allowed / Preferred.* For internal applications and Confidential/Restricted data, run Lambda in private VPC subnets to access internal databases, application servers, or on-prem networks (via VPN/Direct Connect) without traversing the public internet. Ensure proper subnet routing (e.g., to corporate networks or via NAT Gateways for outbound internet); this is required for Restricted data and provides network-level isolation.
  * **VPC Interface Endpoints:** For Lambda functions in a VPC, use AWS PrivateLink endpoints to call AWS services (e.g., S3, DynamoDB) over the AWS private network, enhancing security and avoiding the need for an internet gateway.
  * **Outbound Internet Access:** If a VPC-based Lambda must access external internet services, route traffic through a controlled egress point—typically a NAT Gateway in a public subnet—so outbound traffic can be monitored and restricted by network ACLs or firewalls; for Restricted data, tightly control or disallow outbound internet access unless absolutely necessary.
  * **Hybrid Connectivity:** Lambda can access on-premises systems when its VPC is connected via VPN or AWS Direct Connect; ensure security groups, network ACLs, and on-prem database rules allow required traffic from the VPC, and review all connectivity under enterprise network security policies.

* **Networking & Security Architecture:**

  * **TBD** (to be developed – diagrams or references to architecture patterns for deploying Lambda in a secure, enterprise network context will be provided here)

* **AWS Service Limitations:**

  * **Concurrent Execution Limit:** AWS Lambda defaults to 1,000 concurrent executions per region (a soft, increaseable limit); exceeding it throttles additional invocations, so design for throttling (e.g., queues or backpressure) and request limit increases for production workloads as needed.
  * **Resource Constraints:** Each Lambda invocation can use up to 10,240 MB of memory with proportionate CPU (vCPU scales with memory) and only 512 MB of ephemeral disk. These limits make Lambda unsuitable for very large in-memory datasets or significant disk usage; if you approach them, break the task into smaller chunks or use a different service.
  * **Execution Duration:** AWS Lambda enforces a hard 15-minute per-invocation limit; workloads exceeding this must be refactored (e.g., split across invocations or orchestrated with Step Functions) or migrated to other services, as the timeout is not extensible.
  * **Deployment Package Size:** AWS Lambda limits deployment packages to 50 MB compressed for direct uploads and 250 MB uncompressed (including layers), permits container images up to 10 GB—with larger images increasing cold-start latency—so keep artifacts lean, use Layers for shared dependencies, and note that exceeding these limits blocks function updates.
  * **Limited Internal Extensions:** AWS Lambda’s managed runtime imposes strict constraints—no persistent background daemons beyond an invocation, limited /tmp storage, no administrative OS access, and fixed integrations (e.g., stdout/stderr to CloudWatch Logs)—so workloads requiring deeper runtime control or long-lived processes should use containers or VMs instead.

* **SKU Features:**

  * **Architectures (x86\_64 vs Arm64):** AWS Lambda supports x86_64 and Graviton2 (Arm64); selecting Arm64 on a per‑function basis delivers equivalent functionality with about 20% lower cost and up to 34% better price‑performance, with all runtimes supported or via a compatible custom runtime for Arm.
  * **Provisioned Concurrency:** By default, AWS Lambda scales on demand and may incur cold starts; Provisioned Concurrency pre-initializes a specified number of instances to deliver near-zero latency for performance‑sensitive workloads, at additional cost for the configured capacity time plus invocations.
  * **Lambda\@Edge:** Lambda@Edge runs Node.js or Python functions at CloudFront edge locations in response to distribution events, enabling low‑latency CDN customization (e.g., header/content personalization) with strict limits (e.g., up to 128 MB memory and 5‑second execution for viewer triggers), deployed by attaching a function version to events, and is a CloudFront‑integrated feature rather than a standalone service.
  * **Integration with Other Services:** AWS Lambda natively integrates with 200+ AWS services and SaaS—e.g., Step Functions (orchestration), EventBridge (event routing), SQS (batched queue processing), API Gateway/AppSync (API endpoints), RDS Proxy (connection management), and EFS (shared file access)—extending event-driven, API, and data capabilities as part of the Lambda ecosystem rather than separate SKUs.
  * **Cost Model:** AWS Lambda employs a pay-per-use model—charging by request count and execution duration at 1 ms granularity scaled by configured memory/CPU—with a free tier (e.g., first 1M requests/month), additional dimensions for Provisioned Concurrency and Lambda@Edge, and no long-running instance charges.

* **Related Service:**

  * **TBD** (e.g., other related AWS services such as AWS Step Functions, Amazon EventBridge, or AWS App Runner might be noted here)

* **Alternatives:**

  * **Amazon EC2 or AWS Fargate:** Workloads that exceed Lambda’s constraints (e.g., long‑running tasks, specialized compute, or stateful services) should use serverless containers via AWS Fargate with ECS/EKS to avoid the 15‑minute limit, or Amazon EC2 for full OS/runtime control at the cost of managing servers.
  * **AWS Batch:** AWS Batch orchestrates EC2 or Fargate fleets to run large-scale or scheduled batch jobs that exceed Lambda’s 15-minute limit or require fine-grained resource control (e.g., high memory/CPU), providing a more suitable alternative when Lambda’s simplicity is insufficient.
  * **Serverless Frameworks or Other Cloud Functions:** AWS Lambda is AWS’s primary FaaS (Function-as-a-Service), with conceptual analogs in Azure Functions and Google Cloud Functions; when Lambda’s event-driven constraints preclude a solution, consider traditional architectures or fit-for-purpose managed services (e.g., Step Functions for orchestration, AppFlow for data integration).

## Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * Information Security Specifications – *AWS Lambda* (Ensure alignment with internal InfoSec baseline requirements for serverless functions, including data handling, logging, and access control as per PepsiCo security policies. Refer to the internal AWS Lambda security baseline document for detailed controls and configuration requirements.)

## Ownership and Version Control

* **Service Architect:** Ramakrishna Ramaraju ([<EMAIL>](mailto:<EMAIL>))

* **Version Control:**

  * **v.1:** 30 Jul 2025 – Initial draft prepared for AWS Lambda service catalog item (Ramakrishna Ramaraju)