---
weight: 10
title: "EFS"
date: 2025-07-22
tags: [Network Attached Storage (File Share)]
summary: "Elastic File System"
---

# Amazon Elastic File System (EFS)

## Cloud Service Classification

**Category of the Service**: Network Attached Storage (File Share)
**Cloud Provider:** AWS
**Website**:[Technical Documentation](https://docs.aws.amazon.com/efs/latest/ug/whatisefs.html/)
 
## Cloud Service Overview

**Name:** Amazon Elastic File System (EFS)
**Description:** Fully managed, elastic, shared file storage for AWS and on-premises. Scales automatically, supports NFSv4, strong consistency, file locking, and concurrent access from thousands of clients. Data is encrypted at rest/in transit. Designed for high durability and availability (multi-AZ).

### SKU Approval Status

  - PepsiCo Approved
    - TBD
  - Not Approved 
    - TBD

### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

### Service Lifecycle

  - Release Date: June 28, 2016
  - Planned Decommission: None announced

## Usage Guidelines

### Features
  - Fully managed, elastic NFS file system. Scales automatically, pay only for used storage.
  - Multi-AZ durability and high availability. One Zone class for lower cost, lower durability.
  - High throughput and parallel access; supports thousands of NFS clients and scales with concurrency.
  - Storage classes: Standard (frequent), Standard-IA (infrequent), One Zone, One Zone-IA. Lifecycle management moves cold data to IA.
  - Security: Encryption at rest (KMS) and in transit (TLS). IAM integration for API/NFS access. Access Points for multi-tenant isolation,
    Compliant with major standards.
  - AWS integration: Mount on EC2, ECS, EKS, Lambda. Works with AWS Backup, DataSync, and on-premises via Direct Connect/VPN.

### Sample Use Cases
  - Shared web content, media, or uploads for fleets of servers.
  - Enterprise file shares, home directories, POSIX apps.
  - Big data analytics, ML training (parallel access to large datasets).
  - Persistent storage for containers (ECS/EKS), shared volumes in Kubernetes.

### Limitations
  - Linux/NFS only. No native Windows support (use FSx for Windows if needed).
  - One Zone classes are single-AZ, lower durability. Not for critical data.
  - Small I/O operations may have higher latency; best for parallel/concurrent workloads.
  - Regional only. No native multi-region; replication is async and requires manual failover.

### Additional Guardrails
  - TBD

### Used By
  - TBD

### EA Declaration
   - NOT a declared standard (case-by-case approval required)

## Technical Guidelines

### Best Practices
  - Enable encryption at rest (KMS) and in transit (TLS via EFS mount helper).
  - Use private subnets and restrict NFS traffic to known clients (security groups).
  - General Purpose mode for most workloads; Elastic Throughput is default. Use Provisioned Throughput only for steady, high needs.
  - Enable Lifecycle Management/Intelligent-Tiering to reduce cost for cold data.
  - Tag file systems for cost tracking; monitor with AWS Cost Explorer.
  - Use CloudWatch for key metrics (throughput, burst credits, connections). Enable CloudTrail for API logging, 
    Regularly review and delete unused filsystems.

### High Availability & Disaster Recovery
  - Standard EFS
    Multi-AZ, highly available. Create mount targets in each AZ for failover.
  - Cross-region DR
    Use EFS Replication (async, ~15 min RPO) for DR. Use AWS Backup for point-in-time restores. Test DR and restore processes regularly.

### Backup & Recovery
  - Use AWS Backup for scheduled, automated backups. Enable daily backups by default.
  - Backups can be copied cross-region/account. Set retention per compliance needs.
  - Restore to new file system (full or partial restore supported). EFS is not versioned; restore from backup as needed.

### Access Control & Security Configuration
  - Restrict NFS (TCP 2049) to only required clients (security groups).
  - Always enable encryption at rest (KMS) and in transit (TLS).
  - Use IAM authorization for NFS clients and file system policies for access control.
  - Use Access Points for multi-tenant access; enforce POSIX permissions (no NFSv4 ACLs).
  - Enable CloudTrail for API logging. For file-level audit, use OS-level logging (e.g., auditd).

### Network Connectivity Options
  - Public internet access
    Not allowed. No public endpoints.
  - VPC internal access
    Default/allowed. Mount from EC2/ECS/EKS in same VPC or via peering/TGW (same region).
  - On-premises
    Allowed via VPN/Direct Connect. Ensure secure connection and monitor latency.
  - Interface VPC Endpoints
    Allowed for API calls (not for NFS data plane).
  - AWS Transfer Family
    Allowed for SFTP/FTPS/FTP with controls; use only when needed and with approval for sensitive data.

### Networking & Security Architecture
  - TBD

### AWS Service Limitations

  - OS Support
    Linux/NFS only. No native Windows support. Use FSx for Windows for SMB.
  - File System Size
    No fixed upper limit. Max file size: 47.9 TiB. Max directory depth: 1,000. File/dir name: 255 bytes. Performance may degrade with millions of files in a single directory.
  - Connections/Throughput
    Up to 25,000 NFS client connections per file system. General Purpose mode: ~250,000 ops/sec. Per-client: 500 MiB/s (default), up to 1,500 MiB/s with optimized mount. Scale horizontally if needed.
  - NFS Protocol
    Only NFSv4.0/v4.1 supported. No NFSv3, no NFSv4 ACLs, no mandatory locking, no pNFS. Use POSIX permissions and IAM/access points for access control.
  - No Native Snapshots/WORM
    Use AWS Backup for snapshots. No instant revert or recycle bin. Data consistency is strong (read-after-write). Deleted files are immediately gone.

### SKU Features

  - EFS Standard (Regional)
    Multi-AZ, high durability (11x9s), 99.99% SLA, SSD-backed, low latency, all features enabled. Best for production/critical data.
  - EFS Standard-IA
    Multi-AZ, lower cost for infrequent access, higher latency, lifecycle-managed. Good for archives/cold data.
  - EFS One Zone
    Single-AZ, lower cost, same performance as Standard, less durable. Use for dev/test, non-critical, or backup data.
  - EFS One Zone-IA 
    Single-AZ, lowest cost, for rarely accessed data. Use for archives, backups, or ephemeral data. Lifecycle-managed from One Zone.

### Related Service
  - TBD

## Compliance and Security Guidelines

### Security Baseline InfoSec
  - Enforce encryption (at rest/in transit), strict access controls, regular permission audits, and data classification compliance.
  - Restricted data: encrypt with company-managed keys, private network only.
  - Apply AWS CIS Benchmark and internal security standards. Enable CloudTrail and integrate with SIEM. Follow GDPR and regulatory requirements as needed.

## Ownership and Version Control

### Service Architect
  - Dariusz Korzun - <EMAIL>
### Version Control
  - v.1: 22 Jul 2025 – Document created (Dariusz Korzun)