---
weight: 13
title: "ECS"
date: 2025-08-01
tags: [Amazon ECS, Elastic Container Service, Container, Containers]
summary: ""
---

# Amazon Elastic Container Service (Amazon ECS)

## Cloud Service Classification

* **Category of the Service:** Containers  
* **Cloud Provider:** AWS (Amazon Web Services)
* **Website**: [Technical documentation](https://docs.aws.amazon.com/ecs/)

## Cloud Service Overview

- **Name:** Amazon Elastic Container Service (Amazon ECS)  
- **Description:** Fully managed container orchestration for deploying and scaling Docker containers using Fargate, EC2, or on-premises via ECS Anywhere, with deep AWS integration.

- **SKU Approval Status:**  
  - **PepsiCo Approved:** 
    - A<PERSON> Fargate (serverless)
    - EC2 Launch Type (for custom or GPU instances)  
  - **Not Approved:** 
    - *Amazon ECS Anywhere* (external on-premises instances) – **Not Approved** (subject to review for specific edge use cases).
    - *Fargate Spot*: For non-critical workloads (subject to review and approval)  

- **Allowed PepsiCo Data Classification:**  
  - Public  
  - Internal  
  - Confidential  
  - Restricted (with proper security controls like private networks, encryption, and access control)

## Service Lifecycle

- **Release Date:** April 9, 2015  
- **Planned Decommission Date:** No announced.  
- **Decommission Date:** Not applicable.  

## Usage Guidelines

- **Features:**  
  - **Fully Managed Orchestration:** Fully managed control plane for container orchestration, handling scheduling, cluster state, and deployments without requiring users to manage Kubernetes or Docker Swarm.  
  - **Flexible Compute Options:** Supports Fargate (serverless), EC2 instances, and ECS Anywhere for hybrid environments.    
  - **Deep AWS Integration:** Integrates with AWS services like ECR, Elastic Load Balancing, IAM, CloudMap and CloudWatch.  
  - **Security and Isolation:** Enforces security via task IAM roles, network isolation, and GuardDuty integration.
  - **Scalability and Auto-Scaling:** ECS provides built-in Service Auto-scaling for tasks, Cluster Auto-scaling for EC2 instances with zero-downtime deployments and task placement strategies across Availability Zones (AZs) for high availability (HA).

- **Sample Use Cases:**  
  - **Microservices Web Application:** Deploy microservices like web front-end, API server, and background workers as separate ECS services behind an Application Load Balancer, with ECS handling multi-AZ placement for scalability and resilience. 
  - **Batch Processing and ETL Jobs:** Run containerized batch jobs by enabling on-demand processing that scales up during runs and down to zero afterward for resource efficiency.
  - **Machine Learning Model Training:** ECS allows to run AI/ML training and inference containers with AWS Fargate (Serverless compute, scalable, managed without EC2 provisioning).  
  - **Hybrid Deployment (On-Premises Containers):** Use Amazon ECS Anywhere to manage containers on-premises or edge devices for low-latency local processing. (*with necessary approval from EA*).

- **Limitations:**  
  - **AWS-Only Control Plane:** ECS is AWS-specific, meaning migrating workloads to other orchestrators or clouds requires some rework as it lacks standard portability (e.g., Kubernetes)
  - **Region Specific:** ECS clusters are region-specific and cannot span multiple regions, requiring application-level configurations for multi-region active-active disaster recovery setups.
  - **Ephemeral Storage and State:** ECS containers are ephemeral, with no built-in persistent storage; stateful apps must use external storage like EFS, S3, or databases, adding design complexity to manage external state.  
  - **No Native Image Scanning/Vulnerability Assessment:** ECS does not scan container images for vulnerabilities; users must ensure security using Amazon ECR's image scanning or third-party tools, with optional integration via Amazon Inspector.  
  - **Service Quotas:** ECS has default limits of 5,000 tasks per service, 5,000 services per cluster, and throttling on API calls, requiring adjustments like sharding or staggered deployments for scaling.  
  - **Additional Guardrails:** TBD

- **Used By:** *TBD list & links to some onboardings.*

- **EA Declaration:** NOT a declared standard. *(Amazon ECS is not yet a formally declared PepsiCo standard service; it’s available for use but not mandated or universally adopted across the enterprise.)*

## Technical Guidelines

- **Best Practices:**  
  - **Container Image Management:** Optimize container images by keeping them small, storing them in Amazon ECR, enabling vulnerability scans, using a structured tagging strategy (e.g., `:latest`, `:dev`), and apply ECR lifecycle policies to remove old images.
  - **Use IAM Roles for Tasks:** Assign IAM roles to ECS tasks/services with the least privilege, avoiding static credentials, and separate the task execution role (for ECS agent) from the application task role (for app AWS access).  
  - **Secrets and Configuration Management:** Externalize sensitive data using AWS Secrets Manager or SSM Parameter Store, assign minimal IAM permissions, avoid hardcoding secrets in images, and automate secret rotation with task definition updates.  
  - **Network Isolation and Access:** Deploy ECS services in private subnets to limit internet exposure, use NAT Gateways for outbound internet access, and restrict inbound traffic with security groups allowing only necessary connections (e.g., from load balancers).  
  - **Use AWS PrivateLink for AWS API Calls:** Set up VPC Interface Endpoints for ECS (e.g., ECR, CloudWatch Logs) to securely route control plane traffic within the AWS network, eliminating public internet exposure and enabling operation in VPCs without internet gateways, critical for restricted environments.  
  - **Monitoring and Logging:** Enable detailed ECS monitoring with CloudWatch Logs (e.g., `awslogs` log driver or `Fluent Bit/FireLens`) for centralized log analysis, Container Insights for resource metrics, and set up alarms on key metrics. Use AWS CloudTrail to audit ECS API calls and regularly review logs for configuration drift detection. **AWS Config service is not used in the PepsiCo Environment as standard. Needs EA Approval.** 
  - **High Availability Architecture:** Design ECS services for multi-AZ deployment by ensuring EC2 instances or Fargate capacity in at least two AZs, using a `spread` on `attribute:ecs.availability-zone` placement strategy if needed, and configuring load balancers to route traffic across AZs for fault tolerance. 
  - **Deployment and Testing:** Leverage ECS deployment options like *rolling updates* or *blue/green deployments* (via AWS CodeDeploy) for zero downtime and quick rollback, enabling *deployment circuit breakers* and alarms to stop faulty deployments. Test new task definitions in non-production or canary environments before full rollout.
  - **Resource Limits and Optimization:** Define resource limits in tasks, optimize EC2 with Auto Scaling and *Spot instances* (for eligibe and approved clusters), right-size Fargate tasks, and use AWS Cost Explorer to track and reduce costs.  
  - **Infrastructure as Code and CICD:** Use Infrastructure-as-Code (IaC) tools (CloudFormation/Terraform) for ECS configurations and integrate CI/CD pipelines (e.g., CodePipeline/CodeDeploy) to automate deployments, ensuring consistency and reducing errors.

- **High Availability & Disaster Recovery:**  
  - **Multi-AZ Resilience:** Deploy ECS services in multiple AZs to ensure resilience, use Auto Scaling with *max spreading* for EC2 instances, and configure redundant load balancers (ALBs/NLBs) to handle AZ outages.
  - **In-Region Redundancy:** Amazon ECS has a highly available control plane; focus on the data plane by storing state externally (e.g., multi-AZ database or S3), using health checks, enabling **circuit breakers**, and configuring Auto Scaling for EC2 instance recovery. 
  - **Multi-Region Disaster Recovery:** ECS clusters lack automatic failover; use a warm standby in another region, replicate configurations and container images via **ECR Cross-Region Replication**, and redirect traffic using DNS (e.g., Route 53), testing failover regularly.  
  - **Backup of Configurations:** ECS configurations are managed by AWS, but backup IaC templates and task definition JSONs in source control for quick recovery; use data store backups (e.g., RDS snapshots, EFS) for persistent data.  
  - **Active-Active Considerations:** For active-active multi-region ECS, run separate clusters in each region, use Route 53 latency-based routing policy for traffic distribution, and ensure idempotency and data sync with application logic or global databases.  

- **Backup & Recovery:**  
  - **Task Definition and Service Backup:** Export and version-control ECS task definitions and service configurations in a git repository as backups, enabling quick recovery from accidental deletions or modifications since **ECS lacks point-in-time restore**.  
  - **Container Image Backup:** Use Amazon ECR with cross-region replication for backups, store critical images in external registries if needed, and **enable immutable tags** or **tag locking** to prevent *overwriting* important images.  
  - **Data Persistence and Backup:** For stateful ECS components, use external storage (e.g., Amazon EFS) with backups enabled or jobs to S3, and ensure databases have regular backups and replication, though **running databases in containers** is **not recommended for production**.  
  - **Recovery Automation:** Automate recovery with scripts or IaC to recreate ECS clusters, restore infrastructure, deploy tasks, and configure load balancers in a new region, while documenting manual steps as a runbook for fallback. Automation will reduce the Recovery Time Objective (RTO) significantly.  
  - **Soft Delete (Task Preservation):** Enable ECS deployment circuit breakers with **rollback on failure** to automatically revert to the **last healthy task set**, ensuring quick recovery from failed deployments and minimizing manual intervention in production.  

- **Access Control & Security Configuration:**  
  - **Authentication and Access Control:** Use IAM policies to secure ECS clusters, enforce conditions like MFA or IP restrictions, **assign task-specific IAM roles** for least privilege, and regularly review roles and policies for compliance.  
  - **Network Security and Inbound Access:** Avoid exposing ECS task ENIs to the internet; use ALB/NLB with AWS WAF for external access and place internal services in private subnets with security groups or AWS PrivateLink. For EC2 launch type, disable public SSH access and use Systems Manager or a restricted bastion host for troubleshooting.  
  - **Encryption:** Enforce encryption in transit using TLS for ECS tasks and ACM certificates for HTTPS. Ensure encryption at rest for storage (EBS, EFS) and S3 buckets, leveraging AWS KMS for key management.  
  - **Secrets and Sensitive Data:** Use ECS task definitions to inject secrets via Secrets Manager or Parameter Store and retrieve them at runtime. **Avoid embedding secrets in images or passing them in plain text**. Enable Secrets Manager rotation and update tasks during secret changes.  
  - **Logging and Audit:** Enable CloudTrail in all regions to audit ECS actions, store logs in a central S3 bucket, and use Security Hub or GuardDuty to monitor suspicious ECS events. Forward OS-level and container runtime logs to CloudWatch or a SIEM for analysis.  
  - **Compliance Configuration:** For compliance (e.g., PCI, HIPAA), regularly scan container images and running containers with Amazon Inspector or the tools approved by PepsiCo Infosec team.  
  - **Security Updates:** Keep the ECS agent and container base images updated. For EC2 launch type, patch Amazon Linux AMIs or use AWS Systems Manager. For Fargate, update application images regularly. Automate rebuilding and redeploying with tools like **Dependabot**. 
  - **Isolation Between Environments:** Separate ECS clusters for dev, test, and production or use different AWS accounts to prevent dev misconfigurations from affecting production. Apply network segmentation to isolate production tasks and reduce lateral movement risks.  

- **Network Connectivity Options:**  
  - **Public Internet Access:** **NOT ALLOWED by default for sensitive workloads.** ECS tasks must not use public IPs for direct internet access, *especially* for **PepsiCo Confidential** or **Restricted** data. Use private subnets with NAT Gateways for outbound traffic and Internet-facing ALBs in public subnets for inbound access, ensuring tasks remain in private subnets with tightly controlled security group rules.  
  - **VPC Interface Endpoints (AWS PrivateLink):** **Allowed/Recommended for internal traffic.** Use PrivateLink (VPC Endpoints) for ECS control plane, ECR, CloudWatch Logs, and secrets retrieval, ensuring private connectivity without internet traversal, crucial for Restricted data environments.  
  - **Internal Connectivity:** Ensure service-to-service communication stays within the VPC or uses PrivateLink. Use security groups for same VPC traffic, or PrivateLink for different VPCs/accounts. Avoid internet hairpinning; **use internal DNS** or **AWS Cloud Map** for private routing.  
  - **VPN/Direct Connect for Hybrid:** Use AWS PrivateLink for secure private access to ECS endpoints, and encrypt all data in transit with VPN tunnels or similar methods. 
  - **DNS and Service Discovery:** Integrate ECS with **AWS Cloud Map for service discovery**, ensuring VPC DNS resolution is enabled. **For hybrid setups, use Route 53 Resolver** to link on-prem DNS with AWS without public DNS exposure.  
  - **No AWS “Service Endpoint” (legacy Azure concept):** AWS uses VPC Interface Endpoints, not service endpoints like Azure; use PrivateLink to avoid public communication with AWS services.  
  - **Egress Controls:** Use AWS Network Firewall or egress-only internet gateways to control outbound traffic from ECS tasks, adding an extra layer of defense beyond security groups.  
  - **Bandwidth Considerations:** Size subnets and NAT gateways to handle traffic demands, using multiple gateways (one per AZ) for high throughput. Host images in-region with ECR and PrivateLink to reduce latency and data transfer costs.  

- **Networking & Security Architecture:** TBD

- **AWS Service Limitations:**  
  - **Compute Limits (Fargate):** AWS Fargate tasks support up to **16 vCPU, 120 GiB memory**, and **20 GiB default ephemeral storage** (configurable to 200 GiB). For larger workloads or GPU support, use EC2 launch type with GPU-equipped instances.  
  - **Task Definition Limits:** ECS task definitions support up to 10 containers and a JSON size of **~64 KB**. For more containers, split workloads across multiple task definitions or use a multi-task architecture.  
  - **Scaling and Throughput Constraints:** ECS has a default task launch rate of **500 tasks per service per minute**, requiring ~10 minutes to scale to 5,000 tasks. For faster scaling, distribute tasks across services or request a quota increase. API rate limits may throttle frequent ECS control plane actions.  
  - **Cluster and Service Quotas:** AWS accounts support up to **10,000 ECS clusters per region, 5,000 services per cluster, and 5,000 tasks per service**. Large deployments should segment workloads across clusters to avoid pushing limits, with quota increases available via AWS Support if needed.  
  - **Container Networking:** For **awsvpc** network mode, ensure sufficient VPC IP address space (/16 or /17) as each task consumes an IP. Check ENI limits for EC2 launch type, and plan subnets to avoid IP exhaustion in large deployments.  
  - **ECS Anywhere External Instances Considerations:** These count toward cluster size limits and **lack awsvpc networking**, relying on host networking without per-task security groups.  
  - **Feature Gaps vs Kubernetes:** ECS lacks native support for stateful sets, daemon sets, and built-in extensions like service mesh (use AWS App Mesh). For such features, consider using Kubernetes/EKS instead.  

- **SKU Features:**  

  - **EC2 Launch Type:** 
    - ECS cluster runs on Amazon EC2 instances.  
    - Full control over ECS clusters on EC2 instances, allowing specialized instance types (e.g., GPUs, memory-optimized), privileged Docker tasks, and Auto Scaling.
    - While offering flexibility and lower costs at scale, it requires more management effort (AMIs, instance lifecycle).
    - ECS itself has no extra cost; you pay for EC2 and associated resources. Use EC2 launch type for advanced features unavailable in Fargate.  

  - **AWS Fargate Launch Type:** 
    - Serverless compute engine that eliminates infrastructure management, launching tasks in isolated Firecracker microVMs for enhanced security.
    - Supports flexible CPU/memory configurations (up to 16 vCPUs/120 GB memory) and includes 20 GB ephemeral storage.
    - Fargate lacks support for privileged containers, GPUs, and custom networking.
    - Charges per resource usage, making it cost-efficient for spiky workloads or small services, with Savings Plans and Fargate Spot reducing costs further.
    - Ideal for teams prioritizing agility, microservices, and minimal operational overhead.  

  - **Amazon ECS Anywhere (External Instances):** 
    - ECS Anywhere extends ECS to manage containers on PepsiCo-managed infrastructure, including on-premises or other clouds.  
    - Supports hybrid use cases for edge, factory, or data center deployments, with external instances appearing as the "EXTERNAL" launch type.
    - Connections are secured with AWS IAM and HTTPS, but require reliable network links.
    - Normal on-prem security measures (e.g., disk encryption) apply.
    - While ECS Anywhere has no extra cost, AWS Systems Manager may incur fees, with hardware and network costs borne by the user.
    - **AWS Outposts:** ECS can run on AWS Outposts, treating it as a region extension with EC2 launch type. Outposts provide locally managed AWS hardware, integrating with ECS for low-latency on-prem systems.
    - **Use cases:** ECS Anywhere unifies on-prem container management, ideal for edge computing, eliminating separate orchestration systems. However, connectivity to AWS is critical for management, though tasks continue running locally during outages.  

  - **AWS Fargate vs EC2 – Summary:** ECS supports mixed launch types in a cluster using **Capacity Providers**, allowing tasks to run on Fargate, EC2, or a mix. Services can assign specific strategies to optimize efficiency.  

- **Related Service:** *TBD*

- **Alternatives:**  
  - **Amazon Elastic Kubernetes Service (EKS):** A managed Kubernetes service suited for multi-cloud or portable workloads, leveraging Kubernetes APIs and ecosystem features but with added complexity. EKS is ideal for Kubernetes-savvy teams, while ECS is simpler for AWS-centric environments.  
  - **AWS Batch:** Built on ECS/(EKS in some cases), abstracts batch job orchestration, handling compute provisioning, queuing, retries, and prioritization. Ideal for large-scale batch workloads, it manages job execution on ECS clusters or Fargate.  
  - **AWS App Runner:** App Runner simplifies deploying web apps and APIs, managing building, scaling, load balancing, and HTTPS using Fargate. It hides ECS details, offering ease of use but less flexibility, making it unsuitable for batch jobs or daemon processes. For advanced control, transition to ECS or EKS.  
  - **Serverless (AWS Lambda):** A serverless option for event-driven, short-running workloads, removing the need for managing containers. For long-running processes or complex microservices, use ECS as Lambda and ECS complement rather than replace each other.  
  - **On-Premises Orchestration (self-managed):** Running self-managed Kubernetes or Docker Swarm offers full control but involves high operational overhead. Prefer ECS Anywhere or EKS Anywhere for easier integration with AWS services. **(Not PepsiCo standard approval at this point of time, may be considered on case-case basis with EA approval)** 

## Compliance and Security Guidelines

- **Security Baseline InfoSec:**  
  - Information Security Specifications – *Amazon ECS* (Refer to PepsiCo InfoSec baseline documentation for AWS container services, which outlines required security controls, hardening guidelines, and compliance requirements specific to Amazon ECS and containerized workloads.)

## Ownership and Version Control

- **Service Architect:** Ramakrishna Ramaraju – *Cloud Service Architecture (CSA) Team*, ([<EMAIL>](mailto:<EMAIL>))  
- **Version Control:**  
  - v1.0 – 08 Aug 2025 – *Initial draft for Amazon ECS Service Catalog entry. (Ramakrishna Ramaraju)*