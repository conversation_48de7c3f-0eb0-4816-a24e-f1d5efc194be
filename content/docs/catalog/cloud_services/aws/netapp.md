---
weight: 7
title: "NetApp"
date: 2025-07-22
tags: []
summary: ""
---

# Amazon FSx for NetApp ONTAP

## Cloud Service Classification
- **Category:** Storage  
- **Cloud Provider:** AWS  

## Cloud Service Overview
- **Name:** Amazon FSx for NetApp ONTAP  
- **Description:**  
  Amazon FSx for NetApp ONTAP is a fully managed AWS storage service built on NetApp’s ONTAP file system. It offers reliable, scalable, high-performance shared file storage with advanced data management features (snapshots, clones, replication) and multi-protocol support (NFS, SMB, iSCSI, NVMe-oF). The service enables seamless access from Linux, Windows, and macOS environments, leveraging ONTAP’s efficiency features (compression, deduplication, automatic tiering) in the cloud.
- **SKU Approval Status:**  
  - **PepsiCo Approved:**  
    - **Multi-AZ Deployment (HA):** Fully supported for production use, providing automatic failover and high availability across two Availability Zones.  
    - **Single-AZ Deployment:** Allowed for non-production workloads (development, testing, secondary copies). Offers lower cost and higher performance but lacks AZ-level resiliency.  
  - **Not Approved:**  
    - No other deployment options.  
- **Allowed PepsiCo Data Classification:**  
  - Public  
  - Internal  
  - Confidential  
  - Restricted  

## Service Lifecycle
- **Release Date:** September 2, 2021  
- **Planned Decommission Date:** None announced  
- **Decommission Date:** Not applicable  

## Usage Guidelines
- **Features:**  
  - **Multi-Protocol File Access:** Supports NFS (v3, v4.x), SMB, iSCSI, and NVMe-over-TCP. Enables concurrent access from Linux/UNIX and Windows clients, and shared block storage.  
  - **Performance and Scalability:** Delivers petabyte-scale storage, high throughput (up to tens of GB/s), and sub-millisecond latency on SSDs. Performance is tunable and supports thousands of concurrent connections.  
  - **Advanced Data Management:** Includes ONTAP’s Snapshots, FlexClone for instant writable clones, and SnapMirror for efficient replication between on-premises and AWS or between FSx systems.  
  - **Storage Efficiency and Tiering:** Automatically tiers cold data to low-cost object storage, applies inline compression, deduplication, and compaction to reduce storage costs.  
  - **AWS Integration:** Managed via AWS Console, CLI, SDKs, or ONTAP tools. Integrates with AWS IAM, CloudTrail, CloudWatch, KMS, and Active Directory. Supports encryption in transit and at rest, and third-party security integrations.

- **Sample Use Cases:**  
  1. **Migrate On-Premises NAS Data:** Lift-and-shift file workloads to AWS, preserving features and management processes.  
  2. **High-Performance Storage for Databases & Analytics:** Provides low-latency, high-throughput storage for databases and analytics workloads.  
  3. **Dev/Test with Instant Clones:** Use FlexClone for rapid, space-efficient test environments.  
  4. **Multi-Platform File Sharing:** Enable concurrent access from Windows and Linux environments.  
  5. **Disaster Recovery and Backup:** Use SnapMirror and built-in backups for robust business continuity.

- **Limitations:**  
  - **Single-AZ vs Multi-AZ:** Single-AZ lacks AZ failure resiliency; use Multi-AZ for high availability.  
  - **Provisioned Throughput Ceiling:** File system throughput is fixed and must be monitored to avoid throttling.  
  - **Storage Quotas:** Limits on volumes and snapshots per file system; quotas can be increased via AWS Support.  
  - **No Native Multi-Region Sync:** Cross-region DR requires SnapMirror or backup copies; no global namespace.  
  - **Third-Party Antivirus:** No built-in scanning; customers must manage third-party antivirus solutions.  
  - **Learning Curve:** ONTAP administration may require additional expertise; some configuration choices are fixed at creation.  
- **Additional Guardrails:** TBD  
- **Used By:** TBD  
- **EA Declaration:** NOT a declared standard.

## Technical Guidelines
- **Best Practices:**  
  - **Deployment:** Use Multi-AZ for production and high availability; Single-AZ for non-critical or cost-sensitive workloads. Deployment type cannot be changed after creation.  
  - **Data Tiering:** Use ONTAP tiering policies to balance performance and cost. Monitor tier usage and adjust as needed.  
  - **Monitoring:** Use CloudWatch for health and capacity monitoring. Set alarms for SSD usage and throughput.  
  - **Backup and Recovery:** Combine ONTAP snapshots, FSx backups, and cross-region copies for data protection. Test restores regularly.  
  - **Volume Design:** Group data by access and security needs. Use multiple SVMs for segregation. Avoid extremely large directories.  
  - **Security:** Integrate with Active Directory for SMB, use least-privilege access, disable unused protocols, and monitor audit logs. Encrypt data in transit and at rest.

- **High Availability & Disaster Recovery:**  
  - **In-Region HA:** Multi-AZ provides automatic failover across AZs; Single-AZ offers node-level HA within an AZ. Use DNS endpoints for seamless client failover.  
  - **Cross-Region DR:** Use SnapMirror for near-real-time replication or AWS Backup for periodic copies. Choose based on RPO/RTO needs.  
  - **Failover Testing:** Regularly test failover and recovery procedures. Automate where possible.

- **Backup & Recovery:**  
  - **Automated Backups:** Daily automatic backups with configurable retention (7–90 days).  
  - **Manual Backups:** Take on-demand backups before major changes.  
  - **Snapshot Management:** Schedule frequent snapshots for quick recovery; monitor snapshot space.  
  - **Restore Testing:** Periodically test restores to ensure readiness.

- **Access Control & Security Configuration:**  
  - **Authentication:** Integrate with Active Directory for SMB; use export policies for NFS.  
  - **IAM Control:** Restrict FSx management actions via IAM; audit all operations with CloudTrail.  
  - **Encryption:** All data is encrypted at rest (KMS) and can be encrypted in transit.  
  - **Network Security:** Deploy in private subnets, restrict access via Security Groups, and use in-transit encryption.  
  - **Auditing:** Enable ONTAP audit logging and review configurations for compliance.

- **Network Connectivity Options:**  
  - **Public Endpoint:** Not allowed; FSx is accessible only within VPCs or connected networks.  
  - **VPC Access:** Required; supports VPC peering and Transit Gateway for cross-VPC access.  
  - **On-Premises Access:** Supported via VPN or Direct Connect.  
  - **Cross-Region Access:** Not natively supported; use replication or caching solutions.  
  - **Data Transfer:** Minimize cross-AZ and cross-region data flows to control costs and latency.

- **AWS Service Limitations:**  
  - **Capacity and Scaling:** Each file system supports up to 512 TiB SSD (Multi-AZ) or 1 PiB (Single-AZ with multiple HA pairs).  
  - **Performance:** Multi-AZ supports up to 6 GB/s and 200,000 IOPS; Single-AZ can scale higher with multiple HA pairs.  
  - **Minimum Resources:** Minimum 1 TiB SSD and baseline throughput required.  
  - **Feature Gaps:** Some on-prem ONTAP features may not be available or may have cloud-specific limitations.  
  - **Maintenance:** AWS manages updates; maintenance windows may cause brief failovers.

- **SKU Features:**  
  - **Single-AZ:**  
    - **Architecture:** One or more HA pairs in a single AZ; synchronously replicates data within the AZ.  
    - **Scalability:** Supports up to 12 HA pairs and 1 PiB SSD.  
    - **Performance:** Lowest latency; ideal for high-throughput, non-critical workloads.  
    - **Use Cases:** Dev/test, analytics, or production with external DR.  
  - **Multi-AZ:**  
    - **Architecture:** HA pair across two AZs; synchronous replication for AZ-level resiliency.  
    - **Scalability:** One HA pair per file system; up to 512 TiB SSD.  
    - **Performance:** Slightly higher latency due to cross-AZ writes; built-in redundancy.  
    - **Use Cases:** Production workloads requiring high availability and AZ failure protection.  
  - **Common Features:** Both support ONTAP features, two-tier storage, encryption, and compliance. Pricing differs by deployment type.
- **Related Service:** TBD  

## Compliance and Security Guidelines
- **Security Baseline InfoSec:**  
  - Refer to PepsiCo InfoSec baseline document for required security configurations and controls (TBD).

## Ownership and Version Control
- **Service Architect:** Dariusz Korzun – <EMAIL>  
- **Version Control:**  
  - v.1: 22 Jul 2025 – Document created (Dariusz Korzun)  
  *(Further revisions and approvals to be documented here.)*