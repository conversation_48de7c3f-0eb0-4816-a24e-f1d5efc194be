---
weight: 9
title: "Route53"
date: 2025-07-22
tags: []
summary: ""
---

# Amazon Route 53

## Cloud Service Classification
- **Category of the Service:** Networking (DNS)  
- **Cloud Provider:** AWS

## Cloud Service Overview
- **Name:** Amazon Route 53  
- **Description:**  
  Amazon Route 53 is a scalable and highly available cloud DNS service that translates domain names into IP addresses to route traffic to applications on AWS or on-premises. It offers domain registration, DNS management, health checks, and automated traffic routing to enhance reliability and fault tolerance.
- **SKU Approval Status:**  
  - **PepsiCo Approved:**  
    - Amazon Route 53 Hosted Zones (Public & Private DNS)  
    - Amazon Route 53 Resolver  
    - Amazon Route 53 Health Checks and Traffic Flow
  - **Not Approved:**  
    - Amazon Route 53 Domain Registration
- **Allowed PepsiCo Data Classification:**  
  - Public  
  - Internal  
  - Confidential  
  - Restricted  

## Service Lifecycle
- **Release Date:** December 5, 2010  
- **Planned Decommission Date:** No announced.  
- **Decommission Date:** Not applicable at this time.  

## Usage Guidelines
- **Features:**  
  - **Domain Name Registration** – Ability to register new domain names directly through AWS. Route 53 acts as a domain registrar for numerous top-level domains, allowing management of domain purchase, renewal, and DNS in one place.  
  - **Authoritative DNS (Public & Private Hosted Zones)** – Globally distributed DNS service that routes user queries to infrastructure on AWS or elsewhere, supporting public DNS zones for external applications and private DNS zones within Amazon VPC for internal resolution.
  - **Flexible Traffic Routing Policies** – Supports multiple routing policy types to control DNS responses based on application needs. 
  - **Health Checks and DNS Failover** – Route 53 health checks monitor endpoint availability and performance across protocols like HTTP, HTTPS, and TCP, with integration for CloudWatch alarms.
  - **Amazon Route 53 Resolver (Hybrid DNS)** – A recursive DNS resolver service that bridges AWS and on-premises DNS via Resolver endpoints for hybrid cloud scenarios. 
  - **Traffic Flow (DNS Policy Engine)** – Traffic Flow is a visual tool for creating complex DNS routing configurations, combining rules like latency-based, geolocation, and health checks into automated policies.  
  - **Alias Records and AWS Integration** – Route 53 supports Alias records that map DNS names to various AWS resources, such as Elastic Load Balancers, CloudFront distributions, API Gateways, S3 static websites, and VPC Interface Endpoints. 
  - **DNS Security (DNSSEC)** – Route 53 supports DNSSEC, which cryptographically signs DNS records to ensure responses are authentic and protect against DNS spoofing.
- **Sample Use Cases:**  
  - **Public Website DNS Hosting:** Route 53 hosts DNS for public-facing websites, leveraging its global infrastructure to reduce user lookup latency by responding from the nearest edge location.
  - **Private Internal DNS (Service Discovery):** Route 53 Private Hosted Zones enable secure internal DNS management for AWS services, allowing microservices in linked VPCs to resolve endpoints using custom domain names without exposing data to the public Internet. 
  - **Multi-Region Failover for High Availability:** Implement active-passive failover across regions using Route 53’s failover routing policy. 
  - **Latency-Based Routing for Global Users:** Latency-based routing in Route 53 directs user queries to the AWS region or endpoint with the lowest latency from their location to improve performance.
  - **Hybrid Cloud DNS Resolution:** Route 53 Resolver enables DNS integration between on-premises and AWS, allowing cloud-based applications in a VPC to resolve on-premises domain names via outbound Resolver endpoints.  
- **Limitations:**  
  - **DNS Change Propagation:** DNS updates in Route 53 propagate globally within ~60 seconds, but clients rely on cached responses until the record's TTL expires, requiring careful TTL selection to balance change responsiveness and caching efficiency.
  - **DNS Query Caching and Staleness:** DNS caching can delay failover events, so using short TTLs for dynamic or critical endpoints improves agility at the cost of increased query traffic.  
  - **No Automatic Zone Transfers:** Route 53 does not support AXFR/IXFR transfers, requiring DNS zone integration with other platforms to be done via APIs, custom scripts, or third-party tools.
  - **Private DNS Restrictions:** Route 53 Private Hosted Zones are only resolvable within associated VPCs or on-prem systems if Resolver endpoints are set up to forward queries. Private zones can be linked to multiple VPCs within limits, and exceeding these requires workarounds like duplicating zones or using Resolver Profiles.
  - **Service Availability and Constraints:** Route 53 ensures 100% availability for DNS query resolution, but its management API has rate limits of 5 requests per second per account for DNS changes. Frequent updates, such as during auto-scaling, may require strategies like batching changes or using weighted records to avoid throttling. 
  - **Cost Considerations:** Route 53 incurs costs for hosted zones, queries, routing policies, and health checks, with no free tier, requiring careful budgeting for high-volume or large-scale deployments.
  - **Additional Guardrails:** TBD  
  - **Used By:** TBD (list & links to some onboarded applications/services using Route 53)  
  - **EA Declaration:** NOT a declared standard (not formally part of Enterprise Architecture standards yet).

## Technical Guidelines
- **Best Practices:**  
  - **DNS Record TTL Management:** Choose DNS record TTLs based on update needs, using lower TTLs for quick failovers or changes and higher TTLs for static records to optimize caching and reduce query rates.
  - **Use Alias Records When Possible:** Use Route 53 Alias records instead of CNAMEs for AWS resource targets to enable resolution at the zone apex while improving performance and eliminating alias query costs. For instance, Alias records can directly point example.com to an ALB or CloudFront distribution, avoiding additional DNS lookups and enhancing efficiency. 
  - **Leverage Latency and Geolocation Routing Appropriately:** Use latency-based routing to improve responsiveness by serving users from the nearest endpoints or geolocation routing to direct traffic based on user location for compliance or localization. Combine these with health-check failover to handle outages, and use Route 53 Traffic Flow to design and visualize complex routing policies. 
  - **Ensure a Default DNS Response:** When using advanced routing policies like geolocation, configure a default record to handle queries that don’t match any criteria, ensuring all users receive a DNS response. For example, geolocation records require a "Default" record to prevent resolution failures for unspecified locations.
  - **Delegate and Isolate DNS Zones:** Use a modular DNS design by delegating subdomains and separating hosted zones for environments or applications to enhance security, management, and prevent accidental impacts across zones.
  - **Use Infrastructure as Code for DNS:** Use Infrastructure-as-Code tools or scripts to manage Route 53 configurations, enabling version control, backups, and change reviews to prevent misconfigurations and outages. 
  - **High Availability for Route 53 Resolver:** For hybrid DNS with Route 53 Resolver endpoints, deploy redundant endpoints across separate Availability Zones to ensure availability during outages and distribute queries for load balancing. 
  - **Avoid Resolver Loops:** Avoid routing loops in hybrid DNS by ensuring VPC Resolver rules and outbound endpoints don't create circular forwarding paths, such as on-prem servers forwarding queries back to the same VPC.
  - **Optimize Health Checks:** Use health checks strategically for DNS failover, adhering to AWS best practices, and for non-public endpoints, leverage CloudWatch metrics with Route 53 API updates since health checkers require public IP access.
  - **Monitoring and Auditing:** Enable Route 53 query logging for hosted zones to analyze traffic patterns, troubleshoot DNS issues, and detect anomalies, sending logs to CloudWatch or S3. Additionally, monitor CloudWatch Resolver metrics and use AWS Config or CloudTrail to track changes to Route 53 resources for auditing and compliance. 
- **High Availability & Disaster Recovery:**  
  - **Resilience of DNS Infrastructure:** Route 53 provides 100% availability for DNS queries through globally distributed edge locations, anycast routing, and shuffle-sharding, ensuring reliable responses even during server or network impairments. Its high availability is inherent and requires no customer action, with AWS offering credits for any failures caused by Route 53. 
  - **Application Failover and DR:** Use Route 53 failover records with secondary endpoints and regular testing to ensure automatic traffic redirection during disasters as part of a comprehensive disaster recovery plan.  
  - **Backup of DNS Configurations:** Regularly export and securely store Route 53 DNS records or IaC templates, treating them as critical data to enable quick restoration in case of accidental deletion or corruption.
  - **Cross-Account and External DNS Redundancy:** For business continuity, consider a secondary DNS provider or a backup Route 53 account with synchronized zone data to mitigate account-specific issues. While many rely solely on Route 53's reliability, critical domains may benefit from additional precautions, such as ensuring the registrar can quickly switch name servers and having documented procedures for such scenarios.
- **Backup & Recovery:**  
  - **No Native Snapshot – Use IaC:** Use Infrastructure as Code templates or scripts to back up Route 53 DNS records for quick restoration in case of unintended changes or deletions. 
  - **Periodic Exports:** As an additional safeguard, Periodically export and securely store DNS records to enable quick reconstruction of zones during recovery, updating backups after major changes.  
  - **Domain Registration Recovery:** Enable auto-renew and registrar lock for Route 53 domain registrations to prevent expiration or unauthorized transfers, and act quickly with AWS Support if issues arise. 
  - **Testing Restores:** Periodically test rebuilding hosted zones from saved configurations in a non-production environment to validate backups and ensure familiarity with the restore process.
  - **Soft Delete for Zones:** Deleting a hosted zone in Route 53 is immediate and permanent, so double-check before proceeding, as recreating the zone may not retain the same name servers, potentially causing disruptions. To prevent accidental deletions, restrict zone deletion permissions to administrative roles and confirm all actions carefully.
- **Access Control & Security Configuration:**  
  - **IAM Governance:** Use IAM with least privilege to control Route 53 resource changes, restricting sensitive actions like zone deletion or domain transfers to admin accounts and fine-tuning access with condition keys and tags.
  - **Multi-Factor & Change Management:** Require MFA and change approval workflows for IAM principals managing critical DNS to ensure reviews and logging for impactful Route 53 changes.  
  - **DNSSEC Deployment:** Enable DNSSEC on public hosted zones to secure sensitive domains by signing DNS responses with cryptographic keys, allowing resolvers to validate their authenticity. This requires managing a key-signing key in AWS KMS and publishing a DS record at your registrar, adding overhead but providing essential protection against DNS spoofing for critical domains.
  - **Route 53 Resolver DNS Firewall:** Use the Route 53 Resolver DNS Firewall to enhance VPC security by creating domain allow/deny lists and blocking malicious or unauthorized DNS queries. This is crucial for preventing data exfiltration in VPCs handling sensitive data and can be managed via Route 53 Resolver or integrated with AWS Firewall Manager for centralized control. 
  - **Network Security for DNS Queries:** AWS VPC instances use the internal AmazonProvidedDNS resolver (.2 address) for secure DNS resolution without needing direct Internet access. For locked-down VPCs or on-premises systems using Resolver endpoints, ensure security groups and NACLs allow DNS traffic while restricting access to trusted IPs for enhanced security.
  - **Encryption and Data Protection:** DNS queries are typically unencrypted, but while AWS secures internal queries via its network, external queries to Route 53 require custom setups like DNS over TLS or VPNs for encryption. Route 53 protects hosted zone data at rest under AWS's security measures and complies with various certifications 
  - **Logging and Auditing:** Enable CloudTrail to log Route 53 API calls for auditing and troubleshooting, and use AWS Config to track hosted zone changes and alert on unexpected modifications. For Resolver, monitor VPC Flow Logs or DNS query logs to detect anomalies like NXDOMAIN spikes or unauthorized data exfiltration.
- **Network Connectivity Options:**  
  - **Public Hosted Zones (Internet-Facing DNS):** Public hosted zones in Route 53 are accessible from the Internet and should only contain non-sensitive, intended-to-be-public records like website URLs or product endpoints. Use IAM to restrict who can modify public records, and for applications handling sensitive data, secure the application layer and use opaque hostnames to avoid exposing details. Sensitive internal service names should never be published in public DNS.
  - **Private Hosted Zones (AWS Internal DNS):** Use private hosted zones for DNS names meant to be resolved only by internal AWS resources within specified VPCs, ensuring queries never leave the AWS network. This is essential for Confidential or Restricted data, such as database endpoints or internal service names, to maintain an isolated and secure name resolution environment. 
  - **On-Premises and Hybrid Connectivity:** To enable DNS resolution between AWS private DNS and on-prem systems, configure Route 53 Resolver endpoints with both inbound endpoints for routing on-premises queries to AWS and outbound endpoints for forwarding AWS queries to on-prem DNS servers. 
  - **DNS over VPC Peering and AWS Networking:** Private hosted zones can be shared across accounts and VPCs using Resource Access Manager or by associating VPCs, but DNS queries only work in associated VPCs. To enable cross-VPC name resolution, options include associating VPCs with a common private zone, using a centralized DNS account with outbound Resolver endpoints, or employing AWS Cloud Map, as VPC Peering alone does not forward DNS queries. 
  - **No Customer-managed Endpoints for Route 53 API:** Route 53’s management API endpoints are public and require internet access for API calls, unlike Route 53 Resolver, which uses VPC endpoints for query traffic. DNS query traffic uses AWS's global anycast network and cannot be pinned to a specific region or VPC, as it is automatically handled by AWS.
- **Networking & Security Architecture:** TBD (to be defined per specific solution, showing how Route 53 fits into the application’s network/security design, e.g. diagrams of public DNS plus WAF/Firewall, private DNS in VPC with endpoints, etc.)

## AWS Service Limitations
- **Quotas on DNS Zones and Records:** Each AWS account can create up to 500 hosted zones (public and private) and each zone can hold 10,000 records by default, with quota increases available upon request. Additional limits include 100 records of the same name and type for specific routing policies, and exceeding these limits may require re-evaluating your design or requesting increases from AWS.
- **VPC Association Limits for Private Zones:** A private hosted zone can be associated with up to 300 VPCs, and large organizations nearing this limit can use Route 53 Resolver Profiles or a hierarchical DNS approach to scale. While a VPC can associate with unlimited private zones, excessive associations can complicate management.
- **API Rate Limiting:** Route 53’s API is limited to 5 requests per second per account, with no option to increase this limit, requiring automation to combine up to 1,000 changes per request or space out updates. Additionally, changes to the same zone must wait for prior requests to complete to avoid errors.
- **Health Check Limits:** Each AWS account can have up to 200 Route 53 health checks by default, with the option to request increases or consolidate checks as needed. Health checks have a minimum interval of 30 seconds, so DNS failover may take 1–2 minutes to trigger after an outage is detected.
- **Lack of Transactional Changes:** Route 53 DNS updates are eventually consistent, requiring all changes in a single API call for synchronization, though propagation may still lead to mixed resolutions. Use low TTLs or add new records before removing old ones for smoother transitions during updates.
- **No Native Multi-Cloud DNS Coordination:** Route 53 is AWS-specific and doesn’t coordinate with other cloud DNS services, requiring third-party DNS providers or a unified approach for multi-cloud applications. It focuses on AWS and internet DNS routing.

## SKU Features
*(Amazon Route 53 is a single service; it does not have multiple “tiers” or SKUs like some Azure services. All features are available on a pay-per-use basis. Key components of the service include:)*  
- **Domain Registration:** Route 53 allows domain registration with hundreds of TLDs, auto-creating a public hosted zone for DNS management, while offering features like WHOIS privacy, auto-renewal, and domain transfers. Domain registration is separate from DNS hosting, enabling flexibility to use Route 53 for either independently.
- **Public Hosted Zones (Authoritative DNS):** Route 53 offers managed DNS zones with global name servers, supporting common record types and advanced routing policies like weighted, latency, and failover. It allows alias records for AWS resources with integrated health checks, optional DNSSEC for security, and scales to meet any DNS query traffic.
- **Private Hosted Zones:** Private DNS zones in Route 53 are visible only within specified VPCs, supporting internal domain resolution and the same record types and routing policies as public zones (excluding DNSSEC). They enable split-horizon architectures and simplify service discovery within VPCs without requiring custom DNS servers.  
- **Health Checks:** Route 53 health checks monitor endpoints, integrate with CloudWatch alarms, and can trigger DNS failover by marking endpoints unhealthy when checks fail. They support advanced options like SSL verification, HTTP response matching, and global redundancy, but are billed per check.  
- **Traffic Flow Policies:** Traffic Flow lets you create reusable, versioned policies combining routing rules, such as location-based routing with failover, which can be applied to multiple DNS names. It simplifies managing complex global applications, offering a visual editor and incurring a small charge per policy record.
- **Route 53 Resolver Endpoints:** Route 53 Resolver endpoints enable hybrid DNS by allowing inbound queries to resolve VPC and private zone names, and outbound queries forward to external DNS servers via configurable rules. Each endpoint supports 2–6 IPs for resiliency, while Resolver also offers Query Logging for DNS activity tracking and DNS Firewall to manage allow/deny rules for specific domains.  
- **DNS Firewall:** Route 53 Resolver DNS Firewall filters DNS queries using rule groups tied to VPCs, allowing or blocking domains and configuring responses like NXDOMAIN or override IPs. It integrates with AWS Firewall Manager for centralized enforcement in multi-account environments. 
- **Query Logging:** Route 53 supports query logging for public hosted zones and Resolver (VPC), sending logs to CloudWatch or S3 with details like source IP, queried domain, and response. Query logging is valuable for security audits, analytics, and debugging DNS resolution issues. 
- **Service Integrations:** Route 53 integrates with AWS services like ACM for DNS certificate validation, VPC for private DNS, ELB for health-based alias records, Cloud Map for service discovery, and Outposts for local DNS resolution. It also supports advanced features like Resolver Query Steering and Profiles for scaling private DNS in multi-account setups, all with associated costs
- **Related Service:** TBD (e.g., AWS Cloud Map for service discovery as an alternative, AWS Global Accelerator or AWS CloudFront for traffic management vs DNS-based routing, etc.)

## Compliance and Security Guidelines
- **Security Baseline – InfoSec:**  
  - **Information Security Specifications – Amazon Route 53:** (Ensure alignment with internal InfoSec policies for DNS. Route 53 is covered under AWS SOC, ISO, PCI, and other compliance programs. For internal compliance, ensure DNS is included in cloud architecture reviews, with particular attention to controlling changes, monitoring DNS traffic, and using DNSSEC/DNS Firewall where appropriate. Refer to PepsiCo InfoSec baseline documentation for DNS services on AWS for detailed controls and guidelines.) [Placeholder for a link to internal Security Baseline document specific to Route 53]

## Ownership and Version Control
- **Service Architect:** Rakesh Ponnada (<EMAIL>)
- **Version Control:**  
  - **v.1:** 22 Jul 2025 (TBD)