---
weight: 19
title: "SQS"
date: 2025-08-07
tags: []
summary: ""
---

## Cloud Service Classification

* **Category of the Service** Messaging
* **Cloud Provider** AWS

## Cloud Service Overview

* **Name** Amazon Simple Queue Service (SQS)
* **Description** Amazon SQS is a fully managed distributed message queuing service that enables decoupling of application components. It provides a secure, durable, highly-available hosted queue for storing messages as they travel between microservices or distributed systems. SQS lets you **send, store, and receive messages at any volume** without losing messages or requiring other services to be available. It offers **Standard** queues (which provide high throughput with at-least-once delivery and best-effort ordering) and **FIFO** (First-In-First-Out) queues (which guarantee ordered, exactly-once processing of messages) to suit different use cases. Under the hood, messages are redundantly stored across multiple servers/AZs for durability, and the service automatically handles scaling and infrastructure management on behalf of the user.
* **SKU Approval Status** 

  * **PepsiCo Approved:**

    * Standard Queue
    * FIFO Queue
  * **Not Approved:**

    * None (all SQS queue types are allowed)
* **Allowed PepsiCo Data Classification** 

  * Public
  * Internal
  * Confidential
  * Restricted

## Service Lifecycle

* **Release Date** November 2004 (public preview introduction); production launch on July 13, 2006.
* **Planned Decommission Date** No announced decommissioning plans (service is actively maintained).
* **Decommission Date** Not applicable at this time.

## Usage Guidelines

* **Features** 

  * *Fully Managed Queue Service:* No need to provision or manage servers—SQS handles all infrastructure, allowing unlimited queues and virtually unlimited message volume with automatic scaling. The service delivers messages reliably at scale, storing them redundantly for high availability and durability.
  * *Queue Types (Standard vs. FIFO):* Supports **Standard queues** for maximum throughput (highly parallel, at-least-once delivery, potential duplicates and out-of-order delivery) and **FIFO queues** for ordered, exactly-once processing (guaranteed message order and no duplicates using deduplication mechanisms). FIFO queues also support a *high throughput mode* for higher message rates when needed.
  * *Message Processing & Controls:* Offers configurable **visibility timeouts** to prevent multiple consumers from processing the same message simultaneously, **dead-letter queues** (DLQ) to hold undeliverable messages for later inspection, and **delay queues** to postpone delivery of new messages. Clients can use **long polling** on receives to wait for messages, reducing empty responses, and can send/receive/delete in **batches** (up to 10 messages per call) for efficiency.
  * *Integration & Security:* Natively integrates with other AWS services (e.g., Amazon EC2, AWS Lambda, Amazon SNS) to trigger processing or fan-out messages. Supports **message encryption at rest** using AWS-managed or customer-managed KMS keys for sensitive data, and fine-grained **access control** via IAM policies and SQS queue policies (you control who can send/receive messages).
* **Sample Use Cases** 

  * *Decoupling Microservices:* Use SQS to loosely couple components of a cloud application. For example, one service places tasks (orders, processing requests, etc.) onto a queue, and downstream services consume those tasks asynchronously. This improves resilience and allows each component to scale independently.
  * *Asynchronous Background Processing:* Front-end applications can offload time-consuming jobs to SQS. For instance, in a web application, when a user uploads an image or submits a request, the app enqueues a message for processing (image resizing, email sending, database updates) and immediately returns a response to the user. A back-end worker or AWS Lambda function then processes the message in the background, improving responsiveness.
  * *Auto-Scaling Worker Pools:* SQS buffers incoming work and helps automatically scale worker instances. For example, an image processing service might poll an SQS queue; as the queue length grows, additional EC2 instances or containers can be spawned (triggered via CloudWatch metrics or AWS Auto Scaling) to consume messages, and then scale in when the backlog subsides. This ensures cost-effective processing of fluctuating workloads without dropping messages.
  * *Ordered Task Processing:* In workflows where the order of events is critical (e.g. financial transactions, inventory updates, or audit logs), use FIFO queues. For example, a FIFO queue can ensure that operations on a customer’s account are processed exactly in the order they were received, and that each operation is only executed once. FIFO queues guarantee message sequence and eliminate duplicates, providing consistency for such transactional use cases.
* **Limitations** 

  * *Message Size & Payloads:* Each SQS message can carry at most 256 KB of text in its body. If larger payloads are required, the application must store the data in an external service (such as Amazon S3) and send only a pointer or use the SQS Extended Client Library to automatically offload to S3.
  * *Message Retention:* SQS is not meant for long-term storage of messages. By default, messages are retained for 4 days, and the maximum retention period is 14 days. Any message not processed within the retention window will be automatically purged. This means consumers must handle messages in a timely manner, and important data should be persisted to durable storage if it needs to live longer.
  * *Delivery Semantics:* Standard queues provide **at-least-once delivery**, which means a message might be delivered to consumers multiple times, and there is no guarantee of ordering. Applications using standard queues should be designed to handle duplicate messages and out-of-order processing (e.g., by making operations idempotent). In contrast, FIFO queues ensure exactly-once, ordered delivery but may have lower throughput (see below).
  * *Throughput Constraints:* **Standard queues** have a very high throughput (virtually unlimited number of API calls per second) and scale automatically with demand. **FIFO queues**, however, are throttled to 300 transactions per second per API action (Send, Receive, or Delete) by default, which equates to up to 300 messages/s (or 3,000 messages/s with batches of 10) for each queue. If an application requires higher throughput with ordering, FIFO queues have a high-throughput mode that can be enabled (partitioning messages across multiple message groups) to increase capacity.
  * *In-flight Message Limit:* There is a limit to how many messages can be “in-flight” (received by consumers but not yet deleted) on a queue. For standard queues, the maximum is \~120,000 in-flight messages per queue; exceeding this will result in `OverLimit` errors or the queue refusing to return more messages until some are processed and deleted. (FIFO queues have a similar limit per message group, effectively 20,000 in-flight per group by default.) This requires applications to delete messages promptly and possibly scale out consumers if backlogs build up.
* **Additional Guardrails** TBD
* **Used By** TBD (list & links to some onboarded applications using SQS)
* **EA Declaration** NOT a declared standard. (Not an officially declared enterprise standard service at this time.)

## Technical Guidelines

* **Best Practices** 

  * *Use Long Polling and Batching:* Configure **long polling** (e.g., `WaitTimeSeconds` on `ReceiveMessage`) to avoid excessive empty responses – this reduces API calls and cost while retrieving messages as soon as they arrive. Additionally, use batch operations (`SendMessageBatch`, `DeleteMessageBatch`) to group up to 10 messages in a single API call for better throughput and cost efficiency.
  * *Error Handling & Dead Letters:* Implement robust error handling. If a message fails to process successfully after a few attempts, send it to a **Dead-Letter Queue (DLQ)**. DLQs allow isolating problem messages that cannot be processed (e.g., due to malformed data or downstream issues) so they can be reviewed and remedied separately. Tune the redrive policy (max receive count before moving to DLQ) based on the criticality of messages.
  * *Idempotent Consumers:* Design consumers to handle duplicates gracefully. Because of the at-least-once model, the same message may be delivered more than once. Ensure that processing actions (database updates, etc.) are **idempotent** – e.g., use de-duplication tokens or check if an action was already performed – so that repeated delivery does not cause inconsistent results. If using FIFO queues, take advantage of content-based deduplication or message deduplication IDs to prevent reprocessing of duplicate messages.
  * *Visibility Timeout Tuning:* Set an appropriate **visibility timeout** for your queues and messages. The default visibility timeout is 30 seconds, which may be too short for longer processing tasks, or too long for quick tasks. Adjust it to match the typical processing time of your messages, so that if a consumer crashes or fails to process a message, the message reappears in a timely manner for another consumer to retry. For very long-running tasks, consider periodically extending the visibility timeout via the `ChangeMessageVisibility` API, or breaking the task into smaller sub-tasks.
  * *Monitoring & Scaling:* Leverage Amazon CloudWatch to monitor key metrics on your queues. Track **ApproximateNumberOfMessagesVisible** (queue backlog size) and **ApproximateAgeOfOldestMessage** to detect if consumers are falling behind. Set up CloudWatch Alarms to alert or trigger scaling actions (e.g., launch more consumer instances or AWS Lambda concurrency) when backlogs grow beyond thresholds. This ensures your processing keeps up with demand and helps maintain low latency. Also monitor **NumberOfMessagesNotVisible** (in-flight messages) to know how many messages are being processed at a given time.
  * *Integration with Auto Scaling:* For services running on EC2 or container tasks processing SQS messages, consider integrating with **Auto Scaling** based on queue length. For example, you can use a scaling policy that adds instances when the backlog per instance exceeds a certain limit (ensuring the worker fleet grows to handle surges, then shrinks when the queue is drained). This dynamic scaling approach keeps throughput high while optimizing cost.
  * *Security Best Practices:* Follow AWS security best practices for SQS: **do not allow public access** to queues (limit access to specific IAM principals), apply **least privilege** in IAM policies (each consumer/producer gets only the rights needed for specific queues), and always use endpoints over HTTPS. Enable **server-side encryption** on queues carrying sensitive data so that message content at rest is protected by AWS KMS keys. In transit, SQS endpoints already use TLS; you can enforce this by requiring the `aws:SecureTransport` condition in queue policies. Use **IAM roles** (for EC2, Lambda, etc.) instead of embedding AWS credentials in code, so that credentials are rotated automatically and access is scoped appropriately.
  * *Logging and Auditing:* AWS CloudTrail can be used to log SQS API calls for auditing (including who created or deleted queues, who sent or received messages, etc.). Ensure CloudTrail is enabled for your account and includes SQS data events if auditability of message-level operations is required. Additionally, maintain application logs for message processing results (or use AWS X-Ray for tracing) to have an audit trail of how each message was handled.
* **High Availability & Disaster Recovery** 

  * *In-Region High Availability:* Amazon SQS is a regional service that **automatically spans multiple Availability Zones** in the region. The queue infrastructure is redundant within the region — messages are stored on multiple AZ-resilient storage servers, which means transient failures or even an AZ outage will not lose messages or take the queue offline. There are no user-visible knobs for HA; simply use SQS normally and it will handle failover between redundant infrastructure as needed.
  * *Multi-Region Resilience:* SQS **does not replicate queues across regions** by itself – each queue exists in a single AWS region. If you require **disaster recovery** across regions, you must architect it at the application level. Common approaches include deploying a **secondary queue in a backup region** and using an AWS SNS topic to **fan-out messages** to both regional queues (so that the same messages go to two queues in different regions), or having producers explicitly send messages to two queues. The consuming application in the DR region can remain passive until needed, or process in parallel if doing active-active. Keep in mind, if you do active-active with multi-region queues, you need to handle **duplicate messages and eventual consistency** between regions, as well as **idempotency** in the consumers. Also, ensure that any persisted data triggered by queue messages (in databases, etc.) is replicated across regions for a full DR strategy.
  * *Failover Process:* In a disaster scenario where the primary region is unavailable, the application should have logic to switch to using the queue in the secondary region. This may involve using DNS or configuration flags to redirect producers and consumers. There is no automatic failover for SQS endpoints, so testing the manual or automated failover process is important. After the primary region is restored, you may choose to either continue operating in the secondary or sync any pending messages back to the primary (if feasible) before switching back.
* **Backup & Recovery** 

  * *Ephemeral Nature of Queues:* SQS is intended as a transient message store, and **it does not offer built-in backup or point-in-time restore** for the messages in a queue. In an ideal design, the queue’s consumers should be continuously draining messages, and the queue backlog remains low. If message data must be kept for longer-term auditing or if you want the ability to “replay” messages, you should proactively copy or archive those messages to durable storage.
  * *Message Archival Strategies:* Consider leveraging the **Amazon SQS Extended Client Library** (available for Java and others) which can automatically store message payloads in **Amazon S3** when they exceed a certain size threshold. By using this, or by designing producers to dual-write to an S3 bucket (or DynamoDB, etc.), you have a persistent log of messages outside of SQS. In case of an unexpected deletion or to recover from an outage, those records in S3 could be re-queued or processed independently. This approach also circumvents the 256 KB message size limit by offloading contents to S3.
  * *Dead-Letter Queue Handling:* Treat DLQs as a form of backup for poisonous messages. Messages that land in a DLQ aren’t automatically reprocessed; however, you can set up processes to periodically review DLQ contents. If appropriate, you may move messages from a DLQ back to the main queue (or to an alternate queue) after resolving the underlying issue. This can serve as a manual recovery mechanism for messages that would otherwise be lost after exceeding their processing attempts.
  * *Persistence for Critical Messages:* For mission-critical data that cannot risk loss, you might implement a pattern where each message is also written to a persistent data store (like a database or log system) upon ingestion. That way, if SQS were to lose messages (e.g., beyond the 14-day retention or in a rare failure), you have a record to fall back on. AWS offers capabilities like **AWS Backup** for some services, but AWS Backup does *not* natively support backing up SQS queues (since messages are meant to be short-lived). Therefore, custom backup strategies as described are necessary if needed.
* **Access Control & Security Configuration** 

  * *Queue Policies & IAM:* Use SQS **queue access policies** and IAM policies to strictly control who (which AWS principals) can perform actions on your queues. **Do not allow public access** to SQS. In practice, avoid using wildcard principals (`*`) in policies. Instead, specify the particular AWS accounts, IAM roles, or users that need access. For example, if only a certain application microservice should send messages, only that service’s IAM role should have `SendMessage` permission on the queue. Likewise, consumers should have `ReceiveMessage` (and `DeleteMessage`) rights, but no broader access. Implementing such **least privilege** ensures minimal exposure.
  * *IAM Roles for AWS Services:* If your applications run on AWS compute services (EC2, ECS, Lambda, etc.), leverage **IAM Roles** for those services to grant access to SQS, rather than embedding AWS access keys. For instance, an EC2 instance profile or a Lambda execution role can grant the necessary SQS permissions. This way, credentials are temporary and rotated automatically, and you eliminate the risk of secret leakage. This follows AWS security best practices and prevents the need to manage long-term keys for SQS access.
  * *Encryption at Rest:* Enable **Server-Side Encryption (SSE)** on SQS queues carrying sensitive or confidential data. SSE encrypts message content using an AWS KMS key when the message is at rest in SQS storage. You can use the AWS managed SQS KMS key by default, or specify a customer-managed KMS key if you require control over key rotation and access. Encryption is transparent to the consumer and producer – AWS handles encryption and decryption, so using SSE does not require code changes (just the appropriate queue property and IAM permissions to use the KMS key).
  * *Encryption in Transit:* All communication with SQS should be done over HTTPS (which is default when using AWS SDKs or console). To enforce this, you can add a condition in the queue’s policy to only allow requests that come via SSL/TLS. Specifically, use the `aws:SecureTransport` condition set to `true` in your queue policy, which will deny any attempt to interact with the queue over an unencrypted connection. This ensures data in transit is always protected.
  * *VPC Endpoints (PrivateLink):* For an extra layer of security, especially for Restricted data, use **Amazon VPC Endpoints** for SQS. A VPC Interface Endpoint places an elastic network interface in your VPC that connects to SQS privately. By using an endpoint, your SQS API calls stay within the AWS network and do not traverse the public internet. You can also lock down your SQS queue policy to only allow access via your specific VPC endpoint(s), preventing any traffic to the queue from outside your VPC. This is recommended for sensitive workloads to mitigate exposure and to meet network security requirements.
  * *Auditing:* Enable AWS CloudTrail logging for SQS to capture all management operations on your queues. CloudTrail will log who created or deleted a queue, who sent a message or purged a queue, etc., along with timestamps and source IPs. These logs should be monitored and retained as per compliance needs. Unusual activities (like an unexpected IAM principal purging a queue) can then be investigated. For data-plane operations (Send/Receive), you may also consider CloudTrail data event logging (this can be configured for SQS to log message-level API calls, though it may generate a high volume of logs).
  * *Monitoring & Alerting:* Use Amazon CloudWatch to set up alarms on security-relevant metrics or patterns – for example, an alarm on the NumberOfMessagesDelayed metric could indicate if many messages are being unexpectedly delayed (perhaps due to a misconfiguration), or an alarm on the AgeOfOldestMessage can indicate a consumer failure (which might become a security concern if it leads to data loss). Also consider Amazon SNS or other notification methods to alert ops teams if critical queues have issues (like hitting their in-flight limits or getting purged).
* **Network Connectivity Options** 

  * *Public Endpoint:* **NOT ALLOWED** for Restricted data. By default, SQS endpoints are publicly accessible endpoints (HTTPS URLs under `sqs.<region>.amazonaws.com`). In an enterprise context, direct internet-based access to SQS should be avoided or tightly controlled. If a queue must be accessed from outside AWS or without a VPC endpoint, ensure that only secure, encrypted connections are used and that the queue’s access policy restricts access to specific AWS accounts or IP ranges (if applicable). Never leave an SQS queue open to all AWS users or the public. For PepsiCo Internal/Confidential data, public SQS endpoints should be limited and locked down; for Restricted data, they should not be used at all (use a VPC endpoint as described below).
  * *VPC Endpoint (Interface Endpoint):* **Preferred for Internal/Confidential and required for Restricted data.** AWS allows creating an **interface VPC Endpoint** for SQS in a VPC, which provides a private IP address to access SQS without going through the internet. When using a VPC Endpoint, SQS traffic stays within the AWS backbone network. You can configure your SQS queue policies to **whitelist** access only via your VPC Endpoint – for example, by requiring the `aws:SourceVpc` or `aws:SourceVpce` condition in the policy. This ensures that only clients from your authorized VPCs can interact with the queue. VPC Endpoints also enable you to use SQS even if your environment has no internet gateway or if you have strict egress controls.
  * *Hybrid Connectivity:* If on-premises systems need to send or receive SQS messages, consider setting up a secure connection to AWS (AWS VPN or Direct Connect) and then using the VPC Endpoint to reach SQS. This avoids exposing SQS directly to the internet. Alternatively, on-prem systems can call SQS’s public endpoint but should do so through corporate secure web gateways and with appropriate IAM authentication. In all cases, use IAM policies to ensure only intended systems can access the queues.
  * *(Service Endpoints:* Not applicable – AWS uses VPC Endpoints instead of service endpoints for private connectivity to SQS.)
* **Networking & Security Architecture** TBD (Diagrams and additional architectural specifics to be provided, e.g. reference architectures for using SQS with VPC endpoints, encryption, and monitoring in a secure manner.)
* **AWS Service Limitations** 

  * *Throughput Throttling:* Standard queues scale horizontally and can achieve extremely high throughput (many thousands of messages per second) – essentially “nearly unlimited” throughput as demand increases. However, they are still subject to account-level API request limits and other constraints. FIFO queues have a **hard throughput limit** of 300 API calls per second (per queue per action) by default, which translates to at most 300 messages/s without batching (or 3,000 msg/s with 10-message batches). If higher throughput is needed for FIFO, you must enable high-throughput mode and partition your messages across multiple message groups (or request a quota increase) to scale beyond this. These limits mean that for extremely high volume use cases where ordering is not essential, Standard queues are more suitable.
  * *In-flight and Backlog Limits:* A single SQS queue can have at most \~120k **in-flight messages** (messages that have been received by consumers but not yet deleted or timed-out) at any given time for standard queues. Hitting this limit will cause the ReceiveMessage calls to be throttled (or to temporarily return no messages even if the queue is not empty) until the number of in-flight messages is reduced. For FIFO queues, the in-flight limit is effectively governed by the number of active message groups (only 20k in-flight per message group, and messages per group process sequentially). Also, there’s a soft limit on the number of messages a queue can hold in total (backlog), which is extremely high (millions), but practical limits are defined by the 14-day retention—no message will remain longer than that. If you anticipate very large backlogs, monitor the AgeOfOldestMessage metric and consider scaling up consumers or using multiple queues to shard the load.
  * *No Cross-Region Replication:* SQS queues are confined to a single region – there is **no native cross-region replication** or global queue feature. If you require multi-region redundancy or want to process the same stream of messages in multiple regions, you must implement that at the application level (e.g., have producers send messages to two queues in two different regions, or use Amazon SNS to fan out to SQS queues in each region). This limitation means that disaster recovery setups involving SQS will be more complex, as described in the DR guidelines above.
  * *Ordering Constraints:* Standard queues do not guarantee ordering. If your system uses multiple concurrent consumers on a standard queue, messages might be processed out of order; even with a single consumer, network retries could result in occasional reordering. Only FIFO queues preserve message order. Furthermore, FIFO queues require specifying a **MessageGroupId** for each message; only messages with the same group ID are strictly ordered relative to each other, and separate groups can be processed in parallel. This is a design consideration: the application must decide how to partition messages into groups to balance ordering vs throughput.
  * *Maximum Visibility Timeout:* The SQS visibility timeout (the time a message stays invisible to others after a consumer fetches it) **cannot exceed 12 hours**. This means any single task should ideally complete within 12 hours. If a message needs to be held longer (e.g., waiting on an external process), the application must periodically extend the visibility or re-queue the message. There is no infinite or day(s)-long visibility setting. Messages not deleted after 12 hours will reappear, potentially causing duplicate processing if the consumer is still working on it beyond that time.
  * *Transactional Consistency:* SQS operations are simple and do not support multi-message or multi-queue transactions. Each message send or delete is independent. While this keeps the system scalable, it means you cannot atomically enqueue multiple messages or ensure a message is processed only if another action succeeds (no two-phase commit with SQS). Applications needing these patterns have to handle them in logic (for example, using DynamoDB or database transactions to track processing status). Also, SQS provides at-least-once delivery, so exactly-once processing must be ensured in the consumer logic or by using FIFO with deduplication for strict needs.
* **SKU Features** 

  * **Standard Queue:** SQS standard queues offer **nearly unlimited throughput** and automatic scaling. They guarantee at-least-once delivery (a message will be delivered **one or more times**), and provide **best-effort ordering** (occasionally messages may arrive out of order). Standard queues are designed for high-volume **parallel processing**—they can support a large number of simultaneous producers and consumers, making them ideal for scenarios like background task processing, batch jobs, or log ingestion where throughput matters more than strict ordering. They do not enforce a ordering FIFO sequence, which allows them to scale horizontally without constraints. Standard queues also have **low latency** (usually on the order of tens of milliseconds).
  * **FIFO Queue:** SQS FIFO queues ensure that messages are processed **exactly in the order** in which they were sent and **exactly-once** (no duplicates) due to de-duplication capability. They support message grouping: you assign each message a `MessageGroupId`, and SQS will order messages within that group and ensure only one consumer at a time processes a given group. FIFO queues **preserve message deduplication** for a 5-minute interval by default (meaning if the same deduplication ID is used on a new message within 5 minutes, it will be ignored as a duplicate). The trade-off for strict ordering is **limited throughput**: by default FIFO queues can process up to 300 transactions per second (per API action) or up to 3,000 messages per second with batching. AWS introduced a high throughput mode for FIFO that can scale higher by internally partitioning the queue (each FIFO queue in high-throughput mode can support up to  FIFO partitions, each with 3,000 msg/s, yielding up to 30,000 msg/s or more, depending on region). FIFO queues are suited for workloads like financial ledger events, stock trades, or any workflow that requires **ordered, exactly-once processing**. They also support all the same features as standard queues (long polling, SSE, etc.) but *do not* support per-message delay (only queue-level delay) and currently cannot be used with the AWS SQS trigger for Lambda at more than 5 concurrent Lambdas (due to ordering requirements).
* **Related Service** TBD (e.g., Amazon SNS for pub/sub messaging, Amazon MQ for traditional message broker use cases, etc.)

## Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * Information Security Specifications – Amazon SQS (refer to PepsiCo InfoSec baseline requirements document for Amazon SQS, covering data classification handling, encryption standards, audit requirements, etc.)

## Ownership and Version Control

* **Service Architect** Dariusz Korzun ([<EMAIL>](mailto:<EMAIL>))
* **Version Control** 

  * v.1: 7 Aug 2025 (Dariusz Korzun)
