---
weight: 19
title: "SQS"
date: 2025-08-07
tags: []
summary: ""
---

## Cloud Service Classification

* **Category of the Service** Messaging
* **Cloud Provider** AWS

## Cloud Service Overview

* **Name** Amazon Simple Queue Service (SQS)
* **Description** Amazon SQS is a fully managed message queuing service that reliably decouples applications by providing secure, durable, and highly available queues supporting both high-throughput Standard and ordered, exactly-once FIFO modes, with automatic scaling and redundancy across multiple Availability Zones.

* **SKU Approval Status** 

  * **PepsiCo Approved:**

    * Standard Queue
    * FIFO Queue
  * **Not Approved:**

    * None (all SQS queue types are allowed)
* **Allowed PepsiCo Data Classification** 

  * Public
  * Internal
  * Confidential
  * Restricted

## Service Lifecycle

* **Release Date** November 2004 (public preview introduction); production launch on July 13, 2006.
* **Planned Decommission Date** No announced decommissioning plans (service is actively maintained).
* **Decommission Date** Not applicable at this time.

## Usage Guidelines

* **Features** 

  * *Fully Managed Queue Service:* SQS automatically manages infrastructure, delivering highly available, durable queues at any scale without server management.
  * *Queue Types (Standard vs. FIFO):* SQS offers Standard queues for maximum throughput with at-least-once delivery, and FIFO queues for exactly-once, ordered processing, including high throughput mode for larger volumes.
  * *Message Processing & Controls:* SQS provides configurable visibility timeouts, dead-letter queues, delay queues, long polling, and supports batch operations to ensure reliable and efficient message processing.
  * *Integration & Security:* SQS integrates with AWS services, supports encryption at rest, and enables fine-grained access control via IAM and queue policies.

* **Sample Use Cases** 

  * *Decoupling Microservices:* SQS enables loose coupling between application components by allowing one service to enqueue tasks for downstream services to process asynchronously, improving resilience and scalability.
  * *Asynchronous Background Processing:* Applications can offload time-consuming jobs to SQS queues, enabling background processing and fast user responses while backend workers or Lambda functions handle tasks asynchronously.
  * *Auto-Scaling Worker Pools:* SQS acts as a buffer for incoming work and allows automatic scaling of worker instances based on queue depth, ensuring cost-effective and reliable handling of variable workloads.
  * *Ordered Task Processing:* Use SQS FIFO queues to guarantee in-order, exactly-once task execution for workflows requiring strict processing order, such as financial transactions or audit logs.
* **Limitations** 

  * *Message Size & Payloads:* Each SQS message can be up to 256 KB; for larger payloads, use Amazon S3 and pass a pointer or use the Extended Client Library.
  * *Message Retention:* SQS holds messages for 4 days by default (up to 14 days max), after which unprocessed messages are automatically deleted.
  * *Delivery Semantics:* Standard queues offer at-least-once delivery with possible duplicates and out-of-order messages, while FIFO queues ensure exactly-once, ordered delivery but with lower throughput.
  * *Throughput Constraints:* Standard queues scale automatically for high throughput, but FIFO queues are limited to 300 messages per second per API action by default, with high-throughput mode available for larger volumes.
  * *In-flight Message Limit:* SQS allows approximately 120,000 in-flight messages per standard queue and 20,000 per FIFO message group, so messages must be processed and deleted promptly to avoid hitting limits.
* **Additional Guardrails** TBD
* **Used By** TBD (list & links to some onboarded applications using SQS)
* **EA Declaration** NOT a declared standard. (Not an officially declared enterprise standard service at this time.)

## Technical Guidelines

* **Best Practices** 

  * *Use Long Polling and Batching:* Configure long polling to minimize empty responses and use batch API operations to send or delete up to 10 messages at once, reducing costs and improving efficiency.
  * *Error Handling & Dead Letters:* Implement error handling with Dead-Letter Queues (DLQs) to isolate and analyze messages that repeatedly fail processing for separate remediation.
  * *Idempotent Consumers:* Design message consumers to be idempotent to handle duplicate deliveries reliably, using deduplication features in FIFO queues where necessary.
  * *Visibility Timeout Tuning:* Set the visibility timeout to match expected processing times so failed messages quickly return to the queue, and extend or partition tasks for longer jobs as needed.
  * *Monitoring & Scaling:* Use Amazon CloudWatch to monitor queue metrics like backlog size and age, and trigger alarms or scaling actions when thresholds are reached to maintain performance.
  * *Integration with Auto Scaling:* Integrate SQS queue metrics with Auto Scaling policies to dynamically increase or decrease worker capacity in response to queue length for cost-effective scaling.
  * *Security Best Practices:* Restrict SQS queue access using least-privilege IAM policies, enforce HTTPS endpoints, enable server-side encryption for sensitive data, and use IAM roles for secure credentials management.
  * *Logging and Auditing:* Enable AWS CloudTrail logging for SQS API actions and maintain processing logs to ensure a traceable audit record of both administrative and message-level operations.
* **High Availability & Disaster Recovery** 

  * *In-Region High Availability:* Amazon SQS is a regional service that automatically spans multiple Availability Zones for redundancy and high availability within a region, requiring no extra configuration from users.
  * *Multi-Region Resilience:* SQS queues do not replicate across regions by default, so for cross-region disaster recovery, applications must send messages to queues in multiple regions and handle duplication and consistency at the application level.
  * *Failover Process:* In case of regional failure, applications must switch to a secondary region's queue using configuration changes, as SQS does not provide automatic regional failover, making it important to regularly test and plan manual or automated failover mechanisms.

* **Backup & Recovery** 

  * *Ephemeral Nature of Queues:* SQS stores messages temporarily and does not support built-in backup or point-in-time restore, so persistent message retention requires external storage.
  * *Message Archival Strategies:* Use the SQS Extended Client Library or have producers also write messages to Amazon S3 (or similar), enabling persistent archiving and replay as well as supporting larger message payloads.
  * *Dead-Letter Queue Handling:* DLQs provide a backup for failed messages, which can be periodically reviewed and reprocessed or moved back to the main queue after issues are fixed.
  * *Persistence for Critical Messages:* For critical data retention, dual-write messages to a separate persistent store like a database alongside SQS, as AWS Backup does not natively support SQS queues.

* **Access Control & Security Configuration** 

  * *Queue Policies & IAM:* Apply precise SQS queue and IAM policies to restrict access to only required AWS principals, always enforcing least privilege and never allowing public or wildcard access.
  * *IAM Roles for AWS Services:* Grant SQS access to AWS compute services using IAM Roles rather than embedding access keys, ensuring automatic credential rotation and reduced risk of leakage.
  * *Encryption at Rest:* Activate Server-Side Encryption (SSE) with AWS KMS on SQS queues containing sensitive data to protect messages at rest without code changes.
  * *Encryption in Transit:* Enforce HTTPS by configuring SQS queue policies with the aws:SecureTransport condition, ensuring all communication with SQS is encrypted in transit.
  * *VPC Endpoints (PrivateLink):* Use Amazon VPC Interface Endpoints for SQS to keep traffic private within AWS and restrict access to specific VPCs for sensitive workloads.
  * *Auditing:* Enable AWS CloudTrail logging for SQS to track management and message-level operations for compliance, monitoring, and security investigations.
  * *Monitoring & Alerting:* Set up Amazon CloudWatch alarms for security-relevant SQS metrics and use notifications to alert teams of unusual queue activity or potential issues.

* **Network Connectivity Options** 

  * *Public Endpoint:* Public SQS endpoints must not be used for Restricted data, and for Internal/Confidential data, they should be tightly controlled and access-restricted.
  * *VPC Endpoint (Interface Endpoint):* **Preferred for Internal/Confidential and required for Restricted data.** VPC Endpoints are required for Restricted data and preferred for Internal/Confidential data, ensuring private, secure SQS access within the AWS network via appropriate queue policies.
  * *Hybrid Connectivity:* For on-premises access, use secure AWS connections (VPN or Direct Connect) routed through VPC Endpoints, or highly controlled public endpoints with strong IAM restrictions.
  * *(Service Endpoints:* Not applicable – AWS uses VPC Endpoints instead of service endpoints for private connectivity to SQS.)
* **Networking & Security Architecture** TBD (Diagrams and additional architectural specifics to be provided, e.g. reference architectures for using SQS with VPC endpoints, encryption, and monitoring in a secure manner.)
* **AWS Service Limitations** 

  * *Throughput Throttling:* Standard queues provide near-unlimited throughput, but are subject to account-level API limits, while FIFO queues have a default cap of 300 messages per second (or 3,000 with batching), expandable by using high-throughput mode and message group partitioning.
  * *In-flight and Backlog Limits:* Standard queues allow up to about 120,000 in-flight messages, FIFO queues are limited by active message groups (20,000 per group), and total queue backlog is mainly limited by the 14-day retention period.
  * *No Cross-Region Replication:*  SQS queues operate within a single region, lacking built-in cross-region replication or global queues, so multi-region redundancy requires custom application-level implementation.
  * *Ordering Constraints:* Only FIFO queues guarantee message order (by MessageGroupId), while standard queues may deliver messages out of order, especially when processed concurrently.
  * *Maximum Visibility Timeout:* SQS visibility timeout per message cannot exceed 12 hours, so any processing must be completed or extended within that time, or the message will become visible again.
  * *Transactional Consistency:* SQS does not support multi-message or multi-queue transactions, with each operation independent, requiring applications to handle atomicity and idempotency as needed.

* **SKU Features** 

  * **Standard Queue:** SQS standard queues provide nearly unlimited throughput, low latency, at-least-once delivery, and high scalability, but may deliver messages out of order.
  * **FIFO Queue:** SQS FIFO queues guarantee exactly-once delivery and strict ordering within message groups, with throughput limits and support for deduplication, making them ideal for use cases where message order and uniqueness are critical.

* **Related Service** TBD (e.g., Amazon SNS for pub/sub messaging, Amazon MQ for traditional message broker use cases, etc.)

## Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * Information Security Specifications – Amazon SQS (refer to PepsiCo InfoSec baseline requirements document for Amazon SQS, covering data classification handling, encryption standards, audit requirements, etc.)

## Ownership and Version Control

* **Service Architect** Dariusz Korzun ([<EMAIL>](mailto:<EMAIL>))
* **Version Control** 

  * v.1: 7 Aug 2025 (Dariusz Korzun)
  * v.2: 8 Aug 2025 (Rakesh Ponnada)
