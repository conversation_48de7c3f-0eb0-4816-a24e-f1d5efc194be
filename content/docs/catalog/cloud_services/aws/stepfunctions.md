---
weight: 21
title: "Step Functions"
date: 2025-08-13
tags: [AWS Step Functions, Workflow Orchestration, Workflows, Integration, Serverless]
summary: ""
---

## Cloud Service Classification

* **Category of the Service:** Application Integration
* **Cloud Provider:** AWS
* **Website**: [Technical documentation](https://docs.aws.amazon.com/step-functions/)

## Cloud Service Overview

* **Name:** AWS Step Functions
* **Description:** AWS Step Functions is a fully managed serverless orchestration service that coordinates AWS services and microservices as stateful, visual **state machines** defined in the declarative Amazon States Language (JSON), automatically triggering and tracking steps with built-in retries and error handling, offering a graphical console for visualization and debugging, and enabling reliable multi-step processes and data pipelines without managing workflow infrastructure.
* **SKU Approval Status:**

  * *PepsiCo Approved:*
    • Standard Workflows
    • Express Workflows
  * *Not Approved:* (None – all Step Functions workflow types are allowed)
* **Allowed PepsiCo Data Classification:**

  * Public
  * Internal
  * Confidential
  * Restricted

## Service Lifecycle

* **Release Date:** December 1, 2016 (generally available launch at AWS re\:Invent 2016)
* **Planned Decommission Date:** No announced decommissioning plans.
* **Decommission Date:** Not applicable at this time.

## Usage Guidelines

* **Features:**

  * *Serverless Workflow Orchestration:* AWS Step Functions provides a managed workflow engine to coordinate distributed components, enabling **visual workflow** design via Workflow Studio or JSON definitions without server provisioning, with the console delivering real-time, step-level execution visibility for streamlined debugging.
  * *Deep AWS Service Integrations:* AWS Step Functions natively integrates with 220+ AWS services and 9,000+ API actions via SDK-based service tasks, enabling direct, code-free calls to services such as Lambda, S3, DynamoDB, SNS/SQS, Glue, and SageMaker, with optimized integrations (e.g., Batch, EMR) that automatically handle job waits and IAM policy generation.
  * *Built-in Reliability and Error Handling:* AWS Step Functions provides built-in execution tracking, configurable retries, and error handling (including failure-step routing), making workflows **resilient** to transient faults while logging each step’s inputs, outputs, and results for rapid diagnosis.
  * *Parallel and Conditional Execution:* AWS Step Functions supports conditional branching with Choice and high‑concurrency parallelism with **Parallel** and **Map** states—enabling large‑scale data processing (e.g., concurrent S3 file processing)—and its **Distributed Map** (Standard workflows) scales to up to 10,000 parallel iterations.
  * *Flexible Workflow Modes:* AWS Step Functions offers two workflow types: **Standard**, delivering exactly-once, highly durable executions lasting up to one year, and **Express**, optimized for high-volume, short-duration (≤5 minutes) workloads with low-latency, cost-efficient throughput (thousands of events per second), enabling selection by use case (e.g., mission-critical processes vs. real-time ingestion).

* **Sample Use Cases:**

  * *Data Processing & ETL Pipelines:* Orchestrates ETL pipelines by consolidating multi-source data into warehouses, parallelizing processing of millions of S3 objects or logs, and coordinating multi-step analytics/ML preparation, ensuring ordered completion of long-running jobs with automatic retries for resilience.
  * *Serverless Microservice Orchestration:* Orchestrates multi-step business processes across Lambda and other services—e.g., e-commerce order processing (inventory, payment, shipping)—with robust error handling and compensations, and supports flows such as user registration or transaction processing, including optional human approval.
  * *Machine Learning & AI Workflows:* Orchestrates end-to-end ML and generative AI pipelines—spanning data preparation, training, hyperparameter tuning, evaluation-driven branching, deployment, and human-in-the-loop review—by coordinating SageMaker jobs and invoking Amazon Bedrock to chain prompts and model calls for complex AI workflows.
  * *DevOps & IT Automation:* Implement operational workflows—including CI/CD pipelines with manual approvals (e.g., CodeBuild/CodeDeploy with SNS callbacks), infrastructure provisioning, and **automated security incident response** playbooks (detect, isolate, remediate, notify)—delivering structured, auditable execution.
  * *Backend Business Processes:* Automates mission-critical business workflows by enforcing ordered execution, integrating with external APIs or legacy systems via activities, and providing audit trails with robust error recovery.

* **Limitations:**

  * *Execution Length and History:* **Standard Workflows** can run up to one year but are capped at 25,000 execution-history events (exceeding this triggers an “ExecutionHistoryExceeded” error), so long-running workflows must stay within this limit or be split into sub-workflows, while **Express Workflows** are limited to 5 minutes and are unsuitable for long-running processes.
  * *Throughput and Concurrency:* Standard Workflows are not designed for extremely high-frequency invocations, enforcing default throttles (~300 execution starts/s and ~5,000 state transitions/s in us-east-1) that, though increaseable, can still throttle bursty workloads (ExecutionThrottled), whereas Express Workflows handle >100k events/s at the cost of reduced per-execution durability.
  * *Execution Semantics:* Express Workflows use **at-least-once** semantics for asynchronous invocations (executions may run multiple times), requiring idempotent actions, while synchronous Express uses **at-most-once** semantics; Standard Workflows guarantee exactly-once, in-order execution at the cost of lower throughput.
  * *Feature Gaps in Express Mode:* Express Workflows lack several Step Functions capabilities—callback tasks (waitForTaskToken), Job .sync integrations, Activity tasks, and the Distributed Map state (Standard-only)—thereby precluding long-wait human interactions and certain synchronous integrations, which require Standard Workflows.
  * *Payload Size Limits:* Step Functions limits per-state and execution input/output payloads to **256 KB**; large data should be stored externally (e.g., Amazon S3 or a database) with references passed between states, as exceeding the limit causes execution failures—so design workflows to operate on references or chunked data.
  * *Regional Service:* AWS Step Functions is region-scoped with no built-in cross-region replication or automatic failover; workflows and executions run only in a single region, so a regional outage interrupts running workflows, and mitigation requires manually deploying and triggering the workflow in a secondary region.

* **Additional Guardrails:** TBD

* **Used By:** TBD (list & links to internal onboardings)

* **EA Declaration:** *NOT* a declared standard (not an officially declared standard service in Enterprise Architecture)

## Technical Guidelines

* **Best Practices:**

  * *Workflow Type Selection:* Use Standard for long‑running, mission‑critical workflows requiring exactly‑once execution and durable state, and Express for high‑volume, short‑lived, event‑driven workloads that tolerate at‑least‑once semantics (avoid Express for non‑idempotent steps unless you handle duplicates), optionally composing Standard with Express to balance needs.
  * *Error Handling and Retries:* Implement robust error handling in Step Functions by defining **Retry** policies with backoff for transient failures and **Catch** clauses for fallback/cleanup and **compensation** steps, ensuring single-step errors do not unexpectedly abort long-running workflows.
  * *Timeouts and Heartbeats:* Specify explicit **Task timeouts** (TimeoutSeconds) and, for callback/human-approval or activity tasks, heartbeats (HeartbeatSeconds) to detect stalls—failing and handling when heartbeats cease—to prevent indefinite waits from hung integrations or missed callbacks and improve workflow resilience.
  * *Optimize Data Passing:* Pass only necessary data between states to respect the 256 KB limit—store large payloads in S3 or DynamoDB and pass references—and use Parameters, ResultPath, ResultSelector, and filters to include only relevant fields, thereby avoiding limits and reducing execution latency and memory usage (relevant for Express billing).
  * *Leverage Service Integrations Over Lambda:* Prefer native Step Functions **service integrations** (direct AWS SDK tasks) over Lambda wrappers for actions such as DynamoDB writes, SNS/SQS messages, and Batch/Glue invocations to simplify architecture and reduce cost and cold-start overhead, reserving Lambda for custom logic not supported by direct integrations.
  * *Modularize and Reuse Workflows:* Design modular Step Functions by encapsulating common step sequences as reusable child workflows (invoked via StartExecution or nested tasks) and decomposing large or long processes into sub-workflows to improve reuse, maintainability, and testability while mitigating the 25,000‑event execution-history limit.
  * *Logging and Monitoring:* Enable **CloudWatch Logs** (especially for Express) with appropriate log levels to retain execution data beyond Standard’s 90‑day history, monitor health via **CloudWatch Metrics** and Alarms (e.g., failures, throttling), rely on CloudTrail for API auditing, and regularly review/integrate logs with SIEM.

* **High Availability & Disaster Recovery:**

  * *High Availability:* AWS Step Functions is **inherently highly available** within a region, running across **multiple Availability Zones** with automatic scaling and fault tolerance—requiring no user multi-AZ configuration—so its control plane and execution engine continue despite an AZ outage, and, together with multi-AZ regional services (e.g., Lambda, DynamoDB), provide robust in‑region reliability.
  * *Disaster Recovery (Multi-Region):* There is **no automatic cross‑region failover**, so implement DR by pre‑deploying state machines and dependencies in a secondary region and triggering failover (e.g., via EventBridge or Route 53) to start new executions only, while externalizing/replicating state (e.g., DynamoDB/global tables) or checkpointing for resumability; RTO/RPO depend on failure detection and re‑trigger automation.

* **Backup & Recovery:**

  * *State Machine Definitions:* Treat Step Functions workflow definitions as code by storing ASL JSON in source control or IaC templates and leveraging state machine **versions and aliases** to publish immutable versions, safely promote (e.g., “prod”/“v2”), and rapidly restore the last known good definition.
  * *Execution History Retention:* Standard Workflows retain **execution history for 90 days by default** (reducible to 30 but not extendable); for longer retention, capture details via CloudTrail or CloudWatch Logs (e.g., store CloudTrail in S3 per policy) and, for critical workflows, export or summarize execution history to a database upon completion.
  * *Express Workflows Logging:* Because Express Workflows do not persist execution history, enable CloudWatch Logs (at least errors) and use them as the primary recovery record; failures will not retry beyond configured policies and cannot be resumed, so diagnose via logs and re-run executions—ideally with idempotent design.
  * *Resilience of In-Progress Executions:* Standard Workflows record each state’s inputs and outputs for built-in checkpointing, enabling resumption to the last successful step after transient disruptions so executions complete or fail deterministically, while critical long-running processes should add **application-level checkpoints** (e.g., DynamoDB) to support manual resumption.

* **Access Control & Security Configuration:**

  * *IAM Authorization:* Use IAM to enforce least‑privilege control over Step Functions—restrict actions (e.g., `CreateStateMachine`, `StartExecution`, `StopExecution`) to trusted roles, grant application roles StartExecution on specific ARNs, limit developer updates to non‑production, and leverage resource‑level permissions for state machines and executions.
  * *State Machine Execution Role:* Each Step Functions state machine runs under an execution IAM role that should enforce **least privilege**—grant only required actions on specific ARNs (e.g., lambda:InvokeFunction for a given function, dynamodb:PutItem for a given table), ensure a trust relationship with states.amazonaws.com, rigorously review/tighten any auto‑generated integration policies, and audit regularly to prevent privilege creep.
  * *Data Encryption:* AWS Step Functions **encrypts data at rest by default** with AWS‑managed keys; for sensitive data, configure a customer‑managed KMS CMK to encrypt execution history and workflow inputs/outputs, update the key policy to allow states.amazonaws.com and the execution role, note KMS charges, and ensure downstream services (e.g., S3, DynamoDB) are also encrypted.
  * *Network Security:* Use AWS PrivateLink interface VPC endpoints (com.amazonaws.<region>.states) to invoke Step Functions **privately** from VPC resources—required for Restricted data—ensure downstream calls (e.g., S3, DynamoDB) use their own VPC endpoints since Step Functions is not in the VPC, and expose external access via API Gateway to apply WAF, throttling, and authentication.
  * *Sensitive Data in Workflow Inputs:* Avoid embedding sensitive data in Step Functions definitions, inputs, tags, names, or descriptions; retrieve secrets at runtime via AWS Secrets Manager or Parameter Store and configure logging to redact or omit inputs/outputs, noting that Step Functions is HIPAA‑eligible and SOC/GDPR compliant and suitable for regulated data when properly configured (e.g., VPC endpoints, encryption, minimal retention).
  * *Validation and Testing:* Validate and test Step Functions workflows with the **visual Workflow Studio**, Step Functions Local for offline emulation, and the TestState API for per‑state checks, then run pre‑production executions with sample inputs or test aliases to confirm behavior, performance, and security controls (IAM roles, KMS keys) before go‑live.

* **Network Connectivity Options:**

  * **Public Endpoint:** *Not Allowed.* In PepsiCo cloud environment, avoid direct public access to AWS Step Functions by making API calls over secure, AWS-internal networks via PrivateLink or brokering external triggers through API Gateway/EventBridge, and **if the public endpoint is unavoidable**, tightly restrict it with IAM and network controls.
  * **VPC Endpoint (AWS PrivateLink):** *Allowed / Preferred.* Configure an **Interface VPC Endpoint** for Step Functions to keep traffic on the AWS backbone with a private VPC IP—required for **Restricted data** and recommended for Internal/Confidential—enabling internet-free invocation, potential latency reduction, and tighter access control via security groups and endpoint policies (e.g., limiting specific IAM principals or state machines).
  * **VPN/Direct Connect:** If triggering Step Functions from on-premises via VPN or Direct Connect, ensure that the connections are secure and that IAM authentication is required. Typically, users are not allowed to call Step Functions directly from on-prem; instead, call an API in AWS (API Gateway or an app) that then starts the Step Function, to abstract the direct credentials.
  * **Service Endpoint:** (Not applicable – AWS uses PrivateLink for private connectivity rather than service endpoints like Azure). All private access is achieved via the VPC Interface Endpoint mechanism.
  * **Networking & Security Architecture:** *TBD* (Architecture diagram or specifics to be filled in – e.g., reference architectures showing Step Functions with PrivateLink, etc.)

* **AWS Service Limitations:**

  * *Regional Isolation:* AWS Step Functions is region-bound—state machines run only within their region with **no global construct** or cross‑region synchronization—so cross‑region interactions must be handled at the application layer (e.g., via a Lambda invoking APIs in other regions).
  * *Resource Quotas:* Be aware of Step Functions quotas—e.g., up to **1,000,000 concurrent executions per account/region**, 100,000 state machines, and a 1 MB (hard) state definition size—with most others increaseable via AWS Support; Standard StartExecution throttles can be as low as 2/s (≈300/s in some regions) while Express supports ~6,000/s bursting to 100k+, so plan capacity and request increases before load testing.
  * *State Size & Transitions:* Standard Workflows’ 25,000-event history cap limits single-execution complexity—workflows that might exceed it should be partitioned via Map segmentation or child workflows—and because each state transition adds tens of milliseconds of latency, extremely chatty workflows should consolidate logic or, if acceptable, use Express to reduce overhead.
  * *Integration-Specific Limits:* Step Functions **inherits the limits of integrated services** (e.g., Lambda concurrency, AWS Batch job/array sizes), so align workflow scale accordingly, and while it surfaces downstream errors (e.g., DynamoDB throughput exceeded), you must handle them via retries, backoff, or design.
  * *No Built-in GUI or Mobile App:* AWS Step Functions is primarily accessed via the console and API/SDK, lacks a native end-user portal, and is oriented toward programmatic/devops use—thus you may need to build a front end (Workflow Studio is for developers) and can incorporate mechanisms like SNS email approvals for human steps.
  * *Cost Considerations:* Standard Workflows charge per state transition and Express charge for duration and memory (similar to Lambda), so review pricing for long-running or high-frequency patterns, consolidate tight loops (e.g., into a single Lambda) when cost-sensitive, prefer service integrations to avoid extra Lambda costs, and use Step Functions where its simplicity and reliability justify the expense.

* **SKU Features:**

  * **Standard Workflows:**

    * *Execution Durability:* Standard Workflows support executions up to **1 year**, persist step state with resilience to service restarts, and retain execution history during the run and for up to 90 days post-completion for audit and debugging.
    * *Exactly-Once Processing:* Step Functions Standard Workflows guarantee exactly-once step execution—no reruns occur unless explicitly retried, and duplicate executions with the same name are rejected—making them ideal for critical processes that cannot tolerate duplicates.
    * *Full Feature Set:* Supports the full Amazon States Language feature set—parallel, choice, wait, map (inline and distributed), activities, and callback tasks with task tokens (for human-in-the-loop or third-party callbacks)—and all service integration patterns, including .sync and .waitForTaskToken.
    * *Visual Debugging:* The AWS Console offers step-by-step execution visualization—searchable runs, per-state input/output inspection, and a color-coded workflow graph—for effective troubleshooting of complex workflows, with optional AWS X-Ray tracing for Standard executions.
    * *Performance and Scaling:* Standard Workflows scale horizontally to handle **hundreds of execution starts per second** (soft, increaseable) and substantial parallelism—up to 40 branches per Parallel state or 10,000 iterations via Distributed Map—without a hard step-count cap but effectively bounded by the 25,000-event history limit, and with **slightly higher per‑state latency** than Express.
    * *Pricing Model:* Standard Workflows are billed per state transition on a pay‑per‑use basis (first 4,000 transitions per month are free; regional rates apply), so long, multi‑step workflows can accrue higher costs, though idle time (wait states) is not charged.
  * **Express Workflows:**

    * *Execution Speed & Volume:* Express Workflows are optimized for high‑throughput, short‑lived executions (**up to 5 minutes**), supporting very high start rates (default ~6,000 per second with bursts/increases to 100,000+ per second) for streaming data, IoT events, and other massively concurrent workloads.
    * *At-Least-Once Execution:* Express Workflows use at-least-once semantics for **asynchronous invocations**—lacking per-state checkpoints, failures can trigger full-execution retries that duplicate prior effects—so actions must be idempotent, whereas successful synchronous invocations are effectively at-most-once.
    * *Limited Step Types:* Express Workflows support most states and service integrations but exclude long-wait patterns—no activities, task-token callbacks, or Distributed Map—while still allowing Parallel, inline Map, Choice, short Wait, and calls to other workflows, provided execution completes within five minutes.
    * *Transient Execution History:* Express Workflows do not retain full execution history in the Step Functions console—past executions cannot be listed or inspected—so operational visibility relies on **CloudWatch Logs** and **CloudWatch Metrics** (configure streaming of step inputs/outputs or errors), with only limited near‑real‑time views and no post‑run visual trace.
    * *Use Cases:* Express Workflows, optimized for speed and cost efficiency, are ideal for high-frequency event-driven workloads—such as SNS/Kinesis message processing, API request handling, IoT data ingestion, and per-action mobile/web backends—and integrate with EventBridge to trigger a workflow for each matching event.
    * *Pricing Model:* Express Workflows are billed by **number of executions**, **duration** in GB-seconds, and **memory size** (akin to Lambda), offering high cost efficiency for millions of short runs but capped at 5 minutes with costs rising for large payloads or heavy parallelism, and include a monthly free tier.

* **Related Service:** TBD (e.g., Amazon SWF, AWS Step Functions’ older sibling, etc.)

* **Alternatives:**

  * *AWS Simple Workflow Service (SWF):* AWS Simple Workflow Service (SWF) is an older orchestration service that requires a **decider program** and managed workers, supports human-intervention steps and external signaling with fine-grained, code-level retry control and execution durations beyond one year, but is **far more complex** and lacks Step Functions’ visual interface and many integrations; accordingly, AWS recommends Step Functions for new applications, reserving SWF for legacy use or niche external-signaling needs.
  * *Amazon EventBridge (and EventBridge Scheduler):* AWS EventBridge is an event bus for loosely coupled **choreography**, ideal for triggering actions without strict ordering or centralized state (with **Scheduler** for cron), often complementing Step Functions by triggering workflows and receiving emitted events, while Step Functions suits explicit, stateful orchestration.
  * *AWS Glue Workflows / Managed Airflow:* AWS Glue Workflows and Amazon MWAA (managed Apache Airflow) are domain-specific alternatives for ETL/DAG orchestration—Glue tightly integrates with Glue jobs and Airflow suits teams already invested in Apache Airflow with complex or on‑prem dependencies—while Step Functions can orchestrate similar jobs but is simpler and fully managed for most general-purpose serverless workflows, with Glue/Airflow adding infrastructure and complexity.
  * *Roll-Your-Own Orchestration:* **Not Recommended. Needs EA Approval** While teams can stitch workflows with SQS/SNS, databases, or chained Lambdas, such custom orchestration quickly becomes fragile and hard to manage (lacking centralized state and cross-function retries), whereas Step Functions provides a robust managed alternative—custom code is justified only for ultra–latency-sensitive or on‑prem scenarios.

## Compliance and Security Guidelines

* **Security Baseline – InfoSec:** Information Security specifications for AWS Step Functions should be followed as per internal guidelines. (Refer to **Information Security Specifications – AWS Step Functions** document for baseline controls on data handling, IAM, logging, and network usage in workflows.)

## Ownership and Version Control

* **Service Architect:** Ramakrishna Ramaraju ([<EMAIL>](mailto:<EMAIL>))
* **Version Control:**

  * v.1: 20 Aug 2025 - Initial draft prepared for AWS Step Functions Service Catalog entry (Ramakrishna Ramaraju)
