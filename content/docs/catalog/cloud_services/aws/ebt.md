---
weight: 20
title: "Elastic Beanstalk"
date: 2025-08-07
tags: [Elastic Beanstalk, Compute PaaS]
summary: ""
---

## Cloud Service Classification

* **Category of the Service:** Compute (Platform as a Service)
* **Cloud Provider:** AWS
* **Website**: [Technical documentation](https://docs.aws.amazon.com/elastic-beanstalk/)

## Cloud Service Overview

* **Name:** AWS Elastic Beanstalk

* **Description:** AWS Elastic Beanstalk simplifies deploying and scaling web applications by automatically provisioning resources (EC2, ECS, ELB, Auto Scaling) and managing infrastructure. It supports multiple languages and platforms (Java, .NET, Node.js, Python, Docker, etc.) with minimal changes, making it ideal for scalable applications with reduced operational overhead.

* **SKU Approval Status:**

  * **PepsiCo Approved:**

    * *Single-Instance Environments* – Allowed **for Dev/Test only**. Runs the application on a single EC2 instance with no load balancer (no high availability).
    * *Load-Balanced Environments* – Allowed **for Production** use. Includes an AWS load balancer and auto-scaling across multiple instances for high availability.
  * **Not Approved:**

    * *None.* (All standard Elastic Beanstalk configurations are permitted, with the above usage guidelines on environment types.)

* **Allowed PepsiCo Data Classification:**

  * Public
  * Internal
  * Confidential
  * Restricted

## Service Lifecycle

* **Release Date:** January 19, 2011 (initial beta launch of AWS Elastic Beanstalk).
* **Planned Decommission Date:** No announced decommissioning plans.
* **Decommission Date:** Not applicable at this time.

## Usage Guidelines

* **Features:**

  * **Multi-Language PaaS Platform:** Supports deploying web applications in multiple languages and frameworks (Java, .NET, PHP, Node.js, Python, Ruby, Go, Docker) with minimal changes, making it ideal for diverse web application workloads.
  * **Automatic Provisioning & Management:** Elastic Beanstalk automates provisioning and configuring AWS resources like EC2, S3, load balancers, and Auto Scaling groups, with built-in health monitoring and automatic scaling or instance replacement.
  * **Multiple Deployment Strategies:** Offers deployment policies like all-at-once, rolling, rolling with an extra batch, immutable, and blue/green, enabling controlled updates with minimal downtime and risk.
  * **Integrated Monitoring & Logging:** Provides a unified console for application health with 40+ metrics (e.g., CPU, latency). It integrates with CloudWatch and X-Ray for dashboards, alarms, and logs, which can be accessed via the console or pushed to S3/CloudWatch Logs.
  * **Auto-Scaling & High Availability:** Automatically scales environments with Auto Scaling and Elastic Load Balancing, distributing instances across AZs for high availability. It performs health checks and replaces unhealthy instances to ensure reliability.
  * **Customization & Control:** Elastic Beanstalk manages infrastructure while allowing customization, such as selecting instance types and using YAML/JSON `.ebextensions` for environment configurations. Users can "eject" and directly manage resources via AWS consoles or APIs without lock-in.

* **Sample Use Cases:**

  * **“Lift-and-Shift” Migration of Legacy Apps:** Enables enterprises to rehost monolithic web applications from on-premises or other clouds to AWS with minimal code changes, preserving architectural compatibility while delivering cloud benefits such as auto-scaling and managed updates, making it an effective first step in cloud adoption.
  * **Simple Containerized Application Hosting:** Deploy single-container applications by supplying a Docker image while the service manages EC2 provisioning, networking, scaling, and health monitoring, thereby reducing operational overhead and obviating the need to operate complex orchestration platforms such as Amazon ECS or EKS.
  * **Web Application Deployment for Development Teams:** Quickly setup development, test, or production web application environments by automatically provisioning and managing infrastructure (instances, load balancer, networking), scaling, monitoring, and platform patching, allowing teams to focus on writing code and business logic.
  * **Background Worker Processing:** Beanstalk’s Worker Tier enables asynchronous background processing and scheduled tasks by consuming jobs from Amazon SQS (e.g., order processing or image thumbnail generation), offloading work from the web front end to maintain responsiveness and supporting cron-like schedules via a `cron.yaml` configuration.

* **Limitations:**

  * **Ephemeral Instances (No Persistent Local Storage):** Because Elastic Beanstalk runs applications on EC2 instances with ephemeral local storage that can be lost on restart, termination, or auto-scaling replacement, design the applications to externalize state to durable services—such as Amazon S3 for uploads and databases or ElastiCache for session data.
  * **Limited OS-Level Customization:** Elastic Beanstalk’s pre-configured platforms abstract infrastructure and restrict deep OS/runtime customization beyond supported settings and `.ebextensions`. Workloads requiring specialized system changes or unsupported runtimes should use Docker or a custom platform.
  * **Single-Region by Design:** Elastic Beanstalk environments are region-scoped with no native multi‑region deployment or automatic failover; cross‑region high availability and disaster recovery require provisioning separate environments per region and orchestrating traffic and recovery (e.g., via Route 53/DNS) at the application or operational layer.
  * **Environment Lifecycle Coupling:** Elastic Beanstalk–created resources (e.g., RDS) can be tightly coupled to an environment, and are deleted upon environment termination by default. So, prevent data loss by decoupling or snapshotting it—or, preferably, by provisioning RDS outside Elastic Beanstalk and supplying connection details.

* **Additional Guardrails:** *TBD* – (Any additional internal guardrails or policies for using Elastic Beanstalk within the enterprise are to be determined.)

* **Used By:** *TBD* – (List and links to some internal projects or teams using this service.)

* **EA Declaration:** NOT a declared standard (Elastic Beanstalk is available for use but not established as a PepsiCo “standard” service at this time).

## Technical Guidelines

* **Best Practices:**

  * **Stateless Application Design:** Design Elastic Beanstalk applications to be stateless, externalizing persistent or shared state to services such as databases and ElastiCache rather than in-memory sessions or local files, ensuring auto-scaling and instance replacements occur without disruption and all instances remain interchangeable.
  * **Regular Platform Updates:** Maintain Elastic Beanstalk platforms by enabling **Managed Platform Updates** to automatically patch the OS, runtime, and Elastic Beanstalk agent during a maintenance window, and for critical production systems, validate new versions in staging and use **immutable deployments** for safer upgrades.
  * **Blue/Green Deployments for Changes:** Adopt Blue/Green deployments in Elastic Beanstalk by cloning the environment, deploying and validating the new version in a “green” environment, then swapping CNAMEs to shift traffic from the “blue” environment, enabling near-zero-downtime releases with rapid rollback (supported via environment cloning and CNAME swapping).
  * **Monitoring and Alerting:** Implement proactive monitoring for Elastic Beanstalk by using Amazon CloudWatch to track key metrics (CPU, latency, HTTP 4xx/5xx, and custom memory) with alarms, integrating AWS X-Ray for distributed request tracing, and enabling Enhanced Health Monitoring for granular health and automated instance replacement.
  * **Infrastructure as Code & Configuration Management:** Manage Elastic Beanstalk environments as code by versioning `.ebextensions` and `.platform` (Platform Hooks) with the application and automating provisioning and updates via CloudFormation or the EB CLI, ensuring consistent, repeatable configurations and strengthening disaster recovery.

* **High Availability & Disaster Recovery:**

  * **In-Region High Availability:** To achieve high availability, run Elastic Beanstalk in a load-balanced, multi-AZ configuration with Auto Scaling across **at least two Availability Zones** and a **minimum of two instances**, leveraging Elastic Load Balancing and health checks for replacement, and enable Multi-AZ/zone-redundant backends (e.g., RDS) to mitigate regional single points of failure.
  * **Auto-Healing:** Configure Elastic Beanstalk health checks and the Auto Scaling health check type—using Enhanced Health for application-level checks—so **unhealthy** instances are automatically terminated and replaced to maintain capacity, and enable SNS notifications to be alerted to replacements.
  * **Multi-Region Disaster Recovery:** Because Elastic Beanstalk **lacks native cross-region failover**, architect disaster recovery by maintaining a standby environment in a secondary region, shifting traffic via Route 53 during outages, replicating stateful data (e.g., cross-region RDS replicas and S3 backups), regularly testing failover, and using infrastructure-as-code automation to meet RTOs by rapidly provisioning or scaling the standby.

* **Backup & Recovery:**

  * **Application Version Retention:** Elastic Beanstalk stores application versions in S3 with a default per‑region limit of 1,000, so implement pruning (e.g., lifecycle policies or auto-delete), maintain source code/artifacts in version control to recreate bundles, and export critical builds (e.g., the last known good) to external backup if retention beyond the limit is required.
  * **Environment Configuration Backup:** Regularly export Elastic Beanstalk environment settings by saving **configuration templates** via the console or CLI, and codify variables, software settings, and scaling parameters in Infrastructure as Code (e.g., CloudFormation/Terraform) to enable rapid environment reconstruction after deletion or misconfiguration.
  * **Database and State Backup:** Because Elastic Beanstalk provides no native data backup, protect stateful components by enabling service-level backups (e.g., RDS automated backups/snapshots or scheduled backups for external databases) and by storing persistent data on durable services such as S3 (with versioning) or EFS, ensuring restorability after environment failure or termination.
  * **Log Preservation:** Configure Elastic Beanstalk to persist logs by rotating them to Amazon S3 or streaming to CloudWatch Logs—with periodic automatic uploads so logs survive instance replacement—to support troubleshooting, audit, and recovery, and secure the destinations with restricted access and appropriate retention policies.

* **Access Control & Security Configuration:**

  * **IAM User Access Management:** Use AWS IAM to enforce strict, least‑privilege control over who can create, modify, or delete Elastic Beanstalk applications and environments—differentiating developer, production, operations/SRE, and read‑only roles—while relying on CloudTrail to audit all actions.
  * **Instance Roles (Least Privilege for Applications):** Assign a least‑privilege IAM instance role to Elastic Beanstalk EC2 instances—building on the default permissions (e.g., S3 for bundles, CloudWatch logging), attaching only the specific access required for services such as DynamoDB or Secrets Manager—and avoid hard‑coded credentials by using the role’s temporary credentials.
  * **Network Isolation and Security Groups:** Deploy Elastic Beanstalk in a VPC with instances in private subnets and a public or internal load balancer, lock down security groups (LB-only 80/443; no public SSH), use a NAT Gateway for egress and VPC endpoints to keep service traffic internal, and, for fully private environments, administer via bastion hosts or AWS Systems Manager Session Manager.
  * **Application Security (Encryption & Secrets):** Enforce HTTPS by attaching an ACM certificate to the Elastic Beanstalk load balancer and manage secrets via AWS Secrets Manager or Parameter Store retrieved at runtime through the instance role (e.g., scripted in EB config files, including encrypted S3 artifacts), avoiding plaintext in code or environment variables and regularly rotating credentials.
  * **Logging and Auditing:** Enable CloudTrail to audit Elastic Beanstalk API activity, centralize application logs in CloudWatch with appropriate retention, enforce posture via AWS Config or Security Hub (e.g., VPC usage and restrictive security groups), and document configuration changes via Events and system logs to strengthen compliance and expedite misconfiguration detection.

* **Network Connectivity Options:**

  * **Public Endpoint (Internet-Facing):** **NOT ALLOWED for Restricted data.** Because Elastic Beanstalk environments default to a public `*.elasticbeanstalk.com` endpoint and internet‑facing load balancer, PepsiCo applications classified as Restricted or Confidential **must not** be publicly exposed; only truly public-facing workloads with Public or Internal data **may use a secured HTTPS endpoint with an approved TLS certificate and additional controls** (e.g., WAF or IP allow-lists), while **all others must** be deployed **without public access**.
  * **Internal-Only Access:** For sensitive workloads, configure Elastic Beanstalk in a VPC with an internal load balancer (no public DNS) or no load balancer for worker/single‑instance services, restricting access to corporate networks via VPN/Direct Connect—required for **Restricted** and recommended for Confidential data—and enforce tight controls with security groups and network ACLs for private environments.
  * **VPC Configuration & Service Endpoints:** Elastic Beanstalk fully supports VPC deployment: associate environments with the correct VPC, subnets, and security groups; provide private‑subnet egress via a NAT Gateway or VPC Interface Endpoints (e.g., S3, CloudWatch, SNS) to avoid public internet exposure; ensure internal DNS for custom domains; and, for Restricted workloads, meet PepsiCo standards by deploying fully private with no public endpoints.

* **Networking & Security Architecture:** *TBD* – (A reference architecture diagram or specific network/security architecture guidelines for Elastic Beanstalk within PepsiCo to be defined.)

* **AWS Service Limitations:**

  * **Resource Quotas:** Elastic Beanstalk imposes per‑region quotas—75 applications, 200 environments, and 2,000 configuration templates—alongside limits of 1,000 stored application versions (requiring pruning beyond this) and 50 custom platform versions, with some quotas eligible for increase via AWS Support.
  * **Application Package Size:** Elastic Beanstalk enforces a 500 MB limit on source bundles; applications exceeding this must be slimmed (e.g., remove extraneous assets) or deployed via alternative mechanisms (e.g., fetching code from a repository or S3 at deploy time), otherwise deployments will fail.
  * **Platform Compatibility:** Elastic Beanstalk supports AWS-provided platforms—Amazon Linux stacks (Tomcat, Node.js, PHP, Python, Ruby, Go) and Windows Server/IIS for .NET—or custom platforms, **but not all language versions/frameworks are available**, so unsupported stacks require Docker or a custom platform, and because AWS manages updates and deprecates older versions, periodic migrations are required.
  * **Deployment Behavior:** Elastic Beanstalk environment changes (e.g., environment variables, scaling policies, platform upgrades) can trigger updates that recycle instances and cause brief downtime **unless mitigated with** *rolling or blue/green deployments*; therefore, rigorously stage and test updates to prevent outages from incompatible releases.
  * **No Native Multi-Region Failover:** Elastic Beanstalk lacks native multi‑region replication or failover, so cross‑region redundancy must be engineered—for example, via Active‑Active deployments across multiple Elastic Beanstalk environments with global load balancing or CloudFront routing—for applications requiring extreme global availability.

* **SKU Features:**

  * **Single-Instance Environment:** A single-instance Elastic Beanstalk environment minimizes cost and complexity for development, testing, or proofs of concept but lacks high availability—failure of the lone EC2 instance causes downtime until replacement and Auto Scaling only replaces, not scales out—so use in production only if downtime is acceptable.
  * **Load-Balanced Environment:** A load-balanced Elastic Beanstalk environment fronts an Auto Scaling group of EC2 instances with an Application Load Balancer, providing multi‑AZ high availability, traffic distribution, and demand‑based scale‑out/in via configurable metrics (e.g., CPU, network), and is the standard production configuration for HTTP/HTTPS applications.
  * **Web Server Tier:** Elastic Beanstalk’s Web Server tier is the default for HTTP(S) applications, provisioning a web server on each EC2 instance and supporting Amazon Linux or Windows platforms—e.g., Java/Tomcat, .NET/IIS, Node.js/Nginx, Python (Gunicorn/Apache), Ruby (Puma/Passenger), PHP/Apache, Go, and Docker—and can operate in single-instance or load-balanced modes.
  * **Worker Tier:** Elastic Beanstalk’s Worker tier provisions EC2 instances with a built-in daemon that consumes tasks from Amazon SQS, enabling asynchronous and scheduled background processing (e.g., batch jobs via cron.yaml) without public endpoints and typically complementing a web tier to decouple long-running or periodic work.
  * **Docker & Custom Platforms:** Elastic Beanstalk natively supports Docker to run applications in any language, enabling single‑container deployments (Dockerfile or image) and multi‑container setups (Docker Compose via `Dockerrun.aws.json`) that abstract ECS orchestration for co‑located services/sidecars, while highly specialized runtimes can use Custom Platforms (Packer/YAML AMIs), albeit with added complexity typically unnecessary for most use cases.

* **Related Service:** *TBD* – (List any related or complementary AWS services, e.g., AWS CodeDeploy for more control over deployments, AWS OpsWorks, etc., if relevant.)

* **Alternatives:** *TBD* – (Discuss alternatives such as AWS ECS/Fargate, AWS Lambda, AWS Lightsail, or other cloud providers’ services that could be used in place of Elastic Beanstalk, and under what circumstances, if required by internal guidance.)

## Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * *Information Security Specifications - AWS Elastic Beanstalk* (Refer to PepsiCo’s internal InfoSec baseline document for AWS Elastic Beanstalk, which outlines mandatory security configurations, compliance requirements, and controls that must be implemented when using this service.)

## Ownership and Version Control

* **Service Architect:** Ramakrishna Ramaraju – *Cloud Service Architecture (CSA) Team*, ([<EMAIL>](mailto:<EMAIL>))
* **Version Control:**

  * *v.1:* 12 Aug 2025 - *Initial draft prepared for Amazon Elastic Beanstalk service catalog entry (Ramakrishna Ramaraju)*
