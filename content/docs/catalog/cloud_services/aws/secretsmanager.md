---
weight: 11
title: "Secrets Manager"
date: 2025-08-01
tags: []
summary: ""
---

## Cloud Service Classification  
- **Category of the Service:** Security  
- **Cloud Provider:** AWS  

## Cloud Service Overview  
- **Name:** AWS Secrets Manager  
- **Description:** AWS Secrets Manager is a fully managed secrets management service that helps securely store and retrieve sensitive information such as database credentials, API keys, passwords, and other secrets. It enables you to centrally manage secrets throughout their lifecycle – you can easily store secrets, rotate them automatically or on-demand, and control access using fine-grained AWS IAM policies. Secrets are encrypted at rest with keys in AWS Key Management Service (KMS), and transmitted securely over TLS when retrieved. The service integrates with other AWS services (like Amazon RDS, AWS Lambda, Amazon Redshift, etc.) so that those services can obtain credentials from Secrets Manager directly instead of hard-coding them.  
### **SKU Approval Status:**  
  - PepsiCo Approved
    - AWS Secrets Manager (standard service offering)  
  - Not Approved:
    - None (AWS Secrets Manager has no tiered SKUs; all features are available by default)  
### **Allowed PepsiCo Data Classification:**  
  - Public  
  - Internal  
  - Confidential  
  - Restricted  

### Service Lifecycle  
- **Release Date:** April 4, 2018  
- **Planned Decommission Date:** No announced decommissioning plans.  
- **Decommission Date:** Not applicable at this time.  

## Usage Guidelines  
### **Features:**  
  - **Secure Secret Storage:** Encrypts secrets at rest using AWS KMS keys and integrates with IAM for fine-grained access control via policies.  
  - **Automatic Rotation:** Automatically rotates credentials on schedule or on-demand with native support for AWS databases and extensibility via Lambda functions.  
  - **Multi-Region Replication:** Replicates secrets across AWS regions for disaster recovery and latency reduction with automatic synchronization.  
  - **Programmatic Retrieval & Integration:** Applications can get secrets from Secrets Manager via SDK, CLI, or API (individually or in batches). Supports secure VPC access (PrivateLink), local caching, and seamless integration with AWS services by referencing secret ARNs.
  - **Auditing & Monitoring:** AWS Secrets Manager integrates with tools like CloudTrail, CloudWatch, Config, and SNS for auditing and monitoring. CloudTrail tracks secret activity, CloudWatch alerts on usage or rotation, and Config checks compliance (e.g., rotation and encryption settings). 
### **Sample Use Cases:**  
  - **Replacing Hard-Coded Credentials:** Store credentials in Secrets Manager and retrieve them at runtime to avoid exposing sensitive data in code or config files.
  - **Automated Database Password Rotation:** Automatically rotate database credentials (e.g., for RDS, Redshift, DocumentDB) on a schedule, with no downtime.
  - **Multi-Region Applications and DR:** Use multi-region replication to keep secrets available and reduce latency for applications deployed in multiple regions.
  - **Managing Third-Party & On-Prem Credentials:** Centralize and control access to third-party and on-premises credentials, with optional rotation using Lambda.
### **Limitations:**  
  - **Secret Size and Binary Limits:** Each secret is limited to 64 KB; not for large files or data blobs.
  - **Secret Versions:** Max 100 versions per secret; avoid frequent updates to prevent hitting this limit.
  - **Throughput and API Throttling:** API rate limits apply (e.g., 10,000 reads/sec, 50 writes/sec); use caching and retries to avoid throttling.
  - **Rotation Support Constraints:** Automated rotation is only native for some AWS databases; others require custom Lambda functions.
  - **Cost Considerations:** Charges apply per secret and API call; frequent access or many secrets can increase costs. Use caching and delete unused secrets to save.
### **Additional Guardrails:** 
- TBD  
### **Used By:**
- TBD list & links to some onboardings.  
### **EA Declaration:** 
- NOT a declared standard.  

## Technical Guidelines  
### **Best Practices:**  
  - **Regular Rotation:** Enable automatic or scheduled rotation to keep secrets fresh and reduce risk if compromised.
  - **Use Caching:** Cache secrets in memory to lower latency, reduce costs, and avoid API rate limits; use AWS caching libraries.
  - **Least Privilege Access:** Restrict secret access using IAM and resource policies; deny broad access and use tags for fine-grained control.
  - **Use IAM Roles for Applications:** Use IAM roles (not access keys) for apps to retrieve secrets, with separate roles for admin and runtime access.
  - **Monitoring and Auditing:** Monitor secret usage with CloudTrail, CloudWatch, and Config; set alerts for unusual access or failed rotations.
### **High Availability & Disaster Recovery:**  
  - **High Availability (Within a Region):** Secrets Manager is highly available within a region using multi-AZ infrastructure and built-in replication/failover. No special HA setup is needed beyond retry logic for transient failures.
  - **Cross-Region Redundancy:** Use multi-region replication to keep secrets available if a region fails. Applications must read from the replica in DR scenarios; writes only occur in the primary region and sync to replicas.
### **Backup & Recovery:**  
  - **Recovery from Deletion (Soft Delete):** Deleted secrets can be recovered within a 7–30 day window; restrict delete permissions to privileged roles.
  - **Version Restoration:** You can roll back to the previous version (`AWSPREVIOUS`) if a secret is rotated incorrectly; use external backups for longer history.
  - **External Backup and Cross-Account Storage:** Manually export and encrypt secrets for backup or compliance; cross-account replication is possible but must be tightly controlled.
  - **Multi-Region as Backup:** Multi-region replication helps with availability, but does not protect against accidental updates or deletions.
### **Access Control & Security Configuration:**  
  - **Encryption and Key Management:** All secrets are encrypted with AWS KMS; you can use AWS-managed or customer-managed keys. Secrets are only decrypted in memory and never stored in plaintext.
  - **Fine-Grained IAM Policies:** Use IAM and resource policies to tightly control who can access or manage secrets. Use tags and ABAC for scalable access control.
  - **Resource Policies (Cross-Account Access):** Attach resource policies to share secrets with other AWS accounts, but avoid broad access. Use BlockPublicPolicy to prevent public exposure.
  - **Audit and Logging:** Enable CloudTrail, Config, and CloudWatch for auditing, compliance, and alerting on secret usage and changes.
  - **MFA and Privileged Access:** Require MFA and SSO for human access; use roles with MFA for sensitive operations.
### **Network Connectivity Options:**  
  - **Public Endpoint:** Not allowed for sensitive/internal apps. Use PrivateLink for all production workloads; public endpoint use must be tightly controlled.
  - **VPC Interface Endpoint (PrivateLink):** Strongly recommended for all uses, required for Restricted data. Provides private, secure access to Secrets Manager within your VPC.
  - **VPC Endpoint Policy & Restrictions:** Attach policies to VPC endpoints to restrict access; use SourceVpce conditions for extra security. Test restrictions to avoid blocking AWS integrations.
  - **No Service Endpoints:** Only PrivateLink is supported for internal connectivity.
  - **Internet Access for Lambda Rotators:** Lambda functions in private subnets should use the VPC endpoint; NAT Gateway is a fallback but not preferred.
### **Networking & Security Architecture:**
- TBD  
### **AWS Service Limitations:**  
  - **Lack of Global Namespace:** Secrets are region-scoped; no global endpoint or unified store. Use replication for multi-region needs.
  - **No Bulk Export/Import:** No built-in bulk export/import; must script or automate secret-by-secret.
  - **Scaling and Performance:** High read throughput, but not ideal for frequent config changes; cache secrets for best performance.
  - **Service Dependency on KMS:** Secrets Manager depends on KMS; if KMS is unavailable, secret access is impacted.
  - **Limited UI for Secret Data Visualization:** Console only shows one version at a time; use external tools for version tracking.
### **SKU Features:**  
  - Single-tier service: all features included for all users; pricing is usage-based, not tier-based.
### **Related Service:** 
- TBD  

## Compliance and Security Guidelines  
- **Security Baseline InfoSec:**  
  - Information Security Specifications – AWS Secrets Manager  

## Ownership and Version Control  
- **Service Architect:** Abdul Moyeed (<EMAIL>)  
- **Version Control:**  
  - v.1: 08 Aug 2025 (Abdul Moueed)