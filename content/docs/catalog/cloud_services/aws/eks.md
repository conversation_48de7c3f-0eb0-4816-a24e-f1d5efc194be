---
weight: 11
title: "EKS"
date: 2025-08-01
tags: [<PERSON><PERSON>, Elastic Kubernetes Service, Container, Docker, EKS Cluster]
summary: ""
---

# Amazon Elastic Kubernetes Service (Amazon EKS)

## Cloud Service Classification

- **Category of the Service**: Containers  
- **Cloud Provider**: AWS
- **Website:** [Technical documentation](https://docs.aws.amazon.com/eks/) 

## Cloud Service Overview

- **Name**: Amazon Elastic Kubernetes Service (Amazon EKS)  
- **Description**: Fully managed Kubernetes service that automates control plane management, scales across multiple AZs, and integrates with AWS services (e.g., ALB, IAM, VPC). EKS supports hybrid deployments via Outposts or external clusters and is CNCF-certified for Kubernetes applications and tooling compatibility.
  
- **SKU Approval Status**:  
  - *PepsiCo Approved*: 
    - Standard Amazon EKS (regional managed service) – Approved for use  
    - Amazon EKS Auto Mode – Approved  
  - *Not Approved*: 
    - Amazon EKS Anywhere (on-premises Kubernetes deployment)  
- **Allowed PepsiCo Data Classification**: Public, Internal, Confidential, Restricted (all data classifications are permitted on EKS, with **Restricted** data requiring the use of private cluster endpoints and strict security controls as outlined below)

## Service Lifecycle

- **Release Date**: June 5, 2018 (general availability)  
- **Planned Decommission Date**: No announced end-of-life (service is actively maintained with regular Kubernetes version updates and new features)  
- **Decommission Date**: Not applicable (service is current and ongoing)

## Usage Guidelines

- **Features**:  
  - *Managed Control Plane & High Availability*: Amazon EKS offers a highly available, multi-AZ **managed Kubernetes control plane** with automatic monitoring and recovery, providing a **99.95% SLA** for the Kubernetes API endpoint and reduces operational overhead through AWS-managed patching and upgrades.  
  - *Flexible Compute Options*: EKS supports running pods on Amazon EC2 instances (including GPUs and Graviton) or AWS Fargate for serverless compute, offering flexibility to match workload needs with on-demand, or fully managed options. Fargate ensures pod-level VM isolation and resource-based pricing.  
  - *Deep AWS Service Integrations*: EKS integrates with AWS services, using the VPC CNI for pod IPs and network integration, Elastic Load Balancers for service networking, and IAM for Kubernetes RBAC authentication. It supports persistent storage (EBS, EFS, FSx), Route 53 for service discovery, Auto Scaling, and CloudWatch or Prometheus/Grafana for monitoring.  
  - *Managed Node Groups & Automation*: Amazon EKS offers **Managed Node Groups** for easy EC2 node management, including updates, scaling, and downtime reduction with graceful pod draining. It supports cost-saving Spot instances. **EKS Auto Mode** automates provisioning, scaling, patching, add-ons, and security integration, providing a fully managed Kubernetes experience with minimal operational effort.  
  - *Kubernetes Conformance & Ecosystem*: Amazon EKS runs **100% CNCF-conformant upstream Kubernetes**, ensuring compatibility with standard APIs, plugins, and tools across environments. It offers **14-month Kubernetes version support**, extended options, and managed add-ons (e.g., CoreDNS, AWS CNI) for seamless updates and new feature adoption.  
  - *Hybrid Deployment Capabilities*: EKS provides a unified view of cloud and on-prem clusters using **EKS Connector**, enabling centralized monitoring and management across environments. It's globally available for consistent multi-region and hybrid deployments, with **EKS Anywhere for air-gapped on-prem setups** (needs EA Approval, not a standard approved SKU) to support hybrid cloud strategies.

- **Sample Use Cases**:  
  - *Hybrid Cloud Applications*: Amazon EKS enables unified deployment across AWS and on-prem environments, using **EKS Anywhere for on-site workloads** (needs EA Approval, not a standard approved SKU). Ideal for hybrid cloud scenarios, it supports seamless cloud-datacenter integration and gradual migration while addressing data sovereignty or latency needs.  
  - *High-Performance AI/ML (Generative AI)*: Supports scalable ML platforms by deploying LLMs and AI workloads using GPU instances and elastic scaling. Tools like Kubeflow or MLflow can train models on GPU nodes and serve inference requests with auto-scaling pods, ideal for AI training and batch procesing.  
  - *Web Applications and Microservices*: Host cloud-native web applications with auto-scaling, multi-AZ fault tolerance, and integrations like ALB and CloudWatch. Ideal for apps like e-commerce sites, it ensures scalability, reliability, and resilience to traffic spikes and AZ outages.  
  - *Internal Developer Platforms (DevOps)*: Enables building a cloud-native development and CI/CD platform, providing on-demand namespaces or clusters with GitOps and IaC tools. It **standardizes Kubernetes-based environments, integrates CNCF tools with AWS services, and supports CI/CD pipelines** (e.g., Jenkins, Argo CD), ensuring governance, agility, and multi-tenant security for enterprise developers.

- **Limitations**:  
  - *Managed Control Plane Constraints*: EKS’s managed control plane offers **limited customization**, restricting API server flags, plugins, and access to etcd or master nodes. While ensuring stability, advanced customizations are unsupported or require workarounds.  
  - *Cost Overhead*: EKS clusters incur a control plane fee per cluster, with extended support clusters costing. This cost is separate from underlying AWS resources and can multiply with multiple clusters.  
  - *Kubernetes Version Lifecycle*: Amazon EKS supports Kubernetes versions for 14 months, with up to 12 months of paid extended support before retirement. Upgrades are one-way, one minor version at a time, and downgrades are not allowed. **Regular upgrades are required**, and recovery from issues often involves restoring from backup to a new cluster.  
  - *Single-Region Clusters (No Native Multi-Region)*: EKS clusters are region-specific and cannot span multiple regions. For multi-region availability, deploy separate clusters per region and manage replication and failover at the application or DNS level (e.g., Route 53 or Global Accelerator). Cluster state (etcd) is not replicated across regions by EKS.  
  - *No Built-in Backup/Restore*: EKS lacks native backup/restore for the control plane or etcd state. Use tools like **Velero or Rubrik (TBD)** to backup Kubernetes resources and Persistent Volume data. Recovery requires creating a new cluster and applying backups, emphasizing the importance of IaC, GitOps, and persistent storage backups.

- **Additional Guardrails**: *TBD* (to be defined per organizational policies; e.g. multi-tenancy guidelines, pod security standards, etc.)

- **Used By**: *TBD* (e.g. list of teams or projects within PepsiCo that have onboarded to EKS)

- **EA Declaration**: *TBD* (Enterprise Architecture declaration status – e.g. whether EKS is a declared/standard service or under review)

## Technical Guidelines

- **Best Practices**:  
  - **Signed Container Images:** Implement trusted container images using **AWS Signer** service.
  - *Infrastructure as Code & GitOps*: Use Infrastructure-as-Code tools (e.g., Terraform, CloudFormation, eksctl) to provision EKS clusters and adopt GitOps (e.g., Flux, Argo CD) to manage Kubernetes manifests, ensuring version control, auditability, consistency, and easy replication in multi-cluster environments.  
  - *Scaling and Resource Management*: Optimize EKS scaling with **Horizontal Pod Autoscaler (HPA) for pods** and **Cluster Autoscaler or AWS Karpenter for nodes**. Use resource requests/limits for efficiency and taints/tolerations for workload segregation. Regularly right-size workloads and use Pod Priority for optimal utilization.  
  - *Security and Least Privilege*: Apply least privilege by using **IAM Roles for Service Accounts (IRSA)** for AWS access and RBAC policies to limit Kubernetes permissions. Enable KMS encryption for Kubernetes secrets at rest with a customer-managed key, and regularly review permissions for enhanced security.  
  - *Monitoring, Logging, and Auditing*: **Enable EKS control plane logging** to CloudWatch for auditing and troubleshooting, and use **Container Insights or AWS Distro for OpenTelemetry (ADOT), for metrics**. Monitor with CloudWatch Alarms, **Amazon Managed Prometheus & Grafana**, or **third-party tools** *(Needs EA approval)*. Aggregate application logs via FluentBit/FluentD, and review CloudTrail logs for EKS API actions to ensure security and visibility.  
  - *Resilience and Updates*: Distribute worker nodes across multiple AZs for high availability, use pod disruption budgets, and design stateless applications. Test Kubernetes upgrades in staging, **update worker node AMIs to prevent version skew**, and consider **blue/green deployments for zero-disruption upgrades**. Backup critical data (e.g., etcd, database snapshots) before major upgrades or migrations.

- **High Availability & Disaster Recovery**:  
  - *In-Region High Availability*: EKS provides a **multi-AZ control plane** with a 99.95% SLA. For HA, distribute worker nodes across 2-3 AZs with Auto Scaling Groups, deploy multiple replicas, and use **pod anti-affinity rules**. Managed node groups ensure AZ spreading and recover nodes in healthy AZs during outages.  
  - *Disaster Recovery (Cross-Region)*: EKS clusters are region-specific; achieve cross-region DR with **paired clusters**, sync via IaC/GitOps, and replicate stateful data using DynamoDB Global Tables, Aurora Global Database, or S3 Cross-Region Replication. Use **Route 53** or **AWS Global Accelerator** for failover and test regularly to meet RPO/RTO and ensure reliability. Replicate resources like ECR and S3 for full DR readiness.

- **Backup & Recovery**:  
  - *Kubernetes Resource Backups*: Use Rubrik or Velero (Needs EA Approval) to back up Kubernetes objects (deployments, services, RBAC roles, CRDs) and persistent volume data on EKS. Store the backups in Amazon S3 and create EBS volume snapshots for PersistentVolumes. Use a secure, versioned S3 bucket for storage, and back up cluster state before major changes or upgrades.  
  - *Recovery and Restore Procedures*: For recovery, create a new EKS cluster and restore backups using **Rubrik or Velero (for Kubernetes objects - Valero needs EA Approval)** and EBS snapshots (for persistent volumes). Test restores periodically on a test cluster. Use AWS-native tools like EBS Snapshot Lifecycle policies and database backups for additional protection. Capture critical non-Kubernetes resources (e.g., load balancer settings, Route 53 DNS records) in IaC. Store backups off-site or cross-region, and enable auditing (EKS control plane logs) for forensic analysis to prevent future incidents.

- **Access Control & Security Configuration**:  
  - *Authentication and RBAC*: EKS integrates with **AWS IAM** for authentication, mapping IAM roles/users to Kubernetes RBAC roles via the aws-auth ConfigMap. Use IAM Roles and Groups to manage access, granting least privilege (e.g., CI/CD pipelines get edit access, developers get view-only). **Avoid using the Kubernetes default admin account** beyond initial setup.  
  - *IAM Roles for Service Accounts (IRSA)*: Enable **IRSA** to securely provide AWS API access to pods by mapping Kubernetes service accounts to IAM roles with minimal permissions. This avoids storing API keys in secrets and limits access scope (e.g., S3 or DynamoDB access). EKS uses **OIDC** for secure token exchange, ensuring temporary credentials with automatic rotation.  
  - *Kubernetes Network Policies and Pod Security*: Use **Kubernetes Network Policies** to restrict pod-to-pod traffic, isolating sensitive microservices and controlling external communication. On EKS, leverage the **AWS VPC CNI plugin** or **Calico** for advanced policies. Enhance security with Security Groups for Pods to manage egress and apply Kubernetes Pod Security Standards to prevent privileged pods or enforce read-only filesystems, using tools like **OPA/Gatekeeper**.  
  - *Secrets Management and Encryption*: Store sensitive data in **Kubernetes Secrets**, restrict access with RBAC, and **enable envelope encryption** using AWS KMS for added security in etcd. For highly sensitive credentials, use AWS Secrets Manager and Parameter Store with the **Secrets Store CSI driver** to mount secrets directly into pods, enabling automatic rotation and centralized auditing.  
  - *Cluster Endpoint Access*: Lock down the EKS API server endpoint by enabling the **private endpoint** for internal access via VPC, VPN, or Direct Connect. Use AWS Client VPN or a bastion host for secure administrator access and enforce IAM authentication. Ensure HTTPS is used for all API communication, secure external systems (e.g., CI/CD) via tunnels, and enable audit logging to monitor API activity. 

- **Network Connectivity Options**:  
  - **Public Endpoint Access**: *Not Allowed by Default for Sensitive Data.* In PepsiCo’s environment, public API access on EKS clusters is prohibited for Internal/Confidential/Restricted data workloads. For dev/test clusters requiring public endpoints, restrict access to known corporate or bastion host IPs using CIDR rules. Open API access to the internet is strictly forbidden, and production clusters must disable the public endpoint. 
  - **Private Endpoint (VPC-Only Access)**: *Required for Restricted Data.* Enable the **private cluster endpoint** to restrict Kubernetes API access to within the VPC or connected networks (via VPN/Direct Connect). **This air-gaps the control plane from the internet** and ensures secure management for production clusters, especially those hosting **Restricted data**.  
  - **Internal Pod Networking**: EKS’ **Amazon VPC CNI** assigns each pod a VPC IP, ensuring traffic stays within the VPC and follows its security rules. Pods can connect to on-prem systems via hybrid links. Use native VPC networking or internal load balancers for microservices, and consider VPC peering, Transit Gateway, or service meshes for cross-account or cross-VPC communication.  
  - **Cross-Service Connectivity**: EKS pods in the VPC should use **VPC Endpoints (PrivateLink)** for services like S3 and DynamoDB, and ECR VPC endpoints for image pulls to avoid internet traffic. Keep all production traffic on AWS’s private or PepsiCo’s internal network. **Public pod or load balancer exposure must be explicitly approved by EA**, with controlled egress points and proper security groups in place.  

- **Networking & Security Architecture**: *TBD* (diagram and detailed description of PepsiCo’s specific EKS network architecture, e.g. VPC layout, subnets, firewalls, service mesh, etc., to be provided)

- **AWS Service Limitations**:  
  - *Resource Quotas*: EKS quotas include **100 clusters per region, 10,000 nodes per cluster, 30 managed node groups per cluster, and 10 Fargate profiles per cluster**. For additional capacity, use self-managed nodes or request quota increases via AWS Service Quotas.  
  - *Pods per Node*: EKS supports up to **110 pods per node (default) and 250 pods on large instances (≥30 vCPUs)**, depending on IP availability via the AWS CNI. Prefix delegation on Nitro instances can increase pod density, though scaling horizontally across nodes is often more practical. Subnets must be sized accordingly, and Kubernetes clusters should consider the **100,000-pod etcd recommendation** for optimal performance. Monitor etcd metrics for ultra-scale clusters.  
  - *Fargate Limits*: AWS Fargate on EKS has a default limit of **6 vCPUs per region for concurrent pods**, which scales with usage or can be increased via AWS Service Quotas. Fargate doesn’t support DaemonSets, privileged pods, or host networking, and has a pod launch rate limit. Monitor limits if planning heavy usage.  

- **SKU Features**:  
  - **Standard Amazon EKS (AWS-Managed Regional Service)**: Standard EKS clusters run in a single AWS region, spanning multiple AZs for high availability with a 99.95% SLA for the Kubernetes API. AWS manages the control plane (etcd, API server) with automatic scaling and patching. Per cluster cost for control plane, while add-ons (e.g., CoreDNS) are included. Clusters support up to 10k nodes and 100k pods. Both EC2 (including Outposts) and Fargate are supported for workers. Public or private API access is configurable. This default mode is **PepsiCo approved** for general use.  
  - **Amazon EKS Auto Mode**: EKS Auto Mode automates the provisioning and management of both the control and data plane, dynamically scaling EC2 instances, patching nodes, and optimizing costs through right-sizing and bin packing. Pricing includes the standard EKS cluster fee plus a per-second charge for managed instances. Auto Mode supports seamless operations but may lack support for specialized hardware or custom AMIs. Amazon EKS Auto Mode is **approved** for use and can be considered for new deployments prioritizing operational simplicity.  
  - **Amazon EKS Anywhere**: This is a **separate deployment option** that allows running EKS on-premises using AWS-curated Kubernetes (EKS Distro) on platforms like VMware vSphere or bare metal. It automates cluster creation and integrates optionally with AWS services, but control plane management (etcd, API server) is the customer’s responsibility. Designed for air-gapped or on-prem data center scenarios, it lacks AWS-managed SLAs. **PepsiCo policy**: EKS Anywhere is **not approved for general use** but **may be evaluated case-by-case** for specific on-prem needs.  
  - **Related Services**: *TBD* (e.g., Amazon ECS as an alternative container service, AWS App Mesh for service mesh in EKS, etc., to be detailed)

## Compliance and Security Guidelines

- **Security Baseline InfoSec**: *Information Security Specifications - Amazon EKS* (refer to PepsiCo InfoSec baseline document for EKS which covers hardening guidelines, required controls, compliance standards mapping such as SOC2, PCI for the service, etc.)

## Ownership and Version Control

- **Service Architect**: Ramakrishna Ramaraju – *Cloud Service Architecture (CSA) Team*, ([<EMAIL>](mailto:<EMAIL>))  
- **Version Control**:  
  - v.1 – 08 Aug 2025 : *Initial draft for Amazon EKS Service Catalog entry. (Ramakrishna Ramaraju)*