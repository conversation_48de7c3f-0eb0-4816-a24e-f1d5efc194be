---
weight: 5
title: "EBS"
date: 2025-07-22
tags: [Block Storage, VM Disk]
summary: "Elastic Block Storage"
---

# Amazon Elastic Block Store (EBS)

## Cloud Service Classification

**Category of the Service**: Storage (Block Storage)
**Cloud Provider**: Amazon Web Services (AWS)
**Website**:[Technical Documentation](https://docs.aws.amazon.com/ebs/latest/userguide/what-is-ebs.html/)

## Cloud Service Overview

**Name:** Amazon Elastic Block Store (EBS)
**Description:** Amazon EBS offers persistent, high-performance block storage(VM Disk volume) for EC2 instances. Volumes act like virtual hard drives—attachable, format-ready, and usable like physical disks. 

### SKU Approval Status

  - PepsiCo Approved
    - General Purpose SSD volumes (gp3 only)
    - Provisioned IOPS SSD volumes (io2 for prod databases) 
  - Not Approved
    - Magnetic volumes (standard)

### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

### Service Lifecycle
  - Release Date: August 20, 2008.
  - Planned Decommission Date: No announced decommissioning plans.
  - Decommission Date: Not applicable at this time.

## Usage Guidelines

### Features
  - Persistent Block Storage
    Block-level storage for EC2 that operates independently of instance lifecycle.
  - Multiple Volume Types
    Types are SSD(Solid State Drive),HDD(Hard Disk Drive), Magnetic 
  - Snapshots (Backups): 
    Supports point-in-time snapshots of volumes, which are incremental backups stored in Amazon S3.
  - Elastic Volume Management
    EBS supports Elastic Volumes, allowing you to dynamically modify volume size,performance (IOPS/  throughput), or even volume type without downtime.
  - Multi-Attach (Shared Volume)
    Certain EBS volumes can be attached to multiple EC2 instances simultaneously. Specifically, Multi-Attach requires Nitro-based instances and is only supported on io1/io2 volumes, and applications must use a cluster-aware file system or locking mechanism to maintain data consistency.
  - Encryption & Security
    EBS integrates with AWS KMS to offer transparent encryption for volumes and snapshots.
  - Availability & Durability
    EBS volumes are designed for high availability within a single AZ and are automatically replicated to protect against hardware failures. Standard volumes offer 99.8–99.9% durability, while premium types can reach up to 99.999%, ensuring reliable data persistence

### Sample Use Cases

  - Boot and Application Volumes for EC2
    Use EBS as the root/boot volume for EC2 instances and as attached data volumes for applications and use additional EBS volumes to store application files, logs, and data.
  - Relational & NoSQL Databases on EC2
    Deploy databases like Oracle, SQL Server, MySQL, PostgreSQL, MongoDB, or Cassandra on EC2 with EBS as the underlying storage. EBS volumes (particularly io2 or gp3) provide the low-latency, high-IOPS storage needed for transaction logs and database data files.
  - Enterprise Applications (ERP, CRM, etc.)
    Migrate on-premises workloads that relied on SAN/NAS storage to AWS by using EBS. Applications like SAP HANA or other ERP systems can be run on EC2 instances with multiple EBS volumes striped or attached as needed for performance and capacity.
  - Big Data Analytics & Batch Processing
    Use high-throughput EBS volumes for big data frameworks (Hadoop, Spark, data warehouses).
  - Backup, Recovery, & DR
    Incorporate EBS snapshots as part of a backup and disaster recovery strategy. Critical volumes can be snapshotted regularly (e.g., hourly or daily) to Amazon S3. In case of instance or AZ failure, these snapshots can be used to quickly restore the data by creating a new volume. Snapshots can also be copied to a second region to serve as cross-region backups for disaster recovery. Fast Snapshot Restore can be enabled on important snapshots to ensure that any volume restored from them is instantly fully performant, reducing recovery time.

### Limitations

  - Availability Zone Scope
    EBS volumes are tied to a single AZ and cannot be attached to instances in different AZs or regions. 
  - Single-Instance Attachment (Standard Volumes)
    By default, an EBS volume can only be attached to one EC2 instance at a time. The Multi-Attach feature (for io1/io2 as noted above) is the exception, but comes with specific requirements. For typical volumes, you cannot share an EBS volume between multiple servers concurrently.
  - Performance Constraints
    Volume Type Limits: Performance depends on EBS volume type and size.
    gp2 Volumes: <1 TiB can burst to 3,000 IOPS; sustained load may deplete credits, reducing performance.
    HDD Volumes (st1, sc1): Use burst credits for throughput; drop to baseline when credits run out.
    EC2 Bandwidth Cap: Total EBS throughput is limited by the instance’s EBS bandwidth.
    Optimization: Use EBS-optimized or high-throughput instances to fully utilize fast volumes
  - Initialization Delay from Snapshots
    Lazy Loading: New volumes from snapshots don’t load all data immediately.
    First Read Latency: Initial reads may be slower as blocks are fetched from S3.
    Initialization Penalty: Performance improves after frequently accessed blocks are read once.
    Pre-Warming: Admins often read all blocks or use Fast Snapshot Restore (FSR) to avoid delays in production.
  - No Native Multi-AZ Volume Replication
    Outside of specialized AWS services (like certain multi-AZ database services), EBS does not automatically replicate volumes across Availability Zones. You must use snapshots or application-level replication to protect against AZ failure. Real-time synchronous replication between AZs for a live volume is not an out-of-the-box feature of EBS.
  - Legacy Magnetic Volumes
    The older magnetic (`standard`) volumes have very limited performance (average \~100 IOPS, up to a few hundred IOPS burst). They also max out at 1 TiB in size. These are considered legacy and are not suitable for most modern workloads; AWS recommends using SSD or modern HDD volumes for better performance. In many enterprises, magnetic volumes are disallowed for new deployments due to their performance and capacity limitations.

### Additional Guardrails:
  - TBD

### Used By
  - TBD

### EA Declaration
  - NOT a declared standard 

## Technical Guidelines

### Best Practices

  - Choose the Right Volume Type
    gp3: Default choice for boot volumes and general workloads; good balance of cost and performance.
    io2: Best for latency-sensitive, high-transaction systems (e.g., large databases); highest IOPS and durability.
    st1: Ideal for throughput-heavy tasks like big data scans and log processing.
    sc1: Suitable for infrequent, archival data.
    Avoid Magnetic Volumes: Deprecated; not recommended for new deployment
  - Use EBS-Optimized Instances
    EBS-Optimized or Nitro Instances: Dedicate bandwidth for EBS I/O, avoiding contention with network traffic.
    Consistent Performance: Ensures reliable throughput and IOPS for attached volumes.
    Default Optimization: Most modern instance types (e.g., M5, C5) are EBS-optimized by defaults.
  - Enable Encryption
    Enable encryption by default to meet security and compliance needs.
    Uses AWS KMS keys to encrypt data at rest with minimal performance impact.
    Applies to new volumes and snapshots, reducing risk of data exposure.
    Use customer-managed KMS keys for custom key policies and rotation control
  - Regular Snapshots & Lifecycle Policies
    Automate backups using AWS DLM or AWS Backup for regular point-in-time snapshots.
    Set backup frequency based on data criticality (e.g., hourly for critical, nightly for standard).
    Retention policies should align with compliance and business needs.
    Tag volumes/snapshots (e.g., Environment:Production, Backup:Yes) to manage backup scope.
    Restrict snapshot deletion via IAM policies to protect critical data.
    Test restores periodically to ensure backup reliability.
  - Monitoring and Alerts
    Monitor key EBS metrics in CloudWatch: IOPS (VolumeConsumedRead/WriteOps), VolumeThroughput, VolumeLatency, BurstBalance, and VolumeQueueLength.
    Set CloudWatch Alarms to detect performance bottlenecks like high queue length or hitting IOPS/throughput limits.
    Identify issues early: Frequent bursting (HDD) or throttling (SSD) may indicate need for volume scaling or load distribution.
    Use monitoring insights to optimize performance and prevent degradation..
  - Optimize Performance
    HDD volumes (st1/sc1): Maintain queue depth of 4–8 for large sequential I/O to sustain throughput.
    Increase I/O size or enable read-ahead on Linux for throughput-optimized workloads.
    SSD volumes: Focus on IOPS and latency; use multi-threaded I/O to fully utilize performance.
    RAID0 striping: Combine multiple volumes to bypass per-volume limits, but adds complexity and risk—use only when necessary and avoid mixing volume types or skipping redundancy for critical data
  - Use Multi-Attach Judiciously
    Use only with io1/io2 volumes and applications designed for shared disk access.
    Requires cluster-aware file systems or locking mechanisms (e.g., Oracle RAC, Windows Failover Cluster).
    Best for active-active clustering within the same AZ for fast failover or concurrent processing.
    Does not protect against AZ failure—Multi-Attach volumes are AZ-bound.
    Test failover and data integrity under load to ensure reliability.

### High Availability & Disaster Recovery

  - Intra-AZ Redundancy
    Automatic replication: EBS replicates data within the same Availability Zone across multiple storage nodes.
    No user action needed: This built-in redundancy protects against disk hardware failures.
    High durability: AWS reports low annual failure rates (0.1–0.2%) for standard volumes.
    Transparent fault tolerance: EBS volumes can survive underlying device failures without impacting the application..
  - Cross-AZ Resilience
    EBS volumes are AZ-bound: They cannot span multiple Availability Zones.
    Use application-level replication: Deploy primary and secondary instances in different AZs with their own EBS volumes.
    Keep data in sync using database replication or software mirroring.
    Failover strategy: In case of AZ failure, the secondary instance can take over with a recent data copy.
    Cold standby option: Regularly take EBS snapshots and use them to restore volumes in another AZ or region if needed.
  - Fast Recovery with Snapshots
    Design for quick restoration using EBS snapshots in your DR strategy.
    Enable Fast Snapshot Restore (FSR) on critical snapshots to eliminate lazy-loading delays.
    FSR ensures full performance immediately when creating volumes from those snapshots.
  - Cross-Region DR
    Periodically copy EBS snapshots to a secondary AWS region to maintain up-to-date backups.
    In case of a regional outage, use these snapshots to restore volumes and launch EC2 instances in the DR region.
    AWS Backup can automate cross-region snapshot copies as part of a backup plan.
    Cost considerations: Cross-region data transfer and snapshot storage incur additional charges
  - Multi-Attach for HA within AZ
    Use Multi-Attach with io2 volumes to allow multiple EC2 instances in the same AZ to access the same volume.
    Enables fast failover: If one instance fails, another can continue using the volume without interruption.
    Ideal for clustered applications that support shared disk access.
    Not a substitute for AZ-level redundancy: If the AZ or volume fails, all attached instances are affected.
    Best for active-active setups needing high availability within a single A

### Backup & Recovery

  - Snapshot Strategy
    Use EBS snapshots as the primary backup for volumes. The first snapshot is full; later ones save only changed blocks, enabling efficient frequent backups. Schedule snapshots per RPO needs (e.g., hourly for critical, daily/weekly for others). Automate with AWS Backup or DLM, tagging volumes for backup and retention.
  - Recovery Process
    Recover by creating a new EBS volume from a snapshot. Attach to an EC2 instance to access data. For root volumes, register as AMI to launch a new instance. Always verify restored volume/instance boots and data integrity is intact.
  - Backup Consistency
    Ensure application consistency by freezing or flushing writes before snapshots (use app tools, AWS methods, or pause I/O). For critical databases, use native backup or briefly stop the instance for a clean snapshot.
  - Snapshot Lifecycle & Cost Management
    Use retention policies to control snapshot costs; clean up unneeded snapshots. Automate deletion with AWS Data Lifecycle Manager. For long-term retention, use EBS Snapshot Archive (lower cost, 24–72h retrieval); keep recent snapshots in standard tier for fast restores.
  - Cross-Account Backups
    For extra safety, share encrypted snapshots to a backup AWS account (with KMS key access). Only share intended snapshots; avoid sharing unencrypted ones.
  - Testing Recovery
    Regularly test recovery by restoring snapshots to verify backup usability and process.

### Access Control & Security Configuration

  - IAM Policies for EBS
    Use IAM to tightly control EBS actions. Apply least privilege: grant only needed permissions (e.g., create/read snapshots on specific volumes). Prefer custom policies over broad managed ones.
  - Prevent Unauthorized Data Access
    By default, EBS snapshots are private. Never make sensitive snapshots public. Prefer sharing encrypted snapshots (with KMS key access) for control. Use AWS Config/tools to detect public snapshots. Consider EBS Snapshot Lock to prevent deletion/tampering.
  - Encryption Practices
    Always enable EBS encryption for confidential or restricted data. Enforce encryption account-wide and use customer-managed KMS keys for projects needing strict access control. Encrypted volumes protect data at rest and in transit; snapshots of encrypted volumes and their restores remain encrypted. Avoid unencrypted snapshots for sensitive data.
  - EC2 Instance Security
    Secure EC2 instances that access EBS by using security groups, regular OS patching, and, if needed, OS or file-level encryption for extra protection. EBS encryption secures data at the block level; file-level encryption (e.g., database TDE) adds defense in depth.
  - Logging and Auditing
    Use AWS CloudTrail to log all EBS API activity (e.g., CreateVolume, DeleteSnapshot) for audit and compliance. Set up CloudWatch Events (EventBridge) to alert on key actions, such as public snapshot sharing or volume deletion.
  - Network Access (API Endpoints)
    EBS data moves internally to EC2, but management API calls use public AWS endpoints by default. For VPC security, use AWS PrivateLink (VPC endpoints) for EC2 to keep EBS API calls private. Attach endpoint policies to control allowed EBS actions.

### Network Connectivity Options

  - Public Internet Access
    Not Applicable.
  - VPC Interface Endpoints (PrivateLink)
    Use VPC Interface Endpoints for EC2 to keep EBS API calls private within your VPC. Recommended for sensitive workloads. You can apply endpoint policies for access control. EBS data traffic remains internal to EC2 infrastructure.
  - EC2 Instance Connectivity to EBS
    EC2 and EBS in the same AZ use a dedicated storage network; no security group or NACL config is needed for EBS traffic. Performance depends on instance EBS bandwidth. Use EBS-optimized instances and correct AZ placement for best results.
  - Cross-Region & Cross-AZ Access
    EBS volumes cannot attach across AZs or regions; use snapshots to move data. For shared access across zones/regions, use higher-level services (e.g., FSx, S3). EBS keeps data within a single AZ for performance and durability.
  - Data Transfer Costs
    Data transfer between EC2 and EBS in the same AZ is free. Snapshot copies across regions or direct API reads may incur costs; plan heavy usage accordingly.

  ### Networking & Security Architecture:
    - TBD

### AWS Service Limitations

  - Volume Size & IOPS Limits
    EBS volumes: up to 16 TiB (gp2, gp3, io1, st1, sc1); io2 Block Express: up to 64 TiB. IOPS/throughput capped by type (e.g., gp3/gp2: 16,000 IOPS, io1: 64,000 IOPS, io2 Block Express: 256,000 IOPS). Instance type may further limit total IOPS/throughput.
  - Attachment Limit per Instance
    Most Nitro-based instances: up to 27–28 EBS volumes. Older instances may allow more, but stability varies. High attachment counts can impact performance.
  - Consistency of Performance
    EBS delivers provisioned performance 99% of the time, but snapshots or small random I/O can reduce throughput. Test and monitor for critical workloads.
  - Lack of Native Multi-Region Support
    EBS is AZ/region-bound. Use snapshots for cross-region backup/migration; no real-time replication or automatic failover.
  - Resource Availability
    AWS may set account-level limits (e.g., total storage, volume count, PIOPS). Request quota increases as needed for large-scale use.

### SKU Features

  - General Purpose SSD (gp3)
    Latest SSD, 3,000–16,000 IOPS, 125–1,000 MiB/s, low latency, scalable performance. Recommended for most workloads.
  - General Purpose SSD (gp2)
    Previous SSD, 3 IOPS/GB (up to 16,000), bursts to 3,000 IOPS, performance tied to size. Use for legacy needs.
  - Provisioned IOPS SSD (io2/io1)
    High IOPS SSDs. io2: up to 64 TiB, 256,000 IOPS (Block Express), 99.999% durability. io1: up to 16 TiB, 64,000 IOPS. Use for critical databases and clustered apps.
  - Throughput Optimized HDD (st1)
    HDD for frequent, large sequential I/O. 40–500 MB/s, up to 16 TiB. Not for boot or random I/O.
  - Cold HDD (sc1)
    Lowest-cost HDD for infrequent, sequential access. 12–250 MB/s, up to 16 TiB. Not for boot or random I/O.
  - Magnetic (standard)
    Legacy HDD, up to 1 TiB, ~100 IOPS, low performance. Use only for rare legacy needs.

### Related Service
  - TBD

## Compliance and Security Guidelines

### Security Baseline InfoSec

  - Information Security Specifications – Amazon EBS

## Ownership and Version Control

### Cloud Solutions Architect
  - Battiprolu Kalyan – <EMAIL>
### Version Control
  * v.1: 22 Jul 2025 (Kalyan Battiprolu)
