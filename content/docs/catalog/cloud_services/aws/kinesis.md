---
weight: 20
title: "Kinesis"
date: 2025-08-13
tags: []
summary: ""
---

## Cloud Service Classification

* **Category of the Service:** Analytics (Streaming Data)
* **Cloud Provider:** Amazon Web Services (AWS)

## Cloud Service Overview

* **Name:** Amazon Kinesis Data Streams & Amazon Kinesis Data Firehose
* **Description:** Amazon Kinesis Data Streams is a fully managed service for scalable, real-time data ingestion and processing, while Kinesis Data Firehose automatically delivers streaming data to destinations like S3, Redshift, and OpenSearch without requiring custom ingestion applications; both are part of the AWS Kinesis platform for streaming analytics.

* **SKU Approval Status:**

  * **PepsiCo Approved:**

    * Amazon Kinesis Data Streams (standard service – on-demand or provisioned modes)
    * Amazon Kinesis Data Firehose (delivery stream service)
  * **Not Approved:**

    * Amazon Kinesis Data Analytics (for Apache Flink)
    * Amazon Kinesis Video Streams
* **Allowed PepsiCo Data Classification:**

  * Public
  * Internal
  * Confidential
  * Restricted

## Service Lifecycle

* **Release Date:**

  * *Amazon Kinesis Data Streams:* November 2013
  * *Amazon Kinesis Data Firehose:* August 2015
* **Planned Decommission Date:** No announced decommissioning plans (service is actively supported).
* **Decommission Date:** Not applicable at this time (service is current and ongoing).

## Usage Guidelines

* **Features:**

  * *Real-Time Streaming Ingestion:* Amazon Kinesis Data Streams enables rapid, continuous intake and storage of high-volume data from various sources with sub-second latency, making new data available for processing or analytics within seconds of arrival.
  * *Durable, Scalable Streams:* Kinesis Data Streams shards provide durable, highly available storage replicated across multiple AZs, scaling elastically to support gigabytes per second of throughput, with each shard offering up to 1 MB/s write and 2 MB/s read, and data retention adjustable from 24 hours up to 365 days for replay or delayed processing.
  * *Managed Data Delivery (Firehose):* Amazon Kinesis Data Firehose automatically batches, compresses, encrypts, and reliably delivers streaming data to destinations like S3, Redshift, and OpenSearch without custom consumer code or manual scaling, managing all throughput and retries internally.
  * *On-the-fly Data Transformation:* Kinesis Data Firehose can optionally transform, convert, compress, and partition streaming data in transit—using built-in format conversions or AWS Lambda—enabling filtering, enrichment, and optimized storage and query performance before delivery to the destination.
  * *Multi-Consumer & Parallel Processing:* Kinesis Data Streams supports multiple independent, parallel consumers—including through Enhanced Fan-Out, which provides each consumer up to 2 MB/s per shard—enabling concurrent real-time processing and making it ideal for complex pipelines and event-driven architectures.
  * *Integration with AWS Ecosystem:* Kinesis services integrate seamlessly with AWS, allowing real-time triggers for Lambda, use as sources for analytics (like Kinesis Data Analytics), direct delivery to storage and messaging services, and built-in connections to services such as CloudWatch Logs and IoT Core, simplifying streaming data pipeline development.

* **Sample Use Cases:**

  * *Accelerated Log and Event Ingestion:* Kinesis Data Streams enables real-time log and event collection by pushing log entries directly into a stream as they are generated, providing durable, highly available log collection and allowing multiple consumers to process or archive logs simultaneously with minimal delay.
  * *Real-Time Metrics and Analytics:* Stream operational metrics or user activity into Kinesis Data Streams for live analytics and dashboard updates, enabling near-instant insight and automated responses by continuously aggregating data with tools like Lambda or Kinesis Data Analytics.
  * *Streaming Data Lake Pipeline:* Kinesis Data Firehose can ingest streaming data directly into an enterprise data lake on S3, batching, converting, and organizing it automatically, so analysts and machine learning jobs can access fresh, query-ready data in near real time without custom delivery code.
  * *Security Monitoring and Alerting:* Kinesis Data Firehose routes streaming security logs to analysis platforms like OpenSearch or Splunk and backs up to S3 for retention, enabling real-time dashboards, rapid anomaly detection, and fast, automated alerting for Security Operations teams.

* **Limitations:**

  * *Throughput and Throttling Limits:* Kinesis Data Streams shards have fixed throughput limits (1 MB/s or 1,000 records/s writes, 2 MB/s reads per shard), and exceeding these results in throttling and ProvisionedThroughputExceeded errors, while Firehose auto-scales but may briefly throttle under sudden heavy loads; applications should monitor and handle throttling, and adjust shard counts or request quotas for large workloads.
  * *Data Retention Window:* Kinesis Data Streams retains data by default for 24 hours (configurable up to 7 or 365 days at extra cost), so consumers must process data within this window or risk data loss; Firehose buffers undelivered data for up to 24 hours, after which undeliverable data is dropped, requiring external archiving for long-term storage.
  * *At-Least-Once Delivery (Potential Duplicates):* Kinesis provides at-least-once delivery, so duplicate records may occur due to retries or reprocessing after failures, requiring consumer applications or destinations to handle deduplication for data integrity.
  * *Destination Constraints (Firehose):* Firehose can only deliver to publicly accessible Redshift clusters and supports only single-destination streams, so private Redshift clusters or multiple targets require alternate workflows, and some advanced features like exactly-once processing aren't supported.

* **Additional Guardrails:** *TBD* (To be defined – e.g., internal guidelines or automation to enforce network controls, encryption, tagging, etc.)

* **Used By:** *TBD* – (To be updated with a list of PepsiCo projects or workloads that have onboarded this service, with links to reference implementations.)

* **EA Declaration:** *NOT a declared standard.* (This service is available for use but has not yet been formally declared as a standard service by Enterprise Architecture.)

## Technical Guidelines

* **Best Practices:**

  * *Capacity Mode & Shard Management:* Select on-demand mode for variable workloads or provisioned mode for predictable traffic, adjust shard count as needed, and design partition keys carefully to avoid hot shards and evenly distribute data.
  * *Optimize Producer Throughput:* Batch and compress records, use aggregation libraries like KPL, and configure buffering settings appropriately to maximize throughput efficiency and minimize cost and latency for both Kinesis Data Streams and Firehose.
  * *Idempotent Consumer Design:* Ensure consumers are idempotent to handle potential duplicates by using unique record IDs and tracking processed data, and use upsert/database transactions or deduplication logic for consistent results.
  * *Monitoring and Alerting:* Monitor key Kinesis and Firehose metrics with CloudWatch, set up alarms for issues like throttling or delivery failures, and use CloudTrail for auditing and alerting on configuration changes and access.
  * *Data Security:* Enable server-side encryption at rest and in transit, enforce strict IAM policies for least-privilege access, avoid wildcards, regularly rotate credentials, and use private network access for secure data handling.

* **High Availability & Disaster Recovery:**

  * *In-Region High Availability:* Kinesis Data Streams and Firehose automatically replicate data across multiple Availability Zones for high availability, requiring no manual setup, and you should deploy producers across AZs and use adequate capacity to ensure resilience against partial failures.
  * *Cross-Region DR Strategy:* Since Kinesis has no built-in cross-region replication, disaster recovery requires application-level solutions such as dual writes to streams in multiple regions or cross-region S3 replication, along with pre-provisioning backup infrastructure and thoroughly testing your failover process.
  * *Consumer and Application Resilience:* Deploy consumers across multiple AZs, implement checkpointing for fast recovery, monitor lag with IteratorAge, and test failure scenarios regularly to ensure fault tolerance and maintain uninterrupted data processing.

* **Backup & Recovery:**

  * *Longer Retention for Reprocessing:* Enable extended retention (up to 7 or 365 days) on critical streams when you need the ability to replay and reprocess past data for recovery, keeping in mind the added storage costs.
  * *Archival to S3:* Set up continuous stream archiving to Amazon S3 (via Lambda, KCL, or Firehose backup) for permanent, low-cost backups and retrospective analysis, and ensure proper security settings on S3 storage.
  * *Recovery from Data Loss:* If data is lost or unprocessed, restore from S3 archives by re-ingesting or replaying records into Kinesis or your database, and immediately increase stream retention if you detect processing delays to prevent further loss.
  * *Testing Backup and Restore:* Regularly test your S3 archiving and recovery procedures, simulating data loss scenarios to ensure you can successfully restore and replay data when needed.

* **Access Control & Security Configuration:**

  * *Least Privilege IAM Policies:* Grant only the specific actions and resource ARNs needed for Kinesis streams and Firehose, avoiding wildcards; use cross-account resource policies and regularly review privileges to minimize excess access.
  * *Network Security - Private Endpoints:* Disable public Kinesis access by using AWS PrivateLink VPC interface endpoints to route internal traffic privately, and use VPN or Direct Connect for secure on-premises ingestion, ensuring data never traverses the public internet.
  * *Encryption & Key Management:* Enable encryption at rest for Kinesis streams and all downstream destinations, use customer-managed KMS keys for sensitive data, enable Firehose server-side encryption for buffered data, restrict key access, and rotate keys per policy.
  * *Logging and Auditing:* Enable CloudTrail (with data event logging if needed) and AWS Config for Kinesis and Firehose, review logs and key policies for compliance, and use CloudWatch Logs to monitor Lambda consumers and processing errors.
  * *Data Segmentation:* Use separate streams for different data sensitivities or applications, enforce strict access, leverage tagging and conventions for management and compliance, and use a fan-out model rather than sharing credentials across teams.

* **Network Connectivity Options:**

  * *Public Endpoint:* **Not Allowed (by Policy).** Public endpoints for Kinesis are prohibited except in rare, approved cases; always use PrivateLink or VPN/Direct Connect for producer and consumer connectivity, and never use public access for confidential or restricted data.
  * *Service Endpoints (AWS Backbone):* *(Not applicable for AWS in the same way as Azure Service Endpoints.)* AWS does not provide Azure-like service endpoints for Kinesis, so always use PrivateLink to avoid traffic traversing the public internet.
  * *Private Endpoint (AWS PrivateLink):* **Required for sensitive data and highly recommended for all uses.** PrivateLink VPC interface endpoints are required for restricted data and highly recommended for all use, ensuring Kinesis traffic remains on the AWS private network; configure endpoint-specific policies as needed.
  * *Networking & Security Architecture:* *TBD* – (In progress. This section will include diagrams or patterns showing how Kinesis integrates into PepsiCo’s network architecture, e.g., producers in a VPC sending to Kinesis via PrivateLink, data flowing to S3, etc., along with security groups, NACLs, or other network considerations specific to our enterprise setup.)

* **AWS Service Limitations:**

  * *Shards and Throughput Scaling:* Each shard has fixed throughput and account limits, and while on-demand mode auto-scales, scaling has brief latency; for large or sudden traffic spikes, preemptively increase quotas or use provisioned mode, and note the 20 EFO consumer limit per stream unless increased by request.
  * *Ordering Guarantees:* Kinesis Data Streams guarantee record order per shard/partition key, but not globally—if strict order is required, use a single shard; Firehose does not guarantee ordered delivery to destinations.
  * *Lack of Built-in Multi-Region Replication:* Kinesis lacks native multi-region replication or failover, so you must implement custom cross-region solutions; also, streams cannot be paused, so leverage retention for consumer downtime.
  * *Firehose Delivery Constraints:* Firehose has a 1 MB record size limit, minimum ~1 minute latency due to buffering, Redshift delivery requires public endpoints and batch loads, and failed deliveries are retried for a configurable period—after which manual recovery from S3 backup is required.
  * *Record Processing and Consumer Limits:* Lambda reading from Kinesis is limited by batch size and concurrency; high-throughput streams may need KCL or Flink, and designs must account for checkpointing delays and potential duplicate processing.
  * *Cost Considerations:* (Not exactly a limitation, but important) Kinesis costs scale with shard count and data volume; batching improves efficiency, Firehose charges by volume and transformation, and long-term data retention in Kinesis is expensive compared to S3.

* **SKU Features:**

  * **Amazon Kinesis Data Streams:** A real-time streaming service with fine-grained scaling via shards. Key features:

    * Low latency streaming data ingestion (typically <1 second from producer to consumer), designed for **millions of events per second** throughput when scaled out.
    * **Manual or On-Demand Capacity:** Offers a provisioned throughput mode (you manage shard count) and an on-demand mode (auto scales shards). On-demand simplifies operations but might have initial throttling on sudden bursts.
    * **Multiple Consumers:** Supports many consumers reading in parallel, with optional Enhanced Fan-Out for high-speed parallel reads (each EFO consumer gets its own 2 MB/sec pipe per shard, avoiding contention). This suits fan-out scenarios like feeding multiple downstream systems concurrently.
    * **Ordering & Replay:** Ensures in-order delivery of records per shard/partition key. Data is stored durably and can be replayed within the retention window. Checkpointing via KCL or timestamp-based iterator allows “rewind” to past records if needed.
    * **Integration:** Can directly trigger AWS Lambda, integrate with AWS Analytics (Flink, etc.), and emit to S3, Redshift, EMR, or other services via custom code. Often used to build custom pipelines and streaming applications where flexibility is needed in processing logic.
    * **Security:** Supports KMS encryption at rest, and fine-grained IAM control on stream access. VPC PrivateLink endpoints available for secure access.
    * **Usage Pattern:** Ideal for building **event-driven applications, custom real-time analytics, and scenarios requiring multiple different consumers or complex processing**. Provides maximum control – you can plug in any processing logic – but requires managing the consuming applications or functions.
  * **Amazon Kinesis Data Firehose:** A fully managed **streaming delivery service**. 
  
  Key features:

    * **Automatic Scaling & Delivery:** No servers to manage—Firehose auto-scales within service limits, handles retries, buffers data, and typically delivers to the target within about 60 seconds.
    * **Destination Support:** Delivers natively to S3, Redshift, OpenSearch, Splunk, and custom HTTP endpoints. Manages Redshift staging/COPY automatically, enabling easy pipeline setup with no code.
    * **Data Transformation:** Optionally applies a Lambda function per stream to transform, filter, or enrich records. Scales automatically; failed records go to S3. For S3, can convert JSON to Parquet/ORC with no code.
    * **Data Encryption & Compression:** Supports data compression (GZIP, ZIP, Snappy) and encryption at rest (S3/KMS). Can back up all or failed records to S3 when delivering to OpenSearch or Splunk.
    * **Use Case Fit:** Best for simple, reliable ETL where minimal processing is needed. Ideal when you just need to land data in storage or analytics services, with one destination and optional Lambda transformation.
    * **Costs:** Billed by data volume ingested/delivered and for conversions. No throughput units; high volume increases costs, so use compression and batching for savings.

* **Related Service:** *TBD* – (Possible related services to mention could include Amazon Managed Streaming for Apache Kafka (MSK) as an alternative streaming platform, AWS IoT Core for IoT data ingestion, or Amazon EventBridge for event-driven patterns – this section will be updated with guidance on when to use Kinesis vs other messaging/stream services in AWS.)

## Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * Information Security Specifications – *Amazon Kinesis (Data Streams & Firehose)*. (Refer to internal InfoSec baseline documents for Amazon Kinesis services which outline required security controls such as encryption, network isolation, monitoring, data classification handling, etc., in accordance with PepsiCo policies. This includes ensuring compliance with data privacy regulations for any personal data flowing through streams, and validating that the Kinesis implementation meets all corporate InfoSec requirements.)

## Ownership and Version Control

* **Service Architect:** Dariusz Korzun – *Cloud Service Architecture, PepsiCo* ([<EMAIL>](mailto:<EMAIL>))
* **Version Control:**

  * *v.1:* 12 Aug 2025 – Initial document prepared (Dariusz Korzun)
  * *v.2:* 17 Aug 2025 – Updated additional details (Rakesh Ponnada)
