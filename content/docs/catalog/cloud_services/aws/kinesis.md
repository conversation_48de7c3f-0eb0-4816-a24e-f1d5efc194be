---
weight: 20
title: "Kinesis"
date: 2025-08-13
tags: []
summary: ""
---

## Cloud Service Classification

* **Category of the Service:** Analytics (Streaming Data)
* **Cloud Provider:** Amazon Web Services (AWS)

## Cloud Service Overview

* **Name:** Amazon Kinesis Data Streams & Amazon Kinesis Data Firehose
* **Description:** Amazon Kinesis Data Streams is a fully managed, scalable service for real-time data streaming that enables the collection and processing of large volumes of data records continuously and with very low latency. It can ingest gigabytes of data per second from hundreds of thousands of sources and make it available for immediate processing by multiple applications. Amazon Kinesis Data Firehose is a fully managed service for delivering real-time streaming data to destinations such as Amazon S3, Amazon Redshift, Amazon OpenSearch Service, and third-party providers. With Firehose, you **do not need to build custom applications** for streaming ingestion; you configure data producers to send data to Firehose, and it automatically batches, transforms (if configured), and delivers the data to the specified target within seconds. Both services are part of the Amazon Kinesis platform for streaming data and integrate with other AWS services for analytics and processing.
* **SKU Approval Status:**

  * **PepsiCo Approved:**

    * Amazon Kinesis Data Streams (standard service – on-demand or provisioned modes)
    * Amazon Kinesis Data Firehose (delivery stream service)
  * **Not Approved:**

    * Amazon Kinesis Data Analytics (for Apache Flink)
    * Amazon Kinesis Video Streams
* **Allowed PepsiCo Data Classification:**

  * Public
  * Internal
  * Confidential
  * Restricted

## Service Lifecycle

* **Release Date:**

  * *Amazon Kinesis Data Streams:* November 2013
  * *Amazon Kinesis Data Firehose:* August 2015
* **Planned Decommission Date:** No announced decommissioning plans (service is actively supported).
* **Decommission Date:** Not applicable at this time (service is current and ongoing).

## Usage Guidelines

* **Features:**

  * *Real-Time Streaming Ingestion:* Enables rapid, continuous intake of high-volume data from various sources (application logs, clickstreams, IoT sensor readings, etc.) with **sub-second latency**. Amazon Kinesis Data Streams can continuously capture and store incoming records, making them available to consuming applications **within seconds of production**. This ensures new data is quickly ready for processing or analytics as it arrives.
  * *Durable, Scalable Streams:* Kinesis Data Streams shards provide **durable storage** of data for the retention period and are replicated across multiple Availability Zones for high availability and reliability. The service scales elastically by increasing or decreasing the number of shards (or by using on-demand capacity mode) to handle virtually any throughput. Each shard offers up to 1 MB/s write and 2 MB/s read throughput, and streams can be scaled to **ingest gigabytes per second** by adding shards or using on-demand scaling. Data is stored for 24 hours by default (expandable up to 365 days), allowing applications to replay or process data that might arrive out-of-order or be reprocessed later.
  * *Managed Data Delivery (Firehose):* Amazon Kinesis Data Firehose handles the loading of streaming data into destinations **without requiring custom consumer code**. It automatically **batches, compresses, and encrypts** data, and delivers it to configured targets like S3 buckets, Redshift data warehouses, OpenSearch Service domains, Splunk endpoints, or custom HTTP endpoints. Firehose manages scaling internally; you don’t provision shards or servers – it will scale to accommodate the data throughput and handle retries/buffering to ensure reliable delivery.
  * *On-the-fly Data Transformation:* Kinesis Data Firehose can optionally transform or convert data in transit before delivery. You can enable built-in format conversions (for example, JSON to Parquet/ORC for data lake optimization) and **data transformation via AWS Lambda** functions. This allows filtering, aggregating, or enriching streaming data on the fly. Firehose’s transformation feature can also **compress data** (e.g., GZIP) and **partition data** by keys (such as by date or entity) before writing to the destination, which reduces storage usage and improves downstream query performance.
  * *Multi-Consumer & Parallel Processing:* A single Kinesis data stream can be consumed by multiple independent applications simultaneously. For example, one consumer can aggregate data and load it to a database while another archives raw data to S3, reading from the same stream in parallel. Kinesis Data Streams supports **Enhanced Fan-Out (EFO)** for high-performance parallel reads – each consumer stream can get up to 2 MB/s per shard dedicated throughput, enabling real-time fan-out to multiple applications without contention. This makes it suitable for building complex pipelines and **event-driven architectures**, where different microservices or analytics jobs consume the stream concurrently.
  * *Integration with AWS Ecosystem:* Kinesis services are designed to work seamlessly with other AWS offerings. Kinesis Data Streams can **trigger AWS Lambda functions** to process records in real time, or be used as a source for AWS analytics services like Amazon Kinesis Data Analytics (Apache Flink) for streaming SQL processing. Data from Kinesis can be emitted to storage services (S3, DynamoDB, etc.) or message services, enabling many downstream uses. Kinesis Data Firehose has built-in integrations for AWS services (e.g., it can directly receive data from CloudWatch Logs, IoT Core, or events from Amazon MSK topics) and can send to nearly any data store. This tight integration reduces development effort for streaming data pipelines.

* **Sample Use Cases:**

  * *Accelerated Log and Event Ingestion:* Use Kinesis Data Streams to **collect application logs and system event streams** in real time. Rather than writing logs to local disk and batch uploading, configure servers and applications to push log entries to a Kinesis stream as they are generated. This ensures that if a server fails, recent log data is already in the stream and not lost. Multiple consumers can then process these logs – for example, one might store them in Amazon S3 for long-term archival while another triggers alerts on certain events. This pattern provides durable, highly available log collection with minimal delay.
  * *Real-Time Metrics and Analytics:* Stream operational metrics or user activity events into Kinesis Data Streams and perform **live analytics and dashboard updates**. For example, a web application can send clickstream events into a stream; a Kinesis Data Analytics (Flink) application or AWS Lambda can continuously aggregate these to produce real-time metrics like active users, error rates, or engagement statistics. This enables dashboards and reporting that reflect the last few seconds of data, rather than waiting for hourly or daily batch jobs. Business teams can react to insights (e.g., trends, anomalies) almost immediately, and automated systems can adjust to events in real time.
  * *Streaming Data Lake Pipeline:* Use Kinesis Data Firehose to ingest streaming data directly into an **enterprise data lake on Amazon S3**. For example, IoT sensors or transaction systems send data to a Firehose delivery stream; Firehose **batches and converts** the data into a format like Parquet, then writes it to an S3 bucket organized by date. This pipeline requires no persistent consumer application – the delivery is fully managed. Downstream, analysts can run SQL queries on the up-to-the-minute data in S3 (using Amazon Athena or Redshift Spectrum), and machine learning jobs can train on fresh data continuously. Streaming into the data lake in near real time accelerates data availability for analytics from hours to minutes.
  * *Security Monitoring and Alerting:* Leverage Firehose to route streaming security events and log data into analysis tools for **real-time threat detection**. For instance, VPC Flow Logs or application audit logs can be sent through Kinesis Data Firehose to an Amazon OpenSearch Service cluster (Elasticsearch) or to a SIEM platform like Splunk. Firehose will buffer and deliver the data, and can also **back up each log event to S3** for retention. This use case enables continuous monitoring dashboards that update as events occur, and can trigger alerts (via AWS Lambda or Amazon OpenSearch alerts) within seconds of detecting anomalies. Such an architecture helps Security Operations teams respond to incidents much faster than traditional batch log analysis.

* **Limitations:**

  * *Throughput and Throttling Limits:* Kinesis Data Streams capacity is limited per shard. Each shard supports up to **1 MB/sec or 1,000 records/sec for writes**, and up to **2 MB/sec for reads**. If these limits are exceeded (e.g., a producer sends data faster than 1 MB/s into a shard), the put calls will be throttled with `ProvisionedThroughputExceeded` errors (HTTP 400). Similarly, a consumer reading too fast or with too many simultaneous reads can be throttled. Exceeding read or write limits results in delays or data being rejected; applications must be built to handle retries and backpressure. Firehose delivery streams have their own ingest limits (for Direct PUT calls, default \~5 MB/sec in US regions) which **automatically scale up** if limits are breached, but sudden traffic spikes beyond default can cause temporary throttling until auto-scaling catches up. It’s important to monitor throughput and request quota increases or shard count adjustments in advance of large workload increases.
  * *Data Retention Window:* Kinesis Data Streams is not a long-term data store – by default, records are retained for **24 hours** from the time they are added to the stream. You can increase the retention up to 7 days (or up to 365 days with the long-term retention feature) for special use cases, but this incurs additional costs. Any data older than the retention period is automatically purged and becomes inaccessible. This means consumers **must process and checkpoint data within the retention window** or risk missing data. (If a consumer falls behind by more than the retention period, that data is lost.) Kinesis Data Firehose is designed for **one-way delivery** and does not indefinitely store data either: it will buffer data for up to 24 hours if the delivery destination is unavailable (for streams with direct PUT source). If the outage exceeds 24 hours or the data cannot be delivered (e.g., due to misconfiguration), Firehose will drop the data that can’t be delivered after that buffer window. In short, persistent storage of data beyond these windows must be handled by archiving to databases or S3 if needed.
  * *At-Least-Once Delivery (Potential Duplicates):* The Kinesis platform (both Data Streams and Firehose) **does not guarantee exactly-once processing**; it operates on at-least-once delivery semantics. This means **duplicate records can occur** in certain scenarios. For example, a producer may time out and retry a PutRecord request, causing the same record to be written twice. On the consumer side, if a processing application fails after checkpointing, it might reprocess some records upon restart, resulting in duplicates. Amazon Kinesis will ensure records are not lost, but in doing so may feed the same data more than once. Applications using the data (or the destinations for Firehose) **must be designed to handle duplicates**, e.g., by using unique IDs and idempotent operations or de-duplication logic in the consumer or destination system.
  * *Destination Constraints (Firehose):* When using Kinesis Data Firehose, be aware of certain destination-specific limitations. Notably, Firehose can deliver to Amazon Redshift **only if the Redshift cluster is publicly accessible** on the network. Firehose itself runs outside your VPC, so it needs to reach the Redshift endpoint over the internet or via a publicly reachable address. This requirement may conflict with strict security controls that keep data warehouses private. If a Redshift cluster is private (no public endpoint), Firehose cannot directly load it – an intermediate solution (such as writing to S3 and then copying via a Redshift Spectrum or a custom process) would be needed. Additionally, Firehose delivery streams are single-destination: you cannot natively fan-out one Firehose to multiple endpoints. If you need the same data delivered to multiple targets, you would have to create multiple Firehose streams (or use Kinesis Data Streams with multiple consumer applications instead). Finally, certain advanced integrations (like exactly-once sinks or custom processing beyond Lambda transformations) are not supported in Firehose, as it aims for simplicity and managed convenience over custom logic.

* **Additional Guardrails:** *TBD* (To be defined – e.g., internal guidelines or automation to enforce network controls, encryption, tagging, etc.)

* **Used By:** *TBD* – (To be updated with a list of PepsiCo projects or workloads that have onboarded this service, with links to reference implementations.)

* **EA Declaration:** *NOT a declared standard.* (This service is available for use but has not yet been formally declared as a standard service by Enterprise Architecture.)

## Technical Guidelines

* **Best Practices:**

  * *Capacity Mode & Shard Management:* Choose the appropriate capacity mode for each Kinesis Data Stream. **On-Demand mode** is recommended for new or variable workloads – it automatically manages shard scaling and will seamlessly handle throughput increases or decreases without manual intervention. Use **Provisioned mode** when you need tighter cost control or have a predictable traffic pattern; in this case, regularly monitor shard metrics and perform resharding (split or merge shards) to right-size the capacity. Always design the **partition key strategy** to avoid “hot shards” – use keys that evenly distribute records across shards so that one shard isn’t maxed out while others are idle. If one partition key (e.g., a single user or device ID) produces a disproportionate amount of data, consider adding more entropy to the key (like hashing or adding a random suffix) to spread its data over multiple shards.
  * *Optimize Producer Throughput:* **Batch and compress records** to increase efficiency. When writing to Kinesis Data Streams, leverage the PutRecords API to send records in batches, or use the Kinesis Producer Library (KPL) which aggregates multiple user records into a single Kinesis record automatically. Aggregating records reduces the per-request overhead and helps approach the 1 MB/sec shard limit more effectively (many small records can be combined into one payload). Similarly, use compression (e.g., gzip) for text/log data before sending, to reduce payload size – this can significantly cut down on throughput usage and costs. For Kinesis Data Firehose direct puts, tune the **BufferingHints** (buffer size and buffer interval) according to your use case. For example, if you need low latency, set a smaller buffer size (e.g. 1 MB) and interval (e.g. 60 seconds) so data flushes quickly; if you have high volume and can tolerate a bit more latency, use larger buffers (e.g. 5–10 MB) to optimize batching. By default Firehose uses up to 5 MB or 5 minutes buffering – adjust these to balance cost vs. latency.
  * *Idempotent Consumer Design:* Since duplicates are possible, design stream consumers to **handle reprocessing gracefully**. If using the Kinesis Client Library (KCL) or AWS Lambda, your processing logic should be **idempotent** – meaning if the same record is processed more than once, the outcome remains the same. One common approach is to include a unique identifier (such as a UUID or compound key) in each record and have consumers track which IDs they’ve already processed (for instance, storing the last seen ID per shard in DynamoDB or using a de-duplication cache). Alternatively, if writing to a database, use UPSERT operations or transactions keyed by the record’s unique ID so that duplicates do not create inconsistent data. In aggregate workflows (like time-windowed analytics), consider the impact of duplicates on calculations and perhaps implement **exactly-once** logic at the application level (e.g., subtracting out previously counted duplicates when detected).
  * *Monitoring and Alerting:* Implement thorough monitoring on both Kinesis Data Streams and Firehose to ensure any issues are caught early. Use **Amazon CloudWatch Metrics** – for streams, key metrics include `IncomingBytes`, `IncomingRecords`, `WriteProvisionedThroughputExceeded` (indicates throttling events), and `IteratorAgeMilliseconds` (which indicates if consumers are falling behind). For Firehose, monitor metrics like `DeliveryToS3.Success`/`Failure` (or equivalent for other destinations), `BackupToS3.Success/Failure`, and `IncomingBytes/Records`. Set up CloudWatch Alarms on important indicators, such as if a stream’s put throttling rate goes above 0 (suggesting producers are being throttled) or if a Firehose delivery fails and triggers retries. Additionally, enable **AWS CloudTrail** for Kinesis API calls to audit any changes to stream or Firehose configurations and to detect any unauthorized access. Coupling CloudTrail logs with Amazon CloudWatch Events can allow you to alert on or even automatically remediate unexpected modifications (for example, if someone accidentally deletes a stream).
  * *Data Security:* Ensure that all data in transit and at rest is protected. **Enable server-side encryption (SSE)** on Kinesis Data Streams using AWS Key Management Service (KMS) keys (the service can encrypt data at rest with an AWS-managed KMS key by default, or you can specify a customer-managed CMK for higher control). This is essential if the stream carries sensitive or Restricted data. On Firehose delivery streams, enable **encryption at rest** for the destinations: for S3, use SSE-KMS or SSE-S3 on the bucket; for Redshift, ensure the cluster is encrypted; for OpenSearch, use an encrypted domain. Firehose can also encrypt the data during transit through the service by enabling **Firehose SSE** (which uses a KMS key to encrypt data while buffering). Restrict access to Kinesis resources with fine-grained IAM policies – for instance, a given application’s IAM role should only have permission to write to specific streams or read from specific streams. Avoid the use of wildcard `*` permissions in Kinesis policies; Kinesis Data Streams **does not support wildcard principals or actions in resource policies** specifically to encourage least-privilege access. Regularly review and rotate credentials or keys used by producers and consumers (for example, if using AWS access keys in IoT devices to put to Kinesis, have a rotation strategy). Finally, consider enabling **private network access** (see Network Connectivity below) to reduce exposure of data ingestion endpoints to the public internet.

* **High Availability & Disaster Recovery:**

  * *In-Region High Availability:* Amazon Kinesis Data Streams and Firehose are **highly available by design** within a single region. Data Streams synchronously replicate data across at least **three different Availability Zones** in the region, which means hardware or AZ failures do not lead to data loss. This multi-AZ replication is automatic and requires no user action. In practice, a Kinesis stream continues to operate even if an AZ goes down, as the data is durably stored in the other AZs. Firehose is also managed across multiple AZs in the backend (the service will survive instance failures). When using Kinesis, you do not need to manually set up clustering or replicas for HA – AWS handles it. To maximize availability, ensure producers have network paths to multiple AZs (e.g., if running in EC2, use **multi-AZ deployment** for producer instances or have fallback to an alternate AZ). Also, use the **on-demand capacity** or an adequate shard count to handle traffic spikes, so the stream does not become a bottleneck due to under-provisioning during a partial failure.
  * *Cross-Region DR Strategy:* Kinesis is a regional service, and there is **no built-in cross-region replication** for Data Streams or Firehose. If you have a **disaster recovery requirement** to handle region-wide outages, you must architect it at the application level. One common approach is to implement **dual writes**: for example, have producers send each record to two Kinesis Data Streams in two different regions (perhaps using an asynchronous fan-out or a middleware layer that replicates events to both regions). Alternatively, you can use an intermediate durable store like Amazon S3 with cross-region replication: e.g., Firehose in Region A writes to an S3 bucket in Region A, which is configured to replicate to Region B; in a DR scenario, you could ingest from the replicated bucket or bring up consumers in Region B to read any backlog from S3. Another approach is to run a mirror consumer that reads from the primary region’s stream and writes the data to a stream in a secondary region (this can be done with a lightweight EC2 or Lambda function streaming events across regions). **Plan and test** the failover process: for instance, how will your producers and consumers switch to the secondary region – DNS, configuration flags, etc. It’s important to also duplicate any infrastructure (IAM roles, KMS keys, Firehose configurations, etc.) in the backup region beforehand so that it can take over quickly.
  * *Consumer and Application Resilience:* Ensure the applications reading from Kinesis are built for fault-tolerance. If using KCL, deploy the consumers on multiple compute instances (or containers) across AZs so that if one VM goes down, others can pick up the shards. KCL will rebalance shard assignment in the event of host failure. For AWS Lambda consumers, AWS automatically handles running multiple instances across AZs; you should focus on error handling in the Lambda function (e.g., catching exceptions so the Lambda doesn't continuously fail). The **IteratorAge** metric is especially important – if it starts rising, your consumer is lagging behind, possibly due to failures or insufficient processing capacity. Have alerts for high IteratorAge and scale out consumers or investigate issues when it occurs to prevent data loss (if age approaches the retention limit). In summary, use a **checkpointing** mechanism (like DynamoDB for KCL, or internal tracking for custom code) so that if consumers restart after failure, they resume from the last checkpoint and do not reprocess too much data (or miss data). Regularly test recovery scenarios (e.g., kill a consumer process or simulate a Lambda failure) to ensure the stream processing resumes as expected without manual intervention.

* **Backup & Recovery:**

  * *Longer Retention for Reprocessing:* If the business use case permits, consider **enabling extended retention** on critical Kinesis Data Streams. By increasing retention to the maximum (e.g., 7 days by extended retention, or up to 365 days with long-term retention), you gain the ability to replay and reprocess data for a longer period. This can be invaluable for recovery from downstream failures – for example, if a bug in a consumer caused data to be processed incorrectly, you could fix the bug and then read from the stream at a timestamp that is prior to the bug fix and reprocess the last several days of data. Keep in mind the cost implications (AWS charges for data stored beyond 24 hours) and use this feature selectively when needed for recovery or audit scenarios.
  * *Archival to S3:* For an indefinite backup of all stream data, set up a parallel **archiving process**. One straightforward method is to use AWS Lambda or a KCL consumer to continuously read the stream and dump every record into an **Amazon S3 bucket** (often in a compressed format and partitioned by time). This essentially creates a **permanent log** of the stream that can be kept for months or years in S3 at low cost. If using Firehose, you can configure the primary destination as S3 (which by nature archives the data) or if using another destination (like Redshift or OpenSearch), enable the **backup to S3** option for any data that fails to deliver. The Firehose developer guide notes that for each delivery stream, you can enable source record backup, which will send every record (or at least any record that failed transformation) to a specified S3 bucket. Having all raw data in S3 not only provides a backup for recovery, but also allows retrospective analysis using other tools (Athena, EMR, etc.) without impacting the real-time pipeline. Ensure your S3 backups are properly secured (e.g., encryption, access control) as they will contain the same sensitive data as the stream.
  * *Recovery from Data Loss:* In the event that data was lost or not processed (e.g., a consumer was down for a period and the data expired from the stream, or Firehose dropped data after 24h due to extended outage), your recovery will depend on having an alternate copy of the data. If you have S3 archives as above, you can **replay data from S3** by writing a script or using AWS Glue/Kinesis Data Streams AWS SDK to read objects from S3 and push records back into a Kinesis stream or directly into a database. Another strategy for some use cases is to use Amazon DynamoDB or databases to checkpoint processed offsets or store raw events; however, for high-volume streams this is less common. The key is to identify the gap (which records were not processed) and then re-ingest them if possible. For Kinesis Data Streams, you can also **increase the retention period on the fly** when you detect an issue – for example, if a consumer is broken and you know it won’t be fixed for a day, immediately increase stream retention to the maximum to buy time for recovery. This won’t bring back expired data, but it can prevent impending data loss.
  * *Testing Backup and Restore:* Include your streaming data pipelines in disaster recovery drills. For example, test that your S3 archival Lambda is correctly writing all data and that those objects are readable and not corrupted. Simulate a scenario where you have to use the backups – e.g., spin up a temporary Kinesis stream, and write a tool to read a day’s worth of archived data from S3 and put it into that stream, then see if your consumers can process it end-to-end. If using cross-region replication as a backup (like a duplicated stream in another region), periodically switch consumers to read from the secondary stream to ensure it’s receiving all the data. By verifying these procedures, you’ll be confident that if a real incident occurs, the team knows how to recover lost data with minimal downtime.

* **Access Control & Security Configuration:**

  * *Least Privilege IAM Policies:* Apply the principle of least privilege when granting access to Kinesis streams or Firehose. Instead of using wildcards, specify exact Stream ARNs and allowed actions. For instance, an application that only needs to write data should be granted `kinesis:PutRecord`/`PutRecords` on **only the specific stream(s)** it uses. Avoid permitting broad actions like `kinesis:*` on all resources. In fact, Kinesis Data Streams **resource policies do not support wildcards** for actions or principal fields, in order to prevent overly broad access. If cross-account access is needed (e.g., a producer in Account A writing to a stream in Account B), use a combination of a resource-based policy on the stream (whitelisting Account A’s role) and an IAM policy on the producer’s role. For Kinesis Data Firehose, ensure the IAM role that Firehose assumes (for writing to destinations) is locked down to only the necessary permissions (e.g., if delivering to S3, the role should only have access to the specific bucket prefix; if loading Redshift, only the specific cluster and database). Regularly review IAM policies associated with Kinesis to remove any unused or excessive privileges.
  * *Network Security - Private Endpoints:* **Disable public network access** to Kinesis endpoints for internal applications. By default, producers/consumers communicate with Kinesis Data Streams and Firehose via the public AWS endpoint (internet). For sensitive data (Confidential/Restricted) or any production workload in our enterprise network, use **AWS PrivateLink (VPC Interface Endpoints)** to route traffic privately. AWS provides interface endpoints for both Kinesis Streams and Firehose in each region. Set up these VPC Endpoints in your AWS VPC and configure applications (or AWS SDKs) to use the endpoint – this keeps the traffic within AWS’s network and avoids exposure to the public internet. In addition, configure VPC endpoint policies if needed to restrict which streams can be accessed via the endpoint. On the service side, Kinesis does not have a concept of IP whitelisting (unlike some Azure services); access is purely IAM-based, but using PrivateLink ensures that **no Internet Gateway or NAT** is required for connectivity. For use cases that involve on-premises producers, consider using a secure AWS Site-to-Site VPN or Direct Connect to AWS, so that even those producers can send data without going over the public internet.
  * *Encryption & Key Management:* Enable **encryption at rest** on all data stores in the pipeline. As noted, Kinesis Data Streams can encrypt data at rest with AWS KMS – this should be turned on (it’s transparently handled by the service). Use customer-managed KMS keys for streams that handle highly sensitive data, so that you have full control over key rotation and access (you can set up key policies to restrict who can decrypt the data). For Firehose, when delivering to S3, always specify an encryption mechanism (SSE-KMS with a CMK, or at minimum SSE-S3). When Firehose is used to write to Redshift or OpenSearch, those services should have encryption enabled on their side (e.g., Redshift clusters encrypted with KMS, OpenSearch domains encrypted). Firehose’s **server-side encryption feature** (separate from the destination encryption) can be enabled to encrypt data while it’s being buffered in the service using an AWS KMS key – consider enabling this especially if regulatory compliance requires that data is never plaintext even in transit storage. Manage KMS keys carefully: restrict who can use them (for example, only the Kinesis service and certain role principals), and rotate keys per company policy (without deleting old keys to maintain ability to decrypt historical data).
  * *Logging and Auditing:* Turn on **AWS CloudTrail** for Kinesis and verify that it’s capturing Data Events if needed. By default, CloudTrail will log management events like CreateStream, DeleteStream, CreateDeliveryStream, etc. If deeper auditing is required (such as logging **data plane actions** like PutRecord or GetRecords), you can enable data event logging for Kinesis Data Streams – but be aware this can generate a high volume of logs for busy streams. At a minimum, ensure CloudTrail is capturing who creates or modifies streams and Firehose configurations. Use AWS Config to track configuration changes to streams (for example, Config can alert if a stream’s encryption gets turned off or if retention period is changed). On the consumption side, if using Lambda, use Lambda’s CloudWatch Logs to monitor function execution and any errors processing Kinesis records. For **compliance**, if the data in the stream is regulated, you may need to show evidence of encryption and access control – the combination of KMS key policies (showing restricted access) and CloudTrail logs (showing no unauthorized access attempts) can help with that. Also consider enabling **data integrity checks** if relevant – for instance, producers could attach a hash to each record and consumers verify it, although Kinesis does not do this natively, it would be an application-level measure.
  * *Data Segmentation:* Use separate Kinesis streams or Firehose delivery streams for different data categories to enforce isolation. Do not mix data of different sensitivity levels in one stream; for example, if you have a stream for “Internal” data (PII-free application telemetry) and another for “Confidential” or “Restricted” data (customer personal data or financial transactions), keep those on distinct streams and limit access accordingly. This way, granting a user or role access to the Internal telemetry stream doesn’t inadvertently give access to sensitive data. Similarly, segment by application or team – it’s easier to manage permissions and monitor usage when streams are purpose-specific. If multiple teams need to consume the same data, consider using a **fan-out model** (one team owns the source stream and other teams consume from it via their own processing or via Firehose to their target) rather than sharing credentials broadly. Lastly, implement **resource naming conventions and tagging** for Kinesis streams (and Firehose). Tags can include data classification (e.g., a tag “DataClassification=Confidential”) and owner information, which can be used by automated policies or just to quickly identify the importance of a stream. This tagging can tie into guardrails – for instance, an internal script could check that any stream tagged as “Restricted” has PrivateLink endpoints enforced and encryption enabled, etc.

* **Network Connectivity Options:**

  * *Public Endpoint:* **Not Allowed (by Policy).** By default, Kinesis Data Streams and Firehose service endpoints are public—accessible over the internet. In the PepsiCo cloud environment, using public endpoints for data streaming is **strongly discouraged for security reasons**, especially for Confidential or Restricted data. All producers and consumers should communicate with Kinesis through either a VPN/Direct Connect or via VPC Interface Endpoints (PrivateLink). If there is an exceptional scenario where a public endpoint must be used (for example, a third-party cloud service sending data into Kinesis), then it must be tightly controlled: enable TLS encryption (always use HTTPS, which AWS requires anyway) and restrict the source IP ranges or use firewall rules to only allow known addresses to reach the endpoint. However, as a rule, treat “Public Endpoint” usage as **prohibited** unless explicitly reviewed and approved by security architecture.
  * *Service Endpoints (AWS Backbone):* *(Not applicable for AWS in the same way as Azure Service Endpoints.)* AWS services rely on either public endpoints or PrivateLink for connectivity. There is no direct equivalent to Azure Service Endpoints. Traffic from an AWS resource in a VPC to a Kinesis public endpoint will **traverse the internet** unless a PrivateLink endpoint is configured. Therefore, do not rely on any implicit network security – use PrivateLink as described below.
  * *Private Endpoint (AWS PrivateLink):* **Required for sensitive data and highly recommended for all uses.** AWS PrivateLink allows you to create **VPC Interface Endpoints** for Kinesis Streams (`com.amazonaws.<region>.kinesis-streams`) and Kinesis Firehose (`com.amazonaws.<region>.firehose`). These endpoints create a private IP within your VPC that connects to the Kinesis service internally. By using the private endpoint, all network traffic between your producers/consumers in the VPC and Kinesis stays on the AWS private network – no Internet Gateway or NAT Gateway is needed. For any Kinesis stream or Firehose that will carry Restricted data, PrivateLink **must be used** (i.e., the subnet security policy should not allow internet egress; only the endpoint). For Internal/Confidential data, PrivateLink is highly recommended to reduce exposure and latency. When setting up the endpoint, update your SDK/client configuration to use the **endpoint-specific URL** (AWS SDKs typically do this automatically when an endpoint is present and DNS is enabled for the default domain). Also, consider enabling **endpoint policies** to further restrict what can be done via the endpoint (for example, an endpoint policy could allow writes to Kinesis but not reads, if you only have producers in that VPC). Note that if you have hybrid environments (on-premises to AWS), PrivateLink endpoints can be accessed over VPN/Direct Connect as well, keeping that traffic off the public internet.
  * *Networking & Security Architecture:* *TBD* – (In progress. This section will include diagrams or patterns showing how Kinesis integrates into PepsiCo’s network architecture, e.g., producers in a VPC sending to Kinesis via PrivateLink, data flowing to S3, etc., along with security groups, NACLs, or other network considerations specific to our enterprise setup.)

* **AWS Service Limitations:**

  * *Shards and Throughput Scaling:* Each Kinesis Data Stream shard has fixed throughput as noted (1 MB/s writes, 2 MB/s reads, 1,000 records/s per shard). While you can add virtually unlimited shards, there are some practical limits: AWS accounts have soft limits on the number of shards per region (default is 500 shards per stream soft limit, and a total of 10,000 shards per account by default, which can be increased). Managing very large numbers of shards can complicate consumer scaling and increase costs. Kinesis Data Streams on-demand mode starts with an initial throughput capacity (which can handle up to 4 MB/s write and 8 MB/s read per stream, by default) and then auto-scales – but there is a **scaling latency**; sudden traffic bursts might be throttled for a short time while new capacity is provisioned. AWS mentions that if you anticipate sudden large increases, you should preemptively request a quota increase or use provisioned shards to ensure capacity. Also, **enhanced fan-out (EFO)** for consumers has limits: by default, 20 EFO consumer applications can be registered per stream. If you need more than 20 parallel consuming applications with their own 2 MB/s throughput, you must request a limit increase.
  * *Ordering Guarantees:* Kinesis Data Streams provides record ordering **per shard** (and per partition key). This means if strict global ordering is required, all events must be funneled through one shard, which can limit throughput. Most use cases partition by some key (e.g., by customer or by device) and accept that records with different keys may be processed out of order relative to each other. Firehose does not guarantee ordering of data arriving at the destination in the exact order it was received – especially if transformations or retries occur, data might be delivered slightly out of original sequence. If ordering is critical, Kinesis Data Streams with a single shard (or carefully managed partition keys) should be used, and the consuming application should preserve order in its processing.
  * *Lack of Built-in Multi-Region Replication:* As discussed, there is no native feature to automatically replicate a Kinesis stream to another region or to fail over. This is a limitation compared to some messaging systems. You must design that logic yourself if needed (e.g., using DynamoDB global tables is an analogy for database streams, but no such feature for Kinesis). Also, Kinesis (unlike SQS or DynamoDB Streams) cannot be paused – it’s continuously ingesting; you can’t “pause” producers besides stopping them, so plan maintenance windows accordingly (e.g., if you need to deploy an update to a consumer, data will keep coming – use the retention buffer to your advantage).
  * *Firehose Delivery Constraints:* Firehose has some specific limits: the **maximum record size** that Firehose will accept in a PutRecord is 1,000 KB (before Base64 encoding). Any data larger than that must be chunked by the producer. Firehose buffers data up to 5 MB or 5 minutes by default, and you can configure that between 1 MB/60 seconds up to 128 MB/15 minutes (for certain destinations) – so there’s an upper bound on how real-time Firehose can be (minimum \~1 minute latency). If you need sub-second latency per message, Kinesis Data Streams with direct processing might be necessary. Another limitation: when Firehose writes to Redshift, as noted, the Redshift cluster must be publicly accessible and Firehose performs the load via intermediate S3 and Redshift COPY. This means the *unit of delivery* to Redshift is batches (from S3 files) rather than streaming row-by-row; there’s an inherent latency (could be a few minutes) and it may not fit use cases requiring immediate visibility in Redshift. Firehose also cannot automatically retry failed records indefinitely – it has a retry duration setting (default 300 seconds, max 7,200 seconds for Redshift/OpenSearch), after which failed writes are dropped to S3. That’s a limitation in scenarios where the destination might be down for longer; manual intervention would be needed to reload those dropped records from the S3 backup.
  * *Record Processing and Consumer Limits:* A single Lambda function reading from Kinesis is limited by the Lambda concurrency and has a max batch size of 10,000 records or 6 MB per invocation. If the incoming stream is very fast, Lambda may scale up but there are account limits on concurrent executions. In high-throughput scenarios, using KCL on EC2/ECS or Managed Flink might be more suitable. KCL itself has a checkpointing delay – by default it checkpoints periodically, so in failure scenarios some records may be reprocessed (as duplicates). All these are known limitations/trade-offs of using serverless or managed consumers. Ensure you account for these in design (e.g., use smaller Lambda batch sizes if you need faster checkpointing, etc.).
  * *Cost Considerations:* (Not exactly a limitation, but important) Kinesis costs can scale with throughput; each shard has an hourly cost and PUT payloads are charged per million records. If you send many small records, it can be less cost-efficient than batching them. Firehose charges by volume of data ingested and data delivered, plus any format conversion. Also, long-term retention in Kinesis (beyond 7 days) incurs an increasing cost per GB-month stored. It is not meant to be a cheap long-term store – if you find yourself using 30+ days of retention, consider exporting data to S3 and using other services for historical analysis.

* **SKU Features:**

  * **Amazon Kinesis Data Streams:** A real-time streaming service with fine-grained scaling via shards. Key features:

    * Low latency streaming data ingestion (typically <1 second from producer to consumer), designed for **millions of events per second** throughput when scaled out.
    * **Manual or On-Demand Capacity:** Offers a provisioned throughput mode (you manage shard count) and an on-demand mode (auto scales shards). On-demand simplifies operations but might have initial throttling on sudden bursts.
    * **Multiple Consumers:** Supports many consumers reading in parallel, with optional Enhanced Fan-Out for high-speed parallel reads (each EFO consumer gets its own 2 MB/sec pipe per shard, avoiding contention). This suits fan-out scenarios like feeding multiple downstream systems concurrently.
    * **Ordering & Replay:** Ensures in-order delivery of records per shard/partition key. Data is stored durably and can be replayed within the retention window. Checkpointing via KCL or timestamp-based iterator allows “rewind” to past records if needed.
    * **Integration:** Can directly trigger AWS Lambda, integrate with AWS Analytics (Flink, etc.), and emit to S3, Redshift, EMR, or other services via custom code. Often used to build custom pipelines and streaming applications where flexibility is needed in processing logic.
    * **Security:** Supports KMS encryption at rest, and fine-grained IAM control on stream access. VPC PrivateLink endpoints available for secure access.
    * **Usage Pattern:** Ideal for building **event-driven applications, custom real-time analytics, and scenarios requiring multiple different consumers or complex processing**. Provides maximum control – you can plug in any processing logic – but requires managing the consuming applications or functions.
  * **Amazon Kinesis Data Firehose:** A fully managed **streaming delivery service**. Key features:

    * **Automatic Scaling & Delivery:** There are no shards or servers to manage. Firehose will transparently scale to handle the data throughput (within service limits) and continuously deliver to the target. It **automatically handles retries** on failures and will buffer data to ensure efficient transfers. Typically, it delivers data to the destination within 60 seconds of ingestion (can be faster or slightly slower depending on buffer settings).
    * **Destination Support:** Natively supports delivering to Amazon S3 (data lake), Amazon Redshift (data warehouse), Amazon OpenSearch Service (for search/analytics), Splunk, and custom HTTP endpoints among others. For Amazon Redshift, it manages the S3 staging and COPY command internally. This makes it very easy to set up pipelines to these storages without writing code.
    * **Data Transformation:** You can optionally enable a **Lambda function** on a Firehose delivery stream to transform incoming records. This is often used to convert formats (e.g., CSV to JSON, JSON to Parquet), filter out unwanted records, or enrich data before writing to the destination. Firehose will automatically handle scaling the Lambda invocations based on stream volume and will deliver failed transformation records to S3 for analysis. Additionally, for S3 destinations, Firehose can convert JSON to Parquet/ORC without any code, which is useful for columnar storage in data lakes.
    * **Data Encryption & Compression:** Supports compressing data before delivery (GZIP, ZIP, Snappy for S3/Redshift) to reduce storage and increase throughput. Also allows encryption of data at rest in S3 using KMS. When delivering to OpenSearch or Splunk, it can back up all or failed records to S3 concurrently, providing a safety net.
    * **Use Case Fit:** Ideal for **simpler ETL pipelines** where you just need to get data from point A to B (with optional light processing) reliably. It’s a good choice when you don't need custom stream processing logic for each record, but rather just want the data landed into a storage or analytics service. It trades off some flexibility (cannot branch to multiple destinations from one stream, and custom processing is limited to one Lambda function) in favor of ease of use and lower maintenance.
    * **Costs:** Firehose pricing is based on volume of data ingested and delivered, and there may be charges for format conversion. Because it manages resources, there is no explicit “throughput unit” to pay for as with Kinesis shards, but costs can accrue with high data volume, so compression and batching are beneficial.

* **Related Service:** *TBD* – (Possible related services to mention could include Amazon Managed Streaming for Apache Kafka (MSK) as an alternative streaming platform, AWS IoT Core for IoT data ingestion, or Amazon EventBridge for event-driven patterns – this section will be updated with guidance on when to use Kinesis vs other messaging/stream services in AWS.)

## Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * Information Security Specifications – *Amazon Kinesis (Data Streams & Firehose)*. (Refer to internal InfoSec baseline documents for Amazon Kinesis services which outline required security controls such as encryption, network isolation, monitoring, data classification handling, etc., in accordance with PepsiCo policies. This includes ensuring compliance with data privacy regulations for any personal data flowing through streams, and validating that the Kinesis implementation meets all corporate InfoSec requirements.)

## Ownership and Version Control

* **Service Architect:** Dariusz Korzun – *Cloud Service Architecture, PepsiCo* ([<EMAIL>](mailto:<EMAIL>))
* **Version Control:**

  * *v.1:* 12 Aug 2025 – Initial document prepared (Dariusz Korzun)
