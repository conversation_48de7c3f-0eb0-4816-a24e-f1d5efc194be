---
weight: 12
title: "Event Bridge"
date: 2025-08-01
tags: []
summary: ""
---

# Amazon EventBridge

## Cloud Service Classification

- **Category of the Service:** Application Integration (Event Bus)  
- **Cloud Provider:** AWS

## Cloud Service Overview

- **Name:** Amazon EventBridge  
- **Description:** Amazon EventBridge is a serverless event bus that ingests, filters, and routes events from various sources to multiple targets, enabling scalable and event-driven architectures.
- **SKU Approval Status:**  
  - **PepsiCo Approved:**  
    • Amazon EventBridge (single-tier service – fully approved for use across all environments)  
  - **Not Approved:**  
    • None (all standard features are allowed)  
- **Allowed PepsiCo Data Classification:**  
  - Public  
  - Internal  
  - Confidential  
  - Restricted  

## Service Lifecycle

- **Release Date:** July 11, 2019  
- **Planned Decommission Date:** No announced decommissioning plans.  
- **Decommission Date:** Not applicable at this time.

## Usage Guidelines

- **Features**  
  - **Fully Managed Event Bus:** EventBridge is a fully managed, serverless event bus that automatically scales and eliminates infrastructure management.
  - **Integration with AWS & SaaS:** It natively integrates with 100+ AWS services and various SaaS providers, enabling event routing between custom applications, AWS, and authorized SaaS partners.
  - **Event Filtering & Routing:** Rules allow content-based filtering and routing of events to multiple targets, with optional transformation of payloads before delivery.
  - **EventBridge Pipes:** Pipes enable simple, code-free integrations by linking a single event source to a single target with optional filtering, transformation, and enrichment.
  - **EventBridge Scheduler:** Scheduler lets you trigger events or AWS actions on custom schedules, supporting cron expressions, time windows, retries, and large-scale task orchestration.
  - **Archive & Replay:** You can archive events for retention, auditing, or later replay to reprocess or debug as needed. 
  - **Schema Registry:** EventBridge’s Schema Registry auto-discovers event structures, stores them, and provides code bindings to help teams use events as typed objects. 
  - **API Destinations:** API Destinations enable secure, direct invocation of external HTTP APIs as event targets, supporting authentication and rate limiting without extra infrastructure.

- **Sample Use Cases**  
  - **Microservice Decoupling:** UEventBridge enables loosely-coupled architectures by allowing microservices to communicate asynchronously through published events, so downstream services can independently react to events like an order placement. 
  - **SaaS Application Triggers:** You can trigger custom AWS workflows using events from external SaaS applications, seamlessly extending SaaS functionality within your cloud environment.  
  - **Automated Operational Responses:** EventBridge allows automatic responses to AWS and system events, such as triggering remediation functions in response to specific API calls or resource changes for real-time compliance.  
  - **Scheduled Tasks (Cron Jobs):** EventBridge Scheduler reliably triggers scheduled tasks or AWS actions at defined times, eliminating the need for separate cron or scheduling servers.  
  - **Cross-Account Event Distribution:** EventBridge can route and forward events securely across multiple AWS accounts, enabling centralized event processing and audit logging in complex environments.

- **Limitations**  
  - **Event Size Limit:** Each event can be up to 256 KB; larger payloads should be placed in S3 with only a reference sent via EventBridge.  
  - **At-Least-Once Delivery:** EventBridge guarantees at-least-once delivery, so consumers must handle possible duplicates and out-of-order events
  - **Throughput Throttling:** API operations have region-based throughput quotas; exceeding them delays (but does not drop) event deliveries.
  - **Targets per Rule:** A rule can deliver an event to up to 5 targets; more than 5 requires multiple rules or an intermediary like SNS. 
  - **Regional Service (Multi-Region Limitations):** EventBridge is regional, so events do not cross AWS regions unless you configure global endpoints for active-passive failover.
  - **Ephemerality of Events:** Events are not retained unless archived; once delivered (or dropped after retries), they are removed from the service unless Archive/Replays or Dead Letter Queues are set up.

- **Additional Guardrails**  
  *TBD*

- **Used By**  
  *TBD list & links to some onboardings.*

- **EA Declaration**  
  *NOT a declared standard.*

## Technical Guidelines

- **Best Practices**  
  - **Use Specific Event Filters:** Define precise event patterns to prevent unintended matches or feedback loops in your EventBridge rules.
  - **Idempotent Consumers:** Ensure all event consumers handle duplicate events safely by using idempotency or deduplication logic. 
  - **Leverage Dead-Letter Queues:** Attach a Dead-Letter Queue to each rule target to capture and diagnose events that fail after all retry attempts.  
  - **Schema Management:** Use the Schema Registry for consistent, discoverable, and shareable event schemas to promote smoother integrations.
  - **Monitor and Audit:** Continuously monitor delivery metrics and audit changes to rules and event buses using CloudWatch and CloudTrail.

- **High Availability & Disaster Recovery**  
  - **In-Region Redundancy:** EventBridge is highly available within an AWS region by default through deployment across multiple Availability Zones, with no user action required.  
  - **Multi-Region Failover (Global Endpoints):** Use EventBridge Global Endpoints to automatically fail over event routing between primary and secondary regions during a regional outage based on health checks.
  - **Event Replication for DR:** Enable event replication with global endpoints to asynchronously copy events to a secondary region and minimize data loss during failover, recommended for critical workloads. 
  - **Failback and Testing:** Design consumer applications to handle events from either region and regularly test the failover and failback processes to ensure seamless disaster recovery.

- **Backup & Recovery**  
  - **Event Archiving:** Use EventBridge Archive to retain encrypted event history for a configurable period, aiding in recovery and compliance needs. 
  - **Event Replay:** Reprocess past events by replaying them from an archive to the event bus or specific rules, ensuring no data is lost if consumers miss events. 
  - **Testing Recovery:** Routinely test archiving, replay, and DLQ recovery processes in a development environment, and document recovery procedures for operational readiness.
  - **Dead-Letter Queue Re-drive:** FSet up automated or manual workflows to re-drive failed events from a DLQ back into EventBridge or to the intended target after resolving issues.

- **Access Control & Security Configuration**  
  - **Least Privilege IAM:** Grant only the minimum EventBridge permissions needed to each IAM role or user, scoping access to specific actions and resources using condition keys, ARNs, or tags.
  - **Event Bus Policies (Cross-Account Security):** Define explicit resource policies to allow only trusted external AWS accounts or service principals to send events to your event bus, denying broad access by default.  
  - **Sensitive Data Handling:** Encrypt sensitive event data in archives with a customer-managed KMS key if necessary, and ensure proper key policies and TLS in-transit protection are in place.  
  - **Logging and Auditing:** Enable CloudTrail for EventBridge and monitor resource changes, using AWS Config or alerts to detect overly permissive policies and logging event activity in CloudWatch where needed.  
  - **Network Security for Producers/Consumers:** Use VPC Interface Endpoints for private event traffic and control both VPC and Lambda function permissions to limit exposure to only what’s necessary.
  - **Partner and External Source Security:** Activate SaaS event sources only from trusted partners and filter or drop any irrelevant events using EventBridge rules.
  - **Incident Response:** Create a plan to quickly address unauthorized or misrouted events, including updating policies, disabling rules, or isolating consumers, with alarms to detect unusual event patterns.

- **Network Connectivity Options**  
  - **Public Endpoint:** Avoid using EventBridge’s public HTTPS endpoint for sensitive data, and if used (such as in development), strictly control outbound access with firewalls and restrict to AWS IP ranges.
  - **VPC Interface Endpoint (AWS PrivateLink):** **Preferred method for connectivity.** Prefer using EventBridge VPC Interface Endpoints to send events privately within your VPC, keeping all traffic internal for improved security—especially for confidential or restricted data.
    - *Note:* Also use VPC endpoints for in-VPC targets (like SQS queues or Lambda functions) to maintain private, end-to-end event delivery.
  - **Cross-Service Connectivity:** Leverage VPC endpoints for AWS service targets (like SQS, SNS, or Step Functions) where possible, and for other services, ensure consumers have no unnecessary internet access.  
  - **No Customer-Managed Network Config for Event Routing:** You are not required to configure networking for AWS-internal event routing between services, but must secure connectivity for your own producer and consumer applications.
  - **Regional Endpoints:**  Always use the EventBridge endpoint for the region where your event bus resides, and configure endpoints dynamically to enable seamless failover or region changes.

- **Networking & Security Architecture**  
  *TBD*

- **AWS Service Limitations**  
  - **Service Quotas:** EventBridge limits resource counts and actions per account (e.g., 100 buses, 300 rules per bus, 5 targets per rule), so design your architecture to stay within these quotas or request increases where adjustable.
  - **API Throughput Limits:** EventBridge enforces API rate limits (e.g., 10,000 PutEvents/sec in large regions) and will throttle requests beyond these limits, so partition workloads or seek quota increases for extremely high throughput needs.
  - **Event Payload Constraints:** Each event payload is limited to 256 KB, with additional limits on rule pattern size and JSON nesting, so keep event data concise and split logic if necessary.  
  - **No Guaranteed Ordering:** EventBridge does not guarantee event order, so enforce ordering at the application level or use an alternative service if strict sequencing is required.
  - **Event Delivery Semantics:** EventBridge delivers events on a best-effort basis with retries, but events may be dropped without a DLQ, so monitor for lost events if zero-loss is critical.
  - **Comparisons to Other Services:** EventBridge is designed for flexible event routing, not for ordered delivery, persistent storage, or high-throughput streaming—use Kinesis or SQS FIFO for those scenarios. 
  - **Costs and Limits:** *(Pricing details are omitted as per guidelines, but be aware that EventBridge pricing is based on number of events published, archive storage, etc. There are no fixed tier costs; however, extremely high volumes of events can incur significant costs, so use filters to avoid unnecessary events.)*  
  *(No service-specific pricing or SKUs to list; EventBridge features are available universally.)*

- **SKU Features:** *Not applicable.* Amazon EventBridge is a single-tier service – there are no basic or premium SKUs. All functionality (event buses, rules, pipes, scheduler, archive, etc.) is available by default, with usage governed by quotas and pricing rather than feature tiers. 

- **Related Service:** *TBD*

- **Alternatives**  
  - **Amazon SNS (Simple Notification Service):** SNS enables high-throughput, publish/subscribe messaging to multiple subscribers with basic filtering, and is ideal for simple fan-out scenarios or when direct notifications (SMS, email, push) are required. 
  - **Amazon SQS (Simple Queue Service):** SQS offers reliable, at-least-once delivery with optional FIFO ordering for decoupled, point-to-point messaging, making it a strong choice for durable event buffering where only one consumer processes each message.
  - **AWS Step Functions:** Step Functions orchestrate multi-step workflows with state and error handling, making them suitable for managing defined sequences of tasks rather than simple asynchronous event handling.  
  - **Apache Kafka / Amazon MSK:** Kafka (via MSK) is best for high-volume event streaming that demands strict ordering, log retention, and decoupled replayable consumption, though it involves more operational overhead than EventBridge.  
  *(Each alternative has its niche – the choice depends on requirements for filtering, delivery guarantees, throughput, integration, and complexity.)*

## Compliance and Security Guidelines

- **Security Baseline InfoSec:** *Information Security Specifications – Amazon EventBridge* (Follow PepsiCo’s cloud security baseline requirements for event integration services, including data classification handling, encryption standards, and access control. EventBridge should be implemented in accordance with internal InfoSec guidelines, ensuring that all events and configurations meet the necessary compliance criteria. Specific reference: PepsiCo Information Security Baseline for EventBridge – TBD).* 

*(Amazon EventBridge is SOC, ISO, and HIPAA compliant as per AWS compliance programs; any usage of EventBridge with Restricted data should be reviewed for compliance with PCI/PII regulations as applicable.)*

## Ownership and Version Control

- **Service Architect:** Rakesh Ponnada – *<EMAIL>*  
- **Version Control:**  
  v.1 – 29 Jul 2025 (Dariusz Korzun)
  v.2 - 08 Aug 2025 (Rakesh Ponnada)