---
weight: 16
title: "DynamoDB"
date: 2025-08-01
tags: ["aws", "database", "nosql", "dynamodb"]
summary: "Amazon DynamoDB"
---

# Amazon DynamoDB

## Cloud Service Classification

**Category:** Database
**Cloud Provider:** AWS
**Website**: [Technical documentation](https://docs.aws.amazon.com/dynamodb/)

## Cloud Service Overview

**Name:** Amazon DynamoDB
**Description:** Fully managed NoSQL database for key-value and document data. Serverless, auto-scales to millions of requests/sec, single-digit ms latency, high availability (multi-AZ), ACID transactions, fine-grained access control, encryption at rest.

### SKU Approval Status
  - PepsiCo Approved
    - Standard Table Class 
    - Standard-IA Table Class 
    - On-Demand & Provisioned capacity modes
  - Not Approved: None (all standard offerings allowed)

### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

### Service Lifecycle

  - Release Date: January 18, 2012
  - Planned Decommission: None announced

## Usage Guidelines

### Features
  - Key-value & document store, flexible schema
  - Fully managed, serverless, auto-scaling
  - Single-digit ms latency, SSD-backed
  - Multi-AZ high availability (99.99% SLA)
  - Global Tables for multi-region, multi-active
  - ACID transactions (up to 100 items/4MB)
  - Streams for change data capture
  - Secondary indexes (GSIs/LSIs)
  - Fine-grained IAM access control
  - Encryption at rest (KMS), in transit (HTTPS)

### Sample Use Cases
  - Serverless app backends, session/config stores
  - E-commerce: carts, catalogs, inventory, orders
  - Gaming: player profiles, leaderboards, game state
  - IoT data ingestion, time-series/log storage

### Limitations
  - No SQL joins, complex queries, or aggregates
  - Eventual consistency (default reads); strong consistency only in-region
  - Item size limit: 400 KB; query/scan limit: 1 MB per call
  - Max 5 LSIs, 20 GSIs per table
  - Throughput throttling if hot partition or over limits
  - Global Tables: eventual consistency, last-writer-wins
  - No triggers/stored procedures; limited server-side logic

### Additional Guardrails
  - TBD

### Used By
  - TBD

### EA Declaration
  - NOT a declared standard

## Technical Guidelines

### Best Practices

  - Choose the Right Capacity Mode
    Select the appropriate throughput capacity mode based on your workload pattern. DynamoDB supports **on-demand capacity** (pay-per-request) and **provisioned capacity** (pre-reserve throughput). Use on-demand mode for unpredictable or bursty workloads so that DynamoDB automatically allocates capacity as needed (with no throttling as long as traffic doesn’t exceed previous peaks). For steady or predictable workloads, use provisioned capacity and enable auto-scaling – this can be more cost-effective when you can forecast capacity, as you pay a lower hourly rate for reserved throughput. It’s common to start new applications in on-demand mode and then switch to provisioned with auto-scaling once usage patterns are understood.
  - Design Efficient Partition Keys
    Achieving uniform traffic distribution across partition keys is critical for performance. **Avoid “hot keys”** by using high-cardinality attributes or adding randomness/salting to the partition key for very skewed data sets. DynamoDB partitions each table’s data and allocates throughput per partition; a single partition can handle up to \~3000 RCUs or 1000 WCUs per second before needing to split. If one key receives a disproportionate amount of traffic, it can cause that partition to throttle even if overall usage is below table limits. To prevent this, **design your schema for uniform activity across all partitions**. For example, instead of using a single partition key for all timestamps (which would concentrate all writes on one partition), include a device ID or a hashed prefix as part of the key to spread writes across many partitions. DynamoDB’s adaptive capacity will help by automatically rebalancing hot partitions, but it’s best to minimize hot spots by design.
  - Use Caching for Read-Heavy Workloads
    To reduce read latency and cost, consider introducing a caching layer in front of DynamoDB for heavily accessed data. AWS offers **DynamoDB Accelerator (DAX)**, a fully managed in-memory cache that is API-compatible with DynamoDB, which can speed up repeated eventually-consistent reads by an order of magnitude (microsecond latency) and reduce the consumption of read capacity units. Alternatively, you can use Amazon ElastiCache or application-tier caches. Ensure you implement cache invalidation strategies (e.g., TTL or cache-aside logic) so that stale data is evicted when underlying data changes. Caching is especially effective for read-mostly access patterns (such as user profile lookups, product catalog data, etc.) and can also shield DynamoDB from request bursts.
  - Prefer Query over Scan
    Structure your data model to use **Query** operations with specific keys rather than Scans. Queries in DynamoDB narrowly target a partition (optionally with a sort key range) and are far more efficient and cost-effective than scanning the entire table or index. A Query only reads the items that match the given keys (plus any optional filter on the sort key), whereas a Scan reads *every* item in the table (or index) to find matches, consuming read capacity for all data it passes through. By modeling your access patterns upfront (often using **single-table design** techniques and secondary indexes), you can almost always avoid the need for scans in production. If a full data scan is absolutely necessary (for example, ad-hoc analytics or data export), consider using **AWS Data Pipeline** or DynamoDB’s **S3 export** feature rather than having your application do a scan, to minimize impact on production traffic.
  - Item Size and Attribute Storage
    Keep your items as lean as possible. Because of the 400 KB item size limit and because *larger items consume more throughput*, it’s a best practice to store large blobs or infrequently-used large attributes outside of DynamoDB. For instance, if you have a user profile with a photo or a big JSON blob of details, store the photo in S3 or the JSON in S3 (or compress it) and just keep a reference in DynamoDB. Smaller items not only avoid hitting size limits but also mean more items can be read per API call (since DynamoDB’s read capacity cost is based on 4 KB units). If using **Local Secondary Indexes**, be mindful that they include copy of certain attributes – projecting unnecessary large attributes into an LSI will amplify storage and throughput usage. Only project what's needed into indexes to keep them efficient.
  - Use TTL for Data Lifecycle Management 
    Take advantage of DynamoDB’s **Time to Live (TTL)** feature to automatically expire items that are no longer needed. TTL enables you to set an “expiration timestamp” attribute on items; when the timestamp passes, DynamoDB will asynchronously delete the item within the next day or so. This is very useful for use cases like session records, temporary data, or expired events. For example, if you store web sessions or JWT tokens in DynamoDB, set a TTL so that they get removed automatically after, say, 24 hours. TTL deletions do **not consume write throughput** and they propagate to DynamoDB Streams (with a special marker to indicate system deletion) just like normal deletes. Using TTL keeps tables from eternally growing with old data and saves on storage costs, without you having to build a cron job or manual purge process.
  - Enable Continuous Backups (PITR)
    For critical tables, enable **Point-in-Time Recovery** which provides continuous automatic backups of your DynamoDB table for the last 35 days. This allows you to restore the table to **any second** in that window, which is invaluable if you need to recover from accidental deletes or logical data corruption. Enabling PITR has minimal performance overhead and no effect on ongoing operations. It’s a best practice to enable PITR especially for production data stores, as it acts as an “undo” safety net for operational errors.
  - Test and Monitor with CloudWatch
    Use Amazon CloudWatch to monitor key metrics for your DynamoDB tables. Important metrics include **ConsumedReadCapacityUnits** and **ConsumedWriteCapacityUnits** (to watch for throttling or capacity exhaustion), **ProvisionedCapacity** vs usage (if in provisioned mode), **SuccessfulRequests** and **ThrottledRequests**, and **UserErrors**. Set up CloudWatch Alarms to alert on abnormal conditions (e.g., sudden surge in throttled operations or error rates). Additionally, DynamoDB’s integration with CloudWatch **Contributor Insights** can help identify the most frequently accessed keys or patterns that are driving traffic. This is useful to detect hot keys or imbalanced workloads that might need schema adjustments. Regularly review these metrics and adjust your provisioning or data model accordingly.
  - Security Best Practices 
    Follow the principle of least privilege in IAM policies – for example, if an application or microservice only needs to read from a table, grant it read-only access to that specific table (and perhaps even specific items or attributes via conditions), rather than broad full access. Enable **AWS CloudTrail** logging for DynamoDB to audit all API calls; CloudTrail records who accessed which table, what operations were performed, and when. This audit trail is essential for security reviews and incident investigations. If you have sensitive or regulated data, consider using customer-managed CMK encryption on the table for greater control (e.g., the ability to revoke keys). Also, if integrating with other AWS services (like Lambda, API Gateway, etc.), prefer to use **AWS IAM Roles** and **temporary credentials** (via AWS STS or Cognito) over embedding AWS access keys in config files. This reduces the risk of credential leakage and makes rotation automatic.

### High Availability & Disaster Recovery

  - Built-in Regional Resilience
    DynamoDB is intrinsically designed for high availability within a region. All data is replicated across at least **three Availability Zones** in an AWS region, which means the service can tolerate the failure of an entire AZ without data loss. The multi-AZ design is synchronous and transparent – applications do not need to implement any failover logic for AZ disruptions; DynamoDB will continue serving requests as long as at least one AZ is alive. This gives a baseline of \~99.99% availability SLA for DynamoDB in a single region.
  - Global Tables for Multi-Region DR
    For disaster recovery across regions or active-active global usage, use **DynamoDB Global  Tables**. Global Tables automatically replicate your DynamoDB tables across multiple AWS regions of your choice. In a global table configuration, each region has a replica table that applications can read from and write to locally. The replicas **synchronize changes with each other in near real-time**, and the service guarantees a 99.999% availability when spread across multiple regions. In practice, if one region goes down or becomes unreachable, your application can fail over to using the DynamoDB replica in another region, with the data already present (no restore needed). Because global tables are multi-master (multi-active), there is no manual failover process or promotion of a secondary – the application just switches to another regional endpoint. This dramatically lowers Recovery Time Objective (RTO) since the data is already live in the DR region.
  - Recovery Point Objective (RPO)
    With global tables, RPO can be nearly zero, since all writes are replicated to other regions usually within under a second. Without global tables, your RPO depends on backups. If you rely on daily backups or on-demand backups, RPO could be hours (or up to a day) of data loss in a region-wide failure. Consider the business requirements: for mission-critical systems that cannot afford data loss or downtime, global tables are recommended despite the higher cost (additional storage/throughput in each region).
  - Regional Failover Strategy (without Global Tables)
    If not using global tables, plan a DR strategy using backups. You should regularly take **on-demand backups** of the table (or enable PITR) and ensure those backups are copied to a secondary region (AWS Backup can automate cross-region backup copies). In a disaster scenario where a whole AWS region is unavailable, you would **restore** the DynamoDB table from the latest backup into another region. Note that restoring a large table from backup can take time (potentially minutes to hours, depending on size), so the RTO will be longer compared to global tables. To mitigate this, some organizations keep a **warm standby**: e.g., a smaller copy of the table in another region that is periodically updated (via DynamoDB Streams + Lambda or AWS Database Migration Service). This standby can be scaled up if needed. Such approaches are complex, so leveraging global tables is the simpler AWS-native solution for multi-region resilience.
  - Backup and Restore Testing
    Whichever DR approach is used, regularly test your **restore process**. Ensure that you can successfully restore DynamoDB backups in the target region and that your application can reconnect to the new table (and reconfigure any dependencies such as table names, environment variables, etc.). If using global tables, test that your routing (DNS or application logic) can switch to the secondary region’s endpoints seamlessly. Also, be aware that when a failed primary region comes back, with global tables you may need to re-enable replication or resolve any divergence that occurred during the outage. AWS Fault Injection Simulator can be used in conjunction with global tables to rehearse regional failover scenarios.

### Backup & Recovery

  - Point-in-Time Recovery (Continuous Backup)
    DynamoDB offers an optional continuous backup feature called **Point-in-Time Recovery (PITR)**. When enabled, DynamoDB continuously backs up your table data in the background. You can restore the table to **any exact point in time within the last 35 days**. Restoration is done to a new table (you specify the timestamp to restore to), and you can then swap your application to use that restored table if needed. PITR is useful for recovering from accidental deletions or logical errors – for example, if a bad code deployment corrupted many items, you could rewind the table to the moment before the deployment. Enabling PITR does not impact performance or capacity usage on the table. It’s a best practice to enable this for critical tables, as it provides fine-grained recovery points without having to take manual backups frequently.
  - On-Demand Backups
    You can take **on-demand full backups** of DynamoDB tables at any time (for long-term retention, archives, or before major application changes). These backups are consistent and do not affect table performance or availability when taken. A backup can be restored to a new table in any AWS region (you can choose a different region at restore time to serve as a cross-region backup). On-demand backups can be automated via AWS CLI or SDK, and AWS Backup service can manage DynamoDB backups as part of a centralized backup plan. For example, you can schedule daily backups and have AWS Backup automatically copy them to another region or account for disaster recovery compliance. Backup files are stored internally by AWS (not in your S3 unless you export), and you incur charges for the storage. It’s important to note that **restoring** a backup will create a new table with a new endpoint – you have to redirect your application to that table.
  - Export/Import to S3
    DynamoDB has a feature to **export table data to Amazon S3** (with no impact on the live table) and also to **import** data from S3 back into a new table. The export uses point-in-time snapshots (you can export data as of a given time, which works with PITR). Exporting to S3 is useful for cold storage of data and for analytical use cases – for instance, you might export monthly and then query the data with Athena or load into Redshift. The export process is fully managed and doesn’t consume your table’s RCU/WCUs. The ability to import from S3 (launched more recently) means you can perform transformations or data scrubbing on the data in S3 (or even load data from other sources) and then bulk-load it into a DynamoDB table. This can also serve in recovery scenarios (e.g., export from a backup and re-import with certain items filtered out). Keep in mind that the imported table will be new – it’s not a merge into an existing table.
  - Recovery Considerations
    After a restore (whether point-in-time or from backup), the new table will have a different ARN, and if your application was using the original table, you will likely either need to update configuration to point to the restored table or copy the data from the restored table back into the original. Also, secondary indexes on the table are restored with their configurations. Be aware that global tables are not automatically re-established on restore; if you restore one replica, you’d have to set up new global table relationships between restored replicas. It’s recommended to integrate backup and restore testing in your operations runbook for DynamoDB.
  - Cleanup and TTL
    As part of backup and recovery strategy, consider enabling Time to Live on items that should expire (as mentioned in Best Practices) so that stale data is automatically removed. This reduces the amount of data that needs backing up and potentially speeds up recovery (less data to restore). Also, after a recovery or migration, double-check TTL settings and any Streams or Lambda triggers on the new table, as those might need reconfiguration (for example, TTL metadata and stream ARNs do not carry over through a plain data import).

### Access Control & Security Configuration

  - Identity and Access Management (IAM)
    Access to DynamoDB should be controlled exclusively through AWS IAM. There are no user accounts or passwords in DynamoDB itself – instead, IAM roles and users are granted permissions via policies. Use fine-grained IAM policies to restrict access to tables, and even to specific data within tables. For example, you can use condition keys like `dynamodb:LeadingKeys` to allow a user or service to only access items where the partition key matches their user ID. DynamoDB also supports **resource-level permissions**, so you can give one team access to **TableA** and another team access to **TableB** (as opposed to older AWS services that only allowed all-or-nothing access). Leverage AWS Managed Policies for common access patterns (such as AmazonDynamoDBFullAccess for admins, AmazonDynamoDBReadOnlyAccess for read-only scenarios) as starting points, and then refine with least privilege in mind.
  - Attribute-Level Security
    In some cases, you might need to restrict access not just by item, but by attribute (column). DynamoDB integrates with IAM **Attribute-Based Access Control (ABAC)** using item attributes and tags. You can tag tables or even data items (via item collection metrics or other context) and then use IAM policies that allow or deny operations based on those tags. While DynamoDB does not natively have a column-level permission setting, you can achieve a similar effect by controlling in the application which attributes are returned (using ProjectionExpression) and using IAM conditions to limit certain attributes from being written or read. For most use cases, however, segregating data by table or partition key and using key-based conditions is simpler.
  - Encryption & Keys
    DynamoDB encrypts all customer data at rest by default using AWS-managed encryption keys in KMS. For many, this default encryption is sufficient (it meets compliance standards like PCI DSS, etc.). If your organization has stricter requirements, you can set the DynamoDB table to use a **customer-managed CMK** in AWS KMS. This allows you to control key rotation, access (via KMS key policies), and even revoke access to the data by disabling or deleting the key. Keep in mind that if you manage your own key and it’s not accessible (e.g., disabled), DynamoDB will not be able to decrypt data for that table, effectively causing outages for that table – so manage CMKs carefully. All DynamoDB network endpoints require SSL/TLS, so data is encrypted in transit by default (there is no option to use DynamoDB over plaintext HTTP except maybe for DynamoDB Local in a test environment).
  - Network Access Control 
    By using **VPC Endpoints** for DynamoDB, you can ensure that all DynamoDB network traffic stays within the AWS network and is not exposed to the public internet. Two types of VPC endpoints are available for DynamoDB: **Gateway endpoints** and **Interface endpoints (AWS PrivateLink)**. *Gateway VPC endpoints* are common for DynamoDB – you can attach a gateway endpoint to your VPC, and update route tables, so that any calls to DynamoDB (which is a public AWS service endpoint) are routed through the AWS backbone rather than through an internet gateway. Gateway endpoints are cost-free and simple, but they don’t allow granular security groups since they work at the route level. *Interface VPC endpoints*, on the other hand, provide elastic network interfaces and IP addresses in your subnets for DynamoDB access. These support security groups and give a more fine-grained control; they are necessary if you want on-premise systems (via Direct Connect or VPN) to access DynamoDB without going over the internet. In either case, locking down DynamoDB access to only come from your VPC endpoints (using endpoint policies) adds an extra layer of security beyond IAM.
  - Auditing and Logging
    Enable AWS CloudTrail for your AWS account – DynamoDB is fully integrated with CloudTrail, meaning every API call made to DynamoDB (through the console, SDK, or CLI) is recorded. This includes calls like CreateTable, UpdateTable, PutItem, GetItem, Query, etc. CloudTrail logs can be sent to S3, where you can analyze them for suspicious access patterns or usage. For example, you can detect if someone tried to perform a Scan on a table that normally isn’t scanned, or if credentials from one application are erroneously being used elsewhere. Also, CloudWatch can be used to track DynamoDB metrics as mentioned. There’s also AWS Config rules related to DynamoDB (for example, to flag if PITR is not enabled on a table, or if a table is unencrypted – though encryption is always on by default now). In summary, treat DynamoDB as any critical data store: enforce least-privilege access, keep an audit trail of usage, and regularly review security configurations (AWS Trusted Advisor and Security Hub have checks for DynamoDB as well).

### Network Connectivity Options

  - Public Endpoint (HTTPS API)
    By default, DynamoDB’s service endpoint (e.g. `dynamodb.us-east-1.amazonaws.com`) is a public API endpoint reachable via the internet. **Use of the public endpoint directly from the internet is not allowed for production workloads containing sensitive data.** In a secure enterprise setup, you should access DynamoDB from within AWS networks (e.g., from EC2, Lambda, etc. in a VPC) and/or via VPC Endpoints. If an application must access DynamoDB over the internet (for example, a client mobile app using AWS SDK directly), ensure that proper IAM authentication is in place (such as Amazon Cognito identity pools vending temporary credentials) and that all requests use HTTPS. You can also restrict DynamoDB API access by source IP or VPC using IAM policy conditions (`aws:SourceIp` or `aws:SourceVpc`), effectively limiting who can call the public endpoint. But as a rule, **direct open internet access to DynamoDB is strongly discouraged** due to data security concerns – utilize the private connectivity options below.
  - VPC Gateway Endpoint
    AWS provides a **Gateway VPC Endpoint** for DynamoDB, which allows EC2 instances or other resources in your VPC to communicate with DynamoDB entirely via the AWS internal network. The gateway endpoint is set up by configuring your route tables: traffic destined for DynamoDB’s region endpoint is routed through the gateway, keeping it off the public internet. Gateway endpoints are supported in all regions and are free of charge. This option is ideal when your application is running in AWS (within a VPC) and you want to ensure DynamoDB traffic doesn’t require an internet gateway or NAT. Note that gateway endpoints do not use security groups; access can be controlled using **endpoint policies** – JSON policies attached to the endpoint to restrict which principals or tables can be accessed through that endpoint. In many scenarios, adding a DynamoDB gateway endpoint to your VPC and updating routes is the simplest way to harden network access.
  - VPC Interface Endpoint (PrivateLink)
    For more fine-grained control or hybrid scenarios, you can use an **Interface VPC Endpoint** for DynamoDB (AWS PrivateLink). Unlike a gateway endpoint, an interface endpoint creates one or more ENIs (Elastic Network Interfaces) in your VPC that are assigned private IP addresses in your subnets, through which DynamoDB can be accessed. Your VPC resources can then connect to DynamoDB using those private IPs, and you can attach **security groups** to restrict access. This is particularly useful if you have on-premises systems connecting via AWS Direct Connect or a VPN: they can now use a **private IP** to reach DynamoDB (through your VPC) instead of going out to the internet. Interface endpoints incur hourly costs and per-GB data processing costs, but they provide additional security (security group control, the ability to use NACLs, etc.). Choose this if you need that level of control or connectivity. In summary, both gateway and interface endpoints achieve the goal of isolating DynamoDB traffic to private networks – choose gateway for simplicity and no cost (within VPC only) or interface for more control and hybrid connectivity.
  - Network Architecture Considerations
    When using VPC endpoints, be aware that DynamoDB’s API endpoints are regional. With a gateway endpoint, as long as your VPC has connectivity to the DynamoDB service region endpoint, it will work across AZs. With interface endpoints, you typically create an endpoint in each desired AZ for high availability. Also, if you use Global Tables across multiple regions, you would set up VPC endpoints in each region where your application accesses DynamoDB. Ensure DNS resolution is enabled in your VPC (which it is by default) so that the \*.amazonaws.com hostname for DynamoDB resolves to the endpoint’s private IP. Finally, note that unlike some other AWS services, **DynamoDB does not support customer-controlled inbound network rules** (there’s no concept of whitelisting client IPs at the DynamoDB service level aside from IAM conditions), so the onus is on you to control network access via the VPC endpoints and firewall rules.

### Networking & Security Architecture
  - TBD

### AWS Service Limitations

  - Fixed Throughput per Partition
    DynamoDB automatically partitions tables based on size and throughput. However, a single partition has a finite throughput capacity of approximately 3,000 read capacity units or 1,000 write capacity units per second (which corresponds to 3,000 *4KB reads* or 1,000 *1KB writes* per second, respectively). If a workload against one partition key exceeds that, DynamoDB will throttle requests for that key until it splits the partition (which might take a bit of sustained pressure to trigger). **Adaptive capacity** will increase the throughput for hot partitions, but there’s a reaction time and it’s not instantaneous. Therefore, a heavily skewed workload (e.g., one hot key getting significantly more traffic than others) can hit this limit and experience throttling. It’s a limitation to be aware of: DynamoDB is extremely scalable, but it assumes your access patterns can be parallelized across partitions. Very imbalanced data access can require additional design strategies (like sharding the hot item across multiple logical items).
  - No Native Aggregation or Search
    DynamoDB does not support rich query operations beyond key lookups and simple filters. There’s no *GROUP BY*, *JOIN*, or full text search built into DynamoDB. If you need to perform complex analytics on DynamoDB data, you typically have to **export the data** to another system or use a companion service. AWS has recently introduced **DynamoDB integration with Amazon Redshift and Amazon OpenSearch (Zero-ETL)** for this reason – for example, you can mirror DynamoDB data into Redshift for complex SQL analytics, or into OpenSearch for text search and aggregation queries. These integrations help, but they come with their own costs and eventual consistency considerations. In short, DynamoDB’s design trade-off is that it foregoes complex querying capabilities in exchange for speed and scalability. This is a limitation if your application suddenly needs to run an ad-hoc report – you can’t just run a “SELECT \* with join” on DynamoDB. Many teams solve this by maintaining secondary indexes for specific query patterns, or by using event streams to populate an analytical database separate from DynamoDB.
  - Eventual Consistency in Global Tables
    By design, **Global Tables** operate on an eventual consistency model across regions. If you write to the US-East-1 replica, and immediately read from the EU-West-1 replica, the read might not see the write for a brief period (usually under a second, but it’s not guaranteed). Also, in the case of simultaneous writes to the same item in different regions, the **last writer wins** conflict resolution can lead to some writes being lost if the clocks are not perfectly in sync. The system uses timestamp attribution to determine the last update. This limitation means developers should avoid use cases where two regions actively update the same record frequently (to prevent overwriting each other). Where multi-region writes are needed, sometimes a “partitioned by region” approach or application-level reconciliation is required. It’s important to understand that while Global Tables provide great availability, they are not a multi-master with strong consistency across distances – no such system can avoid the CAP theorem trade-offs.
  - Schema Change Constraints
    DynamoDB being schema-less offers flexibility, but certain changes are not instantaneous or possible. For example, **increasing provisioned throughput** (if not on-demand) can be done via API/Console quickly (typically within a minute or so), but if you do it too frequently, you might hit limits (there are AWS limits on how often you can update throughput per table). Also, **creating or deleting secondary indexes** on an existing large table can be time-consuming and consume resources – it will scan the whole table to backfill the index. There is no way to rename a table; you have to create a new one and move data. There’s also no direct way to **change a table’s primary key schema** once created – that requires a migration to a new table. These limitations mean you should carefully design the table upfront if possible, and use techniques like projection expressions in queries if you want to evolve the “visible schema” without altering the stored schema.
  - Maximum Item Collection Size (LSI constraint)
    If you use *Local Secondary Indexes*, all items that share the same partition key (across the base table and all its LSIs) collectively cannot exceed 10 GB in size. This is an important limitation if you have a use case where a single partition key might accumulate a lot of data (for example, logs for a single customer that could go beyond 10 GB). In such a case, using LSIs would eventually hit a wall. The workaround is usually to either not use LSIs for that data (use GSIs instead, or a different data model) or to partition the data using a more granular partition key. The 10 GB limit is due to how LSIs store data in the same partition as the base table items. This limitation doesn’t exist for GSIs (GSIs can scale independently), but GSIs have their own cost and eventual consistency considerations.
  - Item Size and Attribute Limits
    As mentioned, 400 KB item size is a hard limit. Also, each attribute name can be up to 255 characters, which isn’t usually an issue, but attribute values of type string or binary also are constrained by the item size. Another subtle limitation: the **maximum number of attributes (columns) in an item** – there’s no explicit number, but it’s implicitly limited by item size and the fact each attribute name/value pair contributes to that size. If you find yourself needing hundreds of attributes in a single item, that might indicate a design issue for NoSQL (or it may require breaking the item into multiple items). **Conditional check failures** and transaction conflicts are another area: DynamoDB transactions will fail if another transaction is in flight on the same item or if your conditions aren’t met, so the application has to handle these failures (which is different from, say, a relational database which might block until a lock is free – DynamoDB will instead immediately throw an exception on conflict).

### SKU Features:
 
  - On-Demand Capacity Mode
    In on-demand mode, you do not have to specify any read/write capacity upfront. DynamoDB will instantly accommodate your workloads as they ramp up or down. It **scales automatically** and **charges you per request**. This mode is ideal when workload traffic is **unpredictable or highly variable**, because you never have to worry about provisioning too little or too much capacity. DynamoDB on-demand can handle sudden traffic spikes transparently (it remembers the previous high watermark and can immediately provision up to that level and beyond, incrementally). There is a slight cost premium for on-demand (pricing per million requests) compared to an equivalently well-tuned provisioned capacity, especially at sustained high throughput – roughly 2-3x cost if the provisioned capacity would be mostly utilized. On-demand mode also has *per-table* throughput limits that can increase over time; if a table’s traffic doubles every minute, on-demand will scale, but extremely sharp changes could see throttling until the service adjusts (generally it’s very adaptive within a second or two).
  - Provisioned Capacity Mode 
    In provisioned mode, you **specify the number of read and write capacity units (RCUs/WCUs)** your table (and indexes) should have. This is optimal for **predictable workloads** because you can achieve cost savings by provisioning exactly what you need and utilizing it fully. You can adjust capacity as needed via API (up to several times per day as required), or better, enable **Auto Scaling** on the table. Auto Scaling in DynamoDB will monitor consumption and adjust the provisioned RCU/WCU up or down within configured bounds to maintain a target utilization (e.g., scale up when usage > 70% of provisioned for a few minutes, scale down when usage is consistently low). Provisioned mode is subject to some scaling rules (you can’t suddenly scale from 100 to 100,000 RCUs in one step instantly – AWS may have some gradual ramp-up constraints, though these are more relaxed than they used to be). If your traffic is steady or gently cyclical (like daily peaks), provisioned mode with auto-scaling is very cost-effective. At very high throughput levels, provisioned capacity pricing is significantly cheaper than on-demand.
  - DynamoDB Standard Table Class
    This is the **default table class** for DynamoDB. Standard class provides the traditional DynamoDB storage and performance: **balanced for regular access patterns**. Storage is priced at the standard rate (around \$0.25/GB-month in many regions), and you pay for all reads/writes at the normal RCU/WCU rates. This class is recommended for the majority of use cases – essentially any table where a significant portion of data is accessed regularly or unpredictably. It offers the full DynamoDB feature set (streams, global tables, etc. all supported). The **latency and throughput performance is identical between Standard and Standard-IA classes**, as DynamoDB under the hood simply manages these classes in how it charges for storage.
  - DynamoDB Standard-Infrequent Access (Standard-IA) Table Class
    The Standard-IA table class is an alternative introduced to reduce costs for tables that store lots of data that is **infrequently accessed**. In Standard-IA, the storage cost is about 60% lower than Standard class, **but** the read/write throughput costs are slightly higher (approximately 25% higher per RCU/WCU). This means if you have a table where data volume is large but you rarely read/write most of it (e.g., an archive table, old records, audit logs), Standard-IA can save money. AWS recommends using Standard-IA for tables where **storage is the dominant cost** and access is infrequent – example use cases: application logs, old social media posts, e-commerce order history archives, etc.. It’s easy to convert an existing table to or from Standard-IA with a single API call (UpdateTable), and you can do so without downtime. One limitation: DynamoDB Streams on a Standard-IA table still incur the same higher read cost when retrieving stream records. Also, global tables can mix classes (each replica can be either standard or IA). If unsure, start with Standard and monitor your access patterns; if you see that a table’s data is cold (very low R/W per GB), consider switching to Standard-IA for cost savings.
  - DynamoDB Accelerator (DAX) 
    DAX is an in-memory cache for DynamoDB that delivers microsecond read times by caching results of eventually consistent reads. It’s a separate service you cluster in your VPC. While not part of the DynamoDB table itself, it effectively serves as a "feature" for workloads that need extreme read performance or to offload read pressure. DAX is ideal for workloads with repeated reads on the same keys (e.g., popular items being read many times per second). It is **fully managed** and compatible with existing DynamoDB API calls (the SDK just points to the DAX endpoint). The trade-off is that it introduces eventual consistency (it’s only for eventually consistent reads) and has its own cost, but it can reduce DynamoDB read costs and latency significantly.
  - Additional Features
    DynamoDB integrates with many other AWS services. For example, it’s **integrated with AWS CloudFormation** (for infrastructure as code deployments of tables and settings), with **Amazon CloudWatch** (for metrics and alarms), with **AWS CloudTrail** (for auditing access), and with data pipeline services (AWS Data Pipeline, AWS Glue jobs for ETL, etc.). It also now offers **Amazon Kinesis Data Streams for DynamoDB** which is an enhanced way to tap into the change stream for analytics use cases (basically streaming all changes to Kinesis for real-time processing, beyond the 24h window of DynamoDB Streams). Another feature is **Transactions** which we covered – supporting up to 100 items, 4MB, across tables, which is a big upgrade from the older single-item conditional writes. DynamoDB also has a **Service Level Agreement (SLA)** of 99.99% availability for single-region tables and 99.999% for global tables, reflecting its high reliability as a managed service.

### Related Service
  - TBD

### Alternatives
  - TBD

# Compliance and Security Guidelines

### Security Baseline InfoSec:
  - Information Security Specifications – Amazon DynamoDB 

# Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 7 Apr 2025 (Kalyan Battiprolu)
