---
weight: 2
title: "Architecture Review Checklist"
date: 2025-04-16
tags: []
summary: "Architecture Review Checklist"
---

## Cloud Architecture Review Checklist

Cloud architecture drawings are used to document the cloud soltuion, including all components and their interconnections. This is an important step in the cloud architecture design process as it ensures that all aspects of the design are captured, clearly understood and communicated to relevant stakeholders. The documentation should be concise, clear, and easily accessible to all stakeholders. It should also be reviewed and updated regularly to ensure that it remains accurate and up-to-date as the cloud architecture evolves over time. In general, these diagrams should provide a visual representation of the cloud architecture, including all components such as compute, storage, network, security, and monitoring.

This checklist below is a list of minimum set of information and guidelines to be used for creating **`conceptual`**, **`logical`** and **`physical`** architecture diagrams needed for `azure` cloud onboarding.

{{< details title="Application Summary" open=true >}}
- Application Configuration Item (CI) name in service now. For example see CI for [MypepsiCo]{target=_blank}
- Data Classification (`Confidential`, `Restricted`, `Internal` or `Public`)
- Sector info ( `PBNA`, `PFNA` etc)
- Disaster Recovery Classification (`RP1`, `RP2` etc.)
- Business criticality – BC 
    add description of BC
- Environments (`dev`, `qa`, `pre-prod` and `prod`)
- Brief descriptions of usage of the application
{{< /details >}}


{{< details title="Connectivity" open=true >}}
- Always show Express route information with an icon or `ER` word, for connectivity between onpremises data center, pepsico network/vpn to azure cloud.
- Show peering between vnet’s that are peered.
- For each connection between resources within Azure or outside azure, show
    - Direction of connection (`Initiator of Connection --> Receiver of Connection`)
    - Protocol and Port (For bi-directional connections, give information on both sides or use two arrows with information)
    - Encryption (`SSL/TLS`) and certificate termination point
{{< /details >}}
    

{{< details title="End User Information" open=true >}}
- Types of users
    - Internal - Users who have GPID
    - External - Users without GPID
- Types of connectivity
    - PepsiCo Network / VPN
    - External – public internet
- Methods of Authentication
    - OKTA, site minder, Active Directory, LDAP. (Credentials stored inside the App/DB is not allowed)
- Remote Access 
    - CyberArk
    - Citrix/VDI
    - Terminal Server
    - RDP
    - SSH
    - Bastion Host(For isolated Vnet only)
{{< /details >}}


{{< details title="Cloud Resource Details" open=true >}}
- Regions (Cross Region Communication must be pre-approved)
- Subscriptions
- Resource Group names if existing / TBD for new RG
- Vnets
- Subnets
- For IaaS, availability zones
{{< /details >}}


{{< details title="On-prem Details" open=true >}}
- Clear definition of where they are located - (PDC,RDC,FDC,LDC, SDC and so on) 
- For migrations, show existing infrastructure details
- Data size for migration or transfer from on-premises to cloud and vice versa
{{< /details >}}


{{< details title="PepsiCo cloud solution patterns" open=true >}}
- Check external app gw and external load balancer
- Check placement of firewall
- Usage of restricted patterns for restricted applications. For example, external application -> external app gateway -> firewall -> internal firewall
- Specify application URL for internal and external
- Internal app gateway & load balancer should be in spoke vnet and correct subnets
- Isolated databricks should peer with DMZ firewall vnet  
{{< /details >}}


{{< details title="Connectivity to PaaS Services" open=true >}}
- Restricted – Private Endpoint (PE)
- Rest all data types – Service Endpoint (SE)
- If any resource already enabled with PE, then other projects using the same resource also should connect using PE.
- Public Endpoint – for example access to blob from onprem using ER and not through the firewall
{{< /details >}}


{{< details title="Disaster Recovery" open=true >}}
- SQL Always On / Oracle Dataguard Observer in case of RP1 applications
- ASR is mandatory for all resources except for databases for RP1 apps
- ASR is mandatory for all resources for RP2 apps
{{< /details >}}


{{< details title="File Share" open=true >}}
- Use ANF always - Azure Files is not approved for PepsiCo
- SMB shares from vm’s not approved for PepsiCo
- Use MDT for Internal & External file transfers
{{< /details >}}