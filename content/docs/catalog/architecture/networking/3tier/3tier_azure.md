---
weight: 2
title: "Azure"
date: 2025-04-15
tags: []
summary: ""
---

## Web Application

This example of a multi-tier application built for high availability and disaster recovery. In this scenario, the application consists of three layers.

- Web tier: The user interface with the application.  This layer takes the users input and passes the actions to the business layer for processing
- Business tier: The processing layer where any logic is applied.  This layer ingests the actions passed from the web tier, retrieves data from the data tier, and applies any business logic based on the input and data.
- Data tier: Stores the application data. Either a database, object storage, or file storage is typically used.

Common application scenarios include any application that requires a user interface and has data that the user is interacting with.

## Architecture

{{< tabs "uniqueid" >}}
{{< tab "Restricted Data" >}} 
![Diagram showing the architecture overview of a highly resilient multi-tier web application.](/images/3-tier.restricted.drawio.png) 
{{< /tab >}}
{{< tab "Non-Restricted Data" >}}
 ![Diagram showing the architecture overview of a highly resilient multi-tier web application.](/images/3-tier.drawio.png)
 {{< /tab >}}
{{< /tabs >}}

## Considerations
- For High Availability (HA) scenarios, VMs should be distributed across multiple zones in the region.
- For HA or disaster recovery (DR) scenarios, the database tier can be configured to use Always On availability groups or DataGuard, if it is an BC1 application. 
- For DR of applications that are BC2-3, Azure Site Recovery (ASR) should be used.
- Users connecting from the public internet, first connect to the external application gateway for HTTPS traffic or Azure load balancer for non-https traffic and routed to the Palo Alto firewall.
- Traffic goes to an internal application gateway and SSL offloading occurs.
- The app gateway or load balancer then routes the traffic to one of the web tier VMs.
- From the web tier VM, each call is routed to one of the VM instances in the business tier through an internal app gateway or load balancer for processing. All business tier VMs are in a separate zones.
- The operation is processed in the business tier and the web application connects to a database in the back-end tier.
- In the event of a primary region disruption, Azure Site Recovery failover is used to switch to the secondary region.

## Components

- Availability zones protect your applications and data from datacenter failures. Availability zones are separate physical locations within an Azure region. Each zone consists of one or more datacenters equipped with independent power, cooling, and networking.
- Azure Site Recovery allows you to replicate VMs to another Azure region for business continuity and disaster recovery needs. You can conduct periodic disaster recovery drills to ensure you meet the compliance needs. The VM will be replicated with the specified settings to the selected region so that you can recover your applications in the event of outages in the source region.
- Azure Load Balancer distributes inbound traffic according to defined rules and health probes. A load balancer provides low latency and high throughput, scaling up to millions of flows for all TCP and UDP applications. A public load balancer is used in this scenario to distribute incoming client traffic to the web tier. An internal load balancer is used in this scenario to distribute traffic from the business tier to the back-end SQL Server cluster.
- Azure Application Gateway is a web traffic load balancer that enables you to manage traffic to your web applications. Traditional load balancers operate at the transport layer (OSI layer 4 - TCP and UDP) and route traffic based on source IP address and port, to a destination IP address and port.
- Paloalto Firewall is a stateful firewall, meaning all traffic passing through the firewall is matched against a session and each session is then matched against a security policy. A session consists of two flows. The Client to Server flow (c2s flow) and the Server to Client flow (s2c flow). The endpoint where traffic initiates is always the Client, and the endpoint where traffic is destined is the Server. For defining security policies, only the c2s flow direction needs to be considered. Define policies that allow or deny traffic from the originating zone to the destination zone, that is, in the c2s direction. The return flow, s2c, doesn't require a new rule. The security policy evaluation on the firewall occurs sequentially from top to bottom in the list, so traffic matching the first closest rule in the list applies to the session.

## Alternatives

- Windows can be replaced by other operating systems because nothing in the infrastructure is dependent on the operating system.
- SQL Server for Linux can replace the back-end data store.
- Azure Netapp Files can be used in place of Blob storage.

We've provided a sample cost calculator for configuring disaster recovery for a three-tier application using six virtual machines. All of the services are pre-configured in the cost calculator. To see how the pricing would change for your particular use case, change the appropriate variables to estimate the cost.

## Date of Approval
2/9/2023
