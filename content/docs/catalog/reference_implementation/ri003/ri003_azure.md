---
weight: 2
title: "Azure"
date: 2025-04-15
tags: ["azure", "ri003"]
summary: "RI003 summary Azure"
---

## RI003 - Web application and microservices

### Summary

__RI003__ is a specific implementation of Web Application and Microservices Architecture under Enterprise Integration & Automation Platform (EIAP) domain. <br> This reference implemenation can be used for publishing:

- rontend web application 
- backend microservices 

which are essential components of modern application architecture and microservices pattern consisting of frontend web application that leverages independent services each providing specific business need.

### Architecture

![RI002](/images/ri003.drawio.png)

### Steps

#### Front End

- End users access web application and authenticate via Okta
- Traffic is routed to Istio Service Proxy that load balances and routes traffic to NGiNX HTTP (web) Servers deployed on containers on AKS
- NGiNX returns static content e.g. HTML,CSS, JavaScript, Images
- Browser loads the page, and then via React.JS code makes calls to backend APIs via the API Gateway to get dynamic content

#### Business Logic

- API Gateway checks authorization, and routes requests to Istio Service Proxy
- Istio Service Proxy load balances and routes requests to Microservices running on AKS
- Microservice executes business logic and accesses data from its data stores
- API Response is then sent back through the Service Proxy, API Gateway to the browser
- Messages are sent/received to/from other apps via existing Confluent Kafka instances (EIP, S&T, etc.)

### Required User Inputs

__RI003__ requires the Submitter to provide minimal set of input parameters. The parameters will be used to derive location of Azure Services and their configuration.<br>
For more information and listing of default configuration parameters used in this RI please check [Additional Considerations](#additional-considerations)

> - Clarity Project ID (Improvement ID)
> - Division	
> - Environment	
> - Data classification
> - Application name	

### Technical Components

#### Application Specific
Application specific resources are dedicated instances of Azure services created solely for each application implementation. Cost of the resources must be covered fully by business team. 

- Blob Storage
- Azure Key Vault
- Azure SQL Database

#### Domain Shared
Domain shared components are used by multiple projects. Their cost is shared between different application implementations.

- Azure DevOps
- Azure Kubernetes Service
- Conjur
- Kafka
- Logstash
- Azure Container Registry

#### Pepsico Shared
PepsiCo shared components are building blocks of PepsiCo Azure landing zones and service offering. 

- Enterprise Palo Alto firewall
- API Management (Apigee)
- External Application Gateway
- Okta
- Certificate Management

### Additional Considerations

1. New __Resource Group__ will be created to store Application specific resources.
2. New __AD group__ will be created with permissions to the Application RG.
3. New __Namespace__ will be created in EIAP AKS.
4. New __Kafka topic__ will be created in shared Kafka. 
5. New __Apigee endpoint__ will be created in API Management.
6. New __Conjur group__ will be created.
7. New __Entrust certificate__ will be issued for the App domain.

### Date of Approval

| Version      | Date of approval | Description of changes  |
| ------------ | ---------------- | ----------------------- |
| `1.0`          | TBD              | First version of RI001  |