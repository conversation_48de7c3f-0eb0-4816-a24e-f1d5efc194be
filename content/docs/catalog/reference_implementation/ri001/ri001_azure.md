---
weight: 2
title: "Azure"
date: 2025-04-15
tags: ["azure", "ri001"]
summary: "RI001 summary Azure"
---

## RI001 - Transformation and Visualization of Data

### Summary

__RI001__ is a specific implementation of Business Analytics with Data Lake Reference Architecture under Enterprise Data Analytics Platform (EDAP) domain. <br> This reference implemenation can be used for:

- reporting, 
- dashboarding 
- ad hoc analysis 

which are essential steps in the data analysis process, as they help analysts understand, explore, and communicate insights from complex data.<br>This implementation is not recommended for high concurrency or high performance. 

### Architecture

![RI002](/images/ri001.drawio.png)

### Steps

#### Data Ingestion

- Azure Data Factory (ADF) using Self Hosted Integration Runtime (SHIR) pulls batch, event, stream, and APIs data from:
    - PepsiCo Data Centers
    - 3'rd Party API
    - Internal & External data sources
- SHIR stores ingested data in Data Foundation Blob Storage
- ADF-SHIR move data from Blob into Raw & Archive Zone (Landing) of the Data Foundation residing in Azure Data Lake Storage (ADLS)

#### Data Transformation and harmonization

- Isolated Databricks:
    - Retrieves data from ADLS
    - Transforms and curates data
    - Generates features
    - Store curated data in the Transfer & Exploratory Zone (Bronze) of the Data Foundation residing in Azure Data Lake Storage (ADLS)
    - Harmonizes and integrates the data into the Certified Zone (Silver) of the Data Foundation (ADLS)
    - Aggregates data and builds Specific Data Product (Gold)

    *Isolated Databricks can access external providers to download required libraries.*

#### Data Visualization

- Presto SQL Query engine builds views from Data Lake (ADLS)
- Power BI Gateway reporting layer accesses Presto generated views  
- End User consume analysis using standard business dashboards or self-service dashboards created in Power BI

### Required User Inputs

__RI001__ requires the Submitter to provide minimal set of input parameters. The parameters will be used to derive location of Azure Services and their configuration.<br>
For more information and listing of default configuration parameters used in this RI please check [Additional Considerations](#additional-considerations)

> - Clarity Project ID (Improvement ID)
> - Division	
> - Environment	
> - Data classification
> - Application name	

### Technical Components

#### Application Specific
Application specific resources are dedicated instances of Azure services created solely for each application implementation. Cost of the resources must be covered fully by business team. 

- Azure Data Factory
- Azure Key Vault

#### Domain Shared
Domain shared components are used by multiple projects. Their cost is shared between different application implementations.

- Blob Storage
- Azure Data Lake
- Isolated Databricks
- Presto
- Power BI Gateway
- Self Hosted Integration Runtime (SHIR)

#### Pepsico Shared
PepsiCo shared components are building blocks of PepsiCo Azure landing zones and service offering. 

- Enterprise Palo Alto firewall
- Enterprise DMZ Palo Alto firewall
- Log Analytics workspace

#### Data Sources

- PepsiCo Data Centers
- PepsiCo Azure services and VMs
- 3'rd party services shared in Azure cloud environment
- Public Internet

#### Data Destinations

- Not used

### Additional Considerations

1. Currently available only in __North America__ (SCUS/EUS).
2. New __Resource Group__ will be created to store Application specific resources.
3. New __AD group__ will be created with permissions to the Application RG.
4. New __Log Analytics__ workspace will be used.
5. __Presto-Metastore__ integration is out of scope.
6. __Power BI__ will conect only to Presto.
7. __Databricks__ will use:
    - 1 node for Dev environment
    - 2 nodes for Prod/Preprod environments

### Date of Approval

| Version      | Date of approval | Description of changes  |
| ------------ | ---------------- | ----------------------- |
| `1.0`          | TBD              | First version of RI001  |