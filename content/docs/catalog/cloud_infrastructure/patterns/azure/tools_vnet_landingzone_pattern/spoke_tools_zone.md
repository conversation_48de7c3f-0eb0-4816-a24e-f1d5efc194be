---
weight: 4
title: "Spoke Tools Zone"
date: 2025-06-21
tags: ["azure", "network", "tools", "spoke", "landing zone"]
summary: "Hub Tools Zone"
---

## Cloud Pattern Classification

**Category of the Service**: Network
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: Spoke Tools Zone
**Description**: Spoke zone, defines the landing zone to be used by centralized infrastructure tools. Centralized tool can be considered any infrastructure tools that requires to be hosted into a central managed compute instance/cluster. The tool must be deployed into the Infrastructure Spoke Tool vNET zone with access across to all “Hub VNETs”.

### Allowed PepsiCo Data Classification
  - Internal

### Lifecycle
  - Release Date: November 1, 2022
  - Planned Decommission Date: Not announced.
  - Decommission Date: Not applicable at this time.
 
## Goals & Benefits
  - Network segmentation is achieved by leveraging Network Security Groups (NSGs)
  - Enhanced security by rerouting traffic to be inspected by Network Virtual Appliances (NVAs)

## Usage Guidelines

### When to use
  - For centralized tools instances across multiple vNET in the same region
  - When strong network isolation and packet inspection is required. In line with InfoSec guidelines.

### Limitations
  - TBD

### Example
  - Centralized Tools connecting to agents/systems to collect reports, logs or already consolidate data inspecting the data by rerouting it over Network Virtual Appliance (NVA).

### Sample Use Cases
  - Centralized tool architecture
  - Strong network isolation
  - Packet inspection is mandatory
  
### Additional Guardrails
  - If any traffic originating outside PepsiCo enterprise network is required, such as RDP connection to the tool compute instance should go over standard PepsiCo connectivity.  

## Technical Guidelines
  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/spoke_tools_zone.png)

## Related Patterns
  - Coldo tools zone
  - Hub tools zone
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 21 June 2025 (Sakshi Sharma)
