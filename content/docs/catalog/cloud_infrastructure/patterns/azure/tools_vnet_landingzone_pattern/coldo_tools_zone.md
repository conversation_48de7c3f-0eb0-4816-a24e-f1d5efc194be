---
weight: 2
title: "Coldo Tools Zone"
date: 2025-06-19
tags: ["azure", "network", "tools", "coldo", "landing zone"]
summary: "Coldo Tools Zone"
---

## Cloud Pattern Classification

**Category of the Service**: Network
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: Coldo Tools Zone
**Description**: COLO tools Zone pattern defines the landing zone to be used by decentralized infrastructure tools. Decentralized tool can be considered any infrastructure tools that requires to be hosted across multiple compute instances/nodes. The tool compute instances will be deployed close to the systems for which it will be used.

### Allowed PepsiCo Data Classification
  - Internal

### Lifecycle
  - Release Date: November 1, 2022
  - Planned Decommission Date: No announced.
  - Decommission Date: Not applicable at this time.
 
## Goals & Benefits
  - Reducing data transfer cost by placing the tools in the same vNET as the target systems
  - Improving latency without going through vNET peering or Network Virtual Appliances (NVAs)
  - Network segmentation is achieved by leveraging Network Security Groups (NSGs)

## Usage Guidelines

### When to use
  - For decentralized tools deployed across multiple VNETs 
  - When the traffic volume is high keeping the traffic within the same VNET.
  - When package inspection is not mandatory. 
  - A generic subnet will host all tools inside each VNET

### Limitations
  - Create a separate tools subnet to keep segmentation easier and cleaner.

### Example
  - A single Guardium collector instance can only support up to 20 target databases, volume data traffic is high.

### Sample Use Cases
  - Decentralized tools architecture 
  - Packet inspection is not mandatory
  - Traffic volume is very high.
  

### Additional Guardrails
  - All decentralized tools will be hosted in the COLO Tool Zone. Any exception due to architecture design, requirements or security constraints will be subject to approval by the ISC Team.
  - If any traffic originating outside PepsiCo enterprise network is required, such as RDP connection to the tool compute instance should go over standard PepsiCo connectivity. 

## Technical Guidelines
  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/coldo_tools_zone.png)

## Related Patterns
  - Hub tools zone
  - Spoke tools zone
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 19 June 2025 (Sakshi Sharma)
