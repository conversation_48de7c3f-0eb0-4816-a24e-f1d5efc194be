---
weight: 2
title: "Consumer Facing Application Pattern"
date: 2025-06-16
tags: ["azure", "network", "weak", "authentication", "application"]
summary: "Consumer Facing Application Pattern"
---

## Cloud Pattern Classification

**Category of the Service**: Network
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: Consumer Application 
**Description**: Hosting a consumer-facing web application in an isolated network involves deploying the web application within a secure, isolated network environment. This environment is separate from the enterprise network and limits access to authorized users or systems. Additionally, an API hosted on the enterprise network is made accessible through an API gateway.

### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

### Lifecycle
  - Release Date: May, 2024
  - Last Update Date: May, 2024
  - Decommission Date: Not applicable at this time.

## Goals & Benefits
  - Enhanced Security: By isolating the web application in its own network segment, PepsiCo can minimize the risk of unauthorized access and potential security breaches. This isolation limits the attack surface and prevents lateral movement by malicious actors within the network.
  - Isolation of Risk: In the event of a security incident or compromise, the isolation of the web application limits the scope of the impact, reducing the risk of widespread data breaches or system compromises.
  

## Usage Guidelines

### When to use
  - Web Application those are consumer facing.
  - Web Application is not integrated with Enterprise Okta.
  - Web Application integrated with Consumer Okta.
  - Consumer facing Web Application with requirement to connect to enterprise application/data using API.
  

### Limitations
  - Web Application weak/No Authentication.
  - Only Authenticated call will be allowed to consume Enterprise API published via enterprise APIGEE.
  - Only to be used is Web/API can't be deployed in Azure WebApp.
  - This Pattern should not be used for large data transfer via enterprise APIGEE.

### Example
  - Hosting consumer facing application in PepsiCo Enterprise subscription exposed over internet and consuming enterprise application/data over API.

### Sample Use Cases
  - Hosting Consumer facing app/api in enterprise subscription aligning to enterprise pattern.
  - Web/API Applications do have dependency to connect to API/data sources in enterprise network.
  - Web Application weak/No Authentication.
  Only Authenticated call will be allowed to consume Enterprise API published via enterprise APIGEE.
  Only to be used is Web/API can't be deployed in Azure WebApp.
  This Pattern should not be used for large data transfer via enterprise APIGEE.
  
### Additional Guardrails
  - Code Push  through a DevOps Pipeline with necessary infosec tools.
  Azure service logs should be integrated to Splunk/Elastic  following Infosec guidelines.
  - Keys stored in Key Vault and rotate according to PepsiCo 
  - Authenticate API from the application via a Proxy System for system-to-system authentication.
  - No direct consumer connection termination to the enterprise network. Consumer facing services should use a partner token to  authenticate enterprise API.
  - Consumer-facing app’s API should be published via APIGEE SaaS, protected by - - - Enterprise Imperva
  - WebApp/Container App should accept traffic only from Imperva/APIGEE SaaS, blocking other traffic.
  - VNet integration in non routable vNet which routes traffic to Internet via Isolated DMZ.
  - Any changes in Architecture outside of this pattern need to be reviewed with Security Architecture and Cloud Architecture and Engineering Team.
  
## Technical Guidelines
  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/consumer_app.png)

## Related Patterns
  - TBD
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 16 June 2025 (Sakshi Sharma)
