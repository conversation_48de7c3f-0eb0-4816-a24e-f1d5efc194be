---
weight: 2
title: "Isolated vNet Firewall Pattern"
date: 2025-04-15
tags: ["azure", "network", "firewall", "isolated"]
summary: "Isolated vNet Firewall Pattern"
---

## Cloud Pattern Classification

**Category of the Service**: Network
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: Isolated vNet Firewall Pattern
**Description**: Enable internet connectivity to the isolated vNet hosting Azure Databricks and AML compute to securely connect to the internet.

### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

### Lifecycle
  - Release Date: September, 2022
  - Last Update Date: September, 2022
  - Decommission Date: Not applicable at this time.

## Goals & Benefits
  - Some Azure PaaS Compute Infrastructure requires large and dynamic IP Space. Demands for    these PaaS Services by the Pepsico digital platform team are high.
  - Control planes of these PaaS services need Azure vNet injection into the compute infrastructure. 
  - An isolated vNet pattern was established to provide sufficient IP space and network security to the Pepsico Enterprise network.
  - The isolated vNet pattern was adopted by services such as Databricks and Azure Machine Learning workspace.
  - An isolated DMZ would provide a secure path to connect to the internet.
  
## Usage Guidelines

### When to use
  - Isolated Databricks connecting to the internet
  - AML Workspace leveraging vNet injected Private compute to connect to internet.

### Limitations
  - TBD

### Example
  - AML Workspace leveraging vNet injected Private compute to connect to internet.

### Sample Use Cases
  - Isolated Databricks connecting to the internet
  - AML Workspace leveraging vNet injected Private compute to connect to internet.
  
### Additional Guardrails
  - Routes are non-transitive, so there is no possibility for an isolated spoke route to get published on-prem..  
  - The route table will be associated with trust and untrust interfaces with BGP disabled. This will provide additional guardrails preventing route to publish over the enterprise network.
  - There won't be any route in the isolated DMZ Firewall to connect to on-premises.
  - The firewall will be tightly protected with NSG in Trust Interface. There should not be any Pepsico routable IP ranges added to NSG or Palo Virtual Routes, Policies, etc.
  - Isolated DMZs are not intended to route or connect to enterprise networks; rather, they are intended to facilitate internet outbound from non-routable networks, as well as inbound to non-routable networks for future capability. 
  - Only the management interface will have connectivity to On-Prem for onboarding and management via Enterprise Panorama.
  
## Technical Guidelines
  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/Isolated_vnet_firewall.png)

## Related Patterns
  - Enterprise Firewall
  - Datahub Firewall
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 5 June 2025 (Sakshi Sharma)
