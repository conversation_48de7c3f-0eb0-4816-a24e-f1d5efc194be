---
weight: 2
title: "Datahub Firewall Architecture"
date: 2025-06-06
tags: ["azure", "network", "datahub", "firewall"]
summary: "Datahub Firewall Architecture"
---

## Cloud Pattern Classification

**Category of the Service**: Network
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: Datahub Firewall Architecture
**Description**: Datahub firewall provides network segmentation when integrating with 3rd party data services, and is  separate from Azure DMZ firewall due to the high network traffic volume associated with data services.

### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

### Lifecycle
  - Release Date: September, 2021
  - Last Update Date: September, 2021
  - Decommission Date: Not applicable at this time.

## Goals & Benefits
  - Provide network segmentation and packet inspection when integrating with 3rd party data services hosted outside of PepsiCo network.
  - Network connections to 3rd party data services are always initiated from PepsiCo side, data then will be pushed / pulled over the established connections.
  - 3rd party data services are not allowed to initiate network connections to PepsiCo network
  

## Usage Guidelines

### When to use
  - TBD

### Limitations
  - TBD

### Example
  - TBD

### Sample Use Cases
  - Initiate network connection to 3rd Party data services to pull or push data.
  - For example, ADF SHIR, PBI Gateway, IRI integration.
  
### Additional Guardrails
  - TBD
  
## Technical Guidelines
  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/datahub_isolated_architecture.png)

## Related Patterns
  - Enterprise Firewall
  - Isolated DMZ Firewall
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 6 June 2025 (Sakshi Sharma)
