---
weight: 4
title: "PaaS in remote region using SE"
date: 2025-06-08
tags: ["azure", "network", "paas", "remote region", "service endpoint"]
summary: "Connect to PaaS Services in remote region using Service Endpoint"
---

## Cloud Pattern Classification

**Category of the Service**: Network
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: Connect to PaaS Services in remote region using Service Endpoint
**Description**: Enable IaaS/PaaS services in an Azure VNET to privately access Azure PaaS services outside of Azure VNET in the remote Azure region.

### Allowed PepsiCo Data Classification
  - Internal

### Lifecycle
  - Release Date: July, 2022
  - Last Update Date: July, 2022
  - Decommission Date: Not applicable at this time.

## Goals & Benefits
 - Access to Azure PaaS services through Azure service endpoints is only allowed from specific Azure subnets, access over public Internet is disabled.
 - Access to Azure PaaS services is routed directly from Azure subnet over Azure backbone without going through firewalls or other network appliances except for Azure Storage account which is in preview and SQL don’t have cross region Service Endpoint. 
 

## Usage Guidelines

### When to use
  - Any IaaS/PaaS services hosted in an Azure VNET access Azure PaaS services outside of Azure VNET Cross region.
  - PaaS services do not Process or Store restricted data
  - Access PaaS services within PepsiCo AD tenant
  - Access PaaS services with any PepsiCo approved authentication and encryption methods
  
### Limitations
  - Network access control is at Azure VNET subnet level 
  - Azure SQL do not support cross region Service endpoint yet.
  - Cross region Service Endpoint for Azure Storage Account is in Public Preview.
  
### Example
  - Azure VMs access EventHub, KeyVault etc.
  - Applications in ASE access Service Bus, Event hub etc. Cross region.
  
### Sample Use Cases
  - Disable access to Azure PaaS services over public Internet
  - Access PaaS Services without impacting PepsiCo network Appliances. 
  - Access to PaaS Services cross region from the IaaS/PaaS Services
  
### Additional Guardrails
  - TBD
  
## Technical Guidelines
  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/paas_remoteregion_serviceendpoint.png)

## Related Patterns
  - Connect to PaaS Services in Local region using Service Endpoint.
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 8 June 2025 (Sakshi Sharma)
