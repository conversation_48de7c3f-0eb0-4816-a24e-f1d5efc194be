---
weight: 6
title: "PaaS in local region using PE"
date: 2025-06-09
tags: ["azure", "network", "paas", "local region", "private endpoint"]
summary: "Connect to PaaS Services in Local region using Private Endpoint"
---

## Cloud Pattern Classification

**Category of the Service**: Network
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: Connect to PaaS Services in Local region using Private Endpoint
**Description**: Enable IaaS/PaaS services in an Azure VNET and On-Prem network to privately access Azure PaaS services outside of Azure VNET in the same Azure region.

### Allowed PepsiCo Data Classification
  - Internal
  - Public
  - Confidential
  - Restricted

### Lifecycle
  - Release Date: July, 2022
  - Last Update Date: July, 2022
  - Decommission Date: Not applicable at this time.

## Goals & Benefits
  - Access to Azure PaaS services through Azure Private endpoints is only allowed from specific Azure or On-Prem IP addresses, access over public Internet is disabled.
  - Access to Azure PaaS services from Azure vNet is routed directly over Azure backbone without going through firewalls or other network appliances.
  - Access to Azure PaaS services from On-Prem is routed directly over Express Route and Azure backbone privately. 


## Usage Guidelines

### When to use
  - Any IaaS/PaaS services hosted in an Azure VNET or On-Prem VMs access Azure PaaS services outside of Azure VNET.
  - PaaS services Processes or Store restricted data.
  - Access PaaS services within PepsiCo AD tenant
  - Access PaaS services with any PepsiCo approved authentication and encryption methods.
  
  
### Limitations
  - There can’t be more than one  Private Endpoint attached from PepsiCo Routable network.

### Example
  - Azure VMs access Storage account.
  - Applications in ASE access Azure SQL DB.
  - On-Prem VMs access Storage accounts
  
### Sample Use Cases
  - Disable access to Azure PaaS services over public Internet
  - Access PaaS Services without impacting PepsiCo network Appliances. 
  - Access PaaS Services privately from Azure vNet and on-prem networks
  - Only Allow access to PaaS Services from selective IP addresses.
  
### Additional Guardrails
  - TBD
  
## Technical Guidelines
  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/paas_localregion_privateendpoint.png)

## Related Patterns
  - Connect to PaaS Services in remote region using Service Endpoint.
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 9 June 2025 (Sakshi Sharma)
