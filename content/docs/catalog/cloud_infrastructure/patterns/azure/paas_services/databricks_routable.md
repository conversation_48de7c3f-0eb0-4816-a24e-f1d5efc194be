---
weight: 3
title: "Databricks Routable"
date: 2025-04-15
tags: ["azure", "database", "databricks", "routable"]
summary: "Databricks Routable"
---

## Cloud Pattern Classification

**Category of the Service**: Database
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: Routable Databricks Pattern
**Description**: The Databricks workspace is used to establish connections with data sources hosted on-premises, IaaS components, and PaaS services securely within the enterprise network.

### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

### Lifecycle
  - Release Date: October 7, 2014
  - Planned Decommission Date: No announced.
  - Decommission Date: Not applicable at this time.
 
## Goals & Benefits
  - Accessing data from multiple sources
    Azure Databricks, when hosted in Enterprise vNets, has the capability to establish connections with a variety of Azure services. This includes PaaS services through service endpoints or private endpoints, IaaS services, and on-premises data sources
  - Control Over Traffic Flow
    Secure connections to the internet and third-party services hosted outside of Azure will be directed through the Enterprise Palo from the Enterprise  vNet.

## Usage Guidelines

### When to use
  - Use case for Azure Databricks Clusters to connect to On-Prem Data sources.
  - Use case for Azure Data bricks to connect to Data sources or IaaS services in Azure that don’t support private link.
  - Azure Databricks connects to PaaS service over Service endpoint/Private Endpoint and Approved 3rd Party Private Link service over Private Endpoint.

### Limitations
  - Databricks connects to On-Prem, IaaS services hosted in PepsiCo network. Also PaaS services using Service endpoint/Private Endpoint.

### Example
  - Consumption of PaaS Services (ADLS),Hive meta store hosted in Isolated databricks vNet

### Sample Use Cases
  - Databricks workspace establishes a connection with PaaS services via Service endpoint or Private endpoint. 
  - The Databricks Cluster interacts with the service over a private network. 
  - Databricks services make use of the IaaS services, which are located in the PepsiCo enterprise IaaS and on-prem. Also third-party Azure environment through Private Link

### Additional Guardrails
  - TBD

## Technical Guidelines

  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/databricks_routable.png)

## Related Patterns
  - Azure Isolated Databricks Pattern.
  - Enterprise landing Zone
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 7 Apr 2025 (Dariusz Korzun)
