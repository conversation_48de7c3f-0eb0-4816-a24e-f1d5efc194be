---
weight: 7
title: "PaaS in local region using SE"
date: 2025-06-09
tags: ["azure", "network", "paas", "local region", "service endpoint"]
summary: "Connect to PaaS Services in Local region using Service Endpoint"
---

## Cloud Pattern Classification

**Category of the Service**: Network
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: Connect to PaaS Services in Local region using Service Endpoint
**Description**:Enable IaaS/PaaS services in an Azure VNET to privately access Azure PaaS services outside of Azure VNET in the same Azure region.

### Allowed PepsiCo Data Classification
  - Internal

### Lifecycle
  - Release Date: July, 2022
  - Last Update Date: July, 2022
  - Decommission Date: Not applicable at this time.

## Goals & Benefits
  - Access to Azure PaaS services through Azure service endpoints is only allowed from specific Azure subnets, access over public Internet is disabled.
  - Access to Azure PaaS services is routed directly from Azure subnet over Azure backbone without going through firewalls or other network appliances.
  
  
## Usage Guidelines

### When to use
  - Any IaaS/PaaS services hosted in an Azure VNET access Azure PaaS services outside of Azure VNET.
  - PaaS services do not Process or Store restricted data.
  - Access PaaS services within PepsiCo AD tenant.
  - Access PaaS services with any PepsiCo approved authentication and encryption methods.
  
### Limitations
  - Network access control is at Azure VNET subnet level, not IP level.

### Example
  - Azure VMs access Storage account.
  - Applications in ASE access Azure SQL DB
  
### Sample Use Cases
  - Disable access to Azure PaaS services over public Internet
  - Access PaaS Services without impacting PepsiCo network Appliances. 
  - Access to PaaS Services in same region from the IaaS/PaaS Services
  
### Additional Guardrails
  - TBD
  
## Technical Guidelines
  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/paas_localregion_serviceendpoint.png)

## Related Patterns
  - Connect to PaaS Services in remote region using Service Endpoint.
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 9 June 2025 (Sakshi Sharma)
