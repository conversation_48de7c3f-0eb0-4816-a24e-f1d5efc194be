---
weight: 12
title: "Vnet over private link"
date: 2025-06-10
tags: ["azure", "network", "vnet","private link"]
summary: "Connect PepsiCo Azure vNets over Private Link Services"
---

## Cloud Pattern Classification

**Category of the Service**: Network
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: Connect PepsiCo Azure vNets over Private Link Services.
**Description**: Connect PepsiCo Azure vNets over Private Link Services when vNet peering is not allowed.

### Allowed PepsiCo Data Classification
  - Internal
  - Restricted
  - Confidential
  - Public

### Lifecycle
  - Release Date: July, 2022
  - Last Update Date: July, 2022
  - Decommission Date: Not applicable at this time.

## Goals & Benefits
  - Access to Azure IaaS services through Azure Private endpoints from non-peered vNet.
  - Access to Azure IaaS services is routed directly from Azure subnet in Non-peered vNet over Azure backbone without going through firewalls or other network appliances.
  - Access can only be established once connection is Approved by IaaS Service Administrator

## Usage Guidelines

### When to use
  - Azure Databricks Services deployed in PepsiCo Isolated vNet Pattern only. 
  For all data Classification.
  - Both vNets must be within PepsiCo AD tenant.
  - Access IaaS services with any PepsiCo approved authentication and encryption methods.
  
### Limitations
  - Azure IaaS private link currently does not support a frontend Firewall, both vNet must be locked down without open Internet access.

### Example
  - Consumption of IaaS Services (Gurobi, Metastore) hosted in PepsiCo enterprise network from Isolated databricks vNet.
  
### Sample Use Cases
  - Need to Connect PepsiCo Azure vNets when vNet peering is not allowed as per PepsiCo network Foundation standard.

### Additional Guardrails
  - TBD
  
## Technical Guidelines
  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/paas_vnet_privatelink.png)

## Related Patterns
  - Azure Databricks-Isolated-Pattern.

## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 10 June 2025 (Sakshi Sharma)
