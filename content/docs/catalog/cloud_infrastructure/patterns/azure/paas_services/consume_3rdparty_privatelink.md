---
weight: 10
title: "consume to 3rd party over private link"
date: 2025-06-10
tags: ["azure", "network", "consume", "3rd party","private link"]
summary: "Consume 3rd Party Services hosted in Azure over Private Link Services"
---

## Cloud Pattern Classification

**Category of the Service**: Network
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: Consume 3rd Party Services hosted in Azure over Private Link Services.
**Description**: Enable IaaS/PaaS services in an Azure VNET to privately access 3rd Party Services (IaaS and PaaS) hosted in Azure over Private Link Services.

### Allowed PepsiCo Data Classification
  - Internal
  - Restricted
  - Confidential
  - Public

### Lifecycle
  - Release Date: July, 2022
  - Last Update Date: July, 2022
  - Decommission Date: Not applicable at this time.

## Goals & Benefits
  - Access to 3rd Party Services (IaaS and PaaS) hosted in Azure over Private Link.- Services privately and securely over Azure backbone.
  - Access can only be established upon mutual approval by PepsiCo and the 3rd Party.


## Usage Guidelines

### When to use
  - IaaS/PaaS services hosted in a PepsiCo Azure VNET access to 3rd Party Services (IaaS and PaaS) hosted in Azure over Private Link Services privately and securely over Azure backbone .
  - PaaS services Processes or Store data falls under all data Classification.
  - Access PaaS/IaaS services with any PepsiCo approved authentication and encryption methods
  
### Limitations
  - Services involving high data Transfer volume requires security approval to bypass firewall.

### Example
  - Applications in Azure Subnet consuming Services Provided by Teradata, IRI and o9.
  - Azure VMs accessing Storage account, Azure SQL in 3rd party Subscriptions.
  
### Sample Use Cases
  - Disable access to 3rd Party Azure IaaS/PaaS services over public Internet
  - Access to 3rd Party Azure IaaS/PaaS services privately and securely from Azure vNet and on-prem networks.

### Additional Guardrails
  - TBD
  
## Technical Guidelines
  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/paas_3rdparty_privatelink.png)

## Related Patterns
  - Private Endpoint Pattern
  - Inbound Connection to Private Link Services from Subscription in Different AD Tenant 

## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 10 June 2025 (Sakshi Sharma)
