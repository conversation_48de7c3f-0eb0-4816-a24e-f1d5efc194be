---
weight: 2
title: "Databricks Isolated"
date: 2025-04-15
tags: ["azure", "database", "databricks", "isolated"]
summary: "Databricks Isolated"
---

## Cloud Pattern Classification

**Category of the Service**: Database
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: Isolated Databricks Pattern
**Description**: Databricks workspace is utilized to establish connections with data sources hosted in IaaS, either in the PepsiCo or a third-party Azure environment, and PaaS Services securely from an Isolated virtual network. This setup facilitates the transformation of data for consumption.

### Allowed PepsiCo Data Classification
  - Public
  - Internal
  - Confidential
  - Restricted

### Lifecycle
  - Release Date: October 7, 2014
  - Last Update Date: October 7, 2014
  - Decommission Date: Not applicable at this time.

## Goals & Benefits
  - Scalability and Flexibility
    An isolated vNet helps to scale a Databricks cluster to a large size without need to allocate a large number of routable IP addresses.
  - Integration with Azure Services
    Isolated vNets can connect to various Azure services, including PaaS services, via service endpoints or private endpoints.
  - Control Over Traffic Flow
    Secure connections to the internet and third-party services hosted outside of Azure will be directed through the isolated DMZ Palo from the Isolated vNet.

## Usage Guidelines

### When to use
  - Azure Databricks hosted in the Isolated vNet 
  - Azure Databricks don’t have use case to connect to On-Prem data sources or IaaS application that don’t support private link.

### Limitations
  - Databricks workspace won't be able to connect to on-prem data sources.

### Example
  - Consumption of PaaS Services (ADLS),Hive meta store hosted in Isolated databricks vNet

### Sample Use Cases
  - Databricks workspace establishes a connection with PaaS services via Service endpoint or Private endpoint. 
  - The Databricks Cluster interacts with the service over a private network. 
  - Databrick services make use of the IaaS services, which are located in the Pepsico enterprise or a third-party Azure environment and are accessed through privatelink. 
  - The Databricks workspace requires a substantial IP address space to process data across multiple cluster instances.

### Additional Guardrails
  - __Isolated DMZs are not intended to route or connect to enterprise networks; rather, they are intended to facilitate internet outbound from non-routable networks, as well as inbound to non-routable networks for future capability.__
  - There won't be any route in the isolated DMZ Firewall or Isolated vNet hosting databricks to connect to on-premises.
  - All the internet outbound traffic need to be routed via Isolated DMZ palo.
  - Subnet need to have NSG implemented with “Deny All” in priority 4096 for both Inbound and Outbound rule
  - There should not be any other services other than databricks and private endpoint to be deployed in isolated vNet.
  - There should not be any peering from Isolated vNet to Enterprise routable vNet.

## Technical Guidelines

  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/databricks_isolated.png)

## Related Patterns
  - Isolated DMZ pattern
  - Routable Databricks Pattern
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 7 Apr 2025 (Dariusz Korzun)
