---
weight: 11
title: "Expose to 3rd party over private link"
date: 2025-06-10
tags: ["azure", "network", "expose", "3rd party","private link"]
summary: "Expose PepsiCo PaaS Services to 3rd Party Services hosted in Azure over Private Link"
---

## Cloud Pattern Classification

**Category of the Service**: Network
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: Expose PepsiCo PaaS Services to 3rd Party Services hosted in Azure over Private Link.
**Description**: Expose PepsiCo Azure PaaS Services to 3rd Party IaaS/PaaS Services hosted in Azure over Private Link.

### Allowed PepsiCo Data Classification
  - Internal
  - Restricted
  - Confidential
  - Public

### Lifecycle
  - Release Date: July, 2022
  - Last Update Date: July, 2022
  - Decommission Date: Not applicable at this time.

## Goals & Benefits
  - Access to Azure PaaS services from 3rd party Subscription privately and securely over Azure backbone using private endpoint.
  - Access can only be established upon mutual approval by PepsiCo and the 3rd Party.
  - PepsiCo can always revoke the access at anytime unilaterally.

## Usage Guidelines

### When to use
  - Approved 3rd Party IaaS/PaaS services hosted in an Azure accessing Azure PaaS  Services hosted in PepsiCo Subscription.
  - PaaS services Processes or Store data falls under all data Classification.
  - Access PaaS services with any PepsiCo approved authentication and encryption methods.
  
### Limitations
  - Azure PaaS private link currently does not support a frontend Firewall.

### Example
  - IBM TM1 deployed in Azure accessing  PepsiCo Azure Synapse Workspace.
  
### Sample Use Cases
  - Access to Pepsico hosted PaaS services from 3rd Party Azure IaaS/PaaS services privately and securely
  - Disable access to Azure PaaS services over public Internet

### Additional Guardrails
  - TBD
  
## Technical Guidelines
  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/paas_3rdparty_privatelink_expose.png)

## Related Patterns
  - Private Endpoint Pattern
  - Outbound Connection to Private Link Services from Subscription in Different AD Tenant 

## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 10 June 2025 (Sakshi Sharma)
