---
weight: 4
title: "PaaS in remote region using PE with LR"
date: 2025-06-10
tags: ["azure", "network", "paas", "remote region", "private endpoint","local region"]
summary: "Connect to PaaS Services in remote region using Private Endpoint.With Consumption in local region."
---

## Cloud Pattern Classification

**Category of the Service**: Network
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: Connect to PaaS Services in remote region using Private Endpoint. With Consumption in local region.
**Description**: Enable IaaS/PaaS services in an Azure VNET to privately access Azure PaaS services outside of Azure VNET in the same and remote Azure region.

### Allowed PepsiCo Data Classification
  - Internal
  - Restricted
  - Confidential
  - Public

### Lifecycle
  - Release Date: July, 2022
  - Last Update Date: July, 2022
  - Decommission Date: Not applicable at this time.

## Goals & Benefits
  - Access to Azure PaaS services in same and remote region using Azure Private endpoints with public Internet is disabled.

## Usage Guidelines

### When to use
  - Any IaaS/PaaS services hosted in an Azure VNET access Azure PaaS services outside of Azure VNET in same and remote region.
  - PaaS services Processes or Store restricted data.
  - Access PaaS services within PepsiCo AD tenant.
  - Access PaaS services with any PepsiCo approved authentication and encryption methods.
  
### Limitations
  - Network bandwidth throttling at application layer need to be enabled for consumption from remote Azure region.

### Example
  - Azure VMs accessing Storage account in same and remote region.
  - Applications in ASE access Azure SQL DB in same and remote region.
  
### Sample Use Cases
  - Disable access to Azure PaaS services over public Internet
  - Access PaaS Services privately from Azure vNet in local and remote region.
  - Only Allow access to PaaS Services from selective IP addresses.
### Additional Guardrails
  - TBD
  
## Technical Guidelines
  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/paas_remoteregion_privateendpoint_local.png)

## Related Patterns
  - Private Endpoint Pattern
  - Cross region Private Endpoint pattern

## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 10 June 2025 (Sakshi Sharma)
