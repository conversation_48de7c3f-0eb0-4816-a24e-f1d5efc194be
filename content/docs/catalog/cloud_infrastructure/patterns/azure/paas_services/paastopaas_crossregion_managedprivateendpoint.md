---
weight: 9
title: "PaaS to Paas in Cross region using managed PE"
date: 2025-06-10
tags: ["azure", "network", "paas", "cross region", "private endpoint"]
summary: "PaaS connect to PaaS Services in/cross region using Managed Private Endpoint"
---

## Cloud Pattern Classification

**Category of the Service**: Network
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: PaaS connect to PaaS Services in/cross region using Managed Private Endpoint
**Description**:Enable private connection between PaaS services hosted outside of Pepsico vNet in same or Cross region.

### Allowed PepsiCo Data Classification
  - Internal
  - Public
  - Restricted
  - Confidential

### Lifecycle
  - Release Date: July, 2022
  - Last Update Date: July, 2022
  - Decommission Date: Not applicable at this time.

## Goals & Benefits
  - Access to Azure PaaS services through Azure Managed Private endpoints and public Internet is disabled
  - Access to Azure PaaS services is routed directly from Microsoft Managed vNet  using Azure backbone without going through firewalls or other network appliances.
  
## Usage Guidelines

### When to use
  - Access PaaS services within PepsiCo AD tenant.
  - Access PaaS services with any PepsiCo approved authentication and encryption methods.
  - Connect between PaaS Services within same or Cross region.
  - Applicable for all data Classification
  
### Limitations
  - Not applicable for data sources that require data packet inspection.
  - MPE currently breaks Service endpoint connection from PepsiCo vNet.

### Example
  - ADF or Synapse connecting Securely to approved PaaS Services.
  
### Sample Use Cases
  - Disable access to Azure PaaS services over public Internet.
  - Access PaaS Services without impacting PepsiCo network Appliances. 
  - Enable private connection between PaaS Services outside of PepsiCo vNet in same or Cross region.
  
### Additional Guardrails
  - TBD
  
## Technical Guidelines
  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/paas_crossregion_privateEP.png)

## Related Patterns
  - Cross region data transfer using service endpoint
  - Private Endpoint Pattern
  - Cross region Private Endpoint pattern.
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 10 June 2025 (Sakshi Sharma)
