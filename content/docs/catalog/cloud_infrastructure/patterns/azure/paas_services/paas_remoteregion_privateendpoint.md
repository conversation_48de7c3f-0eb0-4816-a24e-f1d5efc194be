---
weight: 4
title: "PaaS in remote region using PE"
date: 2025-06-08
tags: ["azure", "network", "paas", "remote region", "private endpoint"]
summary: "Connect to PaaS Services in remote region using Private Endpoint.No Consumption in local region."
---

## Cloud Pattern Classification

**Category of the Service**: Network
**Cloud Provider**: Microsoft Azure

## Cloud Pattern Overview

**Name**: Connect to PaaS Services in remote region using Private Endpoint. No Consumption in local region.
**Description**: Enable IaaS/PaaS services in an Azure VNET to privately access Azure PaaS services outside of Azure VNET cross region and there is no Consumption where services is hosted.

### Allowed PepsiCo Data Classification
  - Internal
  - Restricted
  - Confidential
  - Public

### Lifecycle
  - Release Date: July, 2022
  - Last Update Date: July, 2022
  - Decommission Date: Not applicable at this time.

## Goals & Benefits
  - Access to Azure PaaS services Cross region through Azure Private endpoints/Managed Private Endpoint with public Internet is disabled
  - There is no use case to access the PaaS Services in the same local Azure Region as PaaS Services.
  - Access to Azure PaaS services is routed directly from Azure subnet  in remote region over Azure backbone without going through firewalls or other network appliances.

## Usage Guidelines

### When to use
  - Any IaaS/PaaS services hosted in an Azure VNET access Azure PaaS services outside of Azure VNET in remote region.
  - PaaS services Processes or Store restricted data
  - Access PaaS services within PepsiCo AD tenant
  - Access PaaS services with any PepsiCo approved authentication and encryption methods
  
### Limitations
  - There can’t be more than one Private Endpoint attached from PepsiCo Routable network.

### Example
  - Azure VMs access Storage account Cross Region
  
### Sample Use Cases
  - Need to access PaaS Services in remote region, no need to access to PaaS services in same local region 
  - Disable access to Azure PaaS services over public Internet
  - Access PaaS Services without impacting PepsiCo network Appliances. 
  - Only Allow access to PaaS Services from selective IP addresses.
  
### Additional Guardrails
  - TBD
  
## Technical Guidelines
  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/paas_remoteregion_privateendpoint.png)

## Related Patterns
  - Connect to PaaS Services in Local and Remote region using Managed Private Endpoint
  - Connect to PaaS Services in Remote region using Private Endpoint. 

## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 8 June 2025 (Sakshi Sharma)
