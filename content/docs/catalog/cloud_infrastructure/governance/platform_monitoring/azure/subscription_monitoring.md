---
weight: 5
title: "Subscription Monitoring"
date: 2025-16-06
tags: ["multi cloud", "subscription", "monitoring"]
summary: "Subscription Monitoring"
---

## Subscription Monitoring Criteria

**Name**: Subscription Monitoring Criteria
**Objective**: Establish a comprehensive monitoring framework for Azure Subscriptions, ensuring visibility into the health, performance, and security aspects of the subscription level activities without delving into individual resources. The collected logs and metrics will be integrated with Azure Monitor for advanced analysis and visualization.

## Lifecycle
  - Published Date: 07-08-2025

## Scope
  - **Subscription Level Monitoring**: Focuses solely on subscription-wide events and metrics, excluding resource-specific data.
  - **Integration with Azure Monitor**: All logs and metrics are forwarded to an Log Analytics resource for indexing, storage, and analysis.
  - **Wiz Integration**: Utilizes Wiz to collect security and compliance logs, enriching the monitoring data sent to Log Analytics.
  - **Flexera Integration**: Utilizes Flexera to collect cost control data.

**Challenges:**
  - Clear definitions and best practices for how AWS OUs should be structured and managed across PepsiCo’s AWS environment are not fully defined. 
  - TThe current OU hierarchy, if not streamlined, introduces long-term complexity in managing AWS Service Control Policies (SCPs), centralized logging, identity boundaries, and security posture enforcement.

## Standards
 **Activity Logs Collection:**
   - **Description**: Capture all control-plane activities, including management operations initiated by the Azure Resource Manager.
   - **Components**:
      - Administrative Activities
      - Service Health Alerts
      - Resource Health Alerts
      - Configuration Changes

 **Role Assignments Monitoring:**
   - **Description**: Track the number and changes of Azure role assignments within the subscription to maintain control over access and permissions.
   - **Components**:
      - Total Count of Role Assignments
      - Role Assignment Changes (Additions, Removals, Modifications)
      - Role Assignment by Type (Built-in roles vs. Custom roles)
   - **Integration**: Configure Azure Monitor or a custom solution to capture role assignment events and forward them to Log Analytics.

 **Usage and Quotas Monitoring**:
   - **Description**: Monitor the consumption of Azure services against their quotas to prevent service disruptions and manage resource scalability effectively.
   - **Components**:
       - Monitor the consumption of Azure services against their quotas to prevent service disruptions and manage resource scalability effectively.
       - Quota Utilization Percentages by Service
       - Near-Quota and Over-Quota Alerts
   - **Integration**:Configure Azure Monitor or a custom solution to capture role assignment events and forward them to Log Analytics. Track usage and quotas, configuring alerts to notify of potential quota breaches.

 **Wiz Security and Compliance Logs**:
   - **Description**: Leverage Wiz to gather comprehensive security and compliance logs, providing insights into the subscription's security posture and compliance status.
   - **Components**:
       - Security Alerts and Recommendations
       - Compliance Assessment Reports
       - Vulnerability Scans
       - Azure Security Center Integration
       - Regulatory Compliance Dashboard

 **Diagnostic Settings**:
   - **Description**: Enable diagnostic settings at the subscription level to route logs and metrics to Log Analytics.
   - **Components**:
       - Subscription Log Profile
       - Export to Event Hub
   - **Integration**: Direct Integration with Log Analytics (via Azure Function if needed)

 **Azure Monitor Alerts**:
   - **Description**: Leverage Kibana to create alert rules based on metrics or logs that signify unusual activities or potential issues within the subscription.
   - **Components**:
       - Metric Alerts (for supported subscription-level metrics)
       - Activity Log Alerts (for specific events or operations)
       - Administrative Actions (e.g., changes in service health or RBAC roles)
   - **Integration**: Direct Integration with Log Analytics (via Azure Function).

 **Cost Management and Billing Alerts**:
   - **Description**: Monitor and manage Azure costs and billing data to avoid unexpected charges and to maintain budget compliance using Flexera.
   - **Components**:
       - Budget Alerts
       - Cost Anomalies Detection
       - Department and Account Tagging Rules
   - **Integration**: Direct Integration with Flexera.
 
## Dashboard Structure
 - **Overview Dashboard**:
   - Summary Widgets: Subscription Health, Activity Volume
   - Key Metrics: Service Health Incidents, Active Alerts, Compliance Status
 - **Activity Logs Dashboard**:
   - Filters: By Type, Date Range, Severity
   - Visualizations: Administrative Activities, Policy Changes, Resource Health Alerts
 - **Roles Assignment Dashboard**:
   - Filters: By Role Type, Date Range
   - Visualizations: Total Role Assignments Over Time, Recent Role Assignment Changes, Distribution of Roles.
 - **Usage and Quotas Dashboard**:
   - Filters: By Service, Time Period.
   - Visualizations: Service Usage Trends, Quota Utilization by Service, Alerts for Near or Exceeded Quotas
 - **Cost Management Dashboard**:
   - Filters: By Service, Time Period, Department/Tag.
   - Visualizations: Monthly Cost Trends, Budget vs. Spend, Cost Anomalies, Top 5 Services by Cost.
 - **Security and Compliance Dashboard**:
   - Filters: By Compliance Standard, Security Alert Severity.
   - Visualizations: Compliance Status Over Time, Open Security Alerts, Remediation Actions, Non-Compliant Resources.
 - **Performance and Availability Dashboard**:
   - Key Metrics: Subscription-wide Availability, Performance Trends.
   - Visualizations: Service Health Status, Impact Analysis, Availability Trends.

## Dashboard Access and Permissions
 - To define access controls and permissions for different user groups, namely Engineering, Operations, and Security teams, ensuring that each team has appropriate access to relevant data and insights within the monitoring dashboards. This segregation enhances operational security, data privacy, and ensures that team members can efficiently perform their roles with the necessary information.
 ### Scope
  - **Dashboard Access Controls**: Implement granular access controls to the Azure dashboards, tailored to the roles and responsibilities of the Engineering, Operations, and Security teams.
  - **Role-Based Permissions**: Assign permissions based on the principle of least privilege, ensuring users have access only to the data and functionalities essential for their duties.

 ### Standards
  - **Engineering Team Permissions**:
     - **Description**: Engineers require access to performance, availability, and usage metrics to optimize application and service deployments.
     - **Access Rights**:
       - Full access to the Overview Dashboard, with insights into subscription health, cost overview, and service health incidents.
       - Read-only access to the Cost Management Dashboard.
       - Full access to the RBAC, Usage and Quotas Dashboard.
       - Limited access to the Performance and Availability Dashboard for operational metrics.
  - **Security Team Permissions**:
     - **Description**: The Security team requires comprehensive access to security, compliance, and access control data to uphold the organization's security posture and compliance standards.
     - **Access Rights**:
       - Full access to the Wiz Security and Compliance Dashboard.
       - Full access to the Role Assignments Dashboard.
       - Read-only access to the Overview Dashboard with an emphasis on security alerts and compliance status.
       - Limited access to Activity Logs Dashboard for security-related events.

 ## Best Practices
  - Regularly review and update dashboard access controls to reflect changes in team structures, responsibilities, and organizational policies.
  - Ensure that the principle of least privilege is rigorously applied to prevent unauthorized access to sensitive data.
  - Provide training and documentation for each team to maximize the utility and security of the monitoring dashboards.
  - By establishing clear and secure access controls for the Engineering, Operations, and Security teams, the organization can maintain a high level of security and operational efficiency. These tailored permissions ensure that each team has the necessary insights to perform their roles effectively, while safeguarding sensitive data and compliance with organizational policies

## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 18 August 2025 (Sakshi Sharma)
