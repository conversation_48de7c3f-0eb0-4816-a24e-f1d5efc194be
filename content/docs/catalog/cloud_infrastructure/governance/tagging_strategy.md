---
weight: 2
title: "Cloud Tagging Strategy"
date: 2025-01-07
tags: ["multi cloud", "tagging strategy", "", ""]
summary: "Cloud Tagging Strategy"
---

## Tagging Strategy Overview

**Name**: Tagging in Cloud
**Description**: Tagging in cloud is a critical practice for managing and organizing resources efficiently. Tags allow administrators and developers to categorize resources for easier management and cost allocation. Proper tagging is essential for security, automation, and operational efficiency.

### Lifecycle
  - Published Date: 19-05-2025

### Tagging 3.0
   | No | TAG Name                   | Tag Values                                          | Mandatory | Subscription | Resource Group | Resource | Description                                                    | Requested by         |
|----|----------------------------|-----------------------------------------------------|-----------|--------------|----------------|----------|----------------------------------------------------------------|----------------------|
| 1  | pep-environment            | production, staging, qa, development, test, sandbox*, dr | ❌        | ✅           | ✅             | ❌ (✅ for PGT only) | Environment Tag                                                | Cloud Operations     |
| 2  | servicename                | <Service Name Value>                                | ✅        | ✅           | ✅             | ❌       | Name of the application service                                | Cloud FinOps         |
| 3  | pep-allocation             | shared/null                                         | ❌        | ✅           | ✅             | ✅       | Indicates resource allocation model (shared/dedicated)         | Cloud FinOps         |
| 4  | pep-subscriptiontype       | enterprise, legacy                                  | ✅        | ✅           | ❌             | ✅       | Type of subscription                                           | Cloud FinOps         |
| 5  | pep-dataclassification     | internal, confidential, public, restricted          | ❌        | ❌           | ❌             | ✅       | Data sensitivity level                                         | Information Security |
| 6  | pep-cmdb-bus-app-name      | SNOW provided Business App Name                     | ✅        | ❌           | ✅             | ✅       | CMDB business application name                                 | Cloud Engineering    |
| 7  | pep-cmdb-bus-app-id        | SNOW provided Business App ID                       | ✅        | ❌           | ✅             | ✅       | CMDB business application ID                                   | Cloud Engineering    |
| 8  | pep-cmdb-app-service-name  | SNOW provided App Service Name                      | ✅        | ❌           | ✅             | ✅       | CMDB app service name                                          | Cloud Engineering    |
| 9  | pep-cmdb-app-service-id    | SNOW provided App Service ID                        | ✅        | ❌           | ✅             | ✅       | CMDB app service ID                                            | Cloud Engineering    |
| 10 | pep-business-category*     | make, move, sell, all                               | ❌        | ❌           | ✅             | ✅       | High-level business process alignment                          | Cloud Operations     |
| 11 | pep-cmdb-ci-servicetype    | business / technical                                | ❌        | ❌           | ✅             | ✅       | ServiceNow tag to differentiate between technical and business | Cloud Engineering    |

### Pep-Business-Category
This tag is used to classify applications based on their business function within PepsiCo:
- **Make**: Applications supporting **PepsiCo Factory** operations.
- **Move**: Applications supporting **PepsiCo Logistics**.
- **Sell**: Applications supporting **PepsiCo Sales**.
- **All**: Global applications supporting **backend systems** and do **not belong** to any of the above categories.

### Tag Inheritance in Azure
Azure resources do not automatically inherit tags applied to their **resource group** or **subscription**. To implement tag inheritance, you can use **Azure Policy** to propagate tags to child resources.

> **Note:** Some third-party cloud cost management tools like **Flexera** offer native tag inheritance features.To inherit tags from a subscription or resource group to the resources, refer to (https://learn.microsoft.com/en-us/azure/governance/policy/concepts/definition-structure#tagging)

### Tagging Scenarios

| **Scenario**                         | **Example**                                                                                      | **Approach**                                                                 |
|--------------------------------------|--------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------|
| One-to-one tagging               | One tag to one resource group                                                                    | Apply the tag directly on the resource group                                 |
| Multiple tags to one resource group | Multiple `pep-cmdb-app-name` tags for a resource group hosting resources for multiple apps       | Apply tags at the resource level instead of the group                    |
| Multiple tags to one cluster/solution | A VM cluster used by PGT, Data-Hub, and SNOW                                                     | Break the cluster into multiple logical solutions, each with its own tag |
  
## Guidelines
  | Requirement                      | Current Azure Guidelines                       | AWS Capabilities                        | GCP Capabilities                          | Alibaba Capabilities                        | Final Guideline                                                  |
|----------------------------------|-----------------------------------------------|-----------------------------------------|-------------------------------------------|---------------------------------------------|------------------------------------------------------------------|
| Tag Key Character Limit          | 512 chars (128 for storage accounts)           | 128 chars                               | 63 chars                                  | 128 chars                                   | Reduce to 63 chars for all cloud vendors                         |
| Tag Value Character Limit        | 256 chars                                      | 256 chars                               | 63 chars                                  | 128 chars                                   | Reduce to 63 chars for all cloud vendors                         |
| Restricted Characters            | `<`, `>`, `%`, `&`, `\`, `?`, `/`              | No strict restrictions (varies by service) | Only lowercase, numbers, dashes, underscores | Only letters, numbers, `-`, `_`, `.`        | Follow stricter (Azure) character rules for all cloud vendors   |
| Case Sensitivity                 | Case-insensitive                               | Case-sensitive                          | Case-sensitive                            | Case-sensitive                              | Enforce lowercase for consistency across all cloud vendors       |
| Spaces Allowed in Tag Keys       | Not allowed (replace with `-`)                | Allowed                                  | Not allowed                               | Allowed                                     | Not allowed (replace with `-`)                                  |
| Spaces Allowed in Tag Values     | Not allowed (replace with `-`)                | Allowed                                  | Not allowed                               | Allowed                                     | Not allowed (replace with `-`)                                  |
| Tag Inheritance                 | Supported at the resource group level         | No automatic inheritance                | No automatic inheritance                  | No automatic inheritance                    | Manually enforce tag consistency across providers                |
| Tag Governance & Enforcement     | Azure Policy can enforce tagging rules         | Enforced via SCPs, AWS Organizations     | No built-in enforcement (custom automation needed) | Enforced via RAM policies            | Use tagging policies where available, automate governance        |
| Multiple Values in a Single Tag  | Not supported                                  | Not supported                            | Not supported                              | Not supported                                | Keep as single-value key pairs across all clouds                 |

## Multi-Cloud Tagging Policy

### Tag Structure & Length

- **Tag Names**: Maximum **63 characters**  
  (To comply with **GCP's** strictest limitation)
- **Tag Values**: Maximum **63 characters**  
  (Standardized across all cloud platforms)

## Naming Conventions

- Use **only lowercase letters**, **numbers**, and **dashes** (`-`)
- **Do not use spaces** in tag names or values  
- Replace spaces with dashes (`-`)
- **Do not use special characters**: `<`, `>`, `%`, `&`, `\`, `?`, `/`

### Examples

| Valid Tag                   | Invalid Tag                          |
|----------------------------|---------------------------------------|
| `environment=production`   | `Environment = Production` (case + space) |
| `app-id=customer-portal`   | `App_ID=Customer Portal` (case + space + underscore) |

---

## Exceptions

- Some **AWS** resources do not support tagging (e.g., **default VPCs**, **IAM roles**)  
  - Use alternative tracking mechanisms where needed  
  - Always **check if the resource generates cost**

## Governance & Enforcement

- All new tag keys must be reviewed and approved by Cloud Engineering

### Tag Policies & Enforcement

| Cloud Platform | Enforcement Mechanism                       |
|----------------|---------------------------------------------|
| **Azure**      | Azure Policy                                |
| **AWS**        | AWS Organizations Tag Policies (SCP-based)  |
| **GCP**        | Labels (case-sensitive); enforce via IAM    |

---

## Automation & Compliance

- Use **Infrastructure as Code (IaC)** to enforce tag standards:
  - **Terraform**
  - **CloudFormation**
- Integrate tagging into deployment pipelines and CI/CD

## Non-Compliance Actions

Non-compliant tags will trigger alerts and may result in:

- **Adding missing mandatory tags**
- **Renaming** incorrectly formatted tags
- **Escalating violations** to Cloud Operations
- **Removing resources** in severe or repeated cases

## AWS Tagging Note
In AWS, certain resources do not have a dedicated `Name` field and rely on tags (`Name`) for identification. While this is optional, it is recommended to use tags following the standardized naming conventions to maintain consistency, improve resource visibility, and facilitate automation.
By applying a `Name` tag that adheres to the defined naming guidelines, teams can ensure better tracking and management across cloud environments.
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 1 July 2025 (Sakshi Sharma)
