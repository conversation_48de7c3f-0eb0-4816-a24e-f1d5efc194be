---
weight: 5
title: "GCP Folder Structure"
date: 2025-16-06
tags: ["multi cloud", "GCP", "management"]
summary: "GCP Folder Structure"
---

## GCP Folder Structure 

**Name**: GCP Folder Structure 
**Description**: A Google Cloud landing zone is the foundational setup of a multi-project GCP environment that incorporates scalability, security, governance, networking, and identity. A GCP landing zone facilitates application migration, modernization, and innovation at enterprise scale. It includes all platform resources needed to support the customer’s application portfolio, regardless of whether workloads use infrastructure as a service or platform as a service.

## Lifecycle
  - Published Date: 07-08-2025

## Folders
  - Folders in Google Cloud help you structure your environment for organization and governance at scale. Considering how to use folders as part of your environment design is a critical foundational step. Use the following guidelines to inform your cloud architecture decisions.

**Challenges:**
  - Clear definitions and best practices for managing folders within PepsiCo’s current GCP estate are not yet defined. The current folder structure complexity in PepsiCo’s GCP environment may hinder efficient management of security and policy enforcement over time.

### Requirements
 **Management:**
  - Platform and management services should reside in separate folders from workload/application projects. 
  - Long-term operations for cloud management, security, and governance tooling must support GCP, hybrid, or multi-cloud scenarios.
  - Core services should include Security, Infrastructure, and Network folders.
  - The architecture should clearly depict the distinction between organizational layers.

 **Security:**
  - Testing/Sandbox projects must meet a minimum baseline of security controls, with the ability to relax these temporarily for engineering and architecture use cases.
  - All PepsiCo-managed resources must comply with defined security requirements.
  - Legacy projects or non-compliant resources must follow a formal exception process.
  - Update IAM policies and roles in accordance with folder/project structure changes.

 **Isolation of Business Critical Areas:**
  - Shared infrastructure (e.g., Palo Alto, TGW, Direct Connect) must be isolated in pep-connectivity-ou.
  - Business-critical workloads (e.g., SAP, DataHub, PGT) should be isolated into distinct folders under Enterprise Applications.

 **Cloud Finance:**
  - At the organization root, enforce the “serviceName” label, at the project level, except for legacy folders.
  - Legacy folders should enforce the “serviceName” label at the billing account or project level.

 **Lifecycle:**
  - Centralized management of policies, access, and compliance across multiple projects using Organization Policies and IAM.
  - Clear policy lifecycle allowing thorough testing of organization policies in non-production environments (e.g., Sandbox and Testing projects).
    
 ### Enviornment Folder Structure on the lower layer design consideration
  - **Policy Testing Isolation:** Lower-level environment groups enable policy testing in non-prod without risking production stability across sectors.
  - **Granualar Sector Control:** They allow compliance and governance to be tailored per sector and per environment (e.g., different baselines for SAP vs. PFNA).
  - **RBAC Segregation:** RBAC can be scoped per environment and per sector (via their Folder Structure), enforcing least privilege and clear separation of duties across both dimensions.
  - **Reusability and Scalability:** This structure promotes modular policy assignment, making templates reusable across multiple sectors and environments.

## Folder Structure Design
 ### **Folder Design Considerations**
  - Folder structures within a Google Cloud Organization support hierarchical mapping of your enterprise structure. Carefully design your folder hierarchy to align with your organization’s GCP adoption and governance strategy at scale.
  - A folder hierarchy can support up to 10 levels of depth, not including the organization root or individual projects.
  - You can use folders to apply organization policies and IAM roles, which will propagate down to all nested folders and projects.
  - All new projects should be created within the appropriate folder to avoid default placement directly under the organization root.
  - Each folder or project can have only one parent.
  - Each folder can contain multiple sub-folders or projects.

 ### **Folder Hierarchy Guidelines**
  - Keep the folder hierarchy reasonably flat, ideally no more than three levels deep, to reduce operational complexity and management overhead.
  - Avoid replicating the corporate org structure in the folder hierarchy. Design folders primarily for policy enforcement and access control, not for billing.
  - Create top-level folders under the organization root to represent different workload types (e.g., Enterprise Applications, Shared Services, Sandbox, etc.).
  - Use labels on projects and resources to enable horizontal navigation and querying across the folder structure. Reference PepsiCo’s GCP labeling standards.
  - Create a dedicated Sandbox folder at the top level so users can safely experiment without impacting production environments.
  - Define a Platform folder under the root to centralize shared services, policies, and IAM role bindings for foundational infrastructure.
  - Limit organization policy bindings at the root level to reduce complexity in policy inheritance and debugging at lower levels.
  - Use organization policies at folder or project level to enforce compliance, ensuring policy-driven governance.
  - Restrict folder and policy administration to privileged users only using IAM role bindings at appropriate scopes.
  - Configure a default onboarding folder for new project placements to prevent projects from defaulting to the root. This is especially important for test/dev users with GCP trial credits or developer entitlements.
  - Create folders for production, testing, and development environments only if there are multiple projects per environment. For example, if “DataHub” has multiple non-prod projects, you can create a third-level folder under “DataHub” like “non-prod”.
  - If absolutely necessary for testing sensitive business applications, the operations team may manage policies or access directly at the resource level (e.g., within a project)*

  **Note: Not recommended — maintain governance at higher levels where possible.*

![Visual Representation](/images/Folder_Structure.png)

 ### **High Level Folder Structure**

| Folder Name               | Description                                                                                                                                     | Parent                      |
|----------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------|
| Tenant Root Organization   | Root tenant account                                                                                                                            | N/A                         |
| pep-coreservices-fd        | Contains all platform child folders/projects such as connectivity, infrastructure, or management.                                               | Tenant Root Organization    |
| pep-cybersecurity-fd       | Dedicated child folder for Security team projects.                                                                                             | Tenant Root Organization    |
| pep-enterpriseapplications-fd | Parent folder for all workload folders such as SAP, Data Hub, Sector/Region, or Global Production subscriptions.                                | Tenant Root Organization    |
| pep-legacy-fd              | Landing zones created outside of the Cloud Adoption Program (managed by a different MSP).                                                       | Tenant Root Organization    |
| pep-automation-fd          | Hosts automation landing zones (e.g., CMP) that serve PepsiCo cloud environments.                                                              | Tenant Root Organization    |
| pep-googleapplications-fd  | Landing zones/projects hosting Google services (e.g., Cloud Source Repositories, Firebase Test Lab).                                            | Tenant Root Organization    |
| pep-sandbox-fd             | Testing/exploration projects. Disconnected from corporate/online landing zones, with relaxed policies.                                          | Tenant Root Organization    |
| pep-learning-fd            | Learning/training projects (short-lived, < 1 month).                                                                                           | Tenant Root Organization    |
| pep-isolated-fd            | Isolated landing zones not following enterprise governance/security/operations.                                                                | Tenant Root Organization    |
| pep-decommissioned-fd      | Cancelled landing zones. Projects moved here before deletion (30–60 days).                                                                     | Tenant Root Organization    |
| pep-<sector/market>-fd     | Sector/market-specific workload folders (e.g., pep-na-fd, pep-emea-fd).                                                                        | pep-enterpriseapplications-fd |

 ## Project Creation Criteria
  -A GCP project is an isolated environment that organizes cloud resources, billing, and access control.
  - Project Naming Convention: 
     - Format: pep-<environment>-<scope/sector/app>-<sequence number>-prj
     - Example: pep-prod-snt-01-prj, pep-prod-pbna-01-prj, pep-prod-infratools-01-prj

 ### **When to Create a new GCP Project**:
   Below you can find a set of criteria to decide if you should create a new Google Project?
  - Will or are already the resource quotas (e.g. CPU, IPs, API calls) or hard service limits (e.g. App Engine region lock, service account caps) likely exceed project limits?
    - a. → Yes → Create new project under the same Project Folder
    - b. → No → Continue
  - Are IAM permissions or security boundaries different (e.g. different teams, different access controls)?
    - a. → Create new project unde the same Project Folder
    - b. → No → Continue
  - Do you need to apply different regulatory, compliance such as ISO/GDPR/HIPPA/PCI?
    - a. → Yes →  Create new project under a new Folder under respective 1st layer Folder
    - b. → No → Continue
  - Is it a new environment stage (Dev/Test/Prod)?
    - a. → Yes → Create new project under sector/application name Folder
    - b. → No → Continue
  - Is this for a temporary, isolated, or POC (Proof of Concept) workload?
    - a. → Yes → Create a separate project under Sandbox Folder
    - b. → No → Continue

  All No → Reuse existing project, maybe isolate via labels, VPC or subnets.

 ### **Project Limitations**
 | Service Area         | Limit Description                         | Limit Value                         |
|-----------------------|-------------------------------------------|-------------------------------------|
| Compute Engine        | Default CPUs per project per region       | 24 CPUs (default)                   |
| Compute Engine        | Instance groups per project/region        | 200                                 |
| Compute Engine        | VM templates per project                  | 1500                                |
| Compute Engine        | Static external IPs per region            | 7                                   |
| Compute Engine        | Persistent Disks per project/region       | 50 TB                               |
| GKE                   | Clusters per project                      | 100                                 |
| GKE                   | Nodes per cluster                         | 5000                                |
| GKE                   | Pods per node                             | 110                                 |
| App Engine            | App Engine apps per project               | 1 (region locked)                   |
| App Engine            | Versions per service                      | 528                                 |
| App Engine            | Services per project                      | 63                                  |
| Cloud Functions       | Functions per project                     | 1000                                |
| Cloud Functions       | Memory per function                       | 16 GB                               |
| Cloud Run             | Services per project                      | 1000                                |
| Cloud Run             | Revisions per service                     | 1000                                |
| Cloud Run             | Max requests/sec per service              | 10,000 RPS                          |
| VPC Networking        | VPC networks per project                  | 5 (can increase)                    |
| VPC Networking        | Subnets per VPC                           | 15,000                              |
| VPC Networking        | Firewall rules per project                | 2000                                |
| VPC Networking        | Routes per VPC                            | 200 (100 dynamic, 100 static)       |
| IAM & Identity        | Service accounts per project              | 5000                                |
| IAM & Identity        | IAM policy bindings per project           | 3000                                |
| IAM & Identity        | Custom IAM roles per project              | 300                                 |
| Cloud Storage (GCS)   | Buckets                                   | Unlimited                           |
| Cloud Storage (GCS)   | Objects                                   | Unlimited                           |
| Cloud Storage (GCS)   | Object size                               | 5 TB                                |
| BigQuery              | Datasets per project                      | 5000                                |
| BigQuery              | Tables per project                        | 50,000                              |
| BigQuery              | Daily streaming insert per table          | 1 GB/day free                       |
| Cloud Pub/Sub         | Topics per project                        | 10,000                              |
| Cloud Pub/Sub         | Subscriptions per project                 | 10,000                              |
| Cloud Pub/Sub         | Message size                              | 10 MB                               |
| Cloud Spanner         | Spanner instances                         | Scalable, no hard limit             |
| Other                 | Secrets in Secret Manager                 | 10,000                              |
| Other                 | Scheduler jobs per project                | 5000                                |
| Other                 | API Rate limits vary by service           | Varies                              |


## Ownership and Version Control

### **Service Architect**
  - <NAME_EMAIL>

### **Version Control**
  - v.1: 18 August 2025 (Sakshi Sharma)
