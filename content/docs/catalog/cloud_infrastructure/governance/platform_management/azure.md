---
weight: 5
title: "Azure Management Groups"
date: 2025-16-06
tags: ["multi cloud", "azure", "management"]
summary: "Azure Management Groups"
---

## Azure Management Groups

**Name**: Azure Management Groups
**Description**: An Azure landing zone is the output of a multi-subscription Azure environment that accounts for scale, security governance, networking, and identity. An Azure landing zone enables application migration, modernization, and innovation at enterprise-scale in Azure. This approach considers all platform resources that are required to support the customer's application portfolio and doesn't differentiate between infrastructure as a service or platform as a service.

## Lifecycle
  - Published Date: 07-08-2025

## Management Groups
  - [Management groups](https://learn.microsoft.com/en-us/azure/governance/management-groups/overview) are a tool to help you structure your cloud enviornments for oragnization and governance at scale.
  - Considering how to use management groups as part of the environment design is an important foundational step. Use the following guidelines to help make your cloud architecture decisions.

**Challenges:**
  - Clear definitions and best practices applied to PepsiCo's current cloud estate on how Management Groups should be managed are not defined. 
  - The current PepsiCo Management Group structure complexity will make it harder in the long term to manage Azure security and governance policy enablement roadmap efficiently

### Requirements
 **Management:**
  - Management or platform deployments to be located in separate management groups from workload or application resources. 
  - Long-term cloud operations for cloud management, security, and governance tooling to manage Azure, hybrid, or multi-cloud solutions.
  - Core Service to include Security, Infrastructure and Network management groups
  - Depict the difference between layers.

 **Security:**
  - Testing/Sandbox subscription to have a minimum baseline of security requirements that can be easily disabled if required for engineering and architecture purposes.
  - Everything that is PepsiCo Managed has to be security compliant according to the security requirements
  - Legacy subscriptions or any resources that are not compliant to have enforced the "Exception process"
  - Update RBAC Policy with the changes in the management groups

 **Isolation of Business Critical Area:**
  - PepsiCo shared network resources such as Palo Alto or region virtual network hubs to be located in the Connectivity management group
  - Business critical workloads such as SAP, DataHub, PGT to be isolated each in one management group under Enterprise Applications. 

 **Cloud Finance:**
  - At the root level to enforce the "serviceName" tag at the Resource Group level, with the exception of Legacy management groups
  - Legacy management group to enforce the "serviceName" tag at the subscription level.

 **Lifecycle:**
  - Managing policies, access, and compliance across subscriptions at scale
  - Clear Azure Policy lifecycle that will allow us to thoroughly test policies in lower environments such as Sandbox and Testing subscriptions.

 ### Enviornment Folder Structure on the lower layer design consideration
  - **Policy Testing Isolation:** Lower-level environment groups enable policy testing in non-prod without risking production stability across sectors.
  - **Granualar Sector Control:** They allow compliance and governance to be tailored per sector and per environment (e.g., different baselines for SAP vs. PFNA).
  - **RBAC Segregation:** RBAC can be scoped per environment and per sector (via their Folder Structure), enforcing least privilege and clear separation of duties across both dimensions.
  - **Reusability and Scalability:** This structure promotes modular policy assignment, making templates reusable across multiple sectors and environments.

## Management Group Design
 ### **Management Group Design Consideration**
  - Management group structures within an Azure Active Directory (Azure AD) tenant support organizational mapping. Consider your management group structure thoroughly as your organization plans its Azure adoption at scale.
  - A management group tree can support up to [six levels of depth](https://learn.microsoft.com/en-us/azure/governance/management-groups/overview#hierarchy-of-management-groups-and-subscriptions). This limit doesn't include the tenant root level or the subscription level.
  - You can use management groups to apply policies and initiative assignments via Azure Policy to all subscriptions under management group.
  - All new subscriptions must be placed under the proper management group to overwrite the default placement by Azure under tenant root management group.
  - Each management group and subscription can only support one parent.
  - Each management group can have many management groups or subscriptions

 ### **Management Group Guidelines**
  - Keep the management group hierarchy reasonably flat, ideally with no more than three to three levels. This restriction reduces management overhead and complexity.
  - Avoid duplicating your organizational structure into a deeply nested management group hierarchy. Use management groups for policy assignment versus billing purposes.
  - Create management groups under your root-level management group to represent the types of workloads that you'll host.
  - Use resource tags to query and horizontally navigate across the management group hierarchy. Link to PepsiCo Azure tags
  - Create a top-level sandbox management group so that users can immediately experiment with Azure
  - Create a platform management group under the root management group to support common platform policy and Azure role assignment. This grouping structure ensures that different policies can be applied to the subscriptions used for your Azure foundation.
  - Limit the number of Azure Policy assignments made at the root management group scope. This limitation minimizes debugging inherited policies in lower-level management groups.
  - Use policies to enforce compliance requirements either at the management group or subscription scope to achieve policy-driven governance.
  - Ensure that only privileged users can operate management groups in the tenant. Enable Azure RBAC authorization in the management group
  - Configure a default, dedicated management group for new subscriptions. This group ensures that no subscriptions are placed under the root management group. This group is especially important if there are users eligible for Microsoft Developer Network (MSDN) or Visual Studio benefits and subscriptions.​​​​​​​
  - Management groups for production, testing, and development environments should only be created if multiple same environment subscriptions are provisioned, e.g.: multiple NON-PROD Subscription for Data-Hub, a 3rd layer Management Group can be created under "Data-Hub" with the name or the environment (non-prod, prod or DR)
  - If only needed for testing purposes operations team can go up to the resource group level in case of a sensitive business application*
  *(not recommended)

![Visual Representation](/images/Management_Group.png)

 ### **High Level Management Group Structure**
   | Management Group Name       | Description                                                                                                                                                                                                                  | Parent                     |
|-----------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------|
| Tenant Root Group           | Root tenant account, this should be used only as the parent of the PepsiCo root management group.                                                                                                                            | N/A                        |
| pep-root-mg                 | PepsiCo Root Management Group.                                                                                                                                                                                               | Tenant Root Group          |
| pep-coreservices-mg         | Contains all the platform child management groups such as connectivity, infrastructure or management.                                                                                                                         | pep-root-mg                |
| pep-cybersecurity-mg        | Contains a dedicated subscription for Security team.                                                                                                                                                                          | pep-root-mg                |
| pep-enterpriseapplications-mg | Parent for all workload management groups (e.g., SAP, Data Hub, Sector/Region, Global Production). Will have workload-agnostic Azure policies assigned to ensure workloads are secure and compliant.                        | pep-root-mg                |
| pep-<sector/market>-mg      | Sector/market-specific management groups under Enterprise Applications.                                                                                                                                                       | pep-enterpriseapplications-mg |
| pep-legacy-mg               | Dedicated group for legacy landing zones not part of the Cloud Adoption Program, managed by a different MSP.                                                                                                                  | pep-root-mg                |
| pep-automation-mg           | Dedicated group for automation landing zones (e.g., CMP serving PepsiCo cloud landing zones).                                                                                                                                 | pep-root-mg                |
| pep-microsoftapplications-mg | Dedicated group for Microsoft service landing zones (e.g., App Center, DevOps, or similar).                                                                                                                                  | pep-root-mg                |
| pep-sandbox-mg              | Dedicated group for sandbox subscriptions used for testing and exploration. Securely disconnected from enterprise landing zones. Less restrictive policies for flexibility.                                                   | pep-root-mg                |
| pep-learning-mg             | Dedicated group for training/learning subscriptions with a short lifecycle (< 1 month).                                                                                                                                       | pep-root-mg                |
| pep-isolated-mg             | Dedicated group for isolated landing zones outside PepsiCo enterprise governance/security.                                                                                                                                    | pep-root-mg                |
| pep-decommissioned-mg       | Dedicated group for cancelled landing zones. Subscriptions moved here before deletion (30–60 days).                                                                                                                           | pep-root-mg                |


 ## Subscription Creation Criteria
  - A Azure Subscription is an isolated environment that organizes cloud resources, billing, and access control.
  - Subscription Naming Convention: 
     - Format: pep-<environment>-<scope/sector/app>-<sequence number>-sub
     - Example: pep-prod-snt-01-sub, pep-prod-pbna-01-sub, pep-prod-infratools-01-sub

| **Category**                     | **Criteria / Use Case**                                                                 | **Env**           | **Naming Pattern (Example)**                |
|----------------------------------|------------------------------------------------------------------------------------------|-------------------|---------------------------------------------|
| Large Sectors & App Platforms    | Dedicated subscription per large sector / app platform (PBNA, PFNA, etc.)                | Prod / Non-Prod   | `pep-<sector>-<env>-sub` <br> e.g. `pep-pbna-prod-sub` |
| Small Sectors & App Platforms    | Shared continental subscription (Americas, APAC, Europe)                                | Prod / Non-Prod   | `pep-<continent>-<env>-sub` <br> e.g. `pep-am-prod-sub` |
| Enterprise Data & Analytics      | Dedicated for Enterprise Data Platforms (Teradata) & Infra (PBI GW, ADF IR, etc.)        | Prod / Non-Prod   | `pep-datahub-<env>-sub` <br> e.g. `pep-datahub-np-sub` |
| Data Foundation & Digital Prod   | Dedicated for Data foundation / landing zone & Digital products                          | Prod / Non-Prod   | `pep-snt-<env>-sub` <br> e.g. `pep-snt-prod-sub` |
| Modern Data Integration Platform | Dedicated per sector / continent for MDIP & Analytics apps                               | Prod / Non-Prod   | `pep-mdip-<sector/continent>-<env>-sub` <br> e.g. `pep-mdip-pbna-prod-sub` |
| Connectivity Infra Services      | Dedicated for ExpressRoute, Firewall, Ext. AppGW                                         | Prod              | `pep-sharedsvc-prod-sub` |
| Identity Services                | Dedicated for IDX, AD, identity solutions                                                | Prod              | `pep-identity-prod-sub` |
| SAP Workload                     | Dedicated for SAP workloads (e.g., PGT)                                                  | Prod / Non-Prod   | `pep-sap-<env>-sub` |
| Management Workload              | Dedicated for native mgmt solutions (Log Analytics, Workbooks, etc.)                     | Prod              | `pep-mgmt-prod-sub` |
| OpenAI Application               | Dedicated for OpenAI (limits tied per subscription)                                      | Prod / Non-Prod   | `pep-openai-<env>-sub` |
| Reaching Subscription Limits     | When subscription reaches Azure limits, create new (`-01`, `-02`)                        | Any               | `pep-<category>-<env>-0X-sub` <br> e.g. `pep-prod-02-sub` |
| Disaster Recovery                | 1:1 mapping to Prod subscription; Sector/Continent DR                                    | DR                | `pep-<category>-dr-sub` <br> e.g. `pep-pbna-dr-sub` |
| Data Privacy & Compliance        | Dedicated for regulated / trade secret / isolation workloads                             | Any               | `pep-<app>-compliance-<env>-sub` |
| Cost Separation (3rd Parties)    | Dedicated for apps billed to Partner/Customer                                            | Any               | `pep-<partner>-<env>-sub` |
| Third-Party Solutions            | Dedicated if 3rd party requires full subscription control                                | Any               | `pep-<vendor>-<env>-sub` |
| Engineering & Tech Lab           | Dedicated for Cloud Engg, Marketplace Engg, Tech Lab                                     | Any               | `pep-techlab-<env>-sub` |
| Security Tools                   | Dedicated for security / monitoring / testing tools                                      | Any               | `pep-security-<env>-sub` |
| Infrastructure Tools             | Dedicated for infra monitoring / testing tools                                          | Any               | `pep-infra-<env>-sub` |
| Sandbox & POC                    | Dedicated for POCs, isolated, no corporate network connectivity                         | Any               | `pep-sandbox-<env>-sub` |


## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 18 August 2025 (Sakshi Sharma)
