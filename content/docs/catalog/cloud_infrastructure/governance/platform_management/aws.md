---
weight: 5
title: "AWS Organization Units"
date: 2025-16-06
tags: ["multi cloud", "aws", "management"]
summary: "AWS Organization Units"
---

## AWS Organization Units 

**Name**: AWS Organization Units 
**Description**: AWS OUs provide a scalable and secure way to manage AWS accounts. Similar to Azure Management Groups, OUs allow hierarchical organization of accounts, governance enforcement via SCPs (Service Control Policies), and centralized operations. This design reflects PepsiCo’s strategy to manage enterprise, platform, and experimental workloads within AWS Organizations.

## Lifecycle
  - Published Date: 07-08-2025

## Organizational Units
  - AWS Organizational Units (OUs) help structure multi-account AWS environments for governance, security, and operational control at scale.
  - Considering how to design and use OUs as part of your AWS Organization is a critical foundation for cloud governance. The following guidance supports architectural decisions aligned to PepsiCo’s cloud strategy.

**Challenges:**
  - Clear definitions and best practices for how AWS OUs should be structured and managed across PepsiCo’s AWS environment are not fully defined. 
  - TThe current OU hierarchy, if not streamlined, introduces long-term complexity in managing AWS Service Control Policies (SCPs), centralized logging, identity boundaries, and security posture enforcement.

### Requirements
 **Management:**
  - Management and platform accounts must reside in separate **Organizational Units (OUs)** from workload/application accounts. 
  - Long-term cloud operations require dedicated AWS accounts/OUs for centralized management, security, automation, and governance tooling, including hybrid or multi-cloud integrations.
  - Core services should be grouped in the following OUs:
    - pep-coreservices-ou (Infrastructure, Networking, Shared Tools)
    - pep-cybersecurity-ou (Security, Logging, Guardrails)
    - pep-automation-ou (CMP, automation pipelines)
  - OU design should clearly separate foundation, platform, and application layers.

 **Security:**
  - Sandbox/test accounts (e.g., pep-sandbox-ou) must meet minimum baseline security standards, which can be temporarily relaxed for engineering use cases.
  - All PepsiCo-managed AWS accounts must comply with internal security policies and controls via SCPs and AWS Config rules.
  - Non-compliant or legacy workloads must follow a documented Exception Management Process.
  - AWS IAM policies, permission boundaries, and SCPs must be updated in line with OU restructuring.

 **Isolation of Business Critical Areas:**
  - Shared infrastructure (e.g., Palo Alto, TGW, Direct Connect) must be isolated in pep-connectivity-ou.
  - Business-critical applications (e.g., SAP, PGT, DataHub) must be isolated in dedicated OUs under pep-enterprise-applications-prod-ou and pep-enterprise-applications-nonprod-ou.

 **Cloud Finance:**
  - Enforce the serviceName tag key at the account level across all OUs via SCPs or AWS Tag Policies, excluding legacy accounts.
  - For legacy workloads (pep-legacy-ou), tagging must be enforced at the resource level due to inconsistent structure.

 **Lifecycle:**
  - Manage policies, access, and compliance centrally using SCPs, AWS Config, and delegated admin.
  - Define a policy lifecycle strategy:
    - Author and test policies in nonprod OUs
    - Promote to prod OUs post-validation
    - Leverage account-level overrides only when explicitly needed (e.g., via permission sets or exceptions)
    
 ### Enviornment Folder Structure on the mid layer design consideration
  - **Policy Testing Isolation:** Lower-level environment groups enable policy testing in non-prod without risking production stability across sectors.
  - **Granualar Sector Control:** They allow compliance and governance to be tailored per sector and per environment (e.g., different baselines for SAP vs. PFNA).
  - **Reusability and Scalability:** This structure promotes modular policy assignment, making templates reusable across multiple sectors and environments.

## Organiational Units Design
 ### **Organizational Unit Design Consideration**
  - Organizational Units (OUs) within an AWS Organization support logical account grouping for governance, policy enforcement, and operational control. OU design should be thoughtfully planned as your AWS footprint scales across business units and workloads.
  - An OU hierarchy in AWS Organizations supports up to five levels of nested OUs, not including the root.
  - OUs are used to attach Service Control Policies (SCPs) that apply governance across all AWS accounts under that OU.
  - New AWS accounts must be placed into the correct OU during creation or moved immediately afterward to avoid inheriting overly permissive policies from the root.
  - Each account or OU can have only one parent OU.
  - Each OU can contain multiple child OUs or AWS accounts.

 ### **Organizational Unit (OU) Guidelines**
  - Keep the OU hierarchy shallow, ideally within 3 levels. This minimizes complexity and simplifies SCP management and policy debugging.
  - Avoid mirroring organizational charts deeply in the OU structure. OUs should serve policy enforcement, not billing or org structure replication.
  - Create top-level OUs under the root (e.g., pep-coreservices-ou, pep-enterprise-applications-ou) to represent platform, shared services, and workload types.
  - Use resource tagging for horizontal navigation and cost management. Align with PepsiCo’s tagging strategy.
  - Implement a dedicated pep-sandbox-ou where teams can experiment safely with looser SCPs.
  - Define a pep-coreservices-ou for platform services to apply central policies, permissions boundaries, and IAM roles across infrastructure, connectivity, and tooling.
  - Limit SCPs at the root OU level to reduce troubleshooting complexity. Delegate most policies to specific child OUs.
  - Use SCPs and Tag Policies to enforce compliance at the OU or account level, not on individual services.
  - Restrict OU-level changes to privileged users via AWS IAM and delegated admin roles.
  - Configure Account Vending or Control Tower to automatically place new accounts into a designated default OU (e.g., pep-landing-ou) to avoid policy inheritance from the root.

![Visual Representation](/images/Organizational_Unit.png)

 ### **High Level Organization Unit Structure**

| Management Group Name            | Description                                                                                                                                                                                                 | Parent                        |
|----------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------|
| **Tenant Root Group**            | Root tenant account, this should be used only as the parent of the PepsiCo root management group.                                                                                                           | N/A                           |
| **pep-root-mg**                  | PepsiCo Root Management Group.                                                                                                                                                                              | Tenant Root Group             |
| **pep-coreservices-mg**          | Contains all the platform child management groups such as connectivity, infrastructure, or management.                                                                                                       | pep-root-mg                   |
| **pep-cybersecurity-mg**         | Dedicated subscription(s) for the Security team.                                                                                                                                                            | pep-root-mg                   |
| **pep-enterpriseapplications-mg**| Parent MG for all workload MGs such as SAP, Data Hub, Sector/Region, or Global Production subscriptions. Hosts workload-agnostic Azure policies to ensure workloads are secure and compliant.                | pep-root-mg                   |
| **pep-legacy-mg**                | Landing zones created before the Cloud Adoption Program, currently managed by a different MSP.                                                                                                               | pep-root-mg                   |
| **pep-automation-mg**            | Hosts automation services such as CMP (Cloud Management Platform) serving the entire PepsiCo cloud environment.                                                                                              | pep-root-mg                   |
| **pep-microsoftapplications-mg** | Dedicated for landing zones that host Microsoft services (App Center, DevOps, etc.).                                                                                                                         | pep-root-mg                   |
| **pep-sandbox-mg**               | Subscriptions for testing and exploration. Securely disconnected from enterprise networks with less restrictive policies to allow experimentation.                                                          | pep-root-mg                   |
| **pep-learning-mg**              | Short-lived (<1 month) subscriptions for learning/training activities by PepsiCo users.                                                                                                                      | pep-root-mg                   |
| **pep-isolated-mg**              | Landing zones isolated from PepsiCo enterprise environment, not following enterprise governance/security standards.                                                                                            | pep-root-mg                   |
| **pep-decommissioned-mg**        | For cancelled landing zones. Subscriptions are moved here before deletion (after 30–60 days by Azure).                                                                                                       | pep-root-mg                   |
| **pep-<sector/market>-mg**       | Sector-specific management groups (e.g., North America, LATAM, APAC). Part of the second layer under **pep-enterpriseapplications-mg**.                                                                      | pep-enterpriseapplications-mg |


 ## Account Creation Criteria
  - A aws account is an isolated environment that organizes cloud resources and billing.
  - Naming Convention: 
     - Format: pep-aws-<environment>-<scope/sector/app>-<sequence number>-act
     - Example: pep-aws-prod-snt-01-act, pep-aws-prod-pbna-01-act, pep-aws-prod-infratools-01-act

 ### When to Create an AWS Account
   Below you can find a set of criteria to decide if you should create a new AWS account:
  - Will or are already the resource quotas (e.g. ENIs, IPs, Lambda concurrency, VPCs) or hard service limits likely exceed account limits?
    - a. → Yes → Create new account under the same Sector OU and environment OU (e.g. pep-sectorapp-prod-ou)
    - b. → No → Continue
  - Are IAM permissions or security boundaries different (e.g. different teams, vendors, or operational ownership)?
    - a. → Yes → Create new account under the same Sector OU and environment OU to enforce account-level access control.
    - b. → No → Continue
  - Do you need to apply different regulatory, compliance such as ISO/GDPR/HIPAA/PCI?
    - a. → Yes → Create new account under a dedicated OU under pep-enterprise-applications-prod-ou or respective compliance-aligned structure
    - b. → No → Continue
  - Is it a new environment stage (Dev/Test/Prod)?
    - a. → Yes → Create new account under corresponding environment OU under sector-level OU
    - b. → No → Continue
  - Is this for a temporary, isolated, or POC (Proof of Concept) workload?
    - a. → Yes → Create a separate account under pep-sandbox-ou or pep-engineeringlab-ou
    - b. → No → Continue

  All No → Reuse existing account, maybe isolate via tags, IAM roles, VPCs or subnets:
  - Tags for logical grouping
  - VPC-level isolation for network boundaries
  - IAM permission boundaries for scoped access
  - Resource-based policies where needed

## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 18 August 2025 (Sakshi Sharma)
