---
weight: 5
title: "Azure Policy"
date: 2025-16-06
tags: ["multi cloud", "azure", "policy"]
summary: "Azure Policy"
---

## Azure Policy

- **Name**: Azure Policy

## Lifecycle
- **Published Date**: 07-08-2025

## Overview
  - In the Cloud Policy lifecycle, key steps include: 
   ### **Requirements gathering**
   - where requirements are gathered from key stakeholder and tailored to organizational needs.
  - Definition, where policies are defined and tailored to organizational needs.
   ### **Deploy**
   - **Assignment and Planning**, which involves determining how and where policies are applied.
   - **Evaluation**, where policies continuously assess resource compliance.
   - **Remediation**, to correct non-compliance.
   - **Monitoring and Reporting**, to track and report compliance status.
   - **Maintenance**, for regularly reviewing and updating policies.
   - **Decommissioning**, to safely retire outdated policies.

   ### **Governance**
   - **Align**, with the organization future plan and projects.
   - **Update**, any policy based on new compliance standards.
   - **Evaluate**, the need to retire or update any out of scope policy.
   - **Policy Exception** Process to manage necessary deviations from standard policies, ensuring flexibility while maintaining governance
   - **Communication**, with major stakeholders on the compliance and future plans.

  ![.](/images/compliance.png)


## Roles and Responsibilities

  | Team                     | Requirements | Definition | Deploy | Operation | Governance |
|--------------------------|--------------|------------|--------|-----------|------------|
| Cloud and SAP Engineering | A            | RA         | A      | C         | RA         |
| ESS                      | R            | C          | C      | I         | C          |
| Cloud FinOps             | R            | C          | I      | I         | C          |
| Cloud Operations Team    | R            | C          | R      | RA        | R          |
| Cloud Information Security | R          | C          | C      | C         | C          |


| Role        | Definition                                                                 |
|-------------|-----------------------------------------------------------------------------|
| Accountable | The person or team that is ultimately responsible for the quality and end result of the project |
| Responsible | The person or team that completes the task                                  |
| Consulted   | The person or team that provides input before the work is done              |
| Informed    | The person or team that is kept up to date on the project's progress        |

  ## High Level Process Overview
  - Below you can find an high level representation of all the phases required for an Azure Policy from request for production. 

  - ![.](/images/policy.png)
  - ![.](/images/highlevelprocess.png)

  ## Cloud Policy rolled out example simplified version
  - Here’s a very short and concise version for applying the “Audit and Deny VMs that do not use managed disks” policy.
  - ### **Requirements**
      - Ensure all VMs use managed disks to meet security and performance standards.
      - Document the requirements. (document template link - [HERE](https://pepsico.sharepoint.com/:w:/r/teams/AzureGovernance/Shared%20Documents/General/01-Assignment/00_Azure_Policy_Lifecycle_Documentation_Template.docx?d=wd1da326868904470ba4232aea09f8372&csf=1&web=1&e=RND2dQ)) (example document - [HERE](https://pepsico.sharepoint.com/:w:/r/teams/AzureGovernance/Shared%20Documents/General/01-Assignment/00-[Example%20Policy]/00_Audit_Enforce_VMs_that_do_not_use_managed_disks.docx?d=w15e7108914fa475db3c4e2f2553c669f&csf=1&web=1&e=cXJZwJ))

  - ### **Definition**
      - Inform CMP team for upcoming requirements to be included in RIs or Atomic Services
      - All policies have to be enabled on “Audit” mode at root management group level regardless of the scope to be applied with deny to.
      - Assign the policy at the root management group level, starting with Audit mode.
      - Inform stakeholders about the assignment plan and its implications:
        - Based on the information retrieved by enabling the policy in Audit mode, a list of Application Owners can be generated.
        - Inform each Application owner, in a timely manner about the upcoming changes (usually 1 month prior to enabling the policy in Deny).
        - Provide training if there are required actions from their side. E.g.: 1 hour training recorded training session on the changes and indentified potential impact.
        - Inform each Application owner that the policy will also be enabled for DR environments, therefore if configuration changes are required this have to be also implemented in DR environments.
      - Switch to Deny, giving each new management group 1 week while monitoring the impact, following guidelines from [section 8](https://pepsico.sharepoint.com/teams/AzureGovernance/SitePages/Governance-Methodology.aspx#8.-management-group-and-subscription-roll-our-prioritisation).
      - If a new subscription is created enable all the existing approved policies in Deny mode from day 0 

  - ### **Deploy**
      - Immediately evaluate existing resources for compliance and continuously monitor new VM deployments.
      - If during the evaluation of the policy, there are no “non-compliance” check, move the policy in Deny mode ASAP, otherwise move to Remediation phase.
      - Share evaluation results with stakeholders and discuss any immediate actions needed.
      - Identify non-compliant check using the following impact analysis flow:
        - Use Cloud Policy dashboard to identify the management group with the highest number of non-compliant check.
        - Drill down to next level management group or subscription with the highest number of non-compliant check. A phased approach can also be taken, retrofitting first the "non-prod" subscription.
        - Group the non-compliant checks by Application using “servicename” tag (Business Application if available)
        - Reach out to the Application Owner and align with the compliance report and remediations that are required
        - Redo the same based on the non-compliance checks (high to low)
      - Migrate them to managed disks manually or via remediation tasks, either using automation or manual.
      - Update stakeholders on remediation progress and any required actions or prioritization needed on their end.

  - ### **Operations and Governance**
      - Regularly review compliance status through the Cloud Policy dashboard and set up alerts for non-compliance.
        - If there are cases where IaC (Infrastructure as Code) is being executed as part of a release pipelines identify those by setting up alerts to inform Cloud Operations team when that happens.
        - Be ready to create an temporary exception until the Application owner goes through the “Exception Process”. Usually shouldn't take more that a month to reach a decision.
      - Provide regular compliance reports to stakeholders and discuss any issues.
      - Provide Monthly reporting session between Cloud Operation, Engineering and Information Security leadership covering the following:
        - Overall compliance report for Root Management Group
        - Top 5 Cloud Policies with the highest amount of non-compliant check. Add relevant breakdowns such as Environment/Subscription/Region/Application.
        - Top 5 Applications with the most highest of non-compliant check.  Add relevant breakdowns such as Policy/Environment/Subscription/Region.
        - Next Cloud Policies to be prioritized for remediation
        Apply the 80%-20% approach when prioritizing the next policy that requires remediation and receive approval from the group
      - Periodically review and update the policy to align with evolving requirements.
      - Engage stakeholders in the review process to ensure the policy remains relevant.

  - ### **Decommissioning**
      - When the policy is no longer needed, unassign it, monitor for any issues, and safely delete the policy definition.
      - Communicate the decommissioning process to stakeholders and provide guidance on alternative controls if necessary.

  ## Requirements
  - Requirements gathering is the process of identifying your Azure governance policy's exact requirements from start to finish. This process occurs during the initial request, but you’ll continue to manage the requirements throughout the entire process. The requirements should cover the impact of the PepsiCo domains
  - Document template link [HERE](https://pepsico.sharepoint.com/:w:/r/teams/AzureGovernance/Shared%20Documents/General/01-Assignment/00_Azure_Policy_Lifecycle_Documentation_Template.docx?d=wd1da326868904470ba4232aea09f8372&csf=1&web=1&e=RND2dQ) - Use a shared document Requirements, Scope, Policy Definition, Parameters, Efect, Testing, Stakeholders Approval and Planning. Include links to relevant regulations or internal standards.

  - ### **Requirement Analysis**
      - Identify the specific governance or compliance requirements that the policy needs to enforce. This could be related to security, cost management, resource management, or any other operational need.
      - Understand the specific business or regulatory standards that need to be met.
      - Document the requirements

  - Documentation Required:
      - **Purpose Statement**: Clearly state the purpose of the policy.
      - **Compliance Requirements**: Document any regulatory or internal compliance requirements that the policy is meant to enforce.
      - **Stakeholder Input**: Record inputs and decisions made by stakeholders (e.g., governance, security teams).

  - ### **Scope Definition**
      - Determine the scope where the policy will be applied. The scope could be a management group, subscription, resource group, or specific resources.
      - Consider the impact of the policy at different scope levels and whether the policy should be inherited by child scopes (e.g., resource groups within a subscription).

  - Documentation Required:
      - **Scope Description:** Define the scope of the policy (management group, subscription, resource group, etc.).
      - **Impact Analysis:** Document the potential impact of applying the policy at different scopes.
  
  - ### **Policy Definition Selection or Customization**
      - Review existing built-in Azure Policy definitions to see if any meet your requirements.
      - If no built-in policy fits, plan to create a custom policy definition. This involves understanding the policy structure, including the policy rule, parameters, and effects (e.g., Deny, Audit, Modify, DeployIfNotExists).
      - Draft a policy definition that precisely matches the identified requirements.

  -  Documentation Required:
      - **Policy Definition Details:** Document whether an existing policy was selected or a custom one was created.
      - **Custom Policy Details:** Include the JSON definition if the policy is custom, explaining each component (e.g., policy rule, parameters, effects).
      - **Rationale:** Provide a rationale for the choice or customization.

  - ### **Identify Parameters**
      - Determine if the policy requires any parameters that should be configurable at assignment time. This adds flexibility, allowing different values to be applied in different contexts.

  - Documentation Required:
     - **Parameter List:** Document all parameters that the policy includes and their possible values.
     - **Usage Scenarios:** Provide examples or scenarios where different parameter values would be used.

  - ### **Effect Determination**
      - Decide on the effect(s) that the policy should enforce:
         - Deny: Prevents the creation or update of resources that don’t comply.
         - Audit: Flags non-compliant resources without blocking them.
         - DeployIfNotExists: Ensures certain configurations or resources are deployed if they don’t already exist.
         - Modify: Automatically modifies resource configurations to ensure compliance.
      -  Ensure the chosen effect aligns with the desired governance model (strict enforcement vs. audit-only, etc.).

  - Documentation Required:
     - **Effect Description**: Document the chosen effect(s) (Deny, Audit, etc.) and the reasons for choosing them.
     - **Impact Assessment**: Describe the expected impact of the policy effect on resources.

  - ### **Testing**
      - Before wide deployment, test the policy in a controlled environment. This can be done by applying the policy to a limited scope (e.g., a test resource group).
      - Evaluate the impact and ensure it behaves as expected, without unintended side effects.

  - Documentation Required:
     - **Test Plan**: Document the testing strategy, including the scope and criteria for success.
     - **Test Results**: Record the outcomes of the tests, any issues encountered, and their resolutions.

  - ### **Approval Process**
      - Ensure the policy is reviewed and approved by relevant stakeholders, such as governance, security, and operations teams.
      - Document the policy, its purpose, and the reasoning behind its implementation for future reference and audits.

  - Documentation Required
     - **Approval Record**: Document the approval of the policy by relevant stakeholders.
     - **Change History**: Maintain a history of changes to the policy, including who approved each change.
  
  - ### **Assignment Planning**
      - Plan the initial assignment of the policy. Decide where and how it will be rolled out, whether immediately across a large scope or incrementally.
      - Consider the potential need for remediation tasks for existing non-compliant resources.

  - Documentation Required
     - **Assignment Strategy**: Document the plan for assigning the policy, including any phased rollout or specific scopes to be targeted first.
     - **Remediation Plan**: If applicable, document how non-compliant resources will be remediated.

  ## Definition
  - Assign the policy to a specific scope, such as a subscription, resource group, or management group. During this step, parameters can be customized if the policy definition includes them. When assigned, Azure Policy begins evaluating resources within the specified scope against the policy’s rules.

  - ### **Determine the Scope of the Assignment**
     - Assignment planning for an Azure Policy is a critical step to ensure that the policy is deployed effectively and aligns with organizational goals. This planning involves determining how, where, and when the policy will be applied across your Azure environment. Here’s a structured approach to planning the assignment of an Azure Policy:

     - **Identify the Target Scope:** Decide where the policy will be applied. Azure Policy scopes include:
     - **Management Groups:** For broad application across multiple subscriptions.
     - **Subscriptions:** To apply the policy across all resource groups and resources within a single subscription.
     - **Resource Groups:** To target specific sets of resources within a subscription.
     - **Individual Resources:** For very targeted application on specific resources.

  - Consider Inheritance: Understand that policies assigned at a higher level (e.g., management group) are inherited by all lower levels (e.g., subscriptions, resource groups). This inheritance can simplify management but may require planning to avoid conflicts or redundancy.

  - ### **Review the Policy Parameters (if Applicable)**
     - **Configure Parameters**: If the policy definition includes parameters, plan how these will be configured at the time of assignment. Parameters can be used to tailor the policy to different environments or contexts.
     - **Parameter Values for Different Scopes**: Decide if different scopes will have different parameter values. Document these decisions as part of the assignment plan.

  - ### **Plan the Rollout Strategy**
     - **Phased Deployment**: Consider a phased approach, starting with a smaller, non-critical scope (e.g., a test resource group or a non-production subscription) before rolling out to broader scopes. This approach minimizes risk and allows for adjustments based on initial findings.
     - **Immediate vs. Gradual Enforcement**: Decide whether to immediately enforce the policy (e.g., using the Deny effect) or start with a less intrusive effect (e.g., Audit) to gather compliance data before strict enforcement.

  - ### **Evaluate Existing Resources for Compliance**
     - **Pre-assignment Compliance Check**: Assess existing resources within the intended scope for compliance with the new policy. This helps identify potential issues before the policy is enforced.
     - **Remediation Planning**: Develop a plan for remediating non-compliant resources. If using a policy with a DeployIfNotExists or Modify effect, ensure remediation tasks are prepared.

  - ### **Document the Assignment Plan**
     - **Assignment Details:** Document the scope of the policy assignment, the parameter configurations for each scope, and the rationale behind these choices.
     - **Rollout Timeline:** Define a timeline for when the policy will be assigned to each scope. Include key milestones such as testing, initial deployment, and full rollout.
     - **Communication Plan:** Develop a communication plan to inform stakeholders about the policy assignment, especially if it will impact resource creation or management processes.

  - ### **Communicate with Stakeholders**
     - **Stakeholder Notification:** Inform relevant stakeholders (e.g., IT teams, developers, resource owners) about the upcoming policy assignment, its implications, and any actions they need to take.
     - **Provide Training/Guidance:** Offer guidance or training on how the policy will impact their work, particularly if it involves the Deny effect or requires remediation.

  - ### **Assign the Policy**
     - **Policy Assignment:** Use the Azure portal, Azure CLI, PowerShell, or an ARM template to assign the policy to the identified scope. Ensure that the correct parameters are set during assignment.
     - **Verify Assignment:** After assignment, verify that the policy is correctly applied and is evaluating resources as expected.

  - ### **Monitor and Review**
     - **Initial Monitoring:** Closely monitor the impact of the policy after assignment, particularly if it is set to deny non-compliant resources.
     - Compliance Reports: Review compliance reports to assess the policy’s effectiveness and identify any unexpected issues.
     - **Adjust if Necessary:** Based on the monitoring, adjust the policy’s parameters or scope if needed. For example, you might need to expand the scope, change parameter values, or modify the policy’s effect.

  - ### **Prepare for Remediation**
     - **Automated Remediation:** If applicable, set up automated remediation tasks to address non-compliance. This is particularly important for DeployIfNotExists or Modify effects.
     - **Manual Remediation:** If automated remediation is not feasible, prepare a manual remediation plan, including timelines and responsibilities for addressing non-compliant resources.

  ## Deploy
  - The Evaluation stage in the Azure Policy lifecycle involves several key steps that ensure resources within the assigned scope are continuously monitored for compliance. The evaluation stage in the Azure Policy lifecycle is a continuous process that ensures resources remain compliant with defined policies. This involves immediate evaluations during resource changes, periodic reevaluations of all resources, and the updating of compliance states. The process also includes collecting compliance data, generating reports, and potentially triggering remediation actions to address non-compliance. Here’s a breakdown of the steps involved:

  - ### **Initial Policy Evaluation**
     - **Immediate Evaluation on Assignment:** When a policy is first assigned to a scope, Azure Policy triggers an immediate evaluation of all existing resources within that scope. This initial evaluation determines the current compliance state of resources based on the policy rules.
     - **Baseline Compliance Check:** The initial evaluation provides a baseline compliance report, indicating which resources are compliant and which are not. This report helps identify areas that may need remediation or further attention.

  - ### **Real-Time Evaluation**
     - **Evaluation During Resource Creation:** Azure Policy evaluates resources in real-time as they are created or updated within the assigned scope. If a resource does not comply with the policy, the specified effect (e.g., Deny, Audit) is applied immediately.
     - **Immediate Feedback to Users:** For policies with the Deny effect, users attempting to create or modify non-compliant resources receive immediate feedback, preventing the action from completing.

  - ### **Periodic Reevaluation**
     - **Scheduled Compliance Checks:** Azure Policy automatically reevaluates all resources within the scope on a periodic basis (typically every 24 hours by default). This ensures that the compliance state is kept up to date, even if resources have been modified outside of normal processes.
     - **Handling Drift:** Periodic reevaluation helps detect configuration drift—when resources become non-compliant after they were initially compliant, possibly due to manual changes or external factors.

  - ### **Compliance State Update**
     - **Real-Time Compliance State Updates:** For new resources, the compliance state is updated in real-time as soon as the resource is created or modified.
     - **Periodic Compliance State Updates:** For existing resources, the compliance state is updated during the periodic reevaluation cycle. This includes changes in compliance status due to modifications or new policy assignments.

  - ### **Compliance Data Collection**
     - **Collection of Compliance Data:** Azure Policy collects data on the compliance status of resources during each evaluation cycle. This data includes which resources are compliant, which are non-compliant, and the reasons for non-compliance.
     - **Data Storage:** Compliance data is stored in the Azure Policy compliance dashboard and can be accessed through the Azure portal, CLI, PowerShell, or via API.

  - ### **Reporting and Alerts**
     - **Compliance Reporting:** Azure Policy generates reports based on the evaluation data, providing insights into the overall compliance posture of the environment. These reports can be accessed through the Azure portal or exported for further analysis.
     - **Alerts Configuration:** If configured, Azure Policy can trigger alerts when specific compliance thresholds are breached (e.g., when the number of non-compliant resources exceeds a certain limit).

  - ### **Policy Adjustments Based on Evaluation Results**
     - **Refine Policies:** Based on the evaluation results, you may need to adjust the policy definitions, parameters, or assignment scopes. For example, if certain resources consistently fail to comply, the policy might need to be modified or additional remediation steps might be required.
     - **Continuous Improvement:** Use insights from the evaluation process to improve policies over time, ensuring they remain aligned with evolving organizational standards and regulatory requirements.

  - ### **Review and Remediation**
     - **Ongoing Review of Compliance:** Regularly review the compliance reports and data collected during the evaluation process to ensure policies are effective and resources remain compliant.
     - **Triggering Remediation Tasks:** If non-compliance is detected, especially in cases where resources have drifted from compliance, you may need to trigger remediation tasks. This can be automated or manual, depending on the policy’s effect and the organization’s governance model.

  ## Operations
  - The Remediation phase in the Azure Policy lifecycle involves several steps to bring non-compliant resources into compliance. This phase is especially relevant for policies with “DeployIfNotExists” or “Modify” effects, but it can also be applied manually when required. Here’s a breakdown of the steps involved in the remediation process:

  - ### **Identify Non-Compliant Resources**
     - **Compliance Report Review:** Begin by reviewing the compliance reports generated during the evaluation phase. Identify which resources are non-compliant with the assigned policy.
     - **Determine the Cause of Non-Compliance:** Understand why the resources are non-compliant. This could be due to configuration drift, misconfiguration during deployment, or a lack of required resources or settings.

  - ### **Evaluate Remediation Options**
     - **Automatic Remediation with Policy Effects:** If the policy includes a DeployIfNotExists or Modify effect, Azure Policy can automatically bring non-compliant resources into compliance.
     - **DeployIfNotExists:** Deploys the required resources or settings if they do not already exist.
     - **Modify:** Alters existing resources to comply with the policy by modifying their configuration.
     - **Manual Remediation:** For policies without built-in remediation effects, or where automated remediation is not feasible, plan for manual remediation. This might involve manually adjusting configurations, deploying missing resources, or applying scripts to bring resources into compliance.

  - ### **Create Remediation Tasks**
     - **Initiate Remediation Task:** In the Azure portal, locate the policy assignment and select the option to create a remediation task. This will trigger the remediation process for non-compliant resources.
     - **Specify Scope and Parameters:** When creating a remediation task, you may need to specify the scope of resources to be remediated and set any necessary parameters for the task.
     - **Review Potential Impact:** Before executing the remediation task, review the potential impact on the environment. Ensure that the remediation actions will not disrupt services or cause unintended side effects.

  - ### **Execute Remediation**
     - **Automated Remediation Execution:** Allow Azure Policy to automatically apply the remediation actions. Monitor the progress of the remediation task through the Azure portal.
     - **Manual Remediation Execution:** For manual remediation, follow the plan you developed in the previous step. This could involve running scripts, updating configurations, or deploying additional resources.

  - ### **Monitor Remediation Progress**
     - **Tracking Task Status:** In the Azure portal, track the status of the remediation task. Azure will provide updates on which resources have been successfully remediated and which may have encountered issues.
     - **Address Issues:** If the remediation task encounters problems (e.g., failures to deploy resources or modify configurations), investigate and resolve these issues. This may involve adjusting the policy, revising parameters, or manually intervening.

  - ### **Validate Compliance Post-Remediation**
     - **Re-Evaluate Resources:** Once remediation is complete, re-evaluate the resources to ensure they are now compliant with the policy.
     - **Confirm Remediation Success:** Verify that all non-compliant resources have been successfully remediated and that the environment is fully compliant with the policy.

  - ### **Document Remediation Actions**
     - **Record Remediation Details:** Document the remediation process, including the resources affected, the actions taken, and the outcomes. This documentation is important for auditing purposes and for maintaining a history of compliance efforts.
     - **Update Stakeholders:** Inform relevant stakeholders of the remediation outcomes, especially if the remediation involved significant changes to resources or configurations.

  - ### **Access the Azure Policy Dashboard**
     - **Navigate to the Azure Policy Dashboard:** Start by accessing the Azure Policy dashboard in the Azure portal. This is the central location where you can view the compliance status of all resources under your Azure policies.
     - **Select the Appropriate Scope:** Choose the scope you want to monitor (e.g., management group, subscription, resource group). The dashboard provides a hierarchical view of compliance across these scopes.

  - ### **Review Compliance Status**
     - **Compliance Overview:** The dashboard provides an at-a-glance overview of the compliance status, showing the percentage of resources that are compliant versus non-compliant for each policy or initiative.
     - **Drill Down into Details:** Click on individual policies or initiatives to drill down into more detailed compliance data. This includes a list of non-compliant resources, details about the specific compliance issues, and the time since the last evaluation.
     - **Filter and Search:** Use filters and search capabilities to narrow down the results to specific policies, resource types, or compliance states.

  - ### **Generate Detailed Compliance Reports**
     - **Export Compliance Data:** From the Azure Policy dashboard, export compliance data to CSV or other formats for further analysis. This can be useful for reporting to stakeholders or for more detailed examination in external tools like Excel or Power BI.
     - **Scheduled Reports:** If regular reporting is required, set up automated processes to generate and distribute compliance reports on a schedule (e.g., weekly or monthly).
     - **Custom Reports:** Create custom reports using Azure Monitor, Log Analytics, or Power BI to visualize and analyze compliance data according to specific organizational needs.

  - ### **Set Up Alerts for Non-Compliance**
     - **Define Alert Criteria:** Determine the specific conditions under which alerts should be triggered, such as when a certain percentage of resources are non-compliant, or when a critical resource becomes non-compliant.
     - **Create Alerts in Azure Monitor:** Use Azure Monitor to create alerts based on the compliance data. These alerts can be configured to trigger notifications (e.g., via email, SMS, or integration with ITSM tools like ServiceNow) when non-compliance is detected.
     - **Configure Action Groups:** Set up action groups that define who will be notified and what actions should be taken when an alert is triggered. Action groups can include different methods of communication, such as email, SMS, or push notifications.

  - ### **Ongoing Monitoring**
     - **Regular Review of Dashboard:** Regularly review the Azure Policy dashboard to monitor the ongoing compliance status of your environment. This should be part of routine governance and operations practices.
     - **Investigate Non-Compliance:** When non-compliance is detected, investigate the underlying issues. This may involve reviewing the specific resources, understanding the reasons for non-compliance, and deciding on corrective actions (e.g., triggering remediation tasks).

  - ### **Analyze Trends Over Time**
     - **Track Compliance Trends:** Use the data available in the Azure Policy dashboard and exported reports to analyze trends over time. Identify patterns, such as recurring non-compliance issues or specific times when non-compliance spikes.
     - **Assess Policy Effectiveness:** Evaluate the effectiveness of your policies based on these trends. If certain policies are not achieving the desired compliance outcomes, consider revising them or providing additional guidance to resource owners.

  - ### **Stakeholder Reporting**
     - **Generate Executive Reports:** Prepare high-level reports for executives and other stakeholders, summarizing the overall compliance posture, key areas of non-compliance, and actions taken to address issues.
     - **Present Findings:** Regularly present these findings to governance boards, security teams, or other relevant stakeholders to ensure they are aware of the current compliance status and any risks or issues that need attention

  ## Governance
  - ### **Assess Alignment with Organizational Requirements**
     - **Check Compliance with New Regulations:** Assess whether existing policies meet new regulatory requirements or industry standards that may have been introduced since the policies were first implemented.
     - **Evaluate Alignment with Business Objectives:** Ensure that the policies support the current business objectives. For example, if the organization has adopted new cloud services or technologies, policies may need to be adjusted to cover these areas.
     - **Policy Effectiveness Evaluation:** Analyze whether the policies are effectively enforcing the intended controls. Review compliance reports and incident logs to identify gaps or areas where policies are not delivering the desired outcomes.

  - ### **Update Policy Definitions**
     - **Refine Policy Rules:** Modify the policy rules based on the findings from the review. This could include tightening conditions, adding new rules, or changing policy effects to better enforce compliance.
     - **Add or Update Parameters:** Adjust policy parameters to reflect changes in the environment or to provide more flexibility in policy assignments.
     - **Test Updates in a Controlled Environment:** Before rolling out changes to production environments, test the updated policies in a sandbox or staging environment to ensure they function as expected without causing unintended issues.

  - ### **Reassign or Modify Policy Assignments**
     - **Review Policy Assignments:** Assess whether the current scope of policy assignments (e.g., management groups, subscriptions, resource groups) is still appropriate. As the organization grows or restructures, the scope of policies may need to be expanded or redefined.
     - **Update Assignments as Needed:** Modify the scope of existing policy assignments or create new assignments to cover any gaps identified during the review. Ensure that parameter values are updated in accordance with any changes in policy definitions.
     - **Communicate Changes:** Inform stakeholders, especially those managing resources affected by the policies, about any changes in policy assignments. Provide guidance on how these changes may impact their operations and how Operations team executes

  - ### **Stakeholder Communication**
     - **Notify Stakeholders:** Once the policy has been successfully decommissioned, inform all relevant stakeholders that the process is complete. Include details on how the environment will be managed going forward without the decommissioned policy.
     - **Provide Guidance on Alternative Controls:** If the decommissioned policy was replaced or if its controls are now covered by another policy, provide guidance to stakeholders on how compliance will be maintained with the new controls.

  - ### **Plan for Ongoing Compliance**
     - **Prevent Future Non-Compliance:** Based on the remediation process, identify any changes needed to policies, processes, or resource configurations to prevent future non-compliance.
     - **Consider Additional Monitoring or Alerts:** Implement additional monitoring or alerts to detect potential non-compliance issues earlier and trigger remediation more proactively.

  ## Policy Exception Process (Under Review)
  - A Policy Exception Process ensures that exceptions to Azure Policies are handled in a controlled, documented, and auditable manner. It involves submitting a formal request, reviewing and approving the request, documenting and implementing the exception, and monitoring it until expiration or renewal. This process balances the need for flexibility with the requirement to maintain a secure and compliant environment.
  - ### **Request Submission**
     - **Initiate Request:** The resource owner or a relevant stakeholder identifies a need for an exception to a specific Azure Policy and submits a formal request. This request typically includes:
       - **Justification:** A detailed explanation of why the exception is needed, including any business or technical reasons.
       - **Scope:** The specific resources, resource groups, or subscriptions for which the exception is requested.
       - **Duration:** The timeframe for the exception (e.g., temporary for a project duration or permanent).
       - **Risk Assessment:** An assessment of the potential risks associated with granting the exception, including potential impacts on security, compliance, and operations.
     - **Use a Standard Form:** Provide a standardized form or template to ensure that all necessary information is captured in the exception request.

  - ### **Initial Review**
     - **Review by Policy Owner or Governance Team:** The request is initially reviewed by the policy owner, governance team, or a designated compliance officer. The review focuses on:
       - Completeness: Ensuring that all required information is provided.
       - Justification Validity: Evaluating whether the reasons for the exception are valid and necessary.
       - Risk Analysis: Assessing the risks outlined in the request and determining if they are acceptable or require additional mitigation.
     - **Consultation with Experts:** If needed, the reviewer may consult with subject matter experts (e.g., security, compliance, legal) to assess the implications of the exception.

  - ### **Approval Process**
     - **Escalation for Approval:** Depending on the organization’s governance structure, the exception request may need to be escalated to higher authorities for approval. This could involve:
       - Policy Exception Committee: A dedicated committee that reviews and approves exceptions.
       - Executive Approval: For high-risk or high-impact exceptions, approval from senior management or an executive board may be required.
     - **Decision Criteria:** The decision to approve or reject the exception is based on:
       - Risk vs. Benefit: Weighing the benefits of granting the exception against the potential risks.
       - Mitigation Measures: Identifying any compensating controls or mitigation strategies that can reduce the risks associated with the exception.

  - ### **Documentation of the Exception**
     - **Formal Documentation:** If the exception is approved, document the details, including:
       - Exception Scope: Clearly define what is and isn’t covered by the exception.
       - Duration: Specify the start and end dates of the exception, if it’s temporary.
       - Conditions: Outline any conditions or mitigation measures that must be in place during the exception period.
       - Approval Details: Record the names of individuals or committees that approved the exception, along with the date of approval.
     - **Centralized Record Keeping:** Store all policy exceptions in a centralized, accessible repository, such as a governance portal, SharePoint, or a dedicated compliance management tool. This ensures that exceptions can be tracked and audited.

  - ### **Communication and Implementation**
     - **Notify Stakeholders:** Communicate the approval of the exception to all relevant stakeholders, including IT, security, compliance teams, and the resource owners.
     - **Implement the Exception:** Adjust the policy assignments or configurations to reflect the approved exception. This might involve:
       - Excluding Specific Resources: Modifying the scope of the policy to exclude the resources covered by the exception.
       - Custom Policy Deployment: Implementing a custom policy that applies to the exception scope while maintaining compliance for the rest of the environment.

  - ### **Monitoring and Review**
     - **Ongoing Monitoring:** Continuously monitor the resources under the exception to ensure that they are being used appropriately and that any conditions attached to the exception are being met.
     - **Periodic Review:** For temporary exceptions, conduct periodic reviews to determine if the exception is still necessary or if it can be revoked. This could be scheduled based on the initial duration or triggered by changes in the environment.

  - ### **Expiration or Renewal**
     - **Expiration Management:** If the exception is temporary, ensure that it is revoked at the end of the approved period. This involves reapplying the original policy or adjusting the scope to bring the resources back under standard governance.
     - **Renewal Process:** If the exception needs to be extended, require a new request to be submitted, including an updated justification and risk assessment. This request should go through the same review and approval process as the original exception.

  - ### **Audit and Reporting**
     - **Audit Trail:** Maintain a clear audit trail of all policy exceptions, including the initial request, approval, documentation, and any reviews or renewals. This is crucial for demonstrating compliance with governance standards and for internal or external audits.
     - **Regular Reporting:** Generate reports on all active and expired exceptions, and review them with governance bodies or during regular compliance meetings. This helps in understanding the broader impact of exceptions on the organization’s security and compliance posture.

  - ### **Continuous Improvement**
     - **Review Process Effectiveness:** Periodically review the exception process itself to ensure it remains effective and aligns with organizational goals. Adjust the process based on feedback, audit findings, or changes in regulatory requirements.
     - **Policy Adjustments:** If a pattern of similar exceptions emerges, consider adjusting the relevant policies to better align with operational needs, reducing the need for individual exceptions in the future.

  ## Management Group and Suscription roll our prioritisation
  - If the policy has introduces a small impact policy such as Require a tag on resources: 
    - Enable the policy in Deny more on the pep-sandbox-mg Management group.
    - Move forward to each 1st Layer MG as it follows:
      - pep-legacy-mg
      - pep-automation-mg
      - pep-microsoftservices-mg
      - pep-enterpriseapplications-mg
      - pep-coreservices-mg
    - Give each step 1 week for people to report issues and document them if any.
    - If the policy introduces a complex and high impact policy such as Enforce SSL/TLS on App Service:
      - Enable the policy in Deny in one resources group part of an subscription from “pep-sandbox-mg” management group
      - Test potential impact of both new resource deployment and existing resources.
      - Document any potential missed impact during Requirements.
      - Promote the policy to the subscription level. Wait for 1 weeks for potential problems to be reported. Sandbox environments have to reflect the configuration required for the solution to be deployed in Production.
      - Promote the policy to the pep-sandbox-mg management group. Wait for 1 weeks for potential problems to be reported
      - Enforce the policy withing the “non-prod” subscription:
        - Based on the lowest number of non-compliance checks as it following:
          - Pick the "non-prod" subscription with the lowest non-compliance checks
          - Pick the “resource group” from within the above selected subscription with the lowest non-compliance check
        - Wait for 1 weeks for potential problems to be reported
      - Promote the policy to the “non-prod” subscription level. Wait for 1 weeks for potential problems to be reported
      - Promote the policy within “prod” subscription:
        - Enforce the policy based on the lowest number of non-compliance checks as it following:
          - Pick the "prod" subscription with the lowest non-compliance checks
          - Pick the “resource group” from within the above selected subscription with the lowest non-compliance check
        - Wait for 1 weeks for potential problems to be reported.
      - Promote the policy to the “dr” (if exists) subscription level. Wait for 1 weeks for potential problems to be reported
      - Move forward with the next Management Group with the lowest non-compliant checks. Wait for 1 weeks for potential problems to be reported
      - If required, repeat the step steps 7 to 11 for each Management Group, prioritizing based on lowest non-compliant check, for additional isolation of impact.
      - After you promoted the policy to all the Management Groups, enforce the policy on Root Management Level
   - Each step, document any reported issues since it might be a common issue for different Applications, Subscriptions or Management Groups
**IMPORTANT - For policies that bring complex changes in the environment, constantly align with Application Owner to identify impact ASAP and exclude or retrofit the issue.**

  ## Ownership and Version Control

   ### Service Architect
   - <NAME_EMAIL>

   ### Version Control
   - v.1: 4th September 2025 (Sakshi Sharma)
