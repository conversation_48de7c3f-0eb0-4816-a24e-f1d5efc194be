---
weight: 2
title: "Cloud Naming Convention"
date: 2025-22-06
tags: ["multi cloud", "naming convention", "", ""]
summary: "Cloud Naming Convention"
---

## Naming Convention Overview

**Name**: Resource Naming in Multi-Cloud
**Description**: Resource naming in multi-cloud environments is a critical component of consistent resource management across Azure, AWS, GCP or Alibaba. By standardizing naming conventions, resources such as virtual machines, storage accounts, and databases can be efficiently tracked, managed, and reported regardless of their hosting platform.

## Lifecycle
  - Published Date: 2025-22-04 

## Goals & Benefits
  - Standardized names simplify cross-cloud management and reporting.
  - Enables scripting, automated tagging, and policy enforcement with predictable resource names.
  - Clear naming reduces risks of resource misconfiguration or misidentification.
  - Supports large scale environments with environment, region, and sequence differentiation.
  - Enforces adherence to cloud provider naming restrictions and internal policies.
  
## Naming Convention Guidelines

 ### Cloud Resource Naming Standards – Cross-Cloud

| **Guideline**                              | **Azure** | **AWS** | **GCP** | **Alibaba** |
|-------------------------------------------|:---------:|:-------:|:------:|:----------:|
| **Lowercase only**                        | ✅        | ✅      | ✅     | ✅         |
| **Dashes for separation** (-)           | ✅        | ✅      | ✅     | ✅         |
| **Regex rule**: pep[a-z0-9(-)], cannot start or end with - | ✅ | ✅ | ✅ | ✅ |
| **Use <service-name> as app identifier**| ✅        | ✅      | ✅     | ✅         |
| **Remove spaces**                         | Spaces are disallowed and must be removed (e.g., “Data Hub” becomes “datahub”) to comply with naming rules | Spaces are disallowed and must be removed (e.g., “Data Hub” becomes “datahub”) to comply with naming rules | Spaces are disallowed and must be removed (e.g., “Data Hub” becomes “datahub”) to comply with naming rules.  | Spaces are disallowed and must be removed (e.g., “Data Hub” becomes “datahub”) to comply with naming rules. |
| **Cloud vendor value**                    | azr     | aws   | gcp  | ali      |
| **Environment values**                    | sbx, dev, test, qa, preprd, prod, dr, global | Same | Same | Same |
| **Region values**                         | scus = South Central US<br> eus = East US<br>suk` = South UK<br>`gwc` = Germany West Central<br>`sea` = SouthEast Asia<br>`ae` = Australia East | `use1` = US East 1<br>`use2` = US East 2<br>`euw1` = EU West 1<br>`euc1` = EU Central 1<br>`aps1` = AP South 1<br>`apse1` = AP South East 1 | **TBD** | **TBD** |
| **Cloud vendor naming limitations**       | [https://learn.microsoft.com/en-us/azure/azure-resource-manager/management/resource-name-rules]    | [https://docs.aws.amazon.com/search/doc-search.html?searchPath=documentation&searchQuery=naming%20limitation]  | [https://cloud.google.com/compute/docs/naming-resources] |   |
| **Max characters per resource name***     | 63        | 63      | 63     | 63         |

---

- *Currently less than 1% of Azure resources have names with more than 63 characters 

 ### General Naming Format
    pep-<cloudVendor>-<serviceName>-<componentOrFunction>-<environment>-<region>-<sequenceNumber>-<resourceTypeAcronym
  - pep: Prefix identifying the organization (PepsiCo)
  - cloudVendor: Short cloud provider code (e.g., azr, aws, gpc, ali)
  - serviceName: Application or service name (no spaces)
  - componentOrFunction: Optional component or function name
  - environment: Environment code (e.g., dev, test, prod)
  - region: Cloud region code
  - sequenceNumber: Numeric sequence to differentiate instances
  - resourceTypeAcronym: Short acronym identifying resource type

## Limitations
  - Regional codes for AWS, GCP, and Alibaba are TBD and may vary, so care must be taken to update and standardize them once defined.
  - Certain resource types (e.g., storage accounts) disallow dashes, requiring a concatenated naming approach that may reduce readability.
  - Sequence numbers must be carefully managed to avoid naming collisions in large environments.
  - Naming length is capped at 63 characters, limiting descriptive detail for deeply nested naming.

### Cloud Naming Convention: Limitations and Particularities

This table outlines the naming convention limitations and particularities across **Azure**, **AWS**, and **GCP** in alignment with PepsiCo internal standards.

| Limitations and Particularities                                                                                       | Azure | AWS | GCP |
|------------------------------------------------------------------------------------------------------------------------|:-----:|:---:|:---:|
| Leverage the current PepsiCo Server Naming Convention for Virtual Machines/EC2 (Internal link)                        | ✅    | ✅  | ✅  |
| Server name is generated by an internal VM Name Generator Tool (DND) based on SNOW request                            | ✅    | ✅  | ✅  |
| Underscore not allowed; spaces forbidden; periods forbidden                                                            | ✅    | ✅  | ✅  |
| `<component/function>` can be removed if resource is global for one application                                       | ✅    | ✅  | ✅  |
| `<component/function>` can be replaced by `<sector>` if resource is sector-level                                      | ✅    | ✅  | ✅  |
| If `<service name>/<component/function>` has a space (e.g., “Data Hub”), remove the space (e.g., “datahub”)           | ✅    | ✅  | ✅  |
| Resources that cannot have dashes in naming use the same format without dashes (e.g., storage accounts, SQL pools)    | ✅    | ✅  | ✅  |
| Use “connected-to” for resources involving datacenter connectivity (e.g., ExpressRoute, route filters, connections)   | ✅    | ✅  | ✅  |
| Network resources must reference connected resources (e.g., VNET, Subnet, internal/external)                          | ✅    | ✅  | ✅  |
| Resources dependent on others integrate the parent resource name (e.g., `pepvmname01-os-disk-dev-scus-01`)            | ✅    | ✅  | ✅  |

### Restricitons for most common used resource types

Some cloud resources have strict naming constraints that may not accommodate PepsiCo's standard naming conventions depending on the length of `<servicename>` or `<component/function>`. The table below outlines these constraints across major cloud providers:

| **Resource/Service**     | **Azure (Service & Limitations)**                                                                 | **AWS (Equivalent & Limitations)**                                                                                  | **GCP (Equivalent & Limitations)**                                                                                       |
|--------------------------|---------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------|
| **Virtual Machine**      | Virtual Machine: 1–15 characters. Alphanumeric & hyphen; cannot start/end with a hyphen.          | EC2 Instance (Name tag): Up to 127 characters (via tag). Name is a tag, not a strict identifier.                    | Compute Engine VM: 1–63 characters. Lower-case letters, digits, hyphens; must start with a letter and end with letter/digit. |
| **Object Storage**       | Storage Account: 3–24 characters. Lower-case letters & numbers. No special characters or dashes.  | S3 Bucket: 3–63 characters. Lower-case letters, numbers, hyphens; must be globally unique.                           | Cloud Storage Bucket: 3–63 characters. Lower-case letters, digits, dashes, dots; must be globally unique.                  |
| **Managed SQL Database** | SQL DB/Server: 1–128 characters. Some restrictions on special characters.                         | Amazon RDS: 1–63 characters. Lower-case letters, digits, hyphens; must start with a letter.                         | Cloud SQL: 1–63 characters. Lower-case letters, digits, hyphens; must start with letter and end with letter/digit.         |
| **NoSQL Database**       | Cosmos DB Account: 3–44 characters. Lower-case letters & hyphens.                                 | DynamoDB Table: 3–255 characters. Alphanumeric, underscores, hyphens, periods allowed.                              | Cloud Bigtable Table: Up to 50 characters. (Firestore uses internal conventions, not strict naming limits.)               |
| **Container Registry**   | Azure Container Registry: 5–50 characters. Lower-case letters & numbers.                         | ECR Repository: Up to 256 characters. Lower-case letters, numbers, hyphens, underscores, periods allowed.           | Artifact Registry/GCR: Up to 128 characters. Lower-case with dashes; subject to project ID constraints.                   |
| **API Gateway**          | API Management Service: 1–50 characters.                                                          | API Gateway: Up to 128 characters (varies by region).                                                               | API Gateway: Up to 63 characters. Must match pattern [a-z]([-a-z0-9]*[a-z0-9]).                                         |
| **Serverless Functions** | Function App (Azure Functions): 2–60 characters.                                                  | AWS Lambda: Up to 64 characters.                                                                                    | Cloud Functions: Up to 63 characters. Lower-case letters, digits, hyphens; must follow naming pattern.                    |
| **Key/Secret Management**| Key Vault: 3–24 characters. Lower-case, numbers, hyphens. Cannot start/end with a hyphen.         | Secrets Manager / KMS: Secret names up to 512 characters; KMS aliases up to 256 characters.                         | Secret Manager: Up to 255 characters. Typically alphanumeric with dashes/underscores.                                     |

### Cloud Resource Character Restrictions and Naming Guidelines

In addition to name length limits, many cloud services have **specific character restrictions**. These constraints can affect compliance with PepsiCo naming conventions—especially for services with stricter rules around hyphens, case sensitivity, or special characters.

| **Service**                         | **Azure Restrictions**                                                                 | **AWS Equivalent & Restrictions**                                                                                       | **GCP Equivalent & Restrictions**                                                                                          |
|-------------------------------------|----------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------|
| **Virtual Machine**                | Alphanumeric and hyphen; cannot start or end with a hyphen.                           | EC2 Instance (Name tag): No strict format; use alphanumerics and hyphens; avoid leading/trailing hyphens.               | Compute Engine VM: Lowercase letters, digits, hyphens; must start with a letter and end with a letter/digit.               |
| **Storage Account / Object Storage**| Lowercase letters and numbers only. No special characters.                            | S3 Bucket: Lowercase letters, numbers, hyphens; globally unique.                                                        | Cloud Storage Bucket: Lowercase, digits, dashes, dots; globally unique.                                                    |
| **Key Vault / Secret Management**  | Alphanumeric and hyphens; cannot start or end with hyphen; must be globally unique.   | KMS aliases: Must begin with alias/, lowercase alphanumerics and hyphens. Secrets allow broader naming but constrained by policy. | Secret Manager: Lowercase alphanumerics and hyphens; no leading/trailing hyphens.                                          |
| **SQL Database & SQL Server**      | Disallows characters: `<`, `>`, `%`, `&`, `:`, `\`, `?`, `/`, `*`, `+`, `.`, `(`, `)`. | Amazon RDS: Lowercase letters, numbers, hyphens; no trailing hyphens or consecutive hyphens; must start with a letter.  | Cloud SQL: Lowercase letters, digits, hyphens; start with a letter, end with a letter/digit.                               |
| **Cosmos DB / NoSQL DB**           | Lowercase alphanumerics and hyphens only; no leading/trailing hyphens.               | DynamoDB Table: Alphanumerics, underscores, hyphens, periods.                                                          | Firestore/Spanner: Lowercase, alphanumerics, hyphens; specific service limits vary.                                         |
| **Container Registry**             | Lowercase alphanumerics only; must be globally unique.                                | ECR Repository: Lowercase, hyphens, underscores, periods.                                                               | Artifact Registry/GCR: Lowercase, hyphens; globally unique; project ID restrictions apply.                                 |
| **Azure Database for MySQL/PostgreSQL** | Alphanumeric and hyphens; cannot start or end with hyphen.                        | RDS: Lowercase, digits, hyphens; start with letter, no trailing/consecutive hyphens.                                   | Cloud SQL: Lowercase, digits, hyphens; no leading/trailing hyphens.                                                        |
| **Service Bus Namespace**          | Alphanumeric and hyphens; cannot start or end with hyphen.                            | SQS/SNS: Alphanumeric, hyphens, underscores; avoid leading/trailing hyphens.                                            | Pub/Sub Topic: Lowercase letters, digits, hyphens; avoid leading/trailing/consecutive hyphens.                             |
| **API Management Service**         | Alphanumerics, hyphens, and periods; must start with a letter and end with letter/digit. No consecutive hyphens. | API Gateway: Alphanumerics, hyphens, underscores, periods; start with a letter; avoid consecutive hyphens.             | API Gateway: Lowercase alphanumerics and hyphens; regex pattern [a-z]([-a-z0-9]*[a-z0-9]).                               |
| **Machine Learning Workspace**     | Alphanumeric and underscore; cannot start with underscore.                            | SageMaker: Lowercase alphanumerics and hyphens; underscores discouraged.                                                | Vertex AI: Lowercase alphanumerics and hyphens; avoid leading underscores.                                                  |
| **Serverless Functions**           | Lowercase alphanumerics and hyphens; 2–60 characters; follow DNS-like rules.          | Lambda: Letters, numbers, hyphens, underscores; avoid leading/trailing hyphens.                                         | Cloud Functions: Lowercase letters, digits, hyphens; must start with a letter and end with letter/digit.                   |
| **CloudFormation Stack / Deployment** | Not applicable; each Azure resource applies its own naming rules.                | CloudFormation Stack: Alphanumerics and hyphens; must start with a letter; no consecutive hyphens.                     | Deployment Manager: Resource-specific rules apply (e.g., GCE, Storage, etc.).                                               |
| **IAM Entities (Users, Roles, Groups)** | Flexible naming; often uses UPNs or object IDs.                                | IAM: Alphanumerics, +=,.@_-; up to ~64 characters.                                                                    | IAM: Email-like formats for users; service account IDs use lowercase letters, digits, hyphens.                              |
| **Elastic Load Balancers**        | 1–80 characters; alphanumerics and hyphens. DNS-compliant.                            | ALB/NLB: DNS-compliant; lowercase, hyphens; length varies by type (32–64 chars).                                        | GCP Load Balancer: DNS format; lowercase letters, digits, hyphens; must start/end with letter or digit.                    |
| **Pub/Sub & BigQuery Datasets**             | Not applicable (use Event Hubs, etc.).                                                | SNS/SQS: Alphanumerics, hyphens; SNS: up to 256 characters.                                                             | Pub/Sub Topics: Lowercase, digits, hyphens; 1–255 characters. BigQuery: Alphanumerics and underscores.                     |

---

### Cloud Velocity CMP – Azure Resource Naming Conventions

| Identity Name                      | Naming Convention                                                |
|-----------------------------------|------------------------------------------------------------------|
| az_user_assigned_identity_name    | pep-{projectshortname}-{sector}-{env}-{region}-0x-mi             |
| az_adf_de_name                    | pep-{projectshortname}-{sector}-{env}-{region}-de0x-adf          |
| az_adf_de_managed_vnet_ir_name   | {sector}-{projectshortname}-mir                                  |
| az_sector_blob_mpe_name          | {sector}mdipblob-mpe                                             |
| az_sector_datalake_mpe_name      | {sector}mdipadls-mpe                                             |
| az_sector_sql_mpe_name           | {sector}mdipsqlserver-mpe                                        |
| az_adf_ds_name                   | pep-{projectshortname}-{sector}-{env}-{region}-ds0x-adf          |
| az_adf_ds_managed_vnet_ir_name   | {sector}-{projectshortname}-mir                                  |
| az_adf_ds_blob_mpe_name          | {sector}mdipblob-mpe                                             |
| az_adf_ds_datalake_mpe_name      | {sector}mdipadls-mpe                                             |
| az_adf_ds_sql_mpe_name           | {sector}mdipsqlserver-mpe                                        |
| az_adf_ds_dbk_mpe_name           | {sector}mdipdbkws-mpe *(only if created)*                        |
| az_logic_app_wf_name             | pep-{projectshortname}-{sector}-{env}-{region}-0x-logicapp       |

---


## Multi-Cloud Naming Conventions

### Azure

 - **Management Group**
   -  Naming Convention:  
       - pep-<sector/scope/app>-mg
   -  Examples:
       -  pep-coreservices-mg
       -  pep-pfna-mg
       -  pep-cybersecurity-mg

 -  **Subscription**
    - Naming Convention:  
       - pep-<environment>-<scope/sector/app>-<sequence number>-sub
    - Examples:
       -  pep-prod-snt-01-sub
       -  pep-prod-pbna-01-sub
       -  pep-prod-infratools-01-sub

### AWS

 - **Organizational Unit (OU)**
   - Naming Convention:  
     - pep-<sector/scope/app>-ou
   - Examples:
     -  pep-coreservices-ou
     -  pep-snt-ou
     -  pep-cybersecurity-ou

 - **Account**
   - Naming Convention**:  
     - pep-aws-<environment>-<scope/sector/app>-<sequence number>-act
   - Examples:
     - pep-aws-prod-snt-01-act
     - pep-aws-prod-pbna-01-act`
     - pep-aws-prod-infratools-01-act

### GCP

 - **Folder**
   - Naming Convention:  
     - pep-<sector/scope/app>-fd
   - Examples:
     - pep-coreservices-fd
     - pep-pfna-fd
     - pep-cybersecurity-fd

 - **Project**
   - Naming Convention:  
     - pep-<environment>-<scope/sector/app>-<sequence number>-prj
   - Examples:
     -  pep-prod-snt-01-prj
     -  pep-prod-pbna-01-prj
     -  pep-prod-infratools-01-prj

### Examples

  ### Naming Conventions Examples

  **Azure Resource Naming Convention Catalog**

| **Resource Type**                  | **Resource Acronym** | **Naming Convention**                                                                 | **Example**                                           |
|-----------------------------------|----------------------|----------------------------------------------------------------------------------------|-------------------------------------------------------|
| OS Disk                           | os-disk              | `<vm-name>-<resource acronym>-<environment>-<region>-<sequence number>`               | pepvmname01-os-disk-dev-scus-01                      |
| Virtual Machine                   | vm                   | PepsiCo Compute Standards                                                             | pepvmname01                                           |
| Data Disk                         | data-disk            | `<vm-name>-<resource acronym>-<environment>-<region>-<sequence number>`               | pepvmname01-data-dev-scus-disk-01                    |
| Network Interface                 | nic                  | `<vm-name>-<resource acronym>-<environment>-<region>-<sequence number>`               | pepvmname01-nic-dev-scus-01                          |
| Private Endpoint                  | pe                   | `pep-<service name>-<connected resource>-<environment>-<region>-<sequence number>-<resource acronym>` | pep-datahub-blob-dev-scus-01-pe              |
| Storage Account                   | sa                   | `pep<service name><environment><region><sequence number><resource acronym>`           | pepdatahubdevscus01sa                                |
| Application Security Group        | asg                  | `pep-<service name>-<component/function>-<environment>-<region>-<sequence number>-<resource acronym>` | pep-sap-frontend-dev-scus-01-asg            |
| Availability Set                  | aset                 | Same as above                                                                         | pep-datahub-frontend-dev-scus-01-aset                |
| Network Security Group            | nsg                  | `pep-<service name>-<environment>-<region>-<scope>-<sequence number>-<resource acronym>` | pep-paloalto-dev-scus-subnet-01-nsg         |
| NetApp Volume                     | vol                  | Same as ASG naming                                                                    | pep-datahub-frontend-dev-scus01-vol                  |
| Metric Alert Rule                 | mar                  | `<metric monitor>-<resource name>-<resource acronym>`                                 | cpupepvmname01-mar                                    |
| Key Vault                         | kv                   | `pep-<service name/application short name>-<environment>-<region>-<sequence number>-<resource acronym>` | pep-cmp-dev-scus-01-kv               |
| Smart Detector Alert Rule         | sdar                 | `Failure Anomalies <Application Insights Name>`                                       | Failure Anomalies pep-datahub-dev-scus-01-appi-sdar  |
| SQL Database                      | sqldb                | `pep-<service name>-<component/function>-<environment>-<region>-<sequence number>-<resource acronym>` | pep-datahub-frontend-dev-scus-01-sqldb     |
| Load Balancer                     | lb                   | `pep-<service name>-<environment>-<region>-<internal/external>-<sequence number>-<resource acronym>` | pep-datahub-dev-scus-internal-01-lb        |
| Proximity Placement Group         | ppg                  | Same as ASG naming                                                                    | pep-datahub-frontend-dev-scus-01-ppg                 |
| Data Factory (V2)                 | adf                  | Same as ASG naming                                                                    | pep-datahub-frontend-dev-scus-01-adf                 |
| App Service                       | as                   | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-as            |
| App Service (Slot)               | N/A                  | `<App Service>/<Slot Name>`                                                           | pep-datahub-frontend/admin-dev-scus-01-as/staging    |
| Availability Test                 | avtest               | `<App Service>-<resource acronym>`                                                    | pep-datahub-frontend/admin-dev-scus-01-as-avtest     |
| Virtual Network                   | vnet                 | Same as ASG naming                                                                    | pep-datahub-frontend-dev-scus-01-vnet                |
| Route Table                       | rt                   | `pep-<service name>-<environment>-<region>-<scope>-<sequence number>-<resource acronym>` | pep-datahub-dev-scus-network-01-rt         |
| Virtual Machine Scale Set         | vmss                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-vmss          |
| Application Insights              | appi                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-appi          |
| Managed Identity                  | mi                   | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-mi            |
| SQL Server                        | sql                  | Same as ASG naming                                                                    | pep-datahub-frontend-dev-scus-01-sql                 |
| App Service Plan                  | asp                  | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-asp           |
| Recovery Services Vault           | asr                  | `pep-<backupmetallic><schedule|retention><region><number><type>`                     | pep-bronze-scus-01-backup                            |
| Public IP Address                 | pip                  | `<Assigned to Resource>-<resource acronym>`                                           | pep-datahub-dev-scus-external-01-lb-pip              |
| Log Analytics Workspace           | la                   | Same as ASG naming                                                                    | pep-global-operations-dev-scus-01-la                 |
| Action Group                      | ag                   | `pep-<service name>-<action>-<team>-<environment>-<region>-<sequence number>-<resource acronym>` | pep-datahub-sms-operations-dev-scus-01-ag  |
| Function App                      | func                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-func          |
| Activity Log Alert Rule           | alar                 | `<Log Analytics Name>-<alert scope>`                                                  | pep-sap/pfna/operations-dev-scus-01-la-vm-reboot     |
| Event Hubs Namespace              | evhns                | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-evhns         |
| SQL Virtual Machine               | sqlvm                | PepsiCo Compute Standards                                                             | pepvmname01                                           |
| Logic App                         | logic                | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-logic         |
| Event Grid System Topic           | egst                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-egst          |
| Image                             | it                   | `<vm name>-<service>-<version>-<resource acronym>`                                    | pepvmname01-sap-v1-it                                |
| Azure Workbook                    | aw                   | Same as ASG naming                                                                    | pep-governance-compliance-dashboard-prod-01-aw       |
| Azure Databricks Service          | dbms                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-dbks          |
| Container Registry                | acr                  | `pep<service name><component/function><environment><region><sequence number><resource acronym>` | pepdatahubsalesdevscus01acr               |
| Application Gateway               | agw                  | Same as ASG naming                                                                    | pep-shared-restricted-dev-scus-01-agw                |
| Template Spec                     | arm                  | Same as ASG naming                                                                    | pep-datahub-virtualmachines-dev-global-01-arm        |
| Dedicated SQL Pool                | syndp                | `pep<service><component/function><env><region><#><acronym>`                           | pepdatahubsalesdevscus01sundp                        |
| Synapse Workspace                 | synw                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-synw          |
| SSH Key                           | ssh                  | `<Resource Name>-<sequence>-<resource acronym>`                                       | pepvmname01-01-ssh                                    |
| Automation Account                | aa                   | Same as ASG naming                                                                    | pep-pfna/adlogging-dev-scus-01-aa                    |
| Event Grid Topic                  | evgt                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-evgt          |
| Shared Dashboard                  | sd                   | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-sd            |
| Language Understanding (LUIS)     | lu                   | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-lu            |
| App Gateway WAF Policy            | wafp                 | Same as ASG naming                                                                    | pep-datahub-detection-sso-dev-scus-01-wafp           |
| Kubernetes Service                | aks                  | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-aks           |
| Azure MySQL - Single              | mysqlsng             | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-mysqlsng      |
| Front Door WAF Policy             | fdfp                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-fdfp          |
| Front Door & CDN Profile          | afd                  | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-afd           |
| Service Bus Namespace             | sbns                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-sbns          |
| Connection                        | conn                 | `pep-<source>-<region>-<destination>-<sequence number>-<resource acronym>`            | pep-ergw-ae-frankfurt-01-conn                         |

>  **AWS Resource Naming Convention Catalog**
| **Resource Type**                      | **Acronym** | **Naming Pattern**                                                                                   | **Example**                                               |
|---------------------------------------|-------------|-------------------------------------------------------------------------------------------------------|-----------------------------------------------------------|
| Activity Log Alert Rule               | alar        | `<Log Analytics Name>-<alert scope>`                                                                  | `pep-sap/pfna/operations-dev-scus-01-la-vm-reboot`       |
| Event Hubs Namespace                  | evhns       | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-evhns`           |
| SQL Virtual Machine                   | sqlvm       | `PepsiCo Compute Standards`                                                                           | `pepvmname01`                                            |
| Logic App                             | logic       | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-logic`           |
| Event Grid System Topic              | egst        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-egst`            |
| Image                                 | it          | `<VM Name>-<service>-<version>-<resource acronym>`                                                    | `pepvmname01-sap-v1-it`                                  |
| Azure Workbook                        | aw          | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-governance-compliance-dashboard-prod-01-aw`         |
| Azure Databricks Service              | dbms        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-dbks`            |
| Container Registry                    | acr         | `pep<service><component/function><env><region><seq#><resource acronym>`                              | `pepdatahubsalesdevscus01acr`                            |
| Application Gateway                   | agw         | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-shared-restricted-dev-scus-01-agw`                  |
| Template Spec                         | arm         | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-virtualmachines-dev-global-01-arm`          |
| Dedicated SQL Pool                    | syndp       | `pep<service><component/function><env><region><seq#><resource acronym>`                              | `pepdatahubsalesdevscus01sundp`                          |
| Synapse Workspace                     | synw        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-synw`            |
| SSH Key                               | ssh         | `<ResourceName>-<seq#>-<resource acronym>`                                                            | `pepvmname01-01-ssh`                                     |
| Automation Account                    | aa          | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-pfna/adlogging-dev-scus-01-aa`                      |
| Event Grid Topic                      | evgt        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-evgt`            |
| Shared Dashboard                      | sd          | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-sd`              |
| Language Understanding                | lu          | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-lu`              |
| Application Gateway WAF Policy       | wafp        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-detection-sso-dev-scus-01-wafp`             |
| Kubernetes Service (AKS)             | aks         | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-aks`             |
| Azure DB for MySQL - Single Server    | mysqlsng    | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-mysqlsng`        |
| App Service (Slot)                    | N/A         | `<App Service>/<Slot Name>`                                                                           | `pep-datahub-frontend/admin-dev-scus-01-as/staging`      |
| Search Service                        | srch        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-srch`            |
| Availability Test                     | avtest      | `<App Service>-<resource acronym>`                                                                    | `pep-datahub-frontend/admin-dev-scus-01-as-avtest`       |
| Front Door WAF Policy                 | fdfp        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-fdfp`            |
| Front Door/CDN Profiles              | afd         | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-afd`             |
| Service Bus Namespace                 | sbns        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-sbns`            |
| Connection                            | conn        | `pep-<source>-<region>-<destination>-<seq#>-<resource acronym>`                                       | `pep-ergw-ae-frankfurt-01-conn`                          |
| NetApp Account                        | anf         | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-anf`             |
| QnA Maker                             | qna         | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-qna`             |
| Cosmos DB Account                     | cosmo       | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-cosmo`           |
| Web App Bot                           | asbot       | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-asbot`           |
| Azure Cache for Redis                 | redis       | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-redis`           |


## Sample Use Cases
  - TBD

## Additional Guardrails
  - Enforce lowercase only across all naming segments.
  - No underscores (_), spaces, or periods (.) are permitted.
  - For resources that cannot contain dashes (like storage accounts), use concatenated segments without separators.
  - Sequence numbers should be two digits, zero-padded (e.g., 01, 02).
  - Naming must comply with cloud vendor maximum length limits (63 chars).
  - Network-related resources must explicitly reference connected entities (e.g., VNET, subnet) in their names for clarity.
  - Avoid peering isolated or special network resources with routable enterprise VNets unless explicitly approved.
  - Naming must comply with cloud vendor maximum length limits (63 chars).
  - Network-related resources must explicitly reference connected entities (e.g., VNET, subnet) in their names for clarity.
  - Avoid peering isolated or special network resources with routable enterprise VNets unless explicitly approved.
  - Use the internal VM Name Generator tool for virtual machines and EC2 instance names to maintain standardization.
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 22 June 2025 (Sakshi Sharma)
