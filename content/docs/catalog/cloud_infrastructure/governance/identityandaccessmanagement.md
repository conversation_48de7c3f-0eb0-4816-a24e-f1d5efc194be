---
weight: 2
title: "Identity and Access Management"
date: 2025-01-07
tags: ["multi cloud", "Identity", "Access Management", ""]
summary: "Identity and Access Management"
---

## Cloud Governance IAM

**Name**: RBAC & Privileged Identity Management Guidelines for Multi-Cloud Environments
**Description**: This strategy ensures just enough root-level access, while scoping access to second-level MGs/OUs for key teams using Just-in-Time (JIT) access via PIM across Azure, AWS, GCP, and Alibaba Cloud.
To ensure the principle of least privilege and strong security controls across Azure, AWS, GCP, and Alibaba Cloud, the following governance and operational guidelines must be followed for all privileged roles and change execution processes:

## Lifecycle
  - Published Date: 05-07-2025

## Goals & Benefits
  - TBD
  
## Guidelines
  ### General Principles
  - **Least Privilege**– Assign only the permissions required to perform a job function. No more, no  less.
  - **Separation of Duties**– No single user should have enough access to compromise security or operations.
  - **Explicit Role Assignment**– All users and services must be assigned a role. No implicit or default access.
  - **No Shared Accounts**– Every user must have an individual account with traceable actions.
  - **Time-Limited Privileges**– Temporary access must be assigned with an automatic expiration date.

  ### Role Design
  - **Use Predefined Roles First**– Always use existing roles if they meet the needs. Custom roles must be justified and approved by IAM Team
  - **Job Function-Based Roles**– Define roles based on job functions, not individuals.
  - **No Direct Resource Assignment**– Assign permissions to groups, not directly to users.
  - **Minimize Role Overlap**– Avoid excessive role granularity that leads to confusion.
  - **Restrict Role Inheritance**– If a role hierarchy is used, ensure that inherited permissions are strictly necessary.

  ### Permissions Management
  - **No Wildcard Permissions**– Do not grant overly broad permissions (* or AllActions).
  - **Read-Only vs. Write Access**– Separate roles for read-only and write access.
  - **No Admin Privileges by Default**– Administrator roles must be explicitly granted, never assumed.
  - **Application vs. Infrastructure Access**– Separate application-level access from infrastructure/system/platform access.
  - **Monitor and Review Permissions**– Audit permissions regularly (e.g., quarterly) and revoke unnecessary access.

  ### User and Group Management
  - **Use Groups for Assignment**– Assign roles to groups, not individual users.
  - **Group Membership Approval Process**– All role assignments must go through an approval workflow and recorded in ITSM Tool
  - **External Users Require Justification**– Non-employees (e.g., vendors, contractors) must have documented approval.
  - **Role-Based Onboarding/Offboarding**– Automatically grant and revoke access based on employment status.
  - **Multi-Factor Authentication (MFA) for Privileged Roles**– MFA and secondary account is mandatory for all account with privileged permissions.

  ### Service and Automation Accounts
  - **Use Separate Accounts for Automation** – Do not use human accounts for automated tasks.
  - **Limit API Key and Token Lifetimes** – Expire service credentials at regular intervals.
  - **Use Scoped Access for Applications** – Applications should have only the permissions they need.
  - **No Hardcoded Credentials** – Do not store passwords or API keys in code or repositories.
  - **Monitor Service Accounts** – Review and rotate credentials periodically.
 
  ### Auditing & Compliance
  - **Logging Must Be Enabled** – All access and role changes must be logged.
  - **Review High-Privilege Roles Monthly** – Critical roles must undergo frequent audits.
  - **Alert on Privilege Escalation** – Automatically notify security teams of unexpected privilege increases.
  - **Access Requests Must Be Documented** – All changes to roles must be logged with an approval record.
  - **Automated Expiry for Temporary Access** – Time-based access must auto-expire and be reviewed before renewal.

 ## Just-in-Time (JIT) Privileged Access Enforcement
  - All privileged roles in Azure, AWS, GCP, and Alibaba Cloud must be assigned via a Just-in-Time (JIT) mechanism, similar to Azure Privileged Identity Management (PIM), to limit persistent elevated access. 

| **Control**                        | **Azure**                     | **AWS**                      | **GCP**                    | **Alibaba Cloud**         |
|-----------------------------------|-------------------------------|------------------------------|----------------------------|---------------------------|
| **Deny Permanent Global Access**  | ✅ PIM                        | ✅ SCPs                      | ✅ IAM Conditions          | ✅ RAM Policies           |
| **Require JIT for Admin Roles**   | ✅ PIM (Just-In-Time Access)  | ✅ IAM STS (Session Token)   | ✅ IAM Conditions          | ✅ RAM Role Activation    |
| **Least Privilege Enforcement**   | ✅ Azure Policy               | ✅ IAM Policies              | ✅ IAM Recommender         | ✅ RAM Analyzer           |
| **Quarterly Access Reviews**      | ✅ PIM Access Reviews         | ✅ IAM Access Analyzer       | ✅ IAM Audit Logs          | ✅ RAM Audit              |

| **Cloud Provider** | **JIT Mechanism** |
|--------------------|------------------|
| **Azure**          | Azure AD Privileged Identity Management (PIM) for RBAC roles and Entra ID Privileged Access |
| **AWS**            | AWS IAM Identity Center (SSO) with IAM Roles + AWS Security Token Service (STS) for temporary role assumption |
| **GCP**            | Google Cloud IAM Recommender + Just-In-Time IAM Access (via Google Cloud Security Command Center) |
| **Alibaba Cloud**  | Alibaba RAM (Resource Access Management) with STS `AssumeRole` & Temporary Access Tokens |

 ## Change Execution Flow
  - **Request Access**:
    - Users must request JIT access via PIM or equivalent before executing privileged actions.
    - Requests must include justification and expected duration.
    - Time-bound access should not exceed predefined thresholds.
  - **Approval by Owner Team**:
    - The Owner Team of the affected MG/OU/FLD must review and approve access requests before elevation.
    - Approvals should be logged for auditability.
    - Emergency access should be logged, monitored, and reviewed retrospectively.
  - **Execution of Changes**:
    - Once access is granted and approved, the user can perform necessary actions.
    - The system should automatically revoke access upon request expiry.
  - **Post-Change Review & Audit**:
    - All privileged actions must be logged and reviewed periodically.
    - Unauthorized or excessive access requests must trigger alerts for investigation.

 ## Ownership of Management Group/Organization Units/Folders/Resource Directory

| **MG / OU / FL / RD Name**      | **Details**                                                                                                                                                                                                                                    | **Owner Team**                     |
|----------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------|
| **Tenant Root Group**            | Root tenant account, used only as the parent of the PepsiCo root management group. IAM Team can only perform approved changes authorized by the Cloud Engineering Team.                                                                        | IAM Team (Changes via Cloud Engineering Team) |
| **pep-root-mg**                  | PepsiCo Root Management Group.                                                                                                                                                                                                                 | Cloud Engineering Team             |
| **pep-coreservices-<>**         | Contains platform child management groups such as connectivity, infrastructure, or management.                                                                                                                                                 | Cloud Engineering Team             |
| **pep-cybersecurity-<>**        | Contains a dedicated subscription for the Security team.                                                                                                                                                                                       | Information Security Engineering Team |
| **pep-enterpriseapplications-<>** | Parent MG for all workload MGs such as SAP, Data Hub, Sector/Region, or Global Production. Contains workload-agnostic Azure policies for security and compliance.                                         | Cloud Operations Team              |
| **pep-legacy-<>**               | Dedicated MG for landing zones not part of the Cloud Adoption Program, typically created earlier and managed by a different MSP.                                                                                                              | Cloud Operations Team              |
| **pep-automation-<>**           | Hosts automation zones (e.g., CMP) that support the broader PepsiCo cloud landing zones.                                                                                                                                                      | TBD                                 |
| **pep-microsoftapplications-<>** | Hosts Microsoft-specific services such as App Center, Azure DevOps, etc.                                                                                                                                                                      | TBD                                 |
| **pep-sandbox-<>**              | Used for testing and exploration by orgs. Disconnected from corporate networks, with less restrictive policies.                                                                                                                               | Cloud Solution Architecture        |
| **pep-learning-<>**             | Used for training or learning subscriptions with short lifespans (< 1 month).                                                                                                                                                                 | Cloud Operations Team              |
| **pep-isolated-<>**             | Isolated landing zones that do not follow PepsiCo’s enterprise security, operations, or governance models.                                                                                                                                    | Cloud Operations Team              |
| **pep-decommissioned-<>**       | Landing zones moved here for cancellation and deletion by Azure (within 30–60 days).                                                                                                                                                          | Cloud Operations Team              |
| **pep-<sector/market>-<>**      | Sector-specific groups under `pep-enterpriseapplications-<>`. Managed by respective sector leads.                                                                                                                                            | Sector Lead                        |
 
 ## Super Admin Process
  
  ### Objective
  - Define the general security requirements for break-glass top level admin cloud accounts in 
any PepsiCo managed cloud tenant for which identity access is supported and managed 
by the S&T CyberSecurity IAM Directory Services team.
Current top-level admin roles in PepsiCo tenants requiring break-glass account access:
   - Microsoft Azure: Global Administrator
   - Amazon Web Services (AWS): Root user
   - Google Cloud Platform (GCP): Super Admin

  ### Security Considerations
   - Break-glass accounts are cloud-native accounts which are intended to provide emergency 
root-level administrative access to PepsiCo cloud services in the event of any issues 
experienced with identity provider federations which provide user and administrative 
access. Break-glass accounts are not intended for day-to-day use or BAU type work. Since 
an identity provider-based federation only provides access to external (non-cloud native) 
identities, break-glass accounts are necessary for the following reasons:
   - The authentication provider federation service experiences any kind of outage which 
prevents authentication of federated identity access to the cloud service.
   - Any updates or changes to the federation service typically require a cloud-native 
account to perform those changes. Removing and re-adding an authentication 
federation can only be performed using a cloud-native account.
  - Like federated accounts which provide administrative access, cloud-native accounts
(including break-glass) must be both secured and monitored to prevent account 
compromise. This is most important for accounts which provide the highest level of access
to the target cloud.
 
  ### Recommendations
   - **Redundancy**
      - It is recommended to create two Break-Glass top-level accounts per tenant, with a 
separate account owner for each account. This is recommended in case access to one 
account is blocked (owner is on vacation/ETC) and the account is needed immediately for 
a break-fix situation or incident
   - **Access Security**
     - Every Break-Glass account should be configured with at least one multi-factor (MFA) 
option to prevent compromise and ensure access is achieved with the knowledge of the 
organization. MFA applications such as Okta Verify or MS Authenticator are required & 
preferred to Text/Phone options. Also, MFA management must be performed by the Tenant 
service owner (Pepsi Director level or higher) to keep MFA access & ownership separate 
from the account operator. This ensures at least two members of the Pepsi organization are 
involved / approving when using the account to access the tenant.
   - **Access Monitoring**
     - All Break-Glass accounts should be configured for monitoring (via Splunk) for threat 
detection purposes. Any account usage should be validated by the Threat Detection team 
with the perpetrator to ensure account usage is expected. 
The minimum activity monitoring items required are:
        - Account logon to Azure
        - MFA is required & issued
        - Role assignment modification in Azure RBAC and Entra
        - Conditional access policy modification
        - Privileged Identity Management (PIM) Activities – Activation and deactivation of 
privileged roles in Azure AD PIM.
        - Changes to Security Groups – Creation, deletion, or modification of security groups 
in Entra ID.

  ### Request process for cloud accounts
   - Raise an **EntraID Service Request** catalog request through ServiceNow (https://pepsico.service-now.com/)
   - Specify the tenant (Azure/AWS/GCP/etc), the required tenant top-level role, and the naming 
parameters
   - ID naming format is as follows
      - Name: BGA - IAM %TenantRole% %counter%
         - Example: BGA - IAM Global Admin 1 (for Azure)
      - UserPrincipalName (UPN): <EMAIL>
         - Example: <EMAIL>
   - The account ID and password will be stored in the IAM Directory Services team vault in 
myPAM using the **WD_Domain_NonManaged** platform as the password will be non-managed.


## Azure
 - The access flow begins at the Root Management Group (pep-root-mg), where Azure PIM is enabled to control elevated access via eligible role assignments. Roles such as Cloud Engineering, Cloud Network Ops, and Cloud Operations specific permissions  are defined at the Management Group (MG) with respective PIM permissions as shown on the diagram and depicted in the table bellow.

 ### PIM Assignment Types

| **Assignment Type**     | **Description**                                                                 | **Use Case**                                                  | **PepsiCo Guideline**                                |
|--------------------------|----------------------------------------------------------------------------------|----------------------------------------------------------------|--------------------------------------------------------|
| **Eligible**             | User can activate the role when needed but doesn’t have it by default. Requires justification/MFA before activation. | JIT access for Admins, Security Teams, and DevOps.             | ✅ Approved                                             |
| **Active**               | User has permanent role access. No activation required.                         | For roles that require continuous access (e.g., Monitoring).    | ⚠️ Only up to Subscription level and only for reader roles |
| **Expiring (Time-Bound)** | Role is assigned for a specific duration (start & end date).                   | Temporary projects, contractors, or special tasks.              | ❌ Not Applicable                                       |
| **Permanent Eligible**   | User is always eligible to activate the role but does not have active access by default. | Ensures security while allowing access when required.          | ✅ Approved                                             |
| **Permanent Active**     | User has a permanent role assignment with no activation required.               | Avoid unless necessary for system-critical roles.               | ❌ Not Approved                                         |

![Visual Representation](/images/PIM_assignment_type.png)

 ### RBAC Design

| **Role/Team**                                | **Permissions**                                                                                                                                                                                                                                                                                                       | **Scope**                                                                                     | **PIM**                 | **Approval**                  | **Account**       |
|----------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------|-------------------------|--------------------------------|-------------------|
| IAM Engineering                              | User Access Administrator                                                                                                                                                                                                                                                                                             | Tenant/Organization                                                                          | Eligible                | Cloud Engineering              | Admin Account     |
| Cloud Engineering                            | Contributor                                                                                                                                                                                                                                                                                                            | pep-root-<>                                                                                   | Eligible                | IAM Engineering                | Admin Account     |
| FinOps                                       | Billing Contributor                                                                                                                                                                                                                                                                                                   | pep-root-<>                                                                                   | Permanent Eligible      | N/A                            | GPID              |
| Cloud Security Ops                           | Security Admin                                                                                                                                                                                                                                                                                                         | pep-root-<>                                                                                   | Eligible                | Cloud Engineering              | Admin Account     |
| Cloud Observability                          | Monitoring Contributor                                                                                                                                                                                                                                                                                                | pep-root-<>                                                                                   | Eligible                | Cloud Engineering              | Admin Account     |
| Cloud Governance                             | Policy Contributor                                                                                                                                                                                                                                                                                                    | pep-root-<>                                                                                   | Eligible                | Cloud Engineering              | Admin Account     |
| Cloud Architect                              | Contributor                                                                                                                                                                                                                                                                                                            | pep-sandbox-<>                                                                                 | Permanent Eligible      | N/A                            | Admin Account     |
| Enterprise DevOps Team                       | DevOps Contributor                                                                                                                                                                                                                                                                                                    | pep-root-<>                                                                                   | Eligible                | Cloud Engineering              | Admin Account     |
| Enterprise Backup and DR Team                | Backup Contributor                                                                                                                                                                                                                                                                                                    | pep-root-<>                                                                                   | Eligible                | IAM Engineering                | Admin Account     |
| IAM Engineering                              | Contributor, User Access Administrator                                                                                                                                                                                                                                                                                | pep-cybersecurity-<>                                                                          | Eligible                | IAM Leadership                 | Admin Account     |
| Cloud Operations L1                          | Reader                                                                                                                                                                                                                                                                                                                 | pep-root-<>                                                                                   | Permanent Eligible      | N/A                            |                   |
|                                              | Monitoring Reader                                                                                                                                                                                                                                                                                                      | pep-root-<>                                                                                   |                         |                                |                   |
|                                              | Support Request Contributor                                                                                                                                                                                                                                                                                            | pep-root-<>                                                                                   |                         |                                |                   |
| Cloud Network Ops                            | Network Contributor                                                                                                                                                                                                                                                                                                   | All 1st Layer Management Group except pep-cybersecurity-<>                                    | Eligible                | Cloud Engineering              | Admin Account     |
|                                              | DNS Zone Contributor, Private DNS Zone Contributor                                                                                                                                                                                                                                                                     |                                                                                               |                         |                                |                   |
| Cloud Network Ops                            | Network Contributor                                                                                                                                                                                                                                                                                                   | pep-cybersecurity-<>                                                                          | Eligible                | IAM Engineering                | Admin Account     |
| Cloud Operations L2                          | Virtual Machine Operator, Virtual Machine Administrator Login, Storage Account Contributor, Load Balancer Operator, Kubernetes Cluster Operator, Monitoring Contributor, SQL Server Contributor, Cosmos DB Operator, Service Bus Data Owner, Event Hubs Data Receiver, Backup Operator, Security Operator, Key Vault Secrets Officer, App Service Contributor | All 1st Layer Management Group except pep-cybersecurity-<>                                    | Eligible                | Cloud Operations L3            | Admin Account     |
| Cloud Operations L2                          | Same as above                                                                                                                                                                                                                                                                                                          | pep-cybersecurity-<>                                                                          | Eligible                | IAM Engineering                | Admin Account     |
| Cloud Operations L3                          | User Access Administrator, Contributor                                                                                                                                                                                                                                                                                 | pep-automation-<>                                                                             | Eligible                | Cloud Engineering              | Admin Account     |
|                                              |                                                                                                                                                                                                                                                                                                                        | pep-microsoftapplication-<>                                                                    | Eligible                | Enterprise DevOps Team         |                   |
|                                              |                                                                                                                                                                                                                                                                                                                        | pep-legacy-<>                                                                                  | Permanent Eligible      | N/A                            |                   |
|                                              |                                                                                                                                                                                                                                                                                                                        | pep-sandbox-<>                                                                                 |                         |                                |                   |
|                                              |                                                                                                                                                                                                                                                                                                                        | pep-isolated-<>                                                                                |                         |                                |                   |
|                                              |                                                                                                                                                                                                                                                                                                                        | pep-decommissioned-<>                                                                          |                         |                                |                   |
|                                              |                                                                                                                                                                                                                                                                                                                        | pep-learning-<>                                                                                |                         |                                |                   |
|                                              |                                                                                                                                                                                                                                                                                                                        | pep-coreservices-<>                                                                            | Eligible                | Cloud Engineering              |                   |
|                                              |                                                                                                                                                                                                                                                                                                                        | pep-cybersecurity-<>                                                                           | Eligible                | IAM Engineering                |                   |
|                                              |                                                                                                                                                                                                                                                                                                                        | pep-enterpriseapplication-<>                                                                   | Eligible                | TBD                            |                   |
| Application/Service Owner                    | Reader                                                                                                                                                                                                                                                                                                                 | Application specific Resource Groups                                                           | Permanent Eligible      | N/A                            | Admin Account     |

- All roles will be assigned reader permissions on the scope they have elevated permissions without PIM to be requested. 
- Permission that are Management Group/Organization Sector Specific cannot be assigned above scope (2nd level MG/OU).
- Permissions that are Resource Group specific to an Application cannot be assigned above respective subscription scope.

- For Application specific permissions the teams, filtered based on the resource groups tagged with the respective “serviceName” (or pep-cmdb-bus-app-name), the Application teams will receive: 

  - Reader permissions for Production resources

  - Least access privilege permissions for lower environment based on their roles as defined in the IAM guidelines. PIM will be approved by the Applicaiton Owner
    
![Visual Representation](/images/RBAC_design.png)

  ## RBAC for Service Principal Names(SPNs)
  - The Guidelines Table provides a structured overview of Service Principal Names (SPNs), detailing their intended use, required permissions, and appropriate scope. 
  - This table ensures that each SPN is assigned only the necessary roles at the minimum required scope, following the principle of least privilege to enhance security and compliance.

  | **Guideline**              | **Why It’s Important**                              | **Example** |
|----------------------------|----------------------------------------------------|-------------|
| Least Privilege Access | Limits risk of privilege escalation                | Use specific roles like *Storage Blob Data Reader* instead of *Contributor* |
| Scoped Assignments     | Prevents unnecessary broad access                  | Assign SPNs to resource groups instead of subscriptions or management groups |
| Managed Identities     | Removes need for manual credential storage         | Use Managed Identity instead of SPN when possible |
| Rotate Credentials     | Prevents unauthorized long-term access             | Rotate SPN secrets using Azure Key Vault and Automation |
| Conditional Access     | Protects against unauthorized access               | Restrict SPN sign-in to specific IP ranges |
| Use PIM for JIT Access | Reduces exposure time of sensitive permissions     | Grant permissions only when needed for automation, remove after execution |
| Monitor & Audit SPNs   | Detects security risks early                       | Use Log Analytics to track SPN sign-ins and access |

- The Use Case Table categorizes different Azure SPN applications such as billing, security, deployments, networking, and automation. It outlines the specific permissions required for each use case and the recommended scope at which these permissions should be applied. 
- This helps organizations streamline access management while maintaining control over critical resources

| **SPN Use Case**                               | **Required Permissions**                                                                                      | **Scope**                                                       |
|------------------------------------------------|----------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------|
| Billing & Cost Management                  | Cost Management Contributor, Billing Reader                                                                   | pep-root-mg                                                      |
| Security & Compliance                      | Security Administrator, Security Operator, Security Reader                                                    | pep-root-mg                                                      |
| Infrastructure Deployments (CMP)           | Deployment Manager                                                                                             | pep-root-mg                                                      |
| Azure Policy & Governance                   | Policy Contributor, Management Group Contributor                                                              | pep-root-mg                                                      |
| Backup & Disaster Recovery                  | Backup Contributor, Storage Account Contributor                                                               | pep-root-mg                                                      |
| Monitoring & Logging                        | Monitoring Contributor, Log Analytics Contributor                                                             | pep-root-mg                                                      |
| Application Access (Key Vault, Databases, APIs) | Key Vault Secrets Officer, Database Reader/Contributor, App Configuration Data Owner                          | Application Specific Resource Group                              |
| Reporting                                   | Reader                                                                                                         | Application Specific Resource Group / Subscription               |
| DevOps Pipelines & Automation               | Deployment Manager                                                                                             | Application Specific Resource Group / Subscription / Sector Management Group |

 
## AWS
 - TBD
 
## GCP
  ### **Current SSO Set Up**

  ![.](/images/SSO.png)
  - All users like Employees and Contractor information already available in OKTA through IDX integration.​
  - There is a synch in place to GCP via GCDS synch tool to provision all Employee and Contractor information. This is a requirement for myPepsiCo application to search any PepsiCo user through GCP.
  - Users will login to GCP via OKTA SSO​


  ### Current SSO Set Up– IDX Sync

  - **Linux object sync (Identity Provider = idX LDAP)​:**
    - WebSphere sync was configured to provision all idX user & group accounts into GCP to facilitate a user search directory accessible to all mypepsico.com apps. These objects must be maintained until such time that all mypepsico.com apps have migrated into ServiceNow. The GCP sync configuration was managed using the GCDS sync configuration tool (https://tools.google.com/dlpage/dirsync/thankyou.html?hl=en ).

    - **Connectors:**
      - **GCP:** oAuth2 access tokens​
      - **idX:** encrypted service account credentials​
    - **Schedule:** 6 PM nightly​
    - **Source data:** idX user data input file​
    - **Notable option:** the sync configuration was recently updated to not delete GCP objects not found in the source input file/directory. Prior to this the sync would “cleanup” idX deleted objects from GCP.
  - **Summary:** This sync is being maintained for mypepsico.com app business requirements and will not overwrite objects provisioned by alternate sync methods.​

​ ​
​  ​### AD Integration for Foundation

  ### AD Integration for Foundation with GCDS Synch

  ### Permission Sets

  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 1 July 2025 (Sakshi Sharma)
