name: Azure Static Web Apps CI/CD

pr:
  branches:
    include:
      - main
trigger:
  branches:
    include:
      - main

jobs:
- job: build_and_deploy_job
  displayName: Build and Deploy Job
  condition: or(eq(variables['Build.Reason'], 'Manual'),or(eq(variables['Build.Reason'], 'PullRequest'),eq(variables['Build.Reason'], 'IndividualCI')))
  pool:
    vmImage: ubuntu-latest
  variables:
  - group: Azure-Static-Web-Apps-zealous-dune-08185a910-variable-group
  steps:
  - checkout: self
    submodules: true
  
  # Step to clean old files
  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        echo "Cleaning old files from the build directory"
        sudo rm -rf /public/*           # Change `/public/*` to the actual path of old build files if necessary
        echo "Clean up complete"
  - task: Bash@3
    inputs:
      targetType: 'inline'
      script: |
        curl -Ls https://github.com/gohugoio/hugo/releases/download/v0.128.0/hugo_0.128.0_Linux-64bit.tar.gz  -o /tmp/hugo.tar.gz
        tar xf /tmp/hugo.tar.gz -C /tmp
        sudo mv /tmp/hugo /usr/bin/hugo
        sudo rm -rf /tmp/hugo*
        hugo --enableGitInfo
  - task: AzureStaticWebApp@0
    inputs:
      azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_ZEALOUS_DUNE_08185A910)
      app_location: "/public" # App source code path
      api_location: "" # Api source code path - optional
      output_location: "/public" # Built app content directory - optional