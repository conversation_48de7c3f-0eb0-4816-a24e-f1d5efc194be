{"routes": [{"route": "/*", "allowedRoles": ["authenticated"]}], "auth": {"identityProviders": {"azureActiveDirectory": {"registration": {"openIdIssuer": "https://login.microsoftonline.com/42cc3295-cd0e-449c-b98e-5ce5b560c1d3/v2.0", "clientIdSettingName": "AZURE_CLIENT_ID", "clientSecretSettingName": "AZURE_CLIENT_SECRET"}}}}, "responseOverrides": {"401": {"redirect": "/.auth/login/aad?post_login_redirect_uri=.referrer", "statusCode": 302}}}