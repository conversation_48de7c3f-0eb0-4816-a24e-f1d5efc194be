<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>ToDo on PepGenX Platform</title>
    <link>http://localhost:1313/docs/pepgenxdev/todo/</link>
    <description>Recent content in ToDo on PepGenX Platform</description>
    <generator>Hugo</generator>
    <language>en</language>
    <copyright>PepsiCo 2024</copyright>
    <atom:link href="http://localhost:1313/docs/pepgenxdev/todo/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>Korzun</title>
      <link>http://localhost:1313/docs/pepgenxdev/todo/korzun/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/todo/korzun/</guid>
      <description>To be done # Run web based markdown editor for developers writing documentation PepGenX Services Status page Required container interfaces and SIGTERM signals handling Lifecycle hooks AKS deisgn and configuration for different Data Classification Network Policy configuration Service Account with no access Function Container in an application POD DNS on node Topology aware routing Service internal traffic policy Istio / Kiali / Jaeger&#xA;Consumer access using Consumer pattern&#xA;Employee access</description>
    </item>
  </channel>
</rss>
