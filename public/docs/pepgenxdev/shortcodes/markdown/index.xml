<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Markdown on PepGenX Platform</title>
    <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/</link>
    <description>Recent content in Markdown on PepGenX Platform</description>
    <generator>Hugo</generator>
    <language>en</language>
    <copyright>PepsiCo 2024</copyright>
    <atom:link href="http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>Overview</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/overview/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/overview/</guid>
      <description>Overview # Nearly all Markdown applications support the basic syntax outlined in the original Markdown design document.&#xA;<PERSON> uses the Goldmark Markdown processor which is fully CommonMark-compliant.&#xA;GitHub ... GitHub: yuin/goldmark&#xA;About: A markdown parser written in Go. Easy to extend, standard(CommonMark) compliant, well structured. PepGenX standard notation # Headings # First heading of a page must always start with #.&#xA;All subsequent headings at the same level as first heading should also use #.</description>
    </item>
    <item>
      <title>Blockquotes</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/blockquotes/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/blockquotes/</guid>
      <description>Blockquotes # To create a blockquote, add a &amp;gt; in front of a paragraph.&#xA;&amp;gt; Dorothy followed her through many of the beautiful rooms in her castle. The rendered output looks like this:&#xA;Dorothy followed her through many of the beautiful rooms in her castle.&#xA;Blockquotes with Multiple Paragraphs # Blockquotes can contain multiple paragraphs. Add a &amp;gt; on the blank lines between the paragraphs.&#xA;&amp;gt; Dorothy followed her through many of the beautiful rooms in her castle.</description>
    </item>
    <item>
      <title>Bold</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/bold/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/bold/</guid>
      <description>Bold # To bold text, add two asterisks or underscores before and after a word or phrase. To bold the middle of a word for emphasis, add two asterisks without spaces around the letters.&#xA;Markdown HTML Rendered Output I just love **bold text**. I just love &amp;lt;strong&amp;gt;bold text&amp;lt;/strong&amp;gt;. I just love bold text. I just love __bold text__. I just love &amp;lt;strong&amp;gt;bold text&amp;lt;/strong&amp;gt;. I just love bold text. Love**is**bold Love&amp;lt;strong&amp;gt;is&amp;lt;/strong&amp;gt;bold Loveisbold Bold Best Practices # Markdown applications don&amp;rsquo;t agree on how to handle underscores in the middle of a word.</description>
    </item>
    <item>
      <title>Code</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/code/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/code/</guid>
      <description>Code # To denote a word or phrase as code, enclose it in backticks (`).&#xA;Markdown HTML Rendered Output At the command prompt, type `nano`. At the command prompt, type &amp;lt;code&amp;gt;nano&amp;lt;/code&amp;gt;. At the command prompt, type nano. Escaping Backticks # If the word or phrase you want to denote as code includes one or more backticks, you can escape it by enclosing the word or phrase in double backticks (``).</description>
    </item>
    <item>
      <title>Emoji</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/emoji/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/emoji/</guid>
      <description>Emoji # There are two ways to add emoji to Markdown files: copy and paste the emoji into your Markdown-formatted text, or type emoji shortcodes.&#xA;Copying and Pasting Emoji # In most cases, you can simply copy an emoji from a source like Emojipedia and paste it into your document. Many Markdown applications will automatically display the emoji in the Markdown-formatted text. The HTML and PDF files you export from your Markdown application should display the emoji.</description>
    </item>
    <item>
      <title>Emphasis</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/emphasis/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/emphasis/</guid>
      <description>Bold and Italic # To emphasize text with bold and italics at the same time, add three asterisks or underscores before and after a word or phrase. To bold and italicize the middle of a word for emphasis, add three asterisks without spaces around the letters.&#xA;Markdown HTML Rendered Output This text is ***really important***. This text is &amp;lt;em&amp;gt;&amp;lt;strong&amp;gt;really important&amp;lt;/strong&amp;gt;&amp;lt;/em&amp;gt;. This text is really important. This text is ___really important___.</description>
    </item>
    <item>
      <title>Escaping Characters</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/escaping-characters/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/escaping-characters/</guid>
      <description>Escaping Characters # To display a literal character that would otherwise be used to format text in a Markdown document, add a backslash (\) in front of the character.&#xA;\* Without the backslash, this would be a bullet in an unordered list. The rendered output looks like this:&#xA;* Without the backslash, this would be a bullet in an unordered list.&#xA;Characters You Can Escape # You can use a backslash to escape the following characters.</description>
    </item>
    <item>
      <title>Fenced Code Blocks</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/fenced-code-blocks/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/fenced-code-blocks/</guid>
      <description>Fenced Code Blocks # The basic Markdown syntax allows you to create code blocks by indenting lines by four spaces or one tab. If you find that inconvenient, try using fenced code blocks. Depending on your Markdown processor or editor, you&amp;rsquo;ll use three backticks (```) or three tildes (~~~) on the lines before and after the code block. The best part? You don&amp;rsquo;t have to indent any lines!&#xA;``` { &amp;#34;firstName&amp;#34;: &amp;#34;John&amp;#34;, &amp;#34;lastName&amp;#34;: &amp;#34;Smith&amp;#34;, &amp;#34;age&amp;#34;: 25 } ``` The rendered output looks like this:</description>
    </item>
    <item>
      <title>Footnotes</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/footnotes/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/footnotes/</guid>
      <description>Footnotes # Footnotes allow you to add notes and references without cluttering the body of the document. When you create a footnote, a superscript number with a link appears where you added the footnote reference. Readers can click the link to jump to the content of the footnote at the bottom of the page.&#xA;To create a footnote reference, add a caret and an identifier inside brackets ([^1]). Identifiers can be numbers or words, but they can&amp;rsquo;t contain spaces or tabs.</description>
    </item>
    <item>
      <title>Headings</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/headings/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/headings/</guid>
      <description>Heading # To create a heading, add number signs (#) in front of a word or phrase. The number of number signs you use should correspond to the heading level. For example, to create a heading level three (&amp;lt;h3&amp;gt;), use three number signs (e.g., ### My Header).&#xA;Markdown HTML Rendered Output # Heading level 1 &amp;lt;h1&amp;gt;Heading level 1&amp;lt;/h1&amp;gt; Heading level 1 ## Heading level 2 &amp;lt;h2&amp;gt;Heading level 2&amp;lt;/h2&amp;gt; Heading level 2 ### Heading level 3 &amp;lt;h3&amp;gt;Heading level 3&amp;lt;/h3&amp;gt; Heading level 3 #### Heading level 4 &amp;lt;h4&amp;gt;Heading level 4&amp;lt;/h4&amp;gt; Heading level 4 ##### Heading level 5 &amp;lt;h5&amp;gt;Heading level 5&amp;lt;/h5&amp;gt; Heading level 5 ###### Heading level 6 &amp;lt;h6&amp;gt;Heading level 6&amp;lt;/h6&amp;gt; Heading level 6 Alternate Syntax # Alternatively, on the line below the text, add any number of == characters for heading level 1 or -- characters for heading level 2.</description>
    </item>
    <item>
      <title>Horizontal Rules</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/horizontal-rules/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/horizontal-rules/</guid>
      <description>Horizontal Rules # To create a horizontal rule, use three or more asterisks (***), dashes (---), or underscores (___) on a line by themselves.&#xA;*** --- _________________ The rendered output of all three looks identical:&#xA;Horizontal Rule Best Practices # For compatibility, put blank lines before and after horizontal rules.&#xA;✅&amp;nbsp; Do this ❌&amp;nbsp; Don&#39;t do this Try to put a blank line before...&#xA;---&#xA;...and after a horizontal rule.</description>
    </item>
    <item>
      <title>Images</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/images/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/images/</guid>
      <description>Images # To add an image, add an exclamation mark (!), followed by alt text in brackets, and the path or URL to the image asset in parentheses. You can optionally add a title in quotation marks after the path or URL.&#xA;![The San Juan Mountains are beautiful!](/assets/images/san-juan-mountains.jpg &amp;#34;San Juan Mountains&amp;#34;) The rendered output looks like this:&#xA;{% include image.html file=&amp;quot;/assets/images/san-juan-mountains.jpg&amp;quot; alt=&amp;ldquo;The San Juan Mountains are beautiful!&amp;rdquo; title=&amp;ldquo;San Juan Mountains&amp;rdquo; lazy=&amp;ldquo;yes&amp;rdquo; %}</description>
    </item>
    <item>
      <title>Italic</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/italic/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/italic/</guid>
      <description>Italic # To italicize text, add one asterisk or underscore before and after a word or phrase. To italicize the middle of a word for emphasis, add one asterisk without spaces around the letters.&#xA;Markdown HTML Rendered Output Italicized text is the *cat&#39;s meow*. Italicized text is the &amp;lt;em&amp;gt;cat&#39;s meow&amp;lt;/em&amp;gt;. Italicized text is the cat’s meow. Italicized text is the _cat&#39;s meow_. Italicized text is the &amp;lt;em&amp;gt;cat&#39;s meow&amp;lt;/em&amp;gt;. Italicized text is the cat’s meow.</description>
    </item>
    <item>
      <title>Links</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/links/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/links/</guid>
      <description>Links # To create a link, enclose the link text in brackets (e.g., [Duck Duck Go]) and then follow it immediately with the URL in parentheses (e.g., (https://duckduckgo.com)).&#xA;My favorite search engine is [Duck Duck Go](https://duckduckgo.com). The rendered output looks like this:&#xA;My favorite search engine is Duck Duck Go.&#xA;Note: To link to an element on the same page, see linking to heading IDs. To create a link that opens in a new tab or window, see the section on link targets.</description>
    </item>
    <item>
      <title>Lists</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/lists/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/lists/</guid>
      <description>Adding Elements in Lists # To add another element in a list while preserving the continuity of the list, indent the element four spaces or one tab, as shown in the following examples.&#xA;Tip: If things don&#39;t appear the way you expect, double check that you&#39;ve indented the elements in the list four spaces or one tab. Paragraphs # * This is the first list item. * Here&amp;#39;s the second list item.</description>
    </item>
    <item>
      <title>Ordered Lists</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/ordered_list/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/ordered_list/</guid>
      <description>Ordered List # To create an ordered list, add line items with numbers followed by periods. The numbers don&amp;rsquo;t have to be in numerical order, but the list should start with the number one.&#xA;Markdown HTML Rendered Output 1. First item 2. Second item 3. Third item 4. Fourth item &amp;lt;ol&amp;gt;&#xA;&amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;First item&amp;lt;/li&amp;gt; &amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;Second item&amp;lt;/li&amp;gt; &amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;Third item&amp;lt;/li&amp;gt; &amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;Fourth item&amp;lt;/li&amp;gt; &amp;lt;/ol&amp;gt; First item Second item Third item Fourth item 1. First item 1.</description>
    </item>
    <item>
      <title>Paragraphs</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/paragraphs/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/paragraphs/</guid>
      <description>Paragraphs # To create paragraphs, use a blank line to separate one or more lines of text.&#xA;Markdown HTML Rendered Output I really like using Markdown.&#xA;I think I&#39;ll use it to format all of my documents from now on. &amp;lt;p&amp;gt;I really like using Markdown.&amp;lt;/p&amp;gt;&#xA;&amp;lt;p&amp;gt;I think I&#39;ll use it to format all of my documents from now on.&amp;lt;/p&amp;gt; I really like using Markdown.&#xA;I think I&#39;ll use it to format all of my documents from now on.</description>
    </item>
    <item>
      <title>Strikethrough</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/strikethrough/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/strikethrough/</guid>
      <description>Strikethrough # You can strikethrough words by putting a horizontal line through the center of them. The result looks like this. This feature allows you to indicate that certain words are a mistake not meant for inclusion in the document. To strikethrough words, use two tilde symbols (~~) before and after the words.&#xA;~~The world is flat.~~ We now know that the world is round. The rendered output looks like this:</description>
    </item>
    <item>
      <title>Tables</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/tables/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/tables/</guid>
      <description>Tables # To add a table, use three or more hyphens (---) to create each column&amp;rsquo;s header, and use pipes (|) to separate each column. For compatibility, you should also add a pipe on either end of the row.&#xA;| Syntax | Description | | ----------- | ----------- | | Header | Title | | Paragraph | Text | The rendered output looks like this:&#xA;Syntax Description Header Title Paragraph Text Cell widths can vary, as shown below.</description>
    </item>
    <item>
      <title>Task List</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/task_list/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/task_list/</guid>
      <description>Task List # Task lists (also referred to as checklists and todo lists) allow you to create a list of items with checkboxes. In Markdown applications that support task lists, checkboxes will be displayed next to the content. To create a task list, add dashes (-) and brackets with a space ([ ]) in front of task list items. To select a checkbox, add an x in between the brackets ([x]).</description>
    </item>
    <item>
      <title>Unordered Lists</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/unordered-lists/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/unordered-lists/</guid>
      <description>Unordered Lists # To create an unordered list, add dashes (-), asterisks (*), or plus signs (+) in front of line items. Indent one or more items to create a nested list.&#xA;Markdown HTML Rendered Output - First item - Second item - Third item - Fourth item &amp;lt;ul&amp;gt;&#xA;&amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;First item&amp;lt;/li&amp;gt; &amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;Second item&amp;lt;/li&amp;gt; &amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;Third item&amp;lt;/li&amp;gt; &amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;Fourth item&amp;lt;/li&amp;gt; &amp;lt;/ul&amp;gt; First item Second item Third item Fourth item * First item * Second item</description>
    </item>
  </channel>
</rss>
