<!DOCTYPE html>
<html lang="en" dir="ltr">
<head><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>
  <meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="Blockquotes # To create a blockquote, add a &gt; in front of a paragraph.
&gt; <PERSON> followed her through many of the beautiful rooms in her castle. The rendered output looks like this:
<PERSON> followed her through many of the beautiful rooms in her castle.
Blockquotes with Multiple Paragraphs # Blockquotes can contain multiple paragraphs. Add a &gt; on the blank lines between the paragraphs.
&gt; <PERSON> followed her through many of the beautiful rooms in her castle.">
<meta name="theme-color" media="(prefers-color-scheme: light)" content="#ffffff">
<meta name="theme-color" media="(prefers-color-scheme: dark)" content="#343a40">
<meta name="color-scheme" content="light dark"><meta property="og:url" content="http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/blockquotes/">
  <meta property="og:site_name" content="PepGenX Platform">
  <meta property="og:title" content="Blockquotes">
  <meta property="og:description" content="Blockquotes # To create a blockquote, add a &gt; in front of a paragraph.
&gt; Dorothy followed her through many of the beautiful rooms in her castle. The rendered output looks like this:
Dorothy followed her through many of the beautiful rooms in her castle.
Blockquotes with Multiple Paragraphs # Blockquotes can contain multiple paragraphs. Add a &gt; on the blank lines between the paragraphs.
&gt; Dorothy followed her through many of the beautiful rooms in her castle.">
  <meta property="og:locale" content="en">
  <meta property="og:type" content="article">
    <meta property="article:section" content="docs">
    <meta property="article:modified_time" content="2024-07-01T01:22:34+02:00">
<title>Blockquotes | PepGenX Platform</title>
<link rel="manifest" href="/manifest.json">
<link rel="icon" href="/favicon.png" >
<link rel="canonical" href="http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/blockquotes/">
<link rel="stylesheet" href="/book.min.e004910793d7d45ce0216432001fc1dd027da4d03823d63a444453371c0a82c4.css" integrity="sha256-4ASRB5PX1FzgIWQyAB/B3QJ9pNA4I9Y6RERTNxwKgsQ=" crossorigin="anonymous">
  <script defer src="/fuse.min.js"></script>
  <script defer src="/en.search.min.aafa63659a92fa301ba9b9e6cb97780aff5e08aea524831c54000076e8cc6677.js" integrity="sha256-qvpjZZqS&#43;jAbqbnmy5d4Cv9eCK6lJIMcVAAAdujMZnc=" crossorigin="anonymous"></script>

  

<!--
Made with Book Theme
https://github.com/alex-shpak/hugo-book
-->
  
</head>
<body dir="ltr">
  <input type="checkbox" class="hidden toggle" id="menu-control" />
  <input type="checkbox" class="hidden toggle" id="toc-control" />
  <main class="container flex">
    <aside class="book-menu">
      <div class="book-menu-content">
        
  <nav>
<h2 class="book-brand">
  <a class="flex align-center" href="/"><span>PepGenX Platform</span>
  </a>
</h2>


<div class="book-search hidden">
  <input type="text" id="book-search-input" placeholder="Search" aria-label="Search" maxlength="64" data-hotkeys="s/" />
  <div class="book-search-spinner hidden"></div>
  <ul id="book-search-results"></ul>
</div>
<script>document.querySelector(".book-search").classList.remove("hidden")</script>












  



  
  <ul>
    
      
        <li class="book-section-flat" >
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle" checked />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenxdev/" class="">PepGenX Platform Developers</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Must Read</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/home/" class="">Prerequisites</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/kubernetes/" class="">Kubernetes</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/python/" class="">Python</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/libraries/" class="">Libraries</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/standards/" class="">Standards &amp; Patterns</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/docker/" class="">Docker Containers</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/language_model_frameworks/" class="">Language Model Frameworks</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/azure/" class="">Azure Services</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Platform components</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/platform_components/get_started/" class="">Get Started</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Orchestrator</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Traffic controller</a>
    </label>
  

          
  <ul>
    
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">API abstraction layer</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Service delivery</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Deployment controllers</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Registry &amp; Images</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">IAM</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenxdev/platform_components/security_analyzer/" class="">Security Analyzer</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Prompt Guard</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Observability</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Kiali observability</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Jaeger distributed tracing</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">EndUser Access Control</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Istio</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/platform_components/istio/configuration/" class="">Configuration</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle" checked />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Writing Documentation</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle" checked />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Markdown</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/overview/" class="">Overview</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/blockquotes/" class="active">Blockquotes</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/bold/" class="">Bold</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/code/" class="">Code</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/emoji/" class="">Emoji</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/emphasis/" class="">Emphasis</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/escaping-characters/" class="">Escaping Characters</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/fenced-code-blocks/" class="">Fenced Code Blocks</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/footnotes/" class="">Footnotes</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/headings/" class="">Headings</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/horizontal-rules/" class="">Horizontal Rules</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/images/" class="">Images</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/italic/" class="">Italic</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/links/" class="">Links</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/lists/" class="">Lists</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/ordered_list/" class="">Ordered Lists</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/paragraphs/" class="">Paragraphs</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/strikethrough/" class="">Strikethrough</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/tables/" class="">Tables</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/task_list/" class="">Task List</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/unordered-lists/" class="">Unordered Lists</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Hugo Markdown</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenxdev/shortcodes/hugo/section/" class="">Section</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/section/first-page/" class="">First Page</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/section/second-page/" class="">Second Page</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/buttons/" class="">Buttons</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/columns/" class="">Columns</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/details/" class="">Details</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/expand/" class="">Expand</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/hints/" class="">Hints</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/katex/" class="">KaTeX</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/mermaid/" class="">Mermaid Chart</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/tabs/" class="">Tabs</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">ToDo</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/todo/korzun/" class="">Korzun</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li class="book-section-flat" >
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/" class="">Welcome to PepGenX</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/getting_started/" class="">Getting Started</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/what_is_pepgenx/" class="">What is PepGenX</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/environment/" class="">Environment</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/quick_start/" class="">Quick Start</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/best_practicies/" class="">Best Practicies</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/concepts/" class="">Concepts</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/concepts/overview/" class="">Overview</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/concepts/architecture/" class="">Architecture</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/application_structure/" class="">Application Structure</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/operations/" class="">Operations</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/development/" class="">Development</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/deployment/" class="">Deployment</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/configuration/" class="">Configuration</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/integrations/" class="">Integrations</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/application_lifecycle/" class="">Application Lifecycle</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/docker_images/" class="">Docker Images</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/" class="">Development Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/llm_frameworks/" class="">LLM Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/langchain/" class="">Langchain</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/llamaindex/" class="">Llamaindex</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/autogen/" class="">Autogen</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/haystack/" class="">Haystack</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/griptape/" class="">Griptape</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/langroid/" class="">Langroid</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/langstream/" class="">Langstream</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/agentcloud/" class="">Agentcloud</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/vercelai/" class="">Vercel AI</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/semantic_kernel/" class="">Semantic Kernel</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/crewai/" class="">Crewai</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/txtai/" class="">txtai</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/" class="">Software Development Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/python/" class="">Python</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/java/" class="">Java</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/node.js/" class="">Node.js</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/go/" class="">Go</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/ruby_on_rails/" class="">Ruby On Rails</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/php/" class="">PHP</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/csharp/" class="">C#</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/3rd_party_frameworks/" class="">3&#39;rd Party Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/3rd_party_frameworks/rasa/" class="">RASA</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/3rd_party_frameworks/distylai/" class="">Distyl AI</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/api_frameworks/" class="">REST API Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/fastapi/" class="">FastAPI</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/springboot/" class="">SpringBoot</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/express.js/" class="">Express.js</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/gin/" class="">Gin</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/asp.netcore/" class="">ASP .Net Core</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/lumen/" class="">Lumen</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/pepgenx_services/" class="">PepGenX Services</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/" class="">Templates</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/prompts/" class="">Prompts</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/prompts/chains/" class="">Chains</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/prompts/trees/" class="">Trees</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/chains/" class="">Chains</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/workflows/" class="">Workflows</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/agents/" class="">Agents</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/functions/" class="">Functions</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/components/" class="">Components &amp; Tools</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/configuration_tasks/" class="">Configuration Tasks</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/tutorials/" class="">Tutorials</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/reference/" class="">Reference</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/reference/api_overview/" class="">API Overview</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/reference/api_access_control/" class="">API Access Control</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/reference/security/" class="">Security</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/frequently_asked_questions/" class="">Frequently Asked Questions</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>











  
<ul>
  
  <li>
    <a href="/posts/"  >
        Updates
      </a>
  </li>
  
</ul>






</nav>




  <script>(function(){var e=document.querySelector("aside .book-menu-content");addEventListener("beforeunload",function(){localStorage.setItem("menu.scrollTop",e.scrollTop)}),e.scrollTop=localStorage.getItem("menu.scrollTop")})()</script>


 
      </div>
    </aside>

    <div class="book-page">
      <header class="book-header">
        
  <div class="flex align-center justify-between">
  <label for="menu-control">
    <img src="/svg/menu.svg" class="book-icon" alt="Menu" />
  </label>

  <strong>Blockquotes</strong>

  <label for="toc-control">
    
    <img src="/svg/toc.svg" class="book-icon" alt="Table of Contents" />
    
  </label>
</div>

  
  <aside class="hidden clearfix">
    
  


<nav id="TableOfContents">
  <ul>
    <li>
      <ul>
        <li><a href="#blockquotes"><strong>Blockquotes</strong></a></li>
        <li><a href="#blockquotes-with-multiple-paragraphs"><strong>Blockquotes with Multiple Paragraphs</strong></a></li>
        <li><a href="#nested-blockquotes"><strong>Nested Blockquotes</strong></a></li>
        <li><a href="#blockquotes-with-other-elements"><strong>Blockquotes with Other Elements</strong></a>
          <ul>
            <li>
              <ul>
                <li><a href="#the-quarterly-results-look-great">The quarterly results look great!</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li><a href="#blockquotes-best-practices"><strong>Blockquotes Best Practices</strong></a></li>
      </ul>
    </li>
  </ul>
</nav>

  </aside>
  
 
      </header>

      
      
  <article class="markdown book-article"><h2 id="blockquotes">
  <strong>Blockquotes</strong>
  <a class="anchor" href="#blockquotes">#</a>
</h2>
<p>To create a blockquote, add a <code>&gt;</code> in front of a paragraph.</p>
<pre tabindex="0"><code>&gt; Dorothy followed her through many of the beautiful rooms in her castle.
</code></pre><p>The rendered output looks like this:</p>
<blockquote>
<p>Dorothy followed her through many of the beautiful rooms in her castle.</p>
</blockquote>
<h2 id="blockquotes-with-multiple-paragraphs">
  <strong>Blockquotes with Multiple Paragraphs</strong>
  <a class="anchor" href="#blockquotes-with-multiple-paragraphs">#</a>
</h2>
<p>Blockquotes can contain multiple paragraphs. Add a <code>&gt;</code> on the blank lines between the paragraphs.</p>
<pre tabindex="0"><code>&gt; Dorothy followed her through many of the beautiful rooms in her castle.
&gt;
&gt; The Witch bade her clean the pots and kettles and sweep the floor and keep the fire fed with wood.
</code></pre><p>The rendered output looks like this:</p>
<blockquote>
<p>Dorothy followed her through many of the beautiful rooms in her castle.</p>
<p>The Witch bade her clean the pots and kettles and sweep the floor and keep the fire fed with wood.</p>
</blockquote>
<h2 id="nested-blockquotes">
  <strong>Nested Blockquotes</strong>
  <a class="anchor" href="#nested-blockquotes">#</a>
</h2>
<p>Blockquotes can be nested. Add a <code>&gt;&gt;</code> in front of the paragraph you want to nest.</p>
<pre tabindex="0"><code>&gt; Dorothy followed her through many of the beautiful rooms in her castle.
&gt;
&gt;&gt; The Witch bade her clean the pots and kettles and sweep the floor and keep the fire fed with wood.
</code></pre><p>The rendered output looks like this:</p>
<blockquote>
<p>Dorothy followed her through many of the beautiful rooms in her castle.</p>
<blockquote>
<p>The Witch bade her clean the pots and kettles and sweep the floor and keep the fire fed with wood.</p>
</blockquote>
</blockquote>
<h2 id="blockquotes-with-other-elements">
  <strong>Blockquotes with Other Elements</strong>
  <a class="anchor" href="#blockquotes-with-other-elements">#</a>
</h2>
<p>Blockquotes can contain other Markdown formatted elements. Not all elements can be used — you&rsquo;ll need to experiment to see which ones work.</p>
<pre tabindex="0"><code>&gt; #### The quarterly results look great!
&gt;
&gt; - Revenue was off the chart.
&gt; - Profits were higher than ever.
&gt;
&gt;  *Everything* is going according to **plan**.
</code></pre><p>The rendered output looks like this:</p>
<blockquote>
<h4 id="the-quarterly-results-look-great">
  The quarterly results look great!
  <a class="anchor" href="#the-quarterly-results-look-great">#</a>
</h4>
<ul>
<li>Revenue was off the chart.</li>
<li>Profits were higher than ever.</li>
</ul>
<p><em>Everything</em> is going according to <strong>plan</strong>.</p>
</blockquote>
<h2 id="blockquotes-best-practices">
  <strong>Blockquotes Best Practices</strong>
  <a class="anchor" href="#blockquotes-best-practices">#</a>
</h2>
<p>For compatibility, put blank lines before and after blockquotes.</p>


<table class="table table-bordered">
  <thead class="thead-light">
    <tr>
      <th>✅&nbsp; Do this</th>
      <th>❌&nbsp; Don't do this</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>
        <code class="highlighter-rouge">
        Try to put a blank line before...<br><br>

        > This is a blockquote<br><br>

        ...and after a blockquote.
        </code>
      </td>
      <td>
        <code class="highlighter-rouge">
        Without blank lines, this might not look right.<br>
        > This is a blockquote<br>
        Don't do this!
        </code>
      </td>
    </tr>
  </tbody>
</table>


</article>
 
      

      <footer class="book-footer">
        
  <div class="flex flex-wrap justify-between" style="font-size: 80%;">

<div>LastMod: June 30, 2024<br>
      By: Dariusz Korzun<br>
      Commit: 3d819f225670b657600e144a3c9bc31717854253<br>
  </div>

</div>
 
        
      </footer>

      
  
  <div class="book-comments">

</div>
  
 

      <label for="menu-control" class="hidden book-menu-overlay"></label>
    </div>

    
    <aside class="book-toc">
      <div class="book-toc-content">
        
        
        
        <nav class="pepgenx-toc">
        <ol>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/shortcodes/markdown/blockquotes/#blockquotes">Blockquotes</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/shortcodes/markdown/blockquotes/#blockquotes-with-multiple-paragraphs">Blockquotes with Multiple Paragraphs</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/shortcodes/markdown/blockquotes/#nested-blockquotes">Nested Blockquotes</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/shortcodes/markdown/blockquotes/#blockquotes-with-other-elements">Blockquotes with Other Elements</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/shortcodes/markdown/blockquotes/#the-quarterly-results-look-great">The quarterly results look great!</a>
        </div>
          </ol>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/shortcodes/markdown/blockquotes/#blockquotes-best-practices">Blockquotes Best Practices</a>
        </div>
        </ol>
      </nav>
      </div>
    </aside>
    
  </main>

  
</body>
</html>












