<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Must Read on PepGenX Platform</title>
    <link>http://localhost:1313/docs/pepgenxdev/must_read/</link>
    <description>Recent content in Must Read on PepGenX Platform</description>
    <generator>Hugo</generator>
    <language>en</language>
    <copyright>PepsiCo 2024</copyright>
    <atom:link href="http://localhost:1313/docs/pepgenxdev/must_read/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>Prerequisites</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/home/<USER>/link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/home/<USER>/guid>
      <description>Getting Started # Before you begin working with the Platform # PepGenX Platform is a revolutionary platform designed to make building applications with generative AI and LLMs accessible to everyone. We&amp;rsquo;re building a vibrant developer community where talented individuals like yourself can contribute by coding components that will be the building blocks of the pepgenx ecosystem.&#xA;This guide provides an overview of the PepGenX Platform and its core components. Before diving into development, it&amp;rsquo;s essential to understand the fundamental concepts and technologies that underpin the framework.</description>
    </item>
    <item>
      <title>Kubernetes</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/kubernetes/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/kubernetes/</guid>
      <description>Kubernetes container orchestration # Introduction # If you&amp;rsquo;re new to Kubernetes, here&amp;rsquo;s a learning path to get you started:&#xA;Fundamentals: Begin by understanding the core concepts of Kubernetes. This foundation will be crucial when you delve into more advanced topics like Admission Controllers and Operators. Kubernetes basic technical concepts Kubernetes components, functionality, their interactions, and implementation options Admission Controllers and Operators: These two concepts are the crucial components powering PepGenX platform.</description>
    </item>
    <item>
      <title>Python</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/python/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/python/</guid>
      <description> Python Programming Skills # Introduction # This section provides essential background knowledge required for coding PepGenX Platform components. A strong foundation in these Python concepts is crucial for your success as a PepGenX developer. Feel free to explore the provided resources and delve deeper into each topic to ensure you&amp;rsquo;re well-equipped to contribute to the PepGenX Platform!&#xA;Object-oriented programming (OOP) # &amp;ldquo;Python Classes and OOP&amp;rdquo; &amp;ldquo;Understanding Class and Instance Variables&amp;rdquo; &amp;ldquo;Python Object Oriented Programming (OOP) - For Beginners&amp;rdquo; &amp;ldquo;Object-Oriented Programming in Python&amp;rdquo; &amp;ldquo;Classes in Python&amp;rdquo; &amp;ldquo;Inheritance in Python&amp;rdquo; &amp;ldquo;Polymorphism in Python&amp;rdquo; Flow control # &amp;ldquo;Python Flow Control&amp;rdquo; &amp;ldquo;Conditional Statements in Python&amp;rdquo; Functions # &amp;ldquo;Defining Functions&amp;rdquo; &amp;ldquo;Defining Your Own Python Function&amp;rdquo; &amp;ldquo;Functions in Python&amp;rdquo; Data types # &amp;ldquo;Built-in Types Datatypes&amp;rdquo; &amp;ldquo;Basic Data Types in Python&amp;rdquo; Binary data # &amp;ldquo;Binary Data Services&amp;rdquo; &amp;ldquo;Creating a Binary Search in Python&amp;rdquo; Exception handling # &amp;ldquo;Errors and Exceptions&amp;rdquo; &amp;ldquo;Python Exceptions: An Introduction&amp;rdquo; &amp;ldquo;Raising and Handling Python Exceptions&amp;rdquo; JSON # &amp;ldquo;json — JSON encoder and decoder&amp;rdquo; &amp;ldquo;Working with JSON Data in Python&amp;rdquo; &amp;ldquo;JSONS Offcial&amp;rdquo; </description>
    </item>
    <item>
      <title>Libraries</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/libraries/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/libraries/</guid>
      <description>Python libraries # Introduction # A strong foundation in Python libraries is crucial for your success as PepGenX Platform developer. That&amp;rsquo;s why we&amp;rsquo;ve compiled a comprehensive list of essential tools along with valuable learning resources. We highly recommend familiarizing yourself with each library&amp;rsquo;s documentation and exploring the provided tutorials and video resources.&#xA;Each library listed below plays a critical role in building robust and efficient Python components for PepGenX Platform.</description>
    </item>
    <item>
      <title>Standards &amp; Patterns</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/standards/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/standards/</guid>
      <description>Standards &amp;amp; Patterns # Introduction # We&amp;rsquo;ve established a set of core Python standards and patterns that streamline development, ensure a consistent, high-quality user experience, and contribute to a well-organized and maintainable codebase. Following these guidelines is crucial for creating well-integrated code. We encourage you to explore the provided references.&#xA;RESTful API # &amp;ldquo;RESTful API Tutorial&amp;rdquo; &amp;ldquo;Python and REST APIs: Interacting With Web Services&amp;rdquo; &amp;ldquo;What is a REST API?&amp;rdquo; WebSocket API # &amp;ldquo;WebSockets handbook&amp;rdquo; &amp;ldquo;WebSockets Python library&amp;rdquo; &amp;ldquo;How To Build WebSocket Server And Client in Python&amp;rdquo; Asynchronous API Calls # &amp;ldquo;Async and Await in Python&amp;rdquo; &amp;ldquo;Asyncio Tutorial&amp;rdquo; JSON Web Token (JWT) # &amp;ldquo;JWT.</description>
    </item>
    <item>
      <title>Docker Containers</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/docker/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/docker/</guid>
      <description>Docker Containers # Introduction # This guide provides a comprehensive introduction to Docker, packed with valuable resources. These resources include both official Docker documentation and informative articles, offering a well-rounded learning experience. Our goal is to equip you with the knowledge to effectively build, deploy, and manage Docker containers for PepGenX Platform components.&#xA;Dockerfile # &amp;ldquo;Dockerfile reference&amp;rdquo; &amp;ldquo;Building best practices&amp;rdquo; Docker Images # &amp;ldquo;What is an image?&amp;rdquo; &amp;ldquo;Docker image&amp;rdquo; &amp;ldquo;What is Docker Image?</description>
    </item>
    <item>
      <title>Language Model Frameworks</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/language_model_frameworks/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/language_model_frameworks/</guid>
      <description>Language Model Frameworks # Introduction # On this page, you&amp;rsquo;ll find a comprehensive overview of various Language Model Frameworks that integrate seamlessly with the PepGenX Platform. These frameworks offer a wealth of functionalities, from data management and indexing to orchestration and deployment. Dive deeper into the resources we&amp;rsquo;ve provided to learn more about each framework and explore its potential.&#xA;Llamaindex # LlamaIndex is an open-source data framework for building large language model (LLM) applications.</description>
    </item>
    <item>
      <title>Azure Services</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/azure/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/azure/</guid>
      <description>Microsoft Azure Services # Introduction # PepGenX Platform is designed to seamlessly integrate with various Azure services, enabling developers to build robust, scalable, and secure applications. Below, you&amp;rsquo;ll find detailed insights into the core services provided by Microsoft Azure, which form the backbone of our platform infrastructure.&#xA;Azure Kubernetes Services # &amp;ldquo;Introduction to Azure Kubernetes Service&amp;rdquo; Azure Container Registries # &amp;ldquo;Introduction to Azure Container Registry&amp;rdquo; Azure Storage Account # &amp;ldquo;Introduction to Azure Storage&amp;rdquo; Azure Key Vault # &amp;ldquo;About Azure Key Vault&amp;rdquo; Azure AI Search # &amp;ldquo;What&amp;rsquo;s Azure AI Search?</description>
    </item>
  </channel>
</rss>
