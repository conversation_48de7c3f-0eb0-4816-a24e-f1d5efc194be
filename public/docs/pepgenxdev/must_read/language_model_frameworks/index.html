<!DOCTYPE html>
<html lang="en" dir="ltr">
<head><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>
  <meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="Language Model Frameworks # Introduction # On this page, you&rsquo;ll find a comprehensive overview of various Language Model Frameworks that integrate seamlessly with the PepGenX Platform. These frameworks offer a wealth of functionalities, from data management and indexing to orchestration and deployment. Dive deeper into the resources we&rsquo;ve provided to learn more about each framework and explore its potential.
Llamaindex # LlamaIndex is an open-source data framework for building large language model (LLM) applications.">
<meta name="theme-color" media="(prefers-color-scheme: light)" content="#ffffff">
<meta name="theme-color" media="(prefers-color-scheme: dark)" content="#343a40">
<meta name="color-scheme" content="light dark"><meta property="og:url" content="http://localhost:1313/docs/pepgenxdev/must_read/language_model_frameworks/">
  <meta property="og:site_name" content="PepGenX Platform">
  <meta property="og:title" content="Language Model Frameworks">
  <meta property="og:description" content="Language Model Frameworks # Introduction # On this page, you’ll find a comprehensive overview of various Language Model Frameworks that integrate seamlessly with the PepGenX Platform. These frameworks offer a wealth of functionalities, from data management and indexing to orchestration and deployment. Dive deeper into the resources we’ve provided to learn more about each framework and explore its potential.
Llamaindex # LlamaIndex is an open-source data framework for building large language model (LLM) applications.">
  <meta property="og:locale" content="en">
  <meta property="og:type" content="article">
    <meta property="article:section" content="docs">
    <meta property="article:modified_time" content="2024-07-02T22:18:57+02:00">
<title>Language Model Frameworks | PepGenX Platform</title>
<link rel="manifest" href="/manifest.json">
<link rel="icon" href="/favicon.png" >
<link rel="canonical" href="http://localhost:1313/docs/pepgenxdev/must_read/language_model_frameworks/">
<link rel="stylesheet" href="/book.min.e004910793d7d45ce0216432001fc1dd027da4d03823d63a444453371c0a82c4.css" integrity="sha256-4ASRB5PX1FzgIWQyAB/B3QJ9pNA4I9Y6RERTNxwKgsQ=" crossorigin="anonymous">
  <script defer src="/fuse.min.js"></script>
  <script defer src="/en.search.min.aafa63659a92fa301ba9b9e6cb97780aff5e08aea524831c54000076e8cc6677.js" integrity="sha256-qvpjZZqS&#43;jAbqbnmy5d4Cv9eCK6lJIMcVAAAdujMZnc=" crossorigin="anonymous"></script>

  

<!--
Made with Book Theme
https://github.com/alex-shpak/hugo-book
-->
  
</head>
<body dir="ltr">
  <input type="checkbox" class="hidden toggle" id="menu-control" />
  <input type="checkbox" class="hidden toggle" id="toc-control" />
  <main class="container flex">
    <aside class="book-menu">
      <div class="book-menu-content">
        
  <nav>
<h2 class="book-brand">
  <a class="flex align-center" href="/"><span>PepGenX Platform</span>
  </a>
</h2>


<div class="book-search hidden">
  <input type="text" id="book-search-input" placeholder="Search" aria-label="Search" maxlength="64" data-hotkeys="s/" />
  <div class="book-search-spinner hidden"></div>
  <ul id="book-search-results"></ul>
</div>
<script>document.querySelector(".book-search").classList.remove("hidden")</script>












  



  
  <ul>
    
      
        <li class="book-section-flat" >
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle" checked />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenxdev/" class="">PepGenX Platform Developers</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle" checked />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Must Read</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/home/" class="">Prerequisites</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/kubernetes/" class="">Kubernetes</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/python/" class="">Python</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/libraries/" class="">Libraries</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/standards/" class="">Standards &amp; Patterns</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/docker/" class="">Docker Containers</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/language_model_frameworks/" class="active">Language Model Frameworks</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/azure/" class="">Azure Services</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Platform components</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/platform_components/get_started/" class="">Get Started</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Orchestrator</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Traffic controller</a>
    </label>
  

          
  <ul>
    
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">API abstraction layer</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Service delivery</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Deployment controllers</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Registry &amp; Images</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">IAM</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenxdev/platform_components/security_analyzer/" class="">Security Analyzer</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Prompt Guard</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Observability</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Kiali observability</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Jaeger distributed tracing</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">EndUser Access Control</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Istio</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/platform_components/istio/configuration/" class="">Configuration</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Writing Documentation</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Markdown</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/overview/" class="">Overview</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/blockquotes/" class="">Blockquotes</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/bold/" class="">Bold</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/code/" class="">Code</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/emoji/" class="">Emoji</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/emphasis/" class="">Emphasis</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/escaping-characters/" class="">Escaping Characters</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/fenced-code-blocks/" class="">Fenced Code Blocks</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/footnotes/" class="">Footnotes</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/headings/" class="">Headings</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/horizontal-rules/" class="">Horizontal Rules</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/images/" class="">Images</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/italic/" class="">Italic</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/links/" class="">Links</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/lists/" class="">Lists</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/ordered_list/" class="">Ordered Lists</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/paragraphs/" class="">Paragraphs</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/strikethrough/" class="">Strikethrough</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/tables/" class="">Tables</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/task_list/" class="">Task List</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/unordered-lists/" class="">Unordered Lists</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Hugo Markdown</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenxdev/shortcodes/hugo/section/" class="">Section</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/section/first-page/" class="">First Page</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/section/second-page/" class="">Second Page</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/buttons/" class="">Buttons</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/columns/" class="">Columns</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/details/" class="">Details</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/expand/" class="">Expand</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/hints/" class="">Hints</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/katex/" class="">KaTeX</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/mermaid/" class="">Mermaid Chart</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/tabs/" class="">Tabs</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">ToDo</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/todo/korzun/" class="">Korzun</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li class="book-section-flat" >
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/" class="">Welcome to PepGenX</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/getting_started/" class="">Getting Started</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/what_is_pepgenx/" class="">What is PepGenX</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/environment/" class="">Environment</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/quick_start/" class="">Quick Start</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/best_practicies/" class="">Best Practicies</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/concepts/" class="">Concepts</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/concepts/overview/" class="">Overview</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/concepts/architecture/" class="">Architecture</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/application_structure/" class="">Application Structure</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/operations/" class="">Operations</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/development/" class="">Development</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/deployment/" class="">Deployment</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/configuration/" class="">Configuration</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/integrations/" class="">Integrations</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/application_lifecycle/" class="">Application Lifecycle</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/docker_images/" class="">Docker Images</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/" class="">Development Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/llm_frameworks/" class="">LLM Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/langchain/" class="">Langchain</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/llamaindex/" class="">Llamaindex</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/autogen/" class="">Autogen</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/haystack/" class="">Haystack</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/griptape/" class="">Griptape</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/langroid/" class="">Langroid</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/langstream/" class="">Langstream</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/agentcloud/" class="">Agentcloud</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/vercelai/" class="">Vercel AI</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/semantic_kernel/" class="">Semantic Kernel</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/crewai/" class="">Crewai</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/txtai/" class="">txtai</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/" class="">Software Development Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/python/" class="">Python</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/java/" class="">Java</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/node.js/" class="">Node.js</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/go/" class="">Go</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/ruby_on_rails/" class="">Ruby On Rails</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/php/" class="">PHP</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/csharp/" class="">C#</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/3rd_party_frameworks/" class="">3&#39;rd Party Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/3rd_party_frameworks/rasa/" class="">RASA</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/3rd_party_frameworks/distylai/" class="">Distyl AI</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/api_frameworks/" class="">REST API Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/fastapi/" class="">FastAPI</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/springboot/" class="">SpringBoot</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/express.js/" class="">Express.js</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/gin/" class="">Gin</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/asp.netcore/" class="">ASP .Net Core</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/lumen/" class="">Lumen</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/pepgenx_services/" class="">PepGenX Services</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/" class="">Templates</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/prompts/" class="">Prompts</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/prompts/chains/" class="">Chains</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/prompts/trees/" class="">Trees</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/chains/" class="">Chains</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/workflows/" class="">Workflows</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/agents/" class="">Agents</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/functions/" class="">Functions</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/components/" class="">Components &amp; Tools</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/configuration_tasks/" class="">Configuration Tasks</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/tutorials/" class="">Tutorials</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/reference/" class="">Reference</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/reference/api_overview/" class="">API Overview</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/reference/api_access_control/" class="">API Access Control</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/reference/security/" class="">Security</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/frequently_asked_questions/" class="">Frequently Asked Questions</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>











  
<ul>
  
  <li>
    <a href="/posts/"  >
        Updates
      </a>
  </li>
  
</ul>






</nav>




  <script>(function(){var e=document.querySelector("aside .book-menu-content");addEventListener("beforeunload",function(){localStorage.setItem("menu.scrollTop",e.scrollTop)}),e.scrollTop=localStorage.getItem("menu.scrollTop")})()</script>


 
      </div>
    </aside>

    <div class="book-page">
      <header class="book-header">
        
  <div class="flex align-center justify-between">
  <label for="menu-control">
    <img src="/svg/menu.svg" class="book-icon" alt="Menu" />
  </label>

  <strong>Language Model Frameworks</strong>

  <label for="toc-control">
    
    <img src="/svg/toc.svg" class="book-icon" alt="Table of Contents" />
    
  </label>
</div>

  
  <aside class="hidden clearfix">
    
  


<nav id="TableOfContents">
  <ul>
    <li><a href="#language-model-frameworks"><strong>Language Model Frameworks</strong></a>
      <ul>
        <li>
          <ul>
            <li><a href="#introduction"><strong>Introduction</strong></a></li>
            <li><a href="#llamaindex"><strong>Llamaindex</strong></a></li>
            <li><a href="#langchain"><strong>Langchain</strong></a></li>
            <li><a href="#autogen"><strong>AutoGen</strong></a></li>
            <li><a href="#semantic-kernel"><strong>Semantic-Kernel</strong></a></li>
            <li><a href="#haystack"><strong>Haystack</strong></a></li>
            <li><a href="#langroid"><strong>Langroid</strong></a></li>
            <li><a href="#langstream"><strong>LangStream</strong></a></li>
            <li><a href="#autogpt"><strong>AutoGPT</strong></a></li>
            <li><a href="#griptape"><strong>Griptape</strong></a></li>
            <li><a href="#txtai"><strong>txtai</strong></a></li>
            <li><a href="#agentcloud"><strong>Agentcloud</strong></a></li>
            <li><a href="#orchestrai"><strong>OrchestrAI</strong></a></li>
            <li><a href="#xlang-aiopenagents"><strong>xlang-ai/OpenAgents</strong></a></li>
            <li><a href="#crewai"><strong>Crewai</strong></a></li>
            <li><a href="#vercel-ai"><strong>Vercel AI</strong></a></li>
          </ul>
        </li>
      </ul>
    </li>
  </ul>
</nav>

  </aside>
  
 
      </header>

      
      
  <article class="markdown book-article"><h1 id="language-model-frameworks">
  <strong>Language Model Frameworks</strong>
  <a class="anchor" href="#language-model-frameworks">#</a>
</h1>
<h3 id="introduction">
  <strong>Introduction</strong>
  <a class="anchor" href="#introduction">#</a>
</h3>
<p>On this page, you&rsquo;ll find a comprehensive overview of various Language Model Frameworks that integrate seamlessly with the PepGenX Platform. These frameworks offer a wealth of functionalities, from data management and indexing to orchestration and deployment. Dive deeper into the resources we&rsquo;ve provided to learn more about each framework and explore its potential.</p>
<h3 id="llamaindex">
  <strong>Llamaindex</strong>
  <a class="anchor" href="#llamaindex">#</a>
</h3>
<p>LlamaIndex is an open-source data framework for building large language model (LLM) applications. It allows developers to turn enterprise data into production-ready LLM applications. LlamaIndex provides tools for various stages of the LLM application development lifecycle, including data loading, indexing, querying, and evaluation. Additionally, LlamaIndex offers a rich set of community-contributed resources, including connectors, tools, and datasets. Developers can also integrate LlamaIndex with various vector stores, large language models, and data sources.</p>
<p>Learn more about <a href="https://www.llamaindex.ai/">Llamaindex</a></p>
<h3 id="langchain">
  <strong>Langchain</strong>
  <a class="anchor" href="#langchain">#</a>
</h3>
<p>LangChain is a suite of products that supports developers throughout the entire lifecycle of LLM application development. LangChain provides a framework to build applications with LLMs. It includes LangSmith, a tool to debug, collaborate, test, and monitor LLM apps. Additionally, LangChain offers LangGraph, a framework for building controllable agentic workflows. With LangChain, developers can build context-aware, reasoning applications that leverage their company’s data and APIs. LangChain promotes vendor optionality by allowing developers to choose the best tools for the job.  Overall, LangChain empowers developers to build, run, and manage LLM applications.</p>
<p>Learn more about <a href="https://www.langchain.com/">Langchain</a></p>
<h3 id="autogen">
  <strong>AutoGen</strong>
  <a class="anchor" href="#autogen">#</a>
</h3>
<p>AutoGen is a collection of pre-built systems that can be used to build various applications. These systems cover a wide range of domains and complexities, so there is a good chance you will find something that meets your needs.  AutoGen provides a high-level abstraction called a multi-agent conversation framework. This framework makes it easier to build workflows that use large language models (LLMs).  In addition, AutoGen offers enhanced APIs for interacting with LLMs. These APIs can improve the performance of your workflows and reduce the cost of running them.</p>
<p>Learn more about <a href="https://microsoft.github.io/autogen/">AutoGen</a></p>
<h3 id="semantic-kernel">
  <strong>Semantic-Kernel</strong>
  <a class="anchor" href="#semantic-kernel">#</a>
</h3>
<p>Semantic Kernel is an open-source developer kit that lets developers easily build AI agents and integrate AI models into their code. It works across C#, Python, and Java. Semantic Kernel is designed to be modular and future-proof. This means that developers can easily add new features and functionality as needed. Semantic Kernel also allows developers to automate business processes. Overall, Semantic Kernel is a powerful tool that can help developers build and deploy AI-powered applications quickly and easily.</p>
<p>Learn more about <a href="https://learn.microsoft.com/en-us/semantic-kernel/overview/">Semantic-Kernel</a></p>
<h3 id="haystack">
  <strong>Haystack</strong>
  <a class="anchor" href="#haystack">#</a>
</h3>
<p>Haystack is an open-source framework that simplifies building question answering systems. It provides tools for various stages of the development pipeline, from data access and preprocessing to information retrieval and question answering. Haystack offers flexibility in choosing retrieval models and answer scoring strategies, allowing developers to customize their QA systems. With its modular architecture and pre-built components, Haystack accelerates development. Haystack scales well for large datasets and complex workflows, making it suitable for production-grade QA systems. The freely available framework fosters a growing community and allows developers to contribute. The comprehensive Haystack documentation offers guides and tutorials to get you started quickly.</p>
<p>Learn more about <a href="https://docs.haystack.deepset.ai/docs/intro">Haystack</a></p>
<h3 id="langroid">
  <strong>Langroid</strong>
  <a class="anchor" href="#langroid">#</a>
</h3>
<p>Langroid is a Python framework designed for building complex applications powered by Large Language Models (LLMs). It accomplishes this through a multi-agent programming approach. Langroid offers a number of features that make it well-suited for developers, including: a focus on agents as the primary unit of interaction, task delegation for breaking down complex tasks, and caching to improve efficiency. Langroid also supports a variety of tools and integrations, including OpenAI LLMs, vector stores, and retrieval-augmented generation.</p>
<p>Learn more about <a href="https://langroid.github.io/langroid/">Langroid</a></p>
<h3 id="langstream">
  <strong>LangStream</strong>
  <a class="anchor" href="#langstream">#</a>
</h3>
<p>LangStream is a framework built for developers who want to create real-time Generative AI applications. It simplifies the process by allowing you to combine powerful tools like large language models and vector databases with real-time data processing. This lets you build effective Gen AI applications. LangStream uses an event-driven architecture, making it easy to develop responsive Gen AI applications. This architecture offers advantages like scalability, fault-tolerance, and high availability – all crucial aspects for robust applications.</p>
<p>Learn more about <a href="https://docs.langstream.ai/">LangStream</a></p>
<h3 id="autogpt">
  <strong>AutoGPT</strong>
  <a class="anchor" href="#autogpt">#</a>
</h3>
<p>AutoGPT is an open-source project that consists of four main components: Agent, Benchmark, Forge, and Frontend. It aims to provide access to AI assistance and to build the future transparently. Agent, also known as AutoGPT, is a semi-autonomous agent powered by large language models (LLMs) to execute any task for you. Benchmark is a tool that measures your agent&rsquo;s performance. Forge is a ready-to-go template to create your own agent application. Frontend is an easy-to-use and open source frontend for any Agent Protocol-compliant agent.</p>
<p>Learn more about <a href="https://docs.agpt.co/">AutoGPT</a></p>
<h3 id="griptape">
  <strong>Griptape</strong>
  <a class="anchor" href="#griptape">#</a>
</h3>
<p>Griptape is a framework designed to help developers build secure and effective AI applications with Large Language Models (LLMs). It allows you to create AI systems that balance predictability with creativity. Griptape enforces structures and utilizes long-term memory for reliable outcomes, while also providing tools and short-term memory to leverage LLM creativity with external data sources. This lets you switch between predictable and creative functionalities based on your specific needs.  Griptape goes beyond just utilizing LLMs’ potential. It enforces data security through trust boundaries and permission controls. By doing so, Griptape allows you to leverage LLMs’ reasoning capabilities while adhering to strict security guidelines.</p>
<p>Learn more about <a href="https://docs.griptape.ai/stable/griptape-framework/">Griptape</a></p>
<h3 id="txtai">
  <strong>txtai</strong>
  <a class="anchor" href="#txtai">#</a>
</h3>
<p>Txtai is an all-in-one embeddings database that allows you to search for similar text, documents, code, audio, images, and video. It provides functionalities like semantic search, LLM orchestration, and language model workflows. Txtai  supports vector search with SQL, topic modeling, and retrieval augmented generation. You can use txtai to create embeddings for various data formats and use them for various retrieval tasks.</p>
<p>Learn more about <a href="https://neuml.github.io/txtai/">txtai</a></p>
<h3 id="agentcloud">
  <strong>Agentcloud</strong>
  <a class="anchor" href="#agentcloud">#</a>
</h3>
<p>Agent Cloud is a platform that enables companies to host their own AI App platform. It allows developers to build and deploy two types of conversational chat apps and process apps. Conversational chat apps are similar to OpenAI GPTs but can use any LLM and access a library of tools as well as retrieve knowledge from hundreds of data sources. Process apps allow developers to automate processes by allocating goals and tasks for agents to complete.</p>
<p>Learn more about <a href="https://docs.agentcloud.dev/documentation/get-started/introduction">Agentcloud</a></p>
<h3 id="orchestrai">
  <strong>OrchestrAI</strong>
  <a class="anchor" href="#orchestrai">#</a>
</h3>
<p>OrchestrAI is a Python-based framework designed to help developers build and test custom autonomous agents. It leverages the networkx library to manage the dependencies between various AI modules, and YAML to define and manage task pipelines. This framework empowers developers to define and contrast variations of strategies and settings, ultimately assisting them in finding the optimal approach for their specific use case. While current modules only communicate with OpenAI, OrchestrAI can be extended to encompass other language models as well.</p>
<p>Learn more about <a href="https://github.com/samshapley/OrchestrAI">OrchestrAI</a></p>
<h3 id="xlang-aiopenagents">
  <strong>xlang-ai/OpenAgents</strong>
  <a class="anchor" href="#xlang-aiopenagents">#</a>
</h3>
<p>OpenAgents is an open-source framework designed for deploying and using language agents in real-world scenarios. Unlike frameworks focused on testing, OpenAgents prioritizes user experience with a web interface for easy interaction. Developers benefit too, with seamless local deployment for their own agents. The platform includes three pre-built agents: a Data Agent for data analysis, a Plugin Agent with over 200 tools, and a Web Agent for web browsing. OpenAgents empowers further development through its open source code, allowing researchers and developers to build upon this foundation and create innovative language agents.</p>
<p>Learn more about <a href="https://github.com/xlang-ai/OpenAgents">OpenAgents</a></p>
<h3 id="crewai">
  <strong>Crewai</strong>
  <a class="anchor" href="#crewai">#</a>
</h3>
<p>Crewai is an open-source framework for developers to build systems powered by multiple, collaborative AI agents. Crewai lets create teams of agents, each with its own strengths, working together to achieve a complex goal. The framework is designed to be adaptable and can integrate with different large language models especially <a href="#langchain">Langchain</a> and <a href="#llamaindex">Llamaindex</a>. Crewai offers an intuitive workflow for both development and deployment. With Crewai, developers can leverage the power of multiple AI agents to automate tasks and solve problems in innovative ways.</p>
<p>Learn more about <a href="https://docs.crewai.com/">Crewai</a></p>
<h3 id="vercel-ai">
  <strong>Vercel AI</strong>
  <a class="anchor" href="#vercel-ai">#</a>
</h3>
<p>Vercel is a platform for developers that provides the tools, workflows, and infrastructure you need to build and deploy your web apps faster, without the need for additional configuration. It simplifies the integration of large language models (LLMs) into applications built with React, Next.js, Vue, Svelte, Node.js, and more. The Vercel AI SDK offers a unified API for generating text, structured objects, and tool calls with LLMs. It also provides framework-agnostic hooks for building chat and generative user interfaces. Notably, the Vercel AI SDK includes React and Svelte hooks for data fetching and rendering streaming text responses, enabling real-time data representation.</p>
<p>Learn more about <a href="https://vercel.com/docs/getting-started-with-vercel">Vercel AI</a></p>
</article>
 
      

      <footer class="book-footer">
        
  <div class="flex flex-wrap justify-between" style="font-size: 80%;">

<div>LastMod: July 2, 2024<br>
      By: Dariusz Korzun<br>
      Commit: 59e30c766266010643dee4e6a6875ef4d5c5054f<br>
  </div>

</div>
 
        
      </footer>

      
  
  <div class="book-comments">

</div>
  
 

      <label for="menu-control" class="hidden book-menu-overlay"></label>
    </div>

    
    <aside class="book-toc">
      <div class="book-toc-content">
        
        
        
        <nav class="pepgenx-toc">
        <ol>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#introduction">Introduction</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#llamaindex">Llamaindex</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#langchain">Langchain</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#autogen">AutoGen</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#semantic-kernel">Semantic-Kernel</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#haystack">Haystack</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#langroid">Langroid</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#langstream">LangStream</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#autogpt">AutoGPT</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#griptape">Griptape</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#txtai">txtai</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#agentcloud">Agentcloud</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#orchestrai">OrchestrAI</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#xlang-aiopenagents">xlang-ai/OpenAgents</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#crewai">Crewai</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/must_read/language_model_frameworks/#vercel-ai">Vercel AI</a>
        </div>
        </ol>
      </nav>
      </div>
    </aside>
    
  </main>

  
</body>
</html>












