<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Istio on PepGenX Platform</title>
    <link>http://localhost:1313/docs/pepgenxdev/platform_components/istio/</link>
    <description>Recent content in Istio on PepGenX Platform</description>
    <generator>Hugo</generator>
    <language>en</language>
    <copyright>PepsiCo 2024</copyright>
    <atom:link href="http://localhost:1313/docs/pepgenxdev/platform_components/istio/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>Configuration</title>
      <link>http://localhost:1313/docs/pepgenxdev/platform_components/istio/configuration/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/platform_components/istio/configuration/</guid>
      <description>Enable sidecar injection # To automatically install sidecar to any new pods, you need to annotate your namespaces with the revision label corresponding to the control plane revision currently installed.&#xA;If you&amp;rsquo;re unsure which revision is installed, use:&#xA;Bash&#xA;az aks show &amp;ndash;resource-group ${RESOURCE_GROUP} &amp;ndash;name ${CLUSTER} &amp;ndash;query &amp;lsquo;serviceMeshProfile.istio.revisions&amp;rsquo;&#xA;Apply the revision label:&#xA;Bash&#xA;kubectl label namespace default istio.io/rev=asm-X-Y&#xA;Important # The default istio-injection=enabled labeling doesn&amp;rsquo;t work. Explicit versioning matching the control plane revision (ex: istio.</description>
    </item>
  </channel>
</rss>
