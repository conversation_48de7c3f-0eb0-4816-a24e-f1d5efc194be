<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Platform components on PepGenX Platform</title>
    <link>http://localhost:1313/docs/pepgenxdev/platform_components/</link>
    <description>Recent content in Platform components on PepGenX Platform</description>
    <generator>Hugo</generator>
    <language>en</language>
    <copyright>PepsiCo 2024</copyright>
    <atom:link href="http://localhost:1313/docs/pepgenxdev/platform_components/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>Get Started</title>
      <link>http://localhost:1313/docs/pepgenxdev/platform_components/get_started/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/platform_components/get_started/</guid>
      <description>Get Started # PepGenX request parameters # External REQUEST parameters:&#xA;Parameter Description Validation Used by OKTA token authenticate a call from PepsiCo application Registered in OKTA Apigee and Istio Microservice ID authenticate PepGenX registered application Registered in PepGenX IAM Service Microservice Secret authenticate PepGenX registered application Registered in PepGenX IAM Service EndUser ID Maybe EndUser OKTA token Registered in OKTA PepGenX Service delivery Flow # flowchart TD Confidential/Restricted REQ[Request] --&gt;|API Call| API(Apigee) API --&gt; IGG{IngressGateway</description>
    </item>
    <item>
      <title>Test environment</title>
      <link>http://localhost:1313/docs/pepgenxdev/platform_components/test_installation/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/platform_components/test_installation/</guid>
      <description>PAGE 378&#xA;Installation # K8S # Parameter Selection Networking kubenet Network Policy none Istio manual install Ingress and Egress Istio API Istio API (Virutal Service, Gateway) Azure Policy enabled Istio Gateway handles the L4 and L5 concerns, while VirtualService handles the L7 concerns Should we use mutual TLS for Restricted? By default, Istio allows any traffic out of the service mesh meshConfig.outboundTrafficPolicy.mode = REGISTRY_ONLY (ALLOW_ANY) requires ServiceEntry with location: MESH_EXTERNAL What response headers will be sent to end customer App istioctl x precheck istioctl install &amp;ndash;set profile=demo -y istioctl verify-install kubectl apply -f .</description>
    </item>
  </channel>
</rss>
