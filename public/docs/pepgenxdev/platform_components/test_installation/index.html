<!DOCTYPE html>
<html lang="en" dir="ltr">
<head><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>
  <meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="PAGE 378
Installation # K8S # Parameter Selection Networking kubenet Network Policy none Istio manual install Ingress and Egress Istio API Istio API (Virutal Service, Gateway) Azure Policy enabled Istio Gateway handles the L4 and L5 concerns, while VirtualService handles the L7 concerns Should we use mutual TLS for Restricted? By default, Istio allows any traffic out of the service mesh meshConfig.outboundTrafficPolicy.mode = REGISTRY_ONLY (ALLOW_ANY) requires ServiceEntry with location: MESH_EXTERNAL What response headers will be sent to end customer App istioctl x precheck istioctl install &ndash;set profile=demo -y istioctl verify-install kubectl apply -f .">
<meta name="theme-color" media="(prefers-color-scheme: light)" content="#ffffff">
<meta name="theme-color" media="(prefers-color-scheme: dark)" content="#343a40">
<meta name="color-scheme" content="light dark"><meta property="og:url" content="http://localhost:1313/docs/pepgenxdev/platform_components/test_installation/">
  <meta property="og:site_name" content="PepGenX Platform">
  <meta property="og:title" content="Test environment">
  <meta property="og:description" content="PAGE 378
Installation # K8S # Parameter Selection Networking kubenet Network Policy none Istio manual install Ingress and Egress Istio API Istio API (Virutal Service, Gateway) Azure Policy enabled Istio Gateway handles the L4 and L5 concerns, while VirtualService handles the L7 concerns Should we use mutual TLS for Restricted? By default, Istio allows any traffic out of the service mesh meshConfig.outboundTrafficPolicy.mode = REGISTRY_ONLY (ALLOW_ANY) requires ServiceEntry with location: MESH_EXTERNAL What response headers will be sent to end customer App istioctl x precheck istioctl install –set profile=demo -y istioctl verify-install kubectl apply -f .">
  <meta property="og:locale" content="en">
  <meta property="og:type" content="article">
    <meta property="article:section" content="docs">
    <meta property="article:modified_time" content="2024-07-12T17:07:43+02:00">
<title>Test environment | PepGenX Platform</title>
<link rel="manifest" href="/manifest.json">
<link rel="icon" href="/favicon.png" >
<link rel="canonical" href="http://localhost:1313/docs/pepgenxdev/platform_components/test_installation/">
<link rel="stylesheet" href="/book.min.e004910793d7d45ce0216432001fc1dd027da4d03823d63a444453371c0a82c4.css" integrity="sha256-4ASRB5PX1FzgIWQyAB/B3QJ9pNA4I9Y6RERTNxwKgsQ=" crossorigin="anonymous">
  <script defer src="/fuse.min.js"></script>
  <script defer src="/en.search.min.aafa63659a92fa301ba9b9e6cb97780aff5e08aea524831c54000076e8cc6677.js" integrity="sha256-qvpjZZqS&#43;jAbqbnmy5d4Cv9eCK6lJIMcVAAAdujMZnc=" crossorigin="anonymous"></script>

  

<!--
Made with Book Theme
https://github.com/alex-shpak/hugo-book
-->
  
</head>
<body dir="ltr">
  <input type="checkbox" class="hidden toggle" id="menu-control" />
  <input type="checkbox" class="hidden toggle" id="toc-control" />
  <main class="container flex">
    <aside class="book-menu">
      <div class="book-menu-content">
        
  <nav>
<h2 class="book-brand">
  <a class="flex align-center" href="/"><span>PepGenX Platform</span>
  </a>
</h2>


<div class="book-search hidden">
  <input type="text" id="book-search-input" placeholder="Search" aria-label="Search" maxlength="64" data-hotkeys="s/" />
  <div class="book-search-spinner hidden"></div>
  <ul id="book-search-results"></ul>
</div>
<script>document.querySelector(".book-search").classList.remove("hidden")</script>












  



  
  <ul>
    
      
        <li class="book-section-flat" >
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle" checked />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenxdev/" class="">PepGenX Platform Developers</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Must Read</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/home/" class="">Prerequisites</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/kubernetes/" class="">Kubernetes</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/python/" class="">Python</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/libraries/" class="">Libraries</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/standards/" class="">Standards &amp; Patterns</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/docker/" class="">Docker Containers</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/language_model_frameworks/" class="">Language Model Frameworks</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/azure/" class="">Azure Services</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle" checked />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Platform components</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/platform_components/get_started/" class="">Get Started</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Orchestrator</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Traffic controller</a>
    </label>
  

          
  <ul>
    
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">API abstraction layer</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Service delivery</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Deployment controllers</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Registry &amp; Images</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">IAM</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenxdev/platform_components/security_analyzer/" class="">Security Analyzer</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Prompt Guard</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Observability</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Kiali observability</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Jaeger distributed tracing</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">EndUser Access Control</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Istio</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/platform_components/istio/configuration/" class="">Configuration</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Writing Documentation</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Markdown</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/overview/" class="">Overview</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/blockquotes/" class="">Blockquotes</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/bold/" class="">Bold</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/code/" class="">Code</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/emoji/" class="">Emoji</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/emphasis/" class="">Emphasis</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/escaping-characters/" class="">Escaping Characters</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/fenced-code-blocks/" class="">Fenced Code Blocks</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/footnotes/" class="">Footnotes</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/headings/" class="">Headings</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/horizontal-rules/" class="">Horizontal Rules</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/images/" class="">Images</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/italic/" class="">Italic</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/links/" class="">Links</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/lists/" class="">Lists</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/ordered_list/" class="">Ordered Lists</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/paragraphs/" class="">Paragraphs</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/strikethrough/" class="">Strikethrough</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/tables/" class="">Tables</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/task_list/" class="">Task List</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/unordered-lists/" class="">Unordered Lists</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Hugo Markdown</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenxdev/shortcodes/hugo/section/" class="">Section</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/section/first-page/" class="">First Page</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/section/second-page/" class="">Second Page</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/buttons/" class="">Buttons</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/columns/" class="">Columns</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/details/" class="">Details</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/expand/" class="">Expand</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/hints/" class="">Hints</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/katex/" class="">KaTeX</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/mermaid/" class="">Mermaid Chart</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/tabs/" class="">Tabs</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">ToDo</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/todo/korzun/" class="">Korzun</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li class="book-section-flat" >
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/" class="">Welcome to PepGenX</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/getting_started/" class="">Getting Started</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/what_is_pepgenx/" class="">What is PepGenX</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/environment/" class="">Environment</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/quick_start/" class="">Quick Start</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/best_practicies/" class="">Best Practicies</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/concepts/" class="">Concepts</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/concepts/overview/" class="">Overview</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/concepts/architecture/" class="">Architecture</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/application_structure/" class="">Application Structure</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/operations/" class="">Operations</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/development/" class="">Development</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/deployment/" class="">Deployment</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/configuration/" class="">Configuration</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/integrations/" class="">Integrations</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/application_lifecycle/" class="">Application Lifecycle</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/docker_images/" class="">Docker Images</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/" class="">Development Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/llm_frameworks/" class="">LLM Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/langchain/" class="">Langchain</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/llamaindex/" class="">Llamaindex</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/autogen/" class="">Autogen</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/haystack/" class="">Haystack</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/griptape/" class="">Griptape</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/langroid/" class="">Langroid</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/langstream/" class="">Langstream</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/agentcloud/" class="">Agentcloud</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/vercelai/" class="">Vercel AI</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/semantic_kernel/" class="">Semantic Kernel</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/crewai/" class="">Crewai</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/txtai/" class="">txtai</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/" class="">Software Development Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/python/" class="">Python</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/java/" class="">Java</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/node.js/" class="">Node.js</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/go/" class="">Go</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/ruby_on_rails/" class="">Ruby On Rails</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/php/" class="">PHP</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/csharp/" class="">C#</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/3rd_party_frameworks/" class="">3&#39;rd Party Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/3rd_party_frameworks/rasa/" class="">RASA</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/3rd_party_frameworks/distylai/" class="">Distyl AI</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/api_frameworks/" class="">REST API Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/fastapi/" class="">FastAPI</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/springboot/" class="">SpringBoot</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/express.js/" class="">Express.js</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/gin/" class="">Gin</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/asp.netcore/" class="">ASP .Net Core</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/lumen/" class="">Lumen</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/pepgenx_services/" class="">PepGenX Services</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/" class="">Templates</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/prompts/" class="">Prompts</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/prompts/chains/" class="">Chains</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/prompts/trees/" class="">Trees</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/chains/" class="">Chains</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/workflows/" class="">Workflows</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/agents/" class="">Agents</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/functions/" class="">Functions</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/components/" class="">Components &amp; Tools</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/configuration_tasks/" class="">Configuration Tasks</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/tutorials/" class="">Tutorials</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/reference/" class="">Reference</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/reference/api_overview/" class="">API Overview</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/reference/api_access_control/" class="">API Access Control</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/reference/security/" class="">Security</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/frequently_asked_questions/" class="">Frequently Asked Questions</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>











  
<ul>
  
  <li>
    <a href="/posts/"  >
        Updates
      </a>
  </li>
  
</ul>






</nav>




  <script>(function(){var e=document.querySelector("aside .book-menu-content");addEventListener("beforeunload",function(){localStorage.setItem("menu.scrollTop",e.scrollTop)}),e.scrollTop=localStorage.getItem("menu.scrollTop")})()</script>


 
      </div>
    </aside>

    <div class="book-page">
      <header class="book-header">
        
  <div class="flex align-center justify-between">
  <label for="menu-control">
    <img src="/svg/menu.svg" class="book-icon" alt="Menu" />
  </label>

  <strong>Test environment</strong>

  <label for="toc-control">
    
    <img src="/svg/toc.svg" class="book-icon" alt="Table of Contents" />
    
  </label>
</div>

  
  <aside class="hidden clearfix">
    
  


<nav id="TableOfContents">
  <ul>
    <li><a href="#installation"><strong>Installation</strong></a>
      <ul>
        <li>
          <ul>
            <li><a href="#k8s"><strong>K8S</strong></a>
              <ul>
                <li><a href="#additional-k8s-integrations">Additional K8s integrations</a></li>
                <li><a href="#configuration"><strong>Configuration</strong></a></li>
              </ul>
            </li>
          </ul>
        </li>
      </ul>
    </li>
  </ul>
</nav>

  </aside>
  
 
      </header>

      
      
  <article class="markdown book-article"><p>PAGE 378</p>
<h1 id="installation">
  <strong>Installation</strong>
  <a class="anchor" href="#installation">#</a>
</h1>
<h3 id="k8s">
  <strong>K8S</strong>
  <a class="anchor" href="#k8s">#</a>
</h3>
<table>
<thead>
<tr>
<th style="text-align:left">Parameter</th>
<th style="text-align:left">Selection</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">Networking</td>
<td style="text-align:left">kubenet</td>
</tr>
<tr>
<td style="text-align:left">Network Policy</td>
<td style="text-align:left">none</td>
</tr>
<tr>
<td style="text-align:left">Istio</td>
<td style="text-align:left">manual install Ingress and Egress</td>
</tr>
<tr>
<td style="text-align:left">Istio API</td>
<td style="text-align:left">Istio API (Virutal Service, Gateway)</td>
</tr>
<tr>
<td style="text-align:left">Azure Policy</td>
<td style="text-align:left">enabled</td>
</tr>
</tbody>
</table>
<ol>
<li>Istio Gateway handles the L4 and L5 concerns, while VirtualService handles the L7 concerns</li>
<li>Should we use mutual TLS for Restricted?</li>
<li>By default, Istio allows any traffic out of the service mesh
<ul>
<li>meshConfig.outboundTrafficPolicy.mode = REGISTRY_ONLY (ALLOW_ANY)</li>
<li>requires ServiceEntry with location: MESH_EXTERNAL</li>
</ul>
</li>
<li>What response headers will be sent to end customer App</li>
</ol>
<ul>
<li>istioctl x precheck</li>
<li>istioctl install &ndash;set profile=demo -y</li>
<li>istioctl verify-install</li>
<li>kubectl apply -f ./samples/addons</li>
<li>kubectl label namespace NAMESPACENAME istio-injection=enabled</li>
</ul>
<p>Important:</p>
<ul>
<li>
<p>VirtualService / Retries</p>
</li>
<li>
<p>DestinationRule / Filetring and routing (Load Balancing)</p>
<ul>
<li>clien side (round robin/random/weight least)</li>
<li>least connection performs better than both random and round robin
<ul>
<li>The least-connection takes into account the latencies and queue depth</li>
</ul>
</li>
<li>Fortio load-testing client (Fortio (<a href="http://github.com/fortio/fortio">http://github.com/fortio/fortio</a>))
<ul>
<li>brew install fortio</li>
</ul>
</li>
<li>Locality LB requires nodes to be labeled - should be used?<br>
topology.kubernetes.io/region<br>
topology.kubernetes.io/zone</li>
</ul>
</li>
<li>
<p>Retry policies (for layered system) / Status codes / Timeouts / No. of attempts / Request hedging / Max queue, connections</p>
<ul>
<li>skipping retries with Circuit breaking</li>
</ul>
</li>
<li>
<p>Istio OutlierDetection instead of Apigee</p>
</li>
<li>
<p>Handling deployemnt&amp;release (iteratively) of new versions (dark launch):</p>
<ul>
<li>PepGenX components</li>
<li>Sector Microservices</li>
</ul>
</li>
<li>
<p>Trffic shifting based on:</p>
<ul>
<li>header matching</li>
<li>weights (eg.10%)</li>
<li>using Flagger (<a href="https://flagger.app">https://flagger.app</a>) - requires Prometheus</li>
</ul>
</li>
<li>
<p>Trafic mirroring usecases</p>
</li>
<li>
<p>The virtual service applies to all sidecars in the mesh.</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-tpl" data-lang="tpl"><span style="display:flex;"><span>kind: VirtualService
</span></span><span style="display:flex;"><span>spec:
</span></span><span style="display:flex;"><span>gateways:
</span></span><span style="display:flex;"><span>- mesh
</span></span></code></pre></div></li>
<li>
<p>Enchanced observability</p>
<ul>
<li>kube-prometheus (<a href="https://github.com/prometheus-operator/kube-prometheus">https://github.com/prometheus-operator/kube-prometheus</a>)
<ul>
<li>helm repo add prometheus-community https:/./prometheus-community.github.io/helm-charts</li>
<li>helm repo update</li>
<li>kubectl create ns prometheus</li>
<li>helm install prom prometheus-community/kube-prometheus-stack &ndash;version 13.13.1 -n prometheus -f ch7/prom-values.yaml</li>
</ul>
</li>
</ul>
</li>
<li>
<p>Istio Security</p>
<ul>
<li>PeerAuthentication = STRICT (X)/ PERMISSIVE
<ul>
<li>Mesh-wide (X)</li>
<li>Namespace-wide</li>
<li>Workload-specific
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-tpl" data-lang="tpl"><span style="display:flex;"><span>apiVersion: &#34;security.istio.io/v1beta1&#34;
</span></span><span style="display:flex;"><span>kind: &#34;PeerAuthentication&#34;
</span></span><span style="display:flex;"><span>metadata:
</span></span><span style="display:flex;"><span>name: &#34;default&#34;
</span></span><span style="display:flex;"><span>namespace: &#34;istio-system&#34;
</span></span><span style="display:flex;"><span>spec:
</span></span><span style="display:flex;"><span>mtls:
</span></span><span style="display:flex;"><span>    mode: STRICT
</span></span></code></pre></div></li>
</ul>
</li>
<li>RequestAuthentication</li>
<li>AuthorizationPolicy
<ul>
<li><a href="https://istio.io/latest/docs/reference/config/security/authorization-policy">https://istio.io/latest/docs/reference/config/security/authorization-policy</a></li>
<li><a href="https://istio.io/latest/docs/reference/config/security/conditions">https://istio.io/latest/docs/reference/config/security/conditions</a></li>
<li>selector - subset of workloads to which the policy applies
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-tpl" data-lang="tpl"><span style="display:flex;"><span>selector:
</span></span><span style="display:flex;"><span>  matchLabels:
</span></span><span style="display:flex;"><span>    app: webapp
</span></span></code></pre></div></li>
<li>action - ALLOW, DENY, or CUSTOM</li>
<li>rules
<ul>
<li>from - source
<ul>
<li>principals / notPrincipals ([&ldquo;cluster.local/ns/istioinaction/sa/webapp&rdquo;])</li>
<li>requestPrincipals (from JWT)</li>
<li>namespaces</li>
<li>ipBlocks</li>
</ul>
</li>
<li>to - target</li>
<li>when - list of conditions that need to be met after the rule has matched
<ul>
<li>operation
<ul>
<li>methods:</li>
</ul>
</li>
<li>key: (istio specific)<br>
values:</li>
<li>value-match
<ul>
<li>Exact matching</li>
<li>Prefix matching (/api/catalog*)</li>
<li>Suffix matching (*.istioinaction.io)</li>
<li>Presence matching (*)</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li><img src="/images/istio_policy.png" alt="Istio policy" /></li>
</ul>
</li>
<li>mutual TLS</li>
</ul>
</li>
<li>
<p>EnvoyFilter</p>
<ul>
<li>HttpConnectionManager HTTP filters (<a href="https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/http_filters">https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/http_filters</a>)</li>
<li>Lua scripts</li>
<li>Rate limiting</li>
</ul>
</li>
<li>
<p>Sidecar</p>
<ul>
<li>workloadSelector
<ul>
<li>mesh</li>
<li>namespace</li>
<li>workloadSelector</li>
</ul>
</li>
<li>ingress</li>
<li>egress</li>
<li>outboundTrafficPolicy
<ul>
<li>REGISTRY_ONLY</li>
<li>ALLOW_ANY</li>
</ul>
</li>
</ul>
</li>
<li>
<p>External Authorization: Open Policy Agent</p>
<ul>
<li>internal request authorizatio using envoyExtAuthz
<ul>
<li>includeHeadersInCheck: [&ldquo;should include appname/app token or sessionID&rdquo;] (for session replica synch would be required)</li>
</ul>
</li>
</ul>
</li>
<li>
<p>Istio deiscovery namespace selection:</p>
<ul>
<li>matchLabels
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-tpl" data-lang="tpl"><span style="display:flex;"><span>apiVersion: install.istio.io/v1alpha1
</span></span><span style="display:flex;"><span>kind: IstioOperator
</span></span><span style="display:flex;"><span>metadata:
</span></span><span style="display:flex;"><span>  namespace: istio-system
</span></span><span style="display:flex;"><span>spec:
</span></span><span style="display:flex;"><span>  meshConfig:
</span></span><span style="display:flex;"><span>    discoverySelectors:
</span></span><span style="display:flex;"><span>      - matchLabels:
</span></span><span style="display:flex;"><span>        istio-discovery: enabled
</span></span></code></pre></div></li>
<li>matchExpressions
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-tpl" data-lang="tpl"><span style="display:flex;"><span>apiVersion: install.istio.io/v1alpha1
</span></span><span style="display:flex;"><span>kind: IstioOperator
</span></span><span style="display:flex;"><span>metadata:
</span></span><span style="display:flex;"><span>  namespace: istio-system
</span></span><span style="display:flex;"><span>spec:
</span></span><span style="display:flex;"><span>  meshConfig:
</span></span><span style="display:flex;"><span>    discoverySelectors:
</span></span><span style="display:flex;"><span>      - matchExpressions:
</span></span><span style="display:flex;"><span>        - key: istio-exclude
</span></span><span style="display:flex;"><span>          operator: NotIn
</span></span><span style="display:flex;"><span>          values:
</span></span><span style="display:flex;"><span>            - &#34;true&#34;
</span></span></code></pre></div></li>
</ul>
</li>
<li>
<p>Autoscaling istiod deployment</p>
<ul>
<li>istiod because it initiates a 30-minute connection with the workloads, which is used to configure and update the proxies using the Aggregated Discovery Service (ADS). So, newly spun up istiod replicas don’t receive any load until the connections between the service proxies and the previous pilot expire</li>
</ul>
</li>
<li>
<p>istio-init - requires elevated permissions to configure traffic redirection to the Envoy proxy.</p>
</li>
<li>
<p>Istioctl:</p>
<ul>
<li>e.g. brew install istioctl</li>
</ul>
</li>
<li>
<p>Helm:</p>
<ul>
<li>helm repo add istio <a href="https://istio-release.storage.googleapis.com/charts">https://istio-release.storage.googleapis.com/charts</a></li>
<li>helm repo update</li>
</ul>
</li>
<li>
<p>Istio Operator</p>
<ul>
<li>istioctl operator init</li>
<li>configuration yaml</li>
</ul>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-tpl" data-lang="tpl"><span style="display:flex;"><span>apiVersion: install.istio.io/v1alpha1
</span></span><span style="display:flex;"><span>kind: IstioOperator
</span></span><span style="display:flex;"><span>metadata:
</span></span><span style="display:flex;"><span>name: istio-operator
</span></span><span style="display:flex;"><span>namespace: istio-system
</span></span><span style="display:flex;"><span>spec:
</span></span><span style="display:flex;"><span>profile: default
</span></span><span style="display:flex;"><span>components:
</span></span><span style="display:flex;"><span>    ingressGateways:
</span></span><span style="display:flex;"><span>    - name: istio-ingressgateway
</span></span><span style="display:flex;"><span>    enabled: true
</span></span><span style="display:flex;"><span>    egressGateways:
</span></span><span style="display:flex;"><span>    - name: istio-egressgateway
</span></span><span style="display:flex;"><span>    enabled: true
</span></span><span style="display:flex;"><span>    addonComponents:
</span></span><span style="display:flex;"><span>    kiali:
</span></span><span style="display:flex;"><span>        enabled: true
</span></span><span style="display:flex;"><span>    grafana:
</span></span><span style="display:flex;"><span>        enabled: true
</span></span><span style="display:flex;"><span>    prometheus:
</span></span><span style="display:flex;"><span>        enabled: true
</span></span><span style="display:flex;"><span>    tracing:
</span></span><span style="display:flex;"><span>        enabled: true
</span></span><span style="display:flex;"><span>values:
</span></span><span style="display:flex;"><span>    global:
</span></span><span style="display:flex;"><span>    istioNamespace: istio-system
</span></span><span style="display:flex;"><span>    configNamespace: istio-config
</span></span><span style="display:flex;"><span>    telemetryNamespace: istio-telemetry
</span></span><span style="display:flex;"><span>    gateways:
</span></span><span style="display:flex;"><span>    istio-ingressgateway:
</span></span><span style="display:flex;"><span>        autoscaleEnabled: true
</span></span><span style="display:flex;"><span>        resources:
</span></span><span style="display:flex;"><span>        requests:
</span></span><span style="display:flex;"><span>            cpu: 100m
</span></span><span style="display:flex;"><span>            memory: 128Mi
</span></span><span style="display:flex;"><span>    istio-egressgateway:
</span></span><span style="display:flex;"><span>        autoscaleEnabled: true
</span></span><span style="display:flex;"><span>        resources:
</span></span><span style="display:flex;"><span>        requests:
</span></span><span style="display:flex;"><span>            cpu: 100m
</span></span><span style="display:flex;"><span>            memory: 128Mi
</span></span><span style="display:flex;"><span>    kiali:
</span></span><span style="display:flex;"><span>    dashboard:
</span></span><span style="display:flex;"><span>        enableGrafana: true
</span></span><span style="display:flex;"><span>        enablePrometheus: true
</span></span><span style="display:flex;"><span>    grafana:
</span></span><span style="display:flex;"><span>    security:
</span></span><span style="display:flex;"><span>        enabled: true
</span></span><span style="display:flex;"><span>    prometheus:
</span></span><span style="display:flex;"><span>    security:
</span></span><span style="display:flex;"><span>        enabled: true
</span></span><span style="display:flex;"><span>    tracing:
</span></span><span style="display:flex;"><span>    provider: jaeger
</span></span><span style="display:flex;"><span>    jaeger:
</span></span><span style="display:flex;"><span>        tag:
</span></span><span style="display:flex;"><span>        enabled: true
</span></span></code></pre></div><ul>
<li>kubectl apply -f istio-operator.yaml</li>
</ul>
</li>
<li>
<p>Istio base:</p>
<ul>
<li>kubectl create namespace istio-system</li>
<li>helm install istio-base istio/base -n istio-system &ndash;set defaultRevision=default</li>
<li>helm install istiod istio/istiod -n istio-system &ndash;wait</li>
</ul>
</li>
<li>
<p>Istio ingress</p>
<ul>
<li>kubectl create namespace istio-ingress</li>
<li>helm install istio-ingress istio/gateway -n istio-ingress &ndash;wait</li>
</ul>
</li>
</ul>
<h4 id="additional-k8s-integrations">
  Additional K8s integrations
  <a class="anchor" href="#additional-k8s-integrations">#</a>
</h4>
<ul>
<li>cert-manager (without Prometheus)
<ul>
<li>helm repo add jetstack <a href="https://charts.jetstack.io">https://charts.jetstack.io</a> &ndash;force-update</li>
<li><a href="https://istio.io/latest/docs/ops/integrations/certmanager/">https://istio.io/latest/docs/ops/integrations/certmanager/</a></li>
<li>helm install <br>
cert-manager jetstack/cert-manager <br>
&ndash;namespace cert-manager <br>
&ndash;create-namespace <br>
&ndash;version v1.15.1 <br>
&ndash;set crds.enabled=true <br>
&ndash;set prometheus.enabled=false</li>
</ul>
</li>
<li>Prometheus
<ul>
<li>kubectl apply -f <a href="https://raw.githubusercontent.com/istio/istio/release-1.22/samples/addons/prometheus.yaml">https://raw.githubusercontent.com/istio/istio/release-1.22/samples/addons/prometheus.yaml</a></li>
</ul>
</li>
<li>Grafana
<ul>
<li>kubectl apply -f <a href="https://raw.githubusercontent.com/istio/istio/release-1.22/samples/addons/grafana.yaml">https://raw.githubusercontent.com/istio/istio/release-1.22/samples/addons/grafana.yaml</a></li>
</ul>
</li>
<li>Kiali
<ul>
<li>kubectl apply -f <a href="https://raw.githubusercontent.com/istio/istio/release-1.22/samples/addons/kiali.yaml">https://raw.githubusercontent.com/istio/istio/release-1.22/samples/addons/kiali.yaml</a></li>
<li>istioctl dashboard kiali -n aks-istio-system</li>
<li>The issue occurs because Kiali now expects the istiod pod to have the app.kubernetes.io/name=istiod label. However, istiod is deployed with app=istiod</li>
</ul>
</li>
<li>Jaeger
<ul>
<li>kubectl apply -f <a href="https://raw.githubusercontent.com/istio/istio/release-1.22/samples/addons/jaeger.yaml">https://raw.githubusercontent.com/istio/istio/release-1.22/samples/addons/jaeger.yaml</a></li>
<li>updating istio configuration</li>
</ul>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-tpl" data-lang="tpl"><span style="display:flex;"><span>data:
</span></span><span style="display:flex;"><span>    mesh: |-
</span></span><span style="display:flex;"><span>        defaultConfig:
</span></span><span style="display:flex;"><span>        discoveryAddress: istiod.istio-system.svc:15012
</span></span><span style="display:flex;"><span>        defaultProviders:
</span></span><span style="display:flex;"><span>        metrics:
</span></span><span style="display:flex;"><span>        - prometheus
</span></span><span style="display:flex;"><span>        enablePrometheusMerge: true
</span></span><span style="display:flex;"><span>        rootNamespace: istio-system
</span></span><span style="display:flex;"><span>        trustDomain: cluster.local
</span></span><span style="display:flex;"><span>        tracing:
</span></span><span style="display:flex;"><span>        sampling: 100.0
</span></span><span style="display:flex;"><span>        zipkin:
</span></span><span style="display:flex;"><span>            address: &#34;jaeger-collector.default.svc.cluster.local:9411&#34;
</span></span><span style="display:flex;"><span>    meshNetworks: &#39;networks: <span style="color:#75715e">{}</span>&#39;
</span></span></code></pre></div></li>
</ul>
<h4 id="configuration">
  <strong>Configuration</strong>
  <a class="anchor" href="#configuration">#</a>
</h4>
<ul>
<li>meshConfig.outboundTrafficPolicy.mode
<ul>
<li>If the option is set to REGISTRY_ONLY, then the Istio proxy blocks any host without an HTTP service or service entry defined within the mesh. ALLOW_ANY is the default value</li>
</ul>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-tpl" data-lang="tpl"><span style="display:flex;"><span>spec:
</span></span><span style="display:flex;"><span>meshConfig:
</span></span><span style="display:flex;"><span>    outboundTrafficPolicy:
</span></span><span style="display:flex;"><span>    mode: REGISTRY_ONLY
</span></span></code></pre></div><ul>
<li>access external service</li>
</ul>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-tpl" data-lang="tpl"><span style="display:flex;"><span>apiVersion: networking.istio.io/v1alpha3
</span></span><span style="display:flex;"><span>kind: ServiceEntry
</span></span><span style="display:flex;"><span>metadata:
</span></span><span style="display:flex;"><span>    name: httpbin-ext
</span></span><span style="display:flex;"><span>spec:
</span></span><span style="display:flex;"><span>    hosts:
</span></span><span style="display:flex;"><span>    - httpbin.org
</span></span><span style="display:flex;"><span>    ports:
</span></span><span style="display:flex;"><span>    - number: 80
</span></span><span style="display:flex;"><span>        name: http
</span></span><span style="display:flex;"><span>        protocol: HTTP
</span></span><span style="display:flex;"><span>    resolution: DNS
</span></span><span style="display:flex;"><span>    location: MESH_EXTERNAL
</span></span></code></pre></div><div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-tpl" data-lang="tpl"><span style="display:flex;"><span>apiVersion: networking.istio.io/v1alpha3
</span></span><span style="display:flex;"><span>kind: ServiceEntry
</span></span><span style="display:flex;"><span>metadata:
</span></span><span style="display:flex;"><span>    name: google
</span></span><span style="display:flex;"><span>spec:
</span></span><span style="display:flex;"><span>    hosts:
</span></span><span style="display:flex;"><span>    - www.google.com
</span></span><span style="display:flex;"><span>    ports:
</span></span><span style="display:flex;"><span>    - number: 443
</span></span><span style="display:flex;"><span>        name: https
</span></span><span style="display:flex;"><span>        protocol: HTTPS
</span></span><span style="display:flex;"><span>    resolution: DNS
</span></span><span style="display:flex;"><span>    location: MESH_EXTERNAL
</span></span></code></pre></div><div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-tpl" data-lang="tpl"><span style="display:flex;"><span>apiVersion: gateway.networking.k8s.io/v1
</span></span><span style="display:flex;"><span>kind: HTTPRoute
</span></span><span style="display:flex;"><span>metadata:
</span></span><span style="display:flex;"><span>    name: httpbin-ext
</span></span><span style="display:flex;"><span>spec:
</span></span><span style="display:flex;"><span>parentRefs:
</span></span><span style="display:flex;"><span>- kind: ServiceEntry
</span></span><span style="display:flex;"><span>    group: networking.istio.io
</span></span><span style="display:flex;"><span>    name: httpbin-ext
</span></span><span style="display:flex;"><span>hostnames:
</span></span><span style="display:flex;"><span>- httpbin.org
</span></span><span style="display:flex;"><span>rules:
</span></span><span style="display:flex;"><span>- timeouts:
</span></span><span style="display:flex;"><span>    request: 3s
</span></span><span style="display:flex;"><span>  backendRefs:
</span></span><span style="display:flex;"><span>  - kind: Hostname
</span></span><span style="display:flex;"><span>    group: networking.istio.io
</span></span><span style="display:flex;"><span>    name: httpbin.org
</span></span><span style="display:flex;"><span>    port: 80
</span></span></code></pre></div></li>
</ul>
</article>
 
      

      <footer class="book-footer">
        
  <div class="flex flex-wrap justify-between" style="font-size: 80%;">

<div>LastMod: July 12, 2024<br>
      By: Dariusz Korzun<br>
      Commit: d095cd2f53325e68448df24a91f3c52432d0a359<br>
  </div>

</div>
 
        
      </footer>

      
  
  <div class="book-comments">

</div>
  
 

      <label for="menu-control" class="hidden book-menu-overlay"></label>
    </div>

    
    <aside class="book-toc">
      <div class="book-toc-content">
        
        
        
        <nav class="pepgenx-toc">
        <ol>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/platform_components/test_installation/#k8s">K8S</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/platform_components/test_installation/#additional-k8s-integrations">Additional K8s integrations</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/docs/pepgenxdev/platform_components/test_installation/#configuration">Configuration</a>
        </div>
          </ol>
        </div>
        </ol>
      </nav>
      </div>
    </aside>
    
  </main>

  
</body>
</html>












