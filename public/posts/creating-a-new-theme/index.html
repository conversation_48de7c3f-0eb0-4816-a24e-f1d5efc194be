<!DOCTYPE html>
<html lang="en" dir="ltr">
<head><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>
  <meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="Introduction # This tutorial will show you how to create a simple theme in <PERSON>. I assume that you are familiar with HTML, the bash command line, and that you are comfortable using Markdown to format content. I&rsquo;ll explain how <PERSON> uses templates and how you can organize your templates to create a theme. I won&rsquo;t cover using CSS to style your theme.
We&rsquo;ll start with creating a new site with a very basic template.">
<meta name="theme-color" media="(prefers-color-scheme: light)" content="#ffffff">
<meta name="theme-color" media="(prefers-color-scheme: dark)" content="#343a40">
<meta name="color-scheme" content="light dark"><meta property="og:url" content="http://localhost:1313/posts/creating-a-new-theme/">
  <meta property="og:site_name" content="PepGenX Platform">
  <meta property="og:title" content="Creating a New Theme">
  <meta property="og:description" content="Introduction # This tutorial will show you how to create a simple theme in Hugo. I assume that you are familiar with HTML, the bash command line, and that you are comfortable using Markdown to format content. I’ll explain how <PERSON> uses templates and how you can organize your templates to create a theme. I won’t cover using CSS to style your theme.
We’ll start with creating a new site with a very basic template.">
  <meta property="og:locale" content="en">
  <meta property="og:type" content="article">
    <meta property="article:section" content="posts">
    <meta property="article:published_time" content="2014-09-28T00:00:00+00:00">
    <meta property="article:modified_time" content="2024-07-05T14:21:13+02:00">
<title>Creating a New Theme | PepGenX Platform</title>
<link rel="manifest" href="/manifest.json">
<link rel="icon" href="/favicon.png" >
<link rel="canonical" href="http://localhost:1313/posts/creating-a-new-theme/">
<link rel="stylesheet" href="/book.min.e004910793d7d45ce0216432001fc1dd027da4d03823d63a444453371c0a82c4.css" integrity="sha256-4ASRB5PX1FzgIWQyAB/B3QJ9pNA4I9Y6RERTNxwKgsQ=" crossorigin="anonymous">
  <script defer src="/fuse.min.js"></script>
  <script defer src="/en.search.min.aafa63659a92fa301ba9b9e6cb97780aff5e08aea524831c54000076e8cc6677.js" integrity="sha256-qvpjZZqS&#43;jAbqbnmy5d4Cv9eCK6lJIMcVAAAdujMZnc=" crossorigin="anonymous"></script>

  

<!--
Made with Book Theme
https://github.com/alex-shpak/hugo-book
-->
  
</head>
<body dir="ltr">
  <input type="checkbox" class="hidden toggle" id="menu-control" />
  <input type="checkbox" class="hidden toggle" id="toc-control" />
  <main class="container flex">
    <aside class="book-menu">
      <div class="book-menu-content">
        
  <nav>
<h2 class="book-brand">
  <a class="flex align-center" href="/"><span>PepGenX Platform</span>
  </a>
</h2>


<div class="book-search hidden">
  <input type="text" id="book-search-input" placeholder="Search" aria-label="Search" maxlength="64" data-hotkeys="s/" />
  <div class="book-search-spinner hidden"></div>
  <ul id="book-search-results"></ul>
</div>
<script>document.querySelector(".book-search").classList.remove("hidden")</script>












  



  
  <ul>
    
      
        <li class="book-section-flat" >
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenxdev/" class="">PepGenX Platform Developers</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Must Read</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/home/" class="">Prerequisites</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/kubernetes/" class="">Kubernetes</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/python/" class="">Python</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/libraries/" class="">Libraries</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/standards/" class="">Standards &amp; Patterns</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/docker/" class="">Docker Containers</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/language_model_frameworks/" class="">Language Model Frameworks</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/azure/" class="">Azure Services</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Platform components</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/platform_components/get_started/" class="">Get Started</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Orchestrator</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Traffic controller</a>
    </label>
  

          
  <ul>
    
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">API abstraction layer</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Service delivery</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Deployment controllers</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Registry &amp; Images</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">IAM</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenxdev/platform_components/security_analyzer/" class="">Security Analyzer</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Prompt Guard</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Observability</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Kiali observability</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Jaeger distributed tracing</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">EndUser Access Control</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Istio</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/platform_components/istio/configuration/" class="">Configuration</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Writing Documentation</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Markdown</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/overview/" class="">Overview</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/blockquotes/" class="">Blockquotes</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/bold/" class="">Bold</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/code/" class="">Code</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/emoji/" class="">Emoji</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/emphasis/" class="">Emphasis</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/escaping-characters/" class="">Escaping Characters</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/fenced-code-blocks/" class="">Fenced Code Blocks</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/footnotes/" class="">Footnotes</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/headings/" class="">Headings</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/horizontal-rules/" class="">Horizontal Rules</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/images/" class="">Images</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/italic/" class="">Italic</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/links/" class="">Links</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/lists/" class="">Lists</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/ordered_list/" class="">Ordered Lists</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/paragraphs/" class="">Paragraphs</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/strikethrough/" class="">Strikethrough</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/tables/" class="">Tables</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/task_list/" class="">Task List</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/unordered-lists/" class="">Unordered Lists</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Hugo Markdown</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenxdev/shortcodes/hugo/section/" class="">Section</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/section/first-page/" class="">First Page</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/section/second-page/" class="">Second Page</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/buttons/" class="">Buttons</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/columns/" class="">Columns</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/details/" class="">Details</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/expand/" class="">Expand</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/hints/" class="">Hints</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/katex/" class="">KaTeX</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/mermaid/" class="">Mermaid Chart</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/tabs/" class="">Tabs</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">ToDo</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/todo/korzun/" class="">Korzun</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li class="book-section-flat" >
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/" class="">Welcome to PepGenX</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/getting_started/" class="">Getting Started</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/what_is_pepgenx/" class="">What is PepGenX</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/environment/" class="">Environment</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/quick_start/" class="">Quick Start</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/best_practicies/" class="">Best Practicies</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/concepts/" class="">Concepts</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/concepts/overview/" class="">Overview</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/concepts/architecture/" class="">Architecture</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/application_structure/" class="">Application Structure</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/operations/" class="">Operations</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/development/" class="">Development</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/deployment/" class="">Deployment</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/configuration/" class="">Configuration</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/integrations/" class="">Integrations</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/application_lifecycle/" class="">Application Lifecycle</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/docker_images/" class="">Docker Images</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/" class="">Development Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/llm_frameworks/" class="">LLM Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/langchain/" class="">Langchain</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/llamaindex/" class="">Llamaindex</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/autogen/" class="">Autogen</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/haystack/" class="">Haystack</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/griptape/" class="">Griptape</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/langroid/" class="">Langroid</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/langstream/" class="">Langstream</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/agentcloud/" class="">Agentcloud</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/vercelai/" class="">Vercel AI</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/semantic_kernel/" class="">Semantic Kernel</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/crewai/" class="">Crewai</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/txtai/" class="">txtai</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/" class="">Software Development Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/python/" class="">Python</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/java/" class="">Java</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/node.js/" class="">Node.js</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/go/" class="">Go</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/ruby_on_rails/" class="">Ruby On Rails</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/php/" class="">PHP</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/csharp/" class="">C#</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/3rd_party_frameworks/" class="">3&#39;rd Party Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/3rd_party_frameworks/rasa/" class="">RASA</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/3rd_party_frameworks/distylai/" class="">Distyl AI</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/api_frameworks/" class="">REST API Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/fastapi/" class="">FastAPI</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/springboot/" class="">SpringBoot</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/express.js/" class="">Express.js</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/gin/" class="">Gin</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/asp.netcore/" class="">ASP .Net Core</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/lumen/" class="">Lumen</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/pepgenx_services/" class="">PepGenX Services</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/" class="">Templates</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/prompts/" class="">Prompts</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/prompts/chains/" class="">Chains</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/prompts/trees/" class="">Trees</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/chains/" class="">Chains</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/workflows/" class="">Workflows</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/agents/" class="">Agents</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/functions/" class="">Functions</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/components/" class="">Components &amp; Tools</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/configuration_tasks/" class="">Configuration Tasks</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/tutorials/" class="">Tutorials</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/reference/" class="">Reference</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/reference/api_overview/" class="">API Overview</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/reference/api_access_control/" class="">API Access Control</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/reference/security/" class="">Security</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/frequently_asked_questions/" class="">Frequently Asked Questions</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>











  
<ul>
  
  <li>
    <a href="/posts/"  >
        Updates
      </a>
  </li>
  
</ul>






</nav>




  <script>(function(){var e=document.querySelector("aside .book-menu-content");addEventListener("beforeunload",function(){localStorage.setItem("menu.scrollTop",e.scrollTop)}),e.scrollTop=localStorage.getItem("menu.scrollTop")})()</script>


 
      </div>
    </aside>

    <div class="book-page">
      <header class="book-header">
        
  <div class="flex align-center justify-between">
  <label for="menu-control">
    <img src="/svg/menu.svg" class="book-icon" alt="Menu" />
  </label>

  <strong>Creating a New Theme</strong>

  <label for="toc-control">
    
    <img src="/svg/toc.svg" class="book-icon" alt="Table of Contents" />
    
  </label>
</div>

  
  <aside class="hidden clearfix">
    
  


<nav id="TableOfContents">
  <ul>
    <li>
      <ul>
        <li><a href="#introduction">Introduction</a></li>
        <li><a href="#some-definitions">Some Definitions</a>
          <ul>
            <li><a href="#skins">Skins</a></li>
            <li><a href="#the-home-page">The Home Page</a></li>
            <li><a href="#site-configuration-file">Site Configuration File</a></li>
            <li><a href="#content">Content</a>
              <ul>
                <li><a href="#front-matter">Front Matter</a></li>
                <li><a href="#markdown">Markdown</a></li>
              </ul>
            </li>
            <li><a href="#template-files">Template Files</a>
              <ul>
                <li><a href="#single-template">Single Template</a></li>
                <li><a href="#list-template">List Template</a></li>
                <li><a href="#partial-template">Partial Template</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li><a href="#create-a-new-site">Create a New Site</a>
          <ul>
            <li><a href="#generate-the-html-for-the-new-site">Generate the HTML For the New Site</a></li>
            <li><a href="#test-the-new-site">Test the New Site</a></li>
          </ul>
        </li>
        <li><a href="#create-a-new-theme">Create a New Theme</a>
          <ul>
            <li><a href="#create-a-skeleton">Create a Skeleton</a></li>
            <li><a href="#update-the-configuration-file-to-use-the-theme">Update the Configuration File to Use the Theme</a></li>
            <li><a href="#generate-the-site">Generate the Site</a>
              <ul>
                <li><a href="#the-home-page-1">The Home Page</a></li>
                <li><a href="#the-magic-of-static">The Magic of Static</a></li>
              </ul>
            </li>
          </ul>
        </li>
        <li><a href="#the-theme-development-cycle">The Theme Development Cycle</a>
          <ul>
            <li><a href="#purge-the-public-directory">Purge the public/ Directory</a></li>
            <li><a href="#hugos-watch-option">Hugo&rsquo;s Watch Option</a></li>
            <li><a href="#live-reload">Live Reload</a></li>
            <li><a href="#development-commands">Development Commands</a></li>
          </ul>
        </li>
        <li><a href="#update-the-home-page-template">Update the Home Page Template</a>
          <ul>
            <li><a href="#make-a-static-home-page">Make a Static Home Page</a>
              <ul>
                <li><a href="#live-reload-1">Live Reload</a></li>
              </ul>
            </li>
            <li><a href="#build-a-dynamic-home-page">Build a &ldquo;Dynamic&rdquo; Home Page</a>
              <ul>
                <li><a href="#create-new-posts">Create New Posts</a></li>
              </ul>
            </li>
            <li><a href="#list-and-single-templates">List and Single Templates</a></li>
            <li><a href="#add-content-to-the-homepage">Add Content to the Homepage</a></li>
            <li><a href="#add-content-to-the-posts">Add Content to the Posts</a>
              <ul>
                <li><a href="#update-the-template-file">Update the Template File</a></li>
              </ul>
            </li>
            <li><a href="#linking-to-content">Linking to Content</a></li>
            <li><a href="#create-a-post-listing">Create a Post Listing</a></li>
          </ul>
        </li>
        <li><a href="#creating-top-level-pages">Creating Top Level Pages</a></li>
        <li><a href="#sharing-templates">Sharing Templates</a>
          <ul>
            <li><a href="#create-the-header-and-footer-partials">Create the Header and Footer Partials</a></li>
            <li><a href="#update-the-home-page-template-to-use-the-partials">Update the Home Page Template to Use the Partials</a></li>
            <li><a href="#update-the-default-single-template-to-use-the-partials">Update the Default Single Template to Use the Partials</a></li>
          </ul>
        </li>
        <li><a href="#add-date-published-to-posts">Add “Date Published” to Posts</a>
          <ul>
            <li><a href="#add-date-published-to-the-template">Add “Date Published” to the Template</a></li>
            <li><a href="#dont-repeat-yourself">Don&rsquo;t Repeat Yourself</a></li>
          </ul>
        </li>
      </ul>
    </li>
  </ul>
</nav>

  </aside>
  
 
      </header>

      
      
<article class="markdown book-post">
  <h1>
    <a href="/posts/creating-a-new-theme/">Creating a New Theme</a>
  </h1>
  
  <h5>September 28, 2014</h5>



  

  



<h2 id="introduction">
  Introduction
  <a class="anchor" href="#introduction">#</a>
</h2>
<p>This tutorial will show you how to create a simple theme in Hugo. I assume that you are familiar with HTML, the bash command line, and that you are comfortable using Markdown to format content. I&rsquo;ll explain how Hugo uses templates and how you can organize your templates to create a theme. I won&rsquo;t cover using CSS to style your theme.</p>
<p>We&rsquo;ll start with creating a new site with a very basic template. Then we&rsquo;ll add in a few pages and posts. With small variations on that, you will be able to create many different types of web sites.</p>
<p>In this tutorial, commands that you enter will start with the &ldquo;$&rdquo; prompt. The output will follow. Lines that start with &ldquo;#&rdquo; are comments that I&rsquo;ve added to explain a point. When I show updates to a file, the &ldquo;:wq&rdquo; on the last line means to save the file.</p>
<p>Here&rsquo;s an example:</p>
<pre tabindex="0"><code>## this is a comment
$ echo this is a command
this is a command

## edit the file
$ vi foo.md
+++
date = &#34;2014-09-28&#34;
title = &#34;creating a new theme&#34;
+++

bah and humbug
:wq

## show it
$ cat foo.md
+++
date = &#34;2014-09-28&#34;
title = &#34;creating a new theme&#34;
+++

bah and humbug
$
</code></pre><h2 id="some-definitions">
  Some Definitions
  <a class="anchor" href="#some-definitions">#</a>
</h2>
<p>There are a few concepts that you need to understand before creating a theme.</p>
<h3 id="skins">
  Skins
  <a class="anchor" href="#skins">#</a>
</h3>
<p>Skins are the files responsible for the look and feel of your site. It’s the CSS that controls colors and fonts, it’s the Javascript that determines actions and reactions. It’s also the rules that Hugo uses to transform your content into the HTML that the site will serve to visitors.</p>
<p>You have two ways to create a skin. The simplest way is to create it in the <code>layouts/</code> directory. If you do, then you don’t have to worry about configuring Hugo to recognize it. The first place that Hugo will look for rules and files is in the <code>layouts/</code> directory so it will always find the skin.</p>
<p>Your second choice is to create it in a sub-directory of the <code>themes/</code> directory. If you do, then you must always tell Hugo where to search for the skin. It’s extra work, though, so why bother with it?</p>
<p>The difference between creating a skin in <code>layouts/</code> and creating it in <code>themes/</code> is very subtle. A skin in <code>layouts/</code> can’t be customized without updating the templates and static files that it is built from. A skin created in <code>themes/</code>, on the other hand, can be and that makes it easier for other people to use it.</p>
<p>The rest of this tutorial will call a skin created in the <code>themes/</code> directory a theme.</p>
<p>Note that you can use this tutorial to create a skin in the <code>layouts/</code> directory if you wish to. The main difference will be that you won’t need to update the site’s configuration file to use a theme.</p>
<h3 id="the-home-page">
  The Home Page
  <a class="anchor" href="#the-home-page">#</a>
</h3>
<p>The home page, or landing page, is the first page that many visitors to a site see. It is the index.html file in the root directory of the web site. Since Hugo writes files to the public/ directory, our home page is public/index.html.</p>
<h3 id="site-configuration-file">
  Site Configuration File
  <a class="anchor" href="#site-configuration-file">#</a>
</h3>
<p>When Hugo runs, it looks for a configuration file that contains settings that override default values for the entire site. The file can use TOML, YAML, or JSON. I prefer to use TOML for my configuration files. If you prefer to use JSON or YAML, you’ll need to translate my examples. You’ll also need to change the name of the file since Hugo uses the extension to determine how to process it.</p>
<p>Hugo translates Markdown files into HTML. By default, Hugo expects to find Markdown files in your <code>content/</code> directory and template files in your <code>themes/</code> directory. It will create HTML files in your <code>public/</code> directory. You can change this by specifying alternate locations in the configuration file.</p>
<h3 id="content">
  Content
  <a class="anchor" href="#content">#</a>
</h3>
<p>Content is stored in text files that contain two sections. The first section is the “front matter,” which is the meta-information on the content. The second section contains Markdown that will be converted to HTML.</p>
<h4 id="front-matter">
  Front Matter
  <a class="anchor" href="#front-matter">#</a>
</h4>
<p>The front matter is information about the content. Like the configuration file, it can be written in TOML, YAML, or JSON. Unlike the configuration file, Hugo doesn’t use the file’s extension to know the format. It looks for markers to signal the type. TOML is surrounded by “<code>+++</code>”, YAML by “<code>---</code>”, and JSON is enclosed in curly braces. I prefer to use TOML, so you’ll need to translate my examples if you prefer YAML or JSON.</p>
<p>The information in the front matter is passed into the template before the content is rendered into HTML.</p>
<h4 id="markdown">
  Markdown
  <a class="anchor" href="#markdown">#</a>
</h4>
<p>Content is written in Markdown which makes it easier to create the content. Hugo runs the content through a Markdown engine to create the HTML which will be written to the output file.</p>
<h3 id="template-files">
  Template Files
  <a class="anchor" href="#template-files">#</a>
</h3>
<p>Hugo uses template files to render content into HTML. Template files are a bridge between the content and presentation. Rules in the template define what content is published, where it&rsquo;s published to, and how it will rendered to the HTML file. The template guides the presentation by specifying the style to use.</p>
<p>There are three types of templates: single, list, and partial. Each type takes a bit of content as input and transforms it based on the commands in the template.</p>
<p>Hugo uses its knowledge of the content to find the template file used to render the content. If it can’t find a template that is an exact match for the content, it will shift up a level and search from there. It will continue to do so until it finds a matching template or runs out of templates to try. If it can’t find a template, it will use the default template for the site.</p>
<p>Please note that you can use the front matter to influence Hugo’s choice of templates.</p>
<h4 id="single-template">
  Single Template
  <a class="anchor" href="#single-template">#</a>
</h4>
<p>A single template is used to render a single piece of content. For example, an article or post would be a single piece of content and use a single template.</p>
<h4 id="list-template">
  List Template
  <a class="anchor" href="#list-template">#</a>
</h4>
<p>A list template renders a group of related content. That could be a summary of recent postings or all articles in a category. List templates can contain multiple groups.</p>
<p>The homepage template is a special type of list template. Hugo assumes that the home page of your site will act as the portal for the rest of the content in the site.</p>
<h4 id="partial-template">
  Partial Template
  <a class="anchor" href="#partial-template">#</a>
</h4>
<p>A partial template is a template that can be included in other templates. Partial templates must be called using the “partial” template command. They are very handy for rolling up common behavior. For example, your site may have a banner that all pages use. Instead of copying the text of the banner into every single and list template, you could create a partial with the banner in it. That way if you decide to change the banner, you only have to change the partial template.</p>
<h2 id="create-a-new-site">
  Create a New Site
  <a class="anchor" href="#create-a-new-site">#</a>
</h2>
<p>Let&rsquo;s use Hugo to create a new web site. I&rsquo;m a Mac user, so I&rsquo;ll create mine in my home directory, in the Sites folder. If you&rsquo;re using Linux, you might have to create the folder first.</p>
<p>The &ldquo;new site&rdquo; command will create a skeleton of a site. It will give you the basic directory structure and a useable configuration file.</p>
<pre tabindex="0"><code>$ hugo new site ~/Sites/zafta
$ cd ~/Sites/zafta
$ ls -l
total 8
drwxr-xr-x  7 <USER>  <GROUP>  238 Sep 29 16:49 .
drwxr-xr-x  3 <USER>  <GROUP>  102 Sep 29 16:49 ..
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 16:49 archetypes
-rw-r--r--  1 <USER>  <GROUP>   82 Sep 29 16:49 config.toml
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 16:49 content
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 16:49 layouts
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 16:49 static
$
</code></pre><p>Take a look in the content/ directory to confirm that it is empty.</p>
<p>The other directories (archetypes/, layouts/, and static/) are used when customizing a theme. That&rsquo;s a topic for a different tutorial, so please ignore them for now.</p>
<h3 id="generate-the-html-for-the-new-site">
  Generate the HTML For the New Site
  <a class="anchor" href="#generate-the-html-for-the-new-site">#</a>
</h3>
<p>Running the <code>hugo</code> command with no options will read all the available content and generate the HTML files. It will also copy all static files (that&rsquo;s everything that&rsquo;s not content). Since we have an empty site, it won&rsquo;t do much, but it will do it very quickly.</p>
<pre tabindex="0"><code>$ hugo --verbose
INFO: 2014/09/29 Using config file: config.toml
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/
WARN: 2014/09/29 Unable to locate layout: [index.html _default/list.html _default/single.html]
WARN: 2014/09/29 Unable to locate layout: [404.html]
0 draft content 
0 future content 
0 pages created 
0 tags created
0 categories created
in 2 ms
$ 
</code></pre><p>The &ldquo;<code>--verbose</code>&rdquo; flag gives extra information that will be helpful when we build the template. Every line of the output that starts with &ldquo;INFO:&rdquo; or &ldquo;WARN:&rdquo; is present because we used that flag. The lines that start with &ldquo;WARN:&rdquo; are warning messages. We&rsquo;ll go over them later.</p>
<p>We can verify that the command worked by looking at the directory again.</p>
<pre tabindex="0"><code>$ ls -l
total 8
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 16:49 archetypes
-rw-r--r--  1 <USER>  <GROUP>   82 Sep 29 16:49 config.toml
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 16:49 content
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 16:49 layouts
drwxr-xr-x  4 <USER>  <GROUP>  136 Sep 29 17:02 public
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 16:49 static
$
</code></pre><p>See that new public/ directory? Hugo placed all generated content there. When you&rsquo;re ready to publish your web site, that&rsquo;s the place to start. For now, though, let&rsquo;s just confirm that we have what we&rsquo;d expect from a site with no content.</p>
<pre tabindex="0"><code>$ ls -l public
total 16
-rw-r--r--  1 <USER>  <GROUP>  416 Sep 29 17:02 index.xml
-rw-r--r--  1 <USER>  <GROUP>  262 Sep 29 17:02 sitemap.xml
$ 
</code></pre><p>Hugo created two XML files, which is standard, but there are no HTML files.</p>
<h3 id="test-the-new-site">
  Test the New Site
  <a class="anchor" href="#test-the-new-site">#</a>
</h3>
<p>Verify that you can run the built-in web server. It will dramatically shorten your development cycle if you do. Start it by running the &ldquo;server&rdquo; command. If it is successful, you will see output similar to the following:</p>
<pre tabindex="0"><code>$ hugo server --verbose
INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/
WARN: 2014/09/29 Unable to locate layout: [index.html _default/list.html _default/single.html]
WARN: 2014/09/29 Unable to locate layout: [404.html]
0 draft content 
0 future content 
0 pages created 
0 tags created
0 categories created
in 2 ms
Serving pages from /Users/<USER>/Sites/zafta/public
Web Server is available at http://localhost:1313
Press Ctrl+C to stop
</code></pre><p>Connect to the listed URL (it&rsquo;s on the line that starts with &ldquo;Web Server&rdquo;). If everything is working correctly, you should get a page that shows the following:</p>
<pre tabindex="0"><code>index.xml
sitemap.xml
</code></pre><p>That&rsquo;s a listing of your public/ directory. Hugo didn&rsquo;t create a home page because our site has no content. When there&rsquo;s no index.html file in a directory, the server lists the files in the directory, which is what you should see in your browser.</p>
<p>Let’s go back and look at those warnings again.</p>
<pre tabindex="0"><code>WARN: 2014/09/29 Unable to locate layout: [index.html _default/list.html _default/single.html]
WARN: 2014/09/29 Unable to locate layout: [404.html]
</code></pre><p>That second warning is easier to explain. We haven’t created a template to be used to generate “page not found errors.” The 404 message is a topic for a separate tutorial.</p>
<p>Now for the first warning. It is for the home page. You can tell because the first layout that it looked for was “index.html.” That’s only used by the home page.</p>
<p>I like that the verbose flag causes Hugo to list the files that it&rsquo;s searching for. For the home page, they are index.html, _default/list.html, and _default/single.html. There are some rules that we&rsquo;ll cover later that explain the names and paths. For now, just remember that Hugo couldn&rsquo;t find a template for the home page and it told you so.</p>
<p>At this point, you&rsquo;ve got a working installation and site that we can build upon. All that’s left is to add some content and a theme to display it.</p>
<h2 id="create-a-new-theme">
  Create a New Theme
  <a class="anchor" href="#create-a-new-theme">#</a>
</h2>
<p>Hugo doesn&rsquo;t ship with a default theme. There are a few available (I counted a dozen when I first installed Hugo) and Hugo comes with a command to create new themes.</p>
<p>We&rsquo;re going to create a new theme called &ldquo;zafta.&rdquo; Since the goal of this tutorial is to show you how to fill out the files to pull in your content, the theme will not contain any CSS. In other words, ugly but functional.</p>
<p>All themes have opinions on content and layout. For example, Zafta uses &ldquo;post&rdquo; over &ldquo;blog&rdquo;. Strong opinions make for simpler templates but differing opinions make it tougher to use themes. When you build a theme, consider using the terms that other themes do.</p>
<h3 id="create-a-skeleton">
  Create a Skeleton
  <a class="anchor" href="#create-a-skeleton">#</a>
</h3>
<p>Use the hugo &ldquo;new&rdquo; command to create the skeleton of a theme. This creates the directory structure and places empty files for you to fill out.</p>
<pre tabindex="0"><code>$ hugo new theme zafta

$ ls -l
total 8
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 16:49 archetypes
-rw-r--r--  1 <USER>  <GROUP>   82 Sep 29 16:49 config.toml
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 16:49 content
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 16:49 layouts
drwxr-xr-x  4 <USER>  <GROUP>  136 Sep 29 17:02 public
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 16:49 static
drwxr-xr-x  3 <USER>  <GROUP>  102 Sep 29 17:31 themes

$ find themes -type f | xargs ls -l
-rw-r--r--  1 <USER>  <GROUP>  1081 Sep 29 17:31 themes/zafta/LICENSE.md
-rw-r--r--  1 <USER>  <GROUP>     0 Sep 29 17:31 themes/zafta/archetypes/default.md
-rw-r--r--  1 <USER>  <GROUP>     0 Sep 29 17:31 themes/zafta/layouts/_default/list.html
-rw-r--r--  1 <USER>  <GROUP>     0 Sep 29 17:31 themes/zafta/layouts/_default/single.html
-rw-r--r--  1 <USER>  <GROUP>     0 Sep 29 17:31 themes/zafta/layouts/index.html
-rw-r--r--  1 <USER>  <GROUP>     0 Sep 29 17:31 themes/zafta/layouts/partials/footer.html
-rw-r--r--  1 <USER>  <GROUP>     0 Sep 29 17:31 themes/zafta/layouts/partials/header.html
-rw-r--r--  1 <USER>  <GROUP>    93 Sep 29 17:31 themes/zafta/theme.toml
$ 
</code></pre><p>The skeleton includes templates (the files ending in .html), license file, a description of your theme (the theme.toml file), and an empty archetype.</p>
<p>Please take a minute to fill out the theme.toml and LICENSE.md files. They&rsquo;re optional, but if you&rsquo;re going to be distributing your theme, it tells the world who to praise (or blame). It&rsquo;s also nice to declare the license so that people will know how they can use the theme.</p>
<pre tabindex="0"><code>$ vi themes/zafta/theme.toml
author = &#34;michael d henderson&#34;
description = &#34;a minimal working template&#34;
license = &#34;MIT&#34;
name = &#34;zafta&#34;
source_repo = &#34;&#34;
tags = [&#34;tags&#34;, &#34;categories&#34;]
:wq

## also edit themes/zafta/LICENSE.md and change
## the bit that says &#34;YOUR_NAME_HERE&#34;
</code></pre><p>Note that the the skeleton&rsquo;s template files are empty. Don&rsquo;t worry, we&rsquo;ll be changing that shortly.</p>
<pre tabindex="0"><code>$ find themes/zafta -name &#39;*.html&#39; | xargs ls -l
-rw-r--r--  1 <USER>  <GROUP>  0 Sep 29 17:31 themes/zafta/layouts/_default/list.html
-rw-r--r--  1 <USER>  <GROUP>  0 Sep 29 17:31 themes/zafta/layouts/_default/single.html
-rw-r--r--  1 <USER>  <GROUP>  0 Sep 29 17:31 themes/zafta/layouts/index.html
-rw-r--r--  1 <USER>  <GROUP>  0 Sep 29 17:31 themes/zafta/layouts/partials/footer.html
-rw-r--r--  1 <USER>  <GROUP>  0 Sep 29 17:31 themes/zafta/layouts/partials/header.html
$
</code></pre><h3 id="update-the-configuration-file-to-use-the-theme">
  Update the Configuration File to Use the Theme
  <a class="anchor" href="#update-the-configuration-file-to-use-the-theme">#</a>
</h3>
<p>Now that we&rsquo;ve got a theme to work with, it&rsquo;s a good idea to add the theme name to the configuration file. This is optional, because you can always add &ldquo;-t zafta&rdquo; on all your commands. I like to put it the configuration file because I like shorter command lines. If you don&rsquo;t put it in the configuration file or specify it on the command line, you won&rsquo;t use the template that you&rsquo;re expecting to.</p>
<p>Edit the file to add the theme, add a title for the site, and specify that all of our content will use the TOML format.</p>
<pre tabindex="0"><code>$ vi config.toml
theme = &#34;zafta&#34;
baseurl = &#34;&#34;
languageCode = &#34;en-us&#34;
title = &#34;zafta - totally refreshing&#34;
MetaDataFormat = &#34;toml&#34;
:wq

$
</code></pre><h3 id="generate-the-site">
  Generate the Site
  <a class="anchor" href="#generate-the-site">#</a>
</h3>
<p>Now that we have an empty theme, let&rsquo;s generate the site again.</p>
<pre tabindex="0"><code>$ hugo --verbose
INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/themes/zafta/static/ to /Users/<USER>/Sites/zafta/public/
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/
WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html]
0 draft content 
0 future content 
0 pages created 
0 tags created
0 categories created
in 2 ms
$
</code></pre><p>Did you notice that the output is different? The warning message for the home page has disappeared and we have an additional information line saying that Hugo is syncing from the theme&rsquo;s directory.</p>
<p>Let&rsquo;s check the public/ directory to see what Hugo&rsquo;s created.</p>
<pre tabindex="0"><code>$ ls -l public
total 16
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 17:56 css
-rw-r--r--  1 <USER>  <GROUP>    0 Sep 29 17:56 index.html
-rw-r--r--  1 <USER>  <GROUP>  407 Sep 29 17:56 index.xml
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 17:56 js
-rw-r--r--  1 <USER>  <GROUP>  243 Sep 29 17:56 sitemap.xml
$
</code></pre><p>Notice four things:</p>
<ol>
<li>Hugo created a home page. This is the file public/index.html.</li>
<li>Hugo created a css/ directory.</li>
<li>Hugo created a js/ directory.</li>
<li>Hugo claimed that it created 0 pages. It created a file and copied over static files, but didn&rsquo;t create any pages. That&rsquo;s because it considers a &ldquo;page&rdquo; to be a file created directly from a content file. It doesn&rsquo;t count things like the index.html files that it creates automatically.</li>
</ol>
<h4 id="the-home-page-1">
  The Home Page
  <a class="anchor" href="#the-home-page-1">#</a>
</h4>
<p>Hugo supports many different types of templates. The home page is special because it gets its own type of template and its own template file. The file, layouts/index.html, is used to generate the HTML for the home page. The Hugo documentation says that this is the only required template, but that depends. Hugo&rsquo;s warning message shows that it looks for three different templates:</p>
<pre tabindex="0"><code>WARN: 2014/09/29 Unable to locate layout: [index.html _default/list.html _default/single.html]
</code></pre><p>If it can&rsquo;t find any of these, it completely skips creating the home page. We noticed that when we built the site without having a theme installed.</p>
<p>When Hugo created our theme, it created an empty home page template. Now, when we build the site, Hugo finds the template and uses it to generate the HTML for the home page. Since the template file is empty, the HTML file is empty, too. If the template had any rules in it, then Hugo would have used them to generate the home page.</p>
<pre tabindex="0"><code>$ find . -name index.html | xargs ls -l
-rw-r--r--  1 <USER>  <GROUP>  0 Sep 29 20:21 ./public/index.html
-rw-r--r--  1 <USER>  <GROUP>  0 Sep 29 17:31 ./themes/zafta/layouts/index.html
$ 
</code></pre><h4 id="the-magic-of-static">
  The Magic of Static
  <a class="anchor" href="#the-magic-of-static">#</a>
</h4>
<p>Hugo does two things when generating the site. It uses templates to transform content into HTML and it copies static files into the site. Unlike content, static files are not transformed. They are copied exactly as they are.</p>
<p>Hugo assumes that your site will use both CSS and JavaScript, so it creates directories in your theme to hold them. Remember opinions? Well, Hugo&rsquo;s opinion is that you&rsquo;ll store your CSS in a directory named css/ and your JavaScript in a directory named js/. If you don&rsquo;t like that, you can change the directory names in your theme directory or even delete them completely. Hugo&rsquo;s nice enough to offer its opinion, then behave nicely if you disagree.</p>
<pre tabindex="0"><code>$ find themes/zafta -type d | xargs ls -ld
drwxr-xr-x  7 <USER>  <GROUP>  238 Sep 29 17:38 themes/zafta
drwxr-xr-x  3 <USER>  <GROUP>  102 Sep 29 17:31 themes/zafta/archetypes
drwxr-xr-x  5 <USER>  <GROUP>  170 Sep 29 17:31 themes/zafta/layouts
drwxr-xr-x  4 <USER>  <GROUP>  136 Sep 29 17:31 themes/zafta/layouts/_default
drwxr-xr-x  4 <USER>  <GROUP>  136 Sep 29 17:31 themes/zafta/layouts/partials
drwxr-xr-x  4 <USER>  <GROUP>  136 Sep 29 17:31 themes/zafta/static
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 17:31 themes/zafta/static/css
drwxr-xr-x  2 <USER>  <GROUP>   68 Sep 29 17:31 themes/zafta/static/js
$ 
</code></pre><h2 id="the-theme-development-cycle">
  The Theme Development Cycle
  <a class="anchor" href="#the-theme-development-cycle">#</a>
</h2>
<p>When you&rsquo;re working on a theme, you will make changes in the theme&rsquo;s directory, rebuild the site, and check your changes in the browser. Hugo makes this very easy:</p>
<ol>
<li>Purge the public/ directory.</li>
<li>Run the built in web server in watch mode.</li>
<li>Open your site in a browser.</li>
<li>Update the theme.</li>
<li>Glance at your browser window to see changes.</li>
<li>Return to step 4.</li>
</ol>
<p>I’ll throw in one more opinion: never work on a theme on a live site. Always work on a copy of your site. Make changes to your theme, test them, then copy them up to your site. For added safety, use a tool like Git to keep a revision history of your content and your theme. Believe me when I say that it is too easy to lose both your mind and your changes.</p>
<p>Check the main Hugo site for information on using Git with Hugo.</p>
<h3 id="purge-the-public-directory">
  Purge the public/ Directory
  <a class="anchor" href="#purge-the-public-directory">#</a>
</h3>
<p>When generating the site, Hugo will create new files and update existing ones in the <code>public/</code> directory. It will not delete files that are no longer used. For example, files that were created in the wrong directory or with the wrong title will remain. If you leave them, you might get confused by them later. I recommend cleaning out your site prior to generating it.</p>
<p>Note: If you&rsquo;re building on an SSD, you should ignore this. Churning on a SSD can be costly.</p>
<h3 id="hugos-watch-option">
  Hugo&rsquo;s Watch Option
  <a class="anchor" href="#hugos-watch-option">#</a>
</h3>
<p>Hugo&rsquo;s &ldquo;<code>--watch</code>&rdquo; option will monitor the content/ and your theme directories for changes and rebuild the site automatically.</p>
<h3 id="live-reload">
  Live Reload
  <a class="anchor" href="#live-reload">#</a>
</h3>
<p>Hugo&rsquo;s built in web server supports live reload. As pages are saved on the server, the browser is told to refresh the page. Usually, this happens faster than you can say, &ldquo;Wow, that&rsquo;s totally amazing.&rdquo;</p>
<h3 id="development-commands">
  Development Commands
  <a class="anchor" href="#development-commands">#</a>
</h3>
<p>Use the following commands as the basis for your workflow.</p>
<pre tabindex="0"><code>## purge old files. hugo will recreate the public directory.
##
$ rm -rf public
##
## run hugo in watch mode
##
$ hugo server --watch --verbose
</code></pre><p>Here&rsquo;s sample output showing Hugo detecting a change to the template for the home page. Once generated, the web browser automatically reloaded the page. I&rsquo;ve said this before, it&rsquo;s amazing.</p>
<pre tabindex="0"><code>$ rm -rf public
$ hugo server --watch --verbose
INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/themes/zafta/static/ to /Users/<USER>/Sites/zafta/public/
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/
WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html]
0 draft content 
0 future content 
0 pages created 
0 tags created
0 categories created
in 2 ms
Watching for changes in /Users/<USER>/Sites/zafta/content
Serving pages from /Users/<USER>/Sites/zafta/public
Web Server is available at http://localhost:1313
Press Ctrl+C to stop
INFO: 2014/09/29 File System Event: [&#34;/Users/<USER>/Sites/zafta/themes/zafta/layouts/index.html&#34;: MODIFY|ATTRIB]
Change detected, rebuilding site

WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html]
0 draft content 
0 future content 
0 pages created 
0 tags created
0 categories created
in 1 ms
</code></pre><h2 id="update-the-home-page-template">
  Update the Home Page Template
  <a class="anchor" href="#update-the-home-page-template">#</a>
</h2>
<p>The home page is one of a few special pages that Hugo creates automatically. As mentioned earlier, it looks for one of three files in the theme&rsquo;s layout/ directory:</p>
<ol>
<li>index.html</li>
<li>_default/list.html</li>
<li>_default/single.html</li>
</ol>
<p>We could update one of the default templates, but a good design decision is to update the most specific template available. That&rsquo;s not a hard and fast rule (in fact, we&rsquo;ll break it a few times in this tutorial), but it is a good generalization.</p>
<h3 id="make-a-static-home-page">
  Make a Static Home Page
  <a class="anchor" href="#make-a-static-home-page">#</a>
</h3>
<p>Right now, that page is empty because we don&rsquo;t have any content and we don&rsquo;t have any logic in the template. Let&rsquo;s change that by adding some text to the template.</p>
<pre tabindex="0"><code>$ vi themes/zafta/layouts/index.html
&lt;!DOCTYPE html&gt; 
&lt;html&gt; 
&lt;body&gt; 
  &lt;p&gt;hugo says hello!&lt;/p&gt; 
&lt;/body&gt; 
&lt;/html&gt; 
:wq

$
</code></pre><p>Build the web site and then verify the results.</p>
<pre tabindex="0"><code>$ hugo --verbose
INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/themes/zafta/static/ to /Users/<USER>/Sites/zafta/public/
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/
WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html]
0 draft content 
0 future content 
0 pages created 
0 tags created
0 categories created
in 2 ms

$ find public -type f -name &#39;*.html&#39; | xargs ls -l
-rw-r--r--  1 <USER>  <GROUP>  78 Sep 29 21:26 public/index.html

$ cat public/index.html 
&lt;!DOCTYPE html&gt; 
&lt;html&gt; 
&lt;body&gt; 
  &lt;p&gt;hugo says hello!&lt;/p&gt; 
&lt;/html&gt;
</code></pre><h4 id="live-reload-1">
  Live Reload
  <a class="anchor" href="#live-reload-1">#</a>
</h4>
<p>Note: If you&rsquo;re running the server with the <code>--watch</code> option, you&rsquo;ll see different content in the file:</p>
<pre tabindex="0"><code>$ cat public/index.html 
&lt;!DOCTYPE html&gt; 
&lt;html&gt; 
&lt;body&gt; 
  &lt;p&gt;hugo says hello!&lt;/p&gt; 
&lt;script&gt;document.write(&#39;&lt;script src=&#34;http://&#39; 
        + (location.host || &#39;localhost&#39;).split(&#39;:&#39;)[0] 
    + &#39;:1313/livereload.js?mindelay=10&#34;&gt;&lt;/&#39; 
        + &#39;script&gt;&#39;)&lt;/script&gt;&lt;/body&gt; 
&lt;/html&gt;
</code></pre><p>When you use <code>--watch</code>, the Live Reload script is added by Hugo. Look for live reload in the documentation to see what it does and how to disable it.</p>
<h3 id="build-a-dynamic-home-page">
  Build a &ldquo;Dynamic&rdquo; Home Page
  <a class="anchor" href="#build-a-dynamic-home-page">#</a>
</h3>
<p>&ldquo;Dynamic home page?&rdquo; Hugo&rsquo;s a static web site generator, so this seems an odd thing to say. I mean let&rsquo;s have the home page automatically reflect the content in the site every time Hugo builds it. We&rsquo;ll use iteration in the template to do that.</p>
<h4 id="create-new-posts">
  Create New Posts
  <a class="anchor" href="#create-new-posts">#</a>
</h4>
<p>Now that we have the home page generating static content, let&rsquo;s add some content to the site. We&rsquo;ll display these posts as a list on the home page and on their own page, too.</p>
<p>Hugo has a command to generate a skeleton post, just like it does for sites and themes.</p>
<pre tabindex="0"><code>$ hugo --verbose new post/first.md
INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml
INFO: 2014/09/29 attempting to create  post/first.md of post
INFO: 2014/09/29 curpath: /Users/<USER>/Sites/zafta/themes/zafta/archetypes/default.md
ERROR: 2014/09/29 Unable to Cast &lt;nil&gt; to map[string]interface{}

$ 
</code></pre><p>That wasn&rsquo;t very nice, was it?</p>
<p>The &ldquo;new&rdquo; command uses an archetype to create the post file. Hugo created an empty default archetype file, but that causes an error when there&rsquo;s a theme. For me, the workaround was to create an archetypes file specifically for the post type.</p>
<pre tabindex="0"><code>$ vi themes/zafta/archetypes/post.md
+++
Description = &#34;&#34;
Tags = []
Categories = []
+++
:wq

$ find themes/zafta/archetypes -type f | xargs ls -l
-rw-r--r--  1 <USER>  <GROUP>   0 Sep 29 21:53 themes/zafta/archetypes/default.md
-rw-r--r--  1 <USER>  <GROUP>  51 Sep 29 21:54 themes/zafta/archetypes/post.md

$ hugo --verbose new post/first.md
INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml
INFO: 2014/09/29 attempting to create  post/first.md of post
INFO: 2014/09/29 curpath: /Users/<USER>/Sites/zafta/themes/zafta/archetypes/post.md
INFO: 2014/09/29 creating /Users/<USER>/Sites/zafta/content/post/first.md
/Users/<USER>/Sites/zafta/content/post/first.md created

$ hugo --verbose new post/second.md
INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml
INFO: 2014/09/29 attempting to create  post/second.md of post
INFO: 2014/09/29 curpath: /Users/<USER>/Sites/zafta/themes/zafta/archetypes/post.md
INFO: 2014/09/29 creating /Users/<USER>/Sites/zafta/content/post/second.md
/Users/<USER>/Sites/zafta/content/post/second.md created

$ ls -l content/post
total 16
-rw-r--r--  1 <USER>  <GROUP>  104 Sep 29 21:54 first.md
-rw-r--r--  1 <USER>  <GROUP>  105 Sep 29 21:57 second.md

$ cat content/post/first.md 
+++
Categories = []
Description = &#34;&#34;
Tags = []
date = &#34;2014-09-29T21:54:53-05:00&#34;
title = &#34;first&#34;

+++
my first post

$ cat content/post/second.md 
+++
Categories = []
Description = &#34;&#34;
Tags = []
date = &#34;2014-09-29T21:57:09-05:00&#34;
title = &#34;second&#34;

+++
my second post

$ 
</code></pre><p>Build the web site and then verify the results.</p>
<pre tabindex="0"><code>$ rm -rf public
$ hugo --verbose
INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/themes/zafta/static/ to /Users/<USER>/Sites/zafta/public/
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/
INFO: 2014/09/29 found taxonomies: map[string]string{&#34;category&#34;:&#34;categories&#34;, &#34;tag&#34;:&#34;tags&#34;}
WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html]
0 draft content 
0 future content 
2 pages created 
0 tags created
0 categories created
in 4 ms
$
</code></pre><p>The output says that it created 2 pages. Those are our new posts:</p>
<pre tabindex="0"><code>$ find public -type f -name &#39;*.html&#39; | xargs ls -l
-rw-r--r--  1 <USER>  <GROUP>  78 Sep 29 22:13 public/index.html
-rw-r--r--  1 <USER>  <GROUP>   0 Sep 29 22:13 public/post/first/index.html
-rw-r--r--  1 <USER>  <GROUP>   0 Sep 29 22:13 public/post/index.html
-rw-r--r--  1 <USER>  <GROUP>   0 Sep 29 22:13 public/post/second/index.html
$
</code></pre><p>The new files are empty because because the templates used to generate the content are empty. The homepage doesn&rsquo;t show the new content, either. We have to update the templates to add the posts.</p>
<h3 id="list-and-single-templates">
  List and Single Templates
  <a class="anchor" href="#list-and-single-templates">#</a>
</h3>
<p>In Hugo, we have three major kinds of templates. There&rsquo;s the home page template that we updated previously. It is used only by the home page. We also have &ldquo;single&rdquo; templates which are used to generate output for a single content file. We also have &ldquo;list&rdquo; templates that are used to group multiple pieces of content before generating output.</p>
<p>Generally speaking, list templates are named &ldquo;list.html&rdquo; and single templates are named &ldquo;single.html.&rdquo;</p>
<p>There are three other types of templates: partials, content views, and terms. We will not go into much detail on these.</p>
<h3 id="add-content-to-the-homepage">
  Add Content to the Homepage
  <a class="anchor" href="#add-content-to-the-homepage">#</a>
</h3>
<p>The home page will contain a list of posts. Let&rsquo;s update its template to add the posts that we just created. The logic in the template will run every time we build the site.</p>
<pre tabindex="0"><code>$ vi themes/zafta/layouts/index.html 
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;body&gt;
  {{ range first 10 .Data.Pages }}
    &lt;h1&gt;{{ .Title }}&lt;/h1&gt;
  {{ end }}
&lt;/body&gt;
&lt;/html&gt;
:wq

$
</code></pre><p>Hugo uses the Go template engine. That engine scans the template files for commands which are enclosed between &ldquo;{{&rdquo; and &ldquo;}}&rdquo;. In our template, the commands are:</p>
<ol>
<li>range</li>
<li>.Title</li>
<li>end</li>
</ol>
<p>The &ldquo;range&rdquo; command is an iterator. We&rsquo;re going to use it to go through the first ten pages. Every HTML file that Hugo creates is treated as a page, so looping through the list of pages will look at every file that will be created.</p>
<p>The &ldquo;.Title&rdquo; command prints the value of the &ldquo;title&rdquo; variable. Hugo pulls it from the front matter in the Markdown file.</p>
<p>The &ldquo;end&rdquo; command signals the end of the range iterator. The engine loops back to the top of the iteration when it finds &ldquo;end.&rdquo; Everything between the &ldquo;range&rdquo; and &ldquo;end&rdquo; is evaluated every time the engine goes through the iteration. In this file, that would cause the title from the first ten pages to be output as heading level one.</p>
<p>It&rsquo;s helpful to remember that some variables, like .Data, are created before any output files. Hugo loads every content file into the variable and then gives the template a chance to process before creating the HTML files.</p>
<p>Build the web site and then verify the results.</p>
<pre tabindex="0"><code>$ rm -rf public
$ hugo --verbose
INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/themes/zafta/static/ to /Users/<USER>/Sites/zafta/public/
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/
INFO: 2014/09/29 found taxonomies: map[string]string{&#34;tag&#34;:&#34;tags&#34;, &#34;category&#34;:&#34;categories&#34;}
WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html]
0 draft content 
0 future content 
2 pages created 
0 tags created
0 categories created
in 4 ms
$ find public -type f -name &#39;*.html&#39; | xargs ls -l 
-rw-r--r--  1 <USER>  <GROUP>  94 Sep 29 22:23 public/index.html
-rw-r--r--  1 <USER>  <GROUP>   0 Sep 29 22:23 public/post/first/index.html
-rw-r--r--  1 <USER>  <GROUP>   0 Sep 29 22:23 public/post/index.html
-rw-r--r--  1 <USER>  <GROUP>   0 Sep 29 22:23 public/post/second/index.html
$ cat public/index.html 
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;body&gt;
  
    &lt;h1&gt;second&lt;/h1&gt;
  
    &lt;h1&gt;first&lt;/h1&gt;
  
&lt;/body&gt;
&lt;/html&gt;
$
</code></pre><p>Congratulations, the home page shows the title of the two posts. The posts themselves are still empty, but let&rsquo;s take a moment to appreciate what we&rsquo;ve done. Your template now generates output dynamically. Believe it or not, by inserting the range command inside of those curly braces, you&rsquo;ve learned everything you need to know to build a theme. All that&rsquo;s really left is understanding which template will be used to generate each content file and becoming familiar with the commands for the template engine.</p>
<p>And, if that were entirely true, this tutorial would be much shorter. There are a few things to know that will make creating a new template much easier. Don&rsquo;t worry, though, that&rsquo;s all to come.</p>
<h3 id="add-content-to-the-posts">
  Add Content to the Posts
  <a class="anchor" href="#add-content-to-the-posts">#</a>
</h3>
<p>We&rsquo;re working with posts, which are in the content/post/ directory. That means that their section is &ldquo;post&rdquo; (and if we don&rsquo;t do something weird, their type is also &ldquo;post&rdquo;).</p>
<p>Hugo uses the section and type to find the template file for every piece of content. Hugo will first look for a template file that matches the section or type name. If it can&rsquo;t find one, then it will look in the _default/ directory. There are some twists that we&rsquo;ll cover when we get to categories and tags, but for now we can assume that Hugo will try post/single.html, then _default/single.html.</p>
<p>Now that we know the search rule, let&rsquo;s see what we actually have available:</p>
<pre tabindex="0"><code>$ find themes/zafta -name single.html | xargs ls -l
-rw-r--r--  1 <USER>  <GROUP>  132 Sep 29 17:31 themes/zafta/layouts/_default/single.html
</code></pre><p>We could create a new template, post/single.html, or change the default. Since we don&rsquo;t know of any other content types, let&rsquo;s start with updating the default.</p>
<p>Remember, any content that we haven&rsquo;t created a template for will end up using this template. That can be good or bad. Bad because I know that we&rsquo;re going to be adding different types of content and we&rsquo;re going to end up undoing some of the changes we&rsquo;ve made. It&rsquo;s good because we&rsquo;ll be able to see immediate results. It&rsquo;s also good to start here because we can start to build the basic layout for the site. As we add more content types, we&rsquo;ll refactor this file and move logic around. Hugo makes that fairly painless, so we&rsquo;ll accept the cost and proceed.</p>
<p>Please see the Hugo documentation on template rendering for all the details on determining which template to use. And, as the docs mention, if you&rsquo;re building a single page application (SPA) web site, you can delete all of the other templates and work with just the default single page. That&rsquo;s a refreshing amount of joy right there.</p>
<h4 id="update-the-template-file">
  Update the Template File
  <a class="anchor" href="#update-the-template-file">#</a>
</h4>
<pre tabindex="0"><code>$ vi themes/zafta/layouts/_default/single.html 
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;title&gt;{{ .Title }}&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
  &lt;h1&gt;{{ .Title }}&lt;/h1&gt;
  {{ .Content }}
&lt;/body&gt;
&lt;/html&gt;
:wq

$
</code></pre><p>Build the web site and verify the results.</p>
<pre tabindex="0"><code>$ rm -rf public
$ hugo --verbose
INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/themes/zafta/static/ to /Users/<USER>/Sites/zafta/public/
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/
INFO: 2014/09/29 found taxonomies: map[string]string{&#34;tag&#34;:&#34;tags&#34;, &#34;category&#34;:&#34;categories&#34;}
WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html]
0 draft content 
0 future content 
2 pages created 
0 tags created
0 categories created
in 4 ms

$ find public -type f -name &#39;*.html&#39; | xargs ls -l
-rw-r--r--  1 <USER>  <GROUP>   94 Sep 29 22:40 public/index.html
-rw-r--r--  1 <USER>  <GROUP>  125 Sep 29 22:40 public/post/first/index.html
-rw-r--r--  1 <USER>  <GROUP>    0 Sep 29 22:40 public/post/index.html
-rw-r--r--  1 <USER>  <GROUP>  128 Sep 29 22:40 public/post/second/index.html

$ cat public/post/first/index.html 
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;title&gt;first&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
  &lt;h1&gt;first&lt;/h1&gt;
  &lt;p&gt;my first post&lt;/p&gt;

&lt;/body&gt;
&lt;/html&gt;

$ cat public/post/second/index.html 
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;title&gt;second&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
  &lt;h1&gt;second&lt;/h1&gt;
  &lt;p&gt;my second post&lt;/p&gt;

&lt;/body&gt;
&lt;/html&gt;
$
</code></pre><p>Notice that the posts now have content. You can go to localhost:1313/post/first to verify.</p>
<h3 id="linking-to-content">
  Linking to Content
  <a class="anchor" href="#linking-to-content">#</a>
</h3>
<p>The posts are on the home page. Let&rsquo;s add a link from there to the post. Since this is the home page, we&rsquo;ll update its template.</p>
<pre tabindex="0"><code>$ vi themes/zafta/layouts/index.html
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;body&gt;
  {{ range first 10 .Data.Pages }}
    &lt;h1&gt;&lt;a href=&#34;{{ .Permalink }}&#34;&gt;{{ .Title }}&lt;/a&gt;&lt;/h1&gt;
  {{ end }}
&lt;/body&gt;
&lt;/html&gt;
</code></pre><p>Build the web site and verify the results.</p>
<pre tabindex="0"><code>$ rm -rf public
$ hugo --verbose
INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/themes/zafta/static/ to /Users/<USER>/Sites/zafta/public/
INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/
INFO: 2014/09/29 found taxonomies: map[string]string{&#34;tag&#34;:&#34;tags&#34;, &#34;category&#34;:&#34;categories&#34;}
WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html]
0 draft content 
0 future content 
2 pages created 
0 tags created
0 categories created
in 4 ms

$ find public -type f -name &#39;*.html&#39; | xargs ls -l
-rw-r--r--  1 <USER>  <GROUP>  149 Sep 29 22:44 public/index.html
-rw-r--r--  1 <USER>  <GROUP>  125 Sep 29 22:44 public/post/first/index.html
-rw-r--r--  1 <USER>  <GROUP>    0 Sep 29 22:44 public/post/index.html
-rw-r--r--  1 <USER>  <GROUP>  128 Sep 29 22:44 public/post/second/index.html

$ cat public/index.html 
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;body&gt;
  
    &lt;h1&gt;&lt;a href=&#34;/post/second/&#34;&gt;second&lt;/a&gt;&lt;/h1&gt;
  
    &lt;h1&gt;&lt;a href=&#34;/post/first/&#34;&gt;first&lt;/a&gt;&lt;/h1&gt;
  
&lt;/body&gt;
&lt;/html&gt;

$
</code></pre><h3 id="create-a-post-listing">
  Create a Post Listing
  <a class="anchor" href="#create-a-post-listing">#</a>
</h3>
<p>We have the posts displaying on the home page and on their own page. We also have a file public/post/index.html that is empty. Let&rsquo;s make it show a list of all posts (not just the first ten).</p>
<p>We need to decide which template to update. This will be a listing, so it should be a list template. Let&rsquo;s take a quick look and see which list templates are available.</p>
<pre tabindex="0"><code>$ find themes/zafta -name list.html | xargs ls -l
-rw-r--r--  1 <USER>  <GROUP>  0 Sep 29 17:31 themes/zafta/layouts/_default/list.html
</code></pre><p>As with the single post, we have to decide to update _default/list.html or create post/list.html. We still don&rsquo;t have multiple content types, so let&rsquo;s stay consistent and update the default list template.</p>
<h2 id="creating-top-level-pages">
  Creating Top Level Pages
  <a class="anchor" href="#creating-top-level-pages">#</a>
</h2>
<p>Let&rsquo;s add an &ldquo;about&rdquo; page and display it at the top level (as opposed to a sub-level like we did with posts).</p>
<p>The default in Hugo is to use the directory structure of the content/ directory to guide the location of the generated html in the public/ directory. Let&rsquo;s verify that by creating an &ldquo;about&rdquo; page at the top level:</p>
<pre tabindex="0"><code>$ vi content/about.md 
+++
title = &#34;about&#34;
description = &#34;about this site&#34;
date = &#34;2014-09-27&#34;
slug = &#34;about time&#34;
+++

## about us

i&#39;m speechless
:wq
</code></pre><p>Generate the web site and verify the results.</p>
<pre tabindex="0"><code>$ find public -name &#39;*.html&#39; | xargs ls -l
-rw-rw-r--  1 <USER>  <GROUP>   334 Sep 27 15:08 public/about-time/index.html
-rw-rw-r--  1 <USER>  <GROUP>   527 Sep 27 15:08 public/index.html
-rw-rw-r--  1 <USER>  <GROUP>   358 Sep 27 15:08 public/post/first-post/index.html
-rw-rw-r--  1 <USER>  <GROUP>     0 Sep 27 15:08 public/post/index.html
-rw-rw-r--  1 <USER>  <GROUP>   342 Sep 27 15:08 public/post/second-post/index.html
</code></pre><p>Notice that the page wasn&rsquo;t created at the top level. It was created in a sub-directory named &lsquo;about-time/&rsquo;. That name came from our slug. Hugo will use the slug to name the generated content. It&rsquo;s a reasonable default, by the way, but we can learn a few things by fighting it for this file.</p>
<p>One other thing. Take a look at the home page.</p>
<pre tabindex="0"><code>$ cat public/index.html
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;body&gt;
    &lt;h1&gt;&lt;a href=&#34;http://localhost:1313/post/theme/&#34;&gt;creating a new theme&lt;/a&gt;&lt;/h1&gt;
    &lt;h1&gt;&lt;a href=&#34;http://localhost:1313/about-time/&#34;&gt;about&lt;/a&gt;&lt;/h1&gt;
    &lt;h1&gt;&lt;a href=&#34;http://localhost:1313/post/second-post/&#34;&gt;second&lt;/a&gt;&lt;/h1&gt;
    &lt;h1&gt;&lt;a href=&#34;http://localhost:1313/post/first-post/&#34;&gt;first&lt;/a&gt;&lt;/h1&gt;
&lt;script&gt;document.write(&#39;&lt;script src=&#34;http://&#39;
        + (location.host || &#39;localhost&#39;).split(&#39;:&#39;)[0]
		+ &#39;:1313/livereload.js?mindelay=10&#34;&gt;&lt;/&#39;
        + &#39;script&gt;&#39;)&lt;/script&gt;&lt;/body&gt;
&lt;/html&gt;
</code></pre><p>Notice that the &ldquo;about&rdquo; link is listed with the posts? That&rsquo;s not desirable, so let&rsquo;s change that first.</p>
<pre tabindex="0"><code>$ vi themes/zafta/layouts/index.html
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;body&gt;
  &lt;h1&gt;posts&lt;/h1&gt;
  {{ range first 10 .Data.Pages }}
    {{ if eq .Type &#34;post&#34;}}
      &lt;h2&gt;&lt;a href=&#34;{{ .Permalink }}&#34;&gt;{{ .Title }}&lt;/a&gt;&lt;/h2&gt;
    {{ end }}
  {{ end }}

  &lt;h1&gt;pages&lt;/h1&gt;
  {{ range .Data.Pages }}
    {{ if eq .Type &#34;page&#34; }}
      &lt;h2&gt;&lt;a href=&#34;{{ .Permalink }}&#34;&gt;{{ .Title }}&lt;/a&gt;&lt;/h2&gt;
    {{ end }}
  {{ end }}
&lt;/body&gt;
&lt;/html&gt;
:wq
</code></pre><p>Generate the web site and verify the results. The home page has two sections, posts and pages, and each section has the right set of headings and links in it.</p>
<p>But, that about page still renders to about-time/index.html.</p>
<pre tabindex="0"><code>$ find public -name &#39;*.html&#39; | xargs ls -l
-rw-rw-r--  1 <USER>  <GROUP>    334 Sep 27 15:33 public/about-time/index.html
-rw-rw-r--  1 <USER>  <GROUP>    645 Sep 27 15:33 public/index.html
-rw-rw-r--  1 <USER>  <GROUP>    358 Sep 27 15:33 public/post/first-post/index.html
-rw-rw-r--  1 <USER>  <GROUP>      0 Sep 27 15:33 public/post/index.html
-rw-rw-r--  1 <USER>  <GROUP>    342 Sep 27 15:33 public/post/second-post/index.html
</code></pre><p>Knowing that hugo is using the slug to generate the file name, the simplest solution is to change the slug. Let&rsquo;s do it the hard way and change the permalink in the configuration file.</p>
<pre tabindex="0"><code>$ vi config.toml
[permalinks]
	page = &#34;/:title/&#34;
	about = &#34;/:filename/&#34;
</code></pre><p>Generate the web site and verify that this didn&rsquo;t work. Hugo lets &ldquo;slug&rdquo; or &ldquo;URL&rdquo; override the permalinks setting in the configuration file. Go ahead and comment out the slug in content/about.md, then generate the web site to get it to be created in the right place.</p>
<h2 id="sharing-templates">
  Sharing Templates
  <a class="anchor" href="#sharing-templates">#</a>
</h2>
<p>If you&rsquo;ve been following along, you probably noticed that posts have titles in the browser and the home page doesn&rsquo;t. That&rsquo;s because we didn&rsquo;t put the title in the home page&rsquo;s template (layouts/index.html). That&rsquo;s an easy thing to do, but let&rsquo;s look at a different option.</p>
<p>We can put the common bits into a shared template that&rsquo;s stored in the themes/zafta/layouts/partials/ directory.</p>
<h3 id="create-the-header-and-footer-partials">
  Create the Header and Footer Partials
  <a class="anchor" href="#create-the-header-and-footer-partials">#</a>
</h3>
<p>In Hugo, a partial is a sugar-coated template. Normally a template reference has a path specified. Partials are different. Hugo searches for them along a TODO defined search path. This makes it easier for end-users to override the theme&rsquo;s presentation.</p>
<pre tabindex="0"><code>$ vi themes/zafta/layouts/partials/header.html
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
	&lt;title&gt;{{ .Title }}&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
:wq

$ vi themes/zafta/layouts/partials/footer.html
&lt;/body&gt;
&lt;/html&gt;
:wq
</code></pre><h3 id="update-the-home-page-template-to-use-the-partials">
  Update the Home Page Template to Use the Partials
  <a class="anchor" href="#update-the-home-page-template-to-use-the-partials">#</a>
</h3>
<p>The most noticeable difference between a template call and a partials call is the lack of path:</p>
<pre tabindex="0"><code>{{ template &#34;theme/partials/header.html&#34; . }}
</code></pre><p>versus</p>
<pre tabindex="0"><code>{{ partial &#34;header.html&#34; . }}
</code></pre><p>Both pass in the context.</p>
<p>Let&rsquo;s change the home page template to use these new partials.</p>
<pre tabindex="0"><code>$ vi themes/zafta/layouts/index.html
{{ partial &#34;header.html&#34; . }}

  &lt;h1&gt;posts&lt;/h1&gt;
  {{ range first 10 .Data.Pages }}
    {{ if eq .Type &#34;post&#34;}}
      &lt;h2&gt;&lt;a href=&#34;{{ .Permalink }}&#34;&gt;{{ .Title }}&lt;/a&gt;&lt;/h2&gt;
    {{ end }}
  {{ end }}

  &lt;h1&gt;pages&lt;/h1&gt;
  {{ range .Data.Pages }}
    {{ if or (eq .Type &#34;page&#34;) (eq .Type &#34;about&#34;) }}
      &lt;h2&gt;&lt;a href=&#34;{{ .Permalink }}&#34;&gt;{{ .Type }} - {{ .Title }} - {{ .RelPermalink }}&lt;/a&gt;&lt;/h2&gt;
    {{ end }}
  {{ end }}

{{ partial &#34;footer.html&#34; . }}
:wq
</code></pre><p>Generate the web site and verify the results. The title on the home page is now &ldquo;your title here&rdquo;, which comes from the &ldquo;title&rdquo; variable in the config.toml file.</p>
<h3 id="update-the-default-single-template-to-use-the-partials">
  Update the Default Single Template to Use the Partials
  <a class="anchor" href="#update-the-default-single-template-to-use-the-partials">#</a>
</h3>
<pre tabindex="0"><code>$ vi themes/zafta/layouts/_default/single.html
{{ partial &#34;header.html&#34; . }}

  &lt;h1&gt;{{ .Title }}&lt;/h1&gt;
  {{ .Content }}

{{ partial &#34;footer.html&#34; . }}
:wq
</code></pre><p>Generate the web site and verify the results. The title on the posts and the about page should both reflect the value in the markdown file.</p>
<h2 id="add-date-published-to-posts">
  Add “Date Published” to Posts
  <a class="anchor" href="#add-date-published-to-posts">#</a>
</h2>
<p>It&rsquo;s common to have posts display the date that they were written or published, so let&rsquo;s add that. The front matter of our posts has a variable named &ldquo;date.&rdquo; It&rsquo;s usually the date the content was created, but let&rsquo;s pretend that&rsquo;s the value we want to display.</p>
<h3 id="add-date-published-to-the-template">
  Add “Date Published” to the Template
  <a class="anchor" href="#add-date-published-to-the-template">#</a>
</h3>
<p>We&rsquo;ll start by updating the template used to render the posts. The template code will look like:</p>
<pre tabindex="0"><code>{{ .Date.Format &#34;Mon, Jan 2, 2006&#34; }}
</code></pre><p>Posts use the default single template, so we&rsquo;ll change that file.</p>
<pre tabindex="0"><code>$ vi themes/zafta/layouts/_default/single.html
{{ partial &#34;header.html&#34; . }}

  &lt;h1&gt;{{ .Title }}&lt;/h1&gt;
  &lt;h2&gt;{{ .Date.Format &#34;Mon, Jan 2, 2006&#34; }}&lt;/h2&gt;
  {{ .Content }}

{{ partial &#34;footer.html&#34; . }}
:wq
</code></pre><p>Generate the web site and verify the results. The posts now have the date displayed in them. There&rsquo;s a problem, though. The &ldquo;about&rdquo; page also has the date displayed.</p>
<p>As usual, there are a couple of ways to make the date display only on posts. We could do an &ldquo;if&rdquo; statement like we did on the home page. Another way would be to create a separate template for posts.</p>
<p>The &ldquo;if&rdquo; solution works for sites that have just a couple of content types. It aligns with the principle of &ldquo;code for today,&rdquo; too.</p>
<p>Let&rsquo;s assume, though, that we&rsquo;ve made our site so complex that we feel we have to create a new template type. In Hugo-speak, we&rsquo;re going to create a section template.</p>
<p>Let&rsquo;s restore the default single template before we forget.</p>
<pre tabindex="0"><code>$ mkdir themes/zafta/layouts/post
$ vi themes/zafta/layouts/_default/single.html
{{ partial &#34;header.html&#34; . }}

  &lt;h1&gt;{{ .Title }}&lt;/h1&gt;
  {{ .Content }}

{{ partial &#34;footer.html&#34; . }}
:wq
</code></pre><p>Now we&rsquo;ll update the post&rsquo;s version of the single template. If you remember Hugo&rsquo;s rules, the template engine will use this version over the default.</p>
<pre tabindex="0"><code>$ vi themes/zafta/layouts/post/single.html
{{ partial &#34;header.html&#34; . }}

  &lt;h1&gt;{{ .Title }}&lt;/h1&gt;
  &lt;h2&gt;{{ .Date.Format &#34;Mon, Jan 2, 2006&#34; }}&lt;/h2&gt;
  {{ .Content }}

{{ partial &#34;footer.html&#34; . }}
:wq
</code></pre><p>Note that we removed the date logic from the default template and put it in the post template. Generate the web site and verify the results. Posts have dates and the about page doesn&rsquo;t.</p>
<h3 id="dont-repeat-yourself">
  Don&rsquo;t Repeat Yourself
  <a class="anchor" href="#dont-repeat-yourself">#</a>
</h3>
<p>DRY is a good design goal and Hugo does a great job supporting it. Part of the art of a good template is knowing when to add a new template and when to update an existing one. While you&rsquo;re figuring that out, accept that you&rsquo;ll be doing some refactoring. Hugo makes that easy and fast, so it&rsquo;s okay to delay splitting up a template.</p>
</article>
 
      

      <footer class="book-footer">
        
  <div class="flex flex-wrap justify-between" style="font-size: 80%;">

<div>LastMod: July 5, 2024<br>
      By: Dariusz Korzun<br>
      Commit: fbc640c4b8b537adf3b3431f3344ce6814d8eabf<br>
  </div>

</div>
 
        
      </footer>

      
  
  <div class="book-comments">

</div>
  
 

      <label for="menu-control" class="hidden book-menu-overlay"></label>
    </div>

    
    <aside class="book-toc">
      <div class="book-toc-content">
        
        
        
        <nav class="pepgenx-toc">
        <ol>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#introduction">Introduction</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#some-definitions">Some Definitions</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#skins">Skins</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#the-home-page">The Home Page</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#site-configuration-file">Site Configuration File</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#content">Content</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#front-matter">Front Matter</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#markdown">Markdown</a>
        </div>
          </ol>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#template-files">Template Files</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#single-template">Single Template</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#list-template">List Template</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#partial-template">Partial Template</a>
        </div>
          </ol>
        </div>
          </ol>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#create-a-new-site">Create a New Site</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#generate-the-html-for-the-new-site">Generate the HTML For the New Site</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#test-the-new-site">Test the New Site</a>
        </div>
          </ol>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#create-a-new-theme">Create a New Theme</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#create-a-skeleton">Create a Skeleton</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#update-the-configuration-file-to-use-the-theme">Update the Configuration File to Use the Theme</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#generate-the-site">Generate the Site</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#the-home-page-1">The Home Page</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#the-magic-of-static">The Magic of Static</a>
        </div>
          </ol>
        </div>
          </ol>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#the-theme-development-cycle">The Theme Development Cycle</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#purge-the-public-directory">Purge the public/ Directory</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#hugos-watch-option">Hugo&rsquo;s Watch Option</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#live-reload">Live Reload</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#development-commands">Development Commands</a>
        </div>
          </ol>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#update-the-home-page-template">Update the Home Page Template</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#make-a-static-home-page">Make a Static Home Page</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#live-reload-1">Live Reload</a>
        </div>
          </ol>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#build-a-dynamic-home-page">Build a &ldquo;Dynamic&rdquo; Home Page</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#create-new-posts">Create New Posts</a>
        </div>
          </ol>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#list-and-single-templates">List and Single Templates</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#add-content-to-the-homepage">Add Content to the Homepage</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#add-content-to-the-posts">Add Content to the Posts</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#update-the-template-file">Update the Template File</a>
        </div>
          </ol>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#linking-to-content">Linking to Content</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#create-a-post-listing">Create a Post Listing</a>
        </div>
          </ol>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#creating-top-level-pages">Creating Top Level Pages</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#sharing-templates">Sharing Templates</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#create-the-header-and-footer-partials">Create the Header and Footer Partials</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#update-the-home-page-template-to-use-the-partials">Update the Home Page Template to Use the Partials</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#update-the-default-single-template-to-use-the-partials">Update the Default Single Template to Use the Partials</a>
        </div>
          </ol>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#add-date-published-to-posts">Add “Date Published” to Posts</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#add-date-published-to-the-template">Add “Date Published” to the Template</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/creating-a-new-theme/#dont-repeat-yourself">Don&rsquo;t Repeat Yourself</a>
        </div>
          </ol>
        </div>
        </ol>
      </nav>
      </div>
    </aside>
    
  </main>

  
</body>
</html>












