<!DOCTYPE html>
<html lang="en" dir="ltr">
<head><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>
  <meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="<PERSON> uses the excellent Go html/template library for
its template engine. It is an extremely lightweight engine that provides a very
small amount of logic. In our experience that it is just the right amount of
logic to be able to create a good static website. If you have used other
template systems from different languages or frameworks you will find a lot of
similarities in Go templates.
This document is a brief primer on using Go templates.">
<meta name="theme-color" media="(prefers-color-scheme: light)" content="#ffffff">
<meta name="theme-color" media="(prefers-color-scheme: dark)" content="#343a40">
<meta name="color-scheme" content="light dark"><meta property="og:url" content="http://localhost:1313/posts/goisforlovers/">
  <meta property="og:site_name" content="PepGenX Platform">
  <meta property="og:title" content="(Hu)go Template Primer">
  <meta property="og:description" content="<PERSON> uses the excellent Go html/template library for
its template engine. It is an extremely lightweight engine that provides a very
small amount of logic. In our experience that it is just the right amount of
logic to be able to create a good static website. If you have used other
template systems from different languages or frameworks you will find a lot of
similarities in Go templates.
This document is a brief primer on using Go templates.">
  <meta property="og:locale" content="en">
  <meta property="og:type" content="article">
    <meta property="article:section" content="posts">
    <meta property="article:published_time" content="2014-04-02T00:00:00+00:00">
    <meta property="article:modified_time" content="2024-06-26T18:08:06+02:00">
    <meta property="article:tag" content="Go">
    <meta property="article:tag" content="Golang">
    <meta property="article:tag" content="Templates">
    <meta property="article:tag" content="Themes">
    <meta property="article:tag" content="Development">
<title>(Hu)go Template Primer | PepGenX Platform</title>
<link rel="manifest" href="/manifest.json">
<link rel="icon" href="/favicon.png" >
<link rel="canonical" href="http://localhost:1313/posts/goisforlovers/">
<link rel="stylesheet" href="/book.min.e004910793d7d45ce0216432001fc1dd027da4d03823d63a444453371c0a82c4.css" integrity="sha256-4ASRB5PX1FzgIWQyAB/B3QJ9pNA4I9Y6RERTNxwKgsQ=" crossorigin="anonymous">
  <script defer src="/fuse.min.js"></script>
  <script defer src="/en.search.min.aafa63659a92fa301ba9b9e6cb97780aff5e08aea524831c54000076e8cc6677.js" integrity="sha256-qvpjZZqS&#43;jAbqbnmy5d4Cv9eCK6lJIMcVAAAdujMZnc=" crossorigin="anonymous"></script>

  

<!--
Made with Book Theme
https://github.com/alex-shpak/hugo-book
-->
  
</head>
<body dir="ltr">
  <input type="checkbox" class="hidden toggle" id="menu-control" />
  <input type="checkbox" class="hidden toggle" id="toc-control" />
  <main class="container flex">
    <aside class="book-menu">
      <div class="book-menu-content">
        
  <nav>
<h2 class="book-brand">
  <a class="flex align-center" href="/"><span>PepGenX Platform</span>
  </a>
</h2>


<div class="book-search hidden">
  <input type="text" id="book-search-input" placeholder="Search" aria-label="Search" maxlength="64" data-hotkeys="s/" />
  <div class="book-search-spinner hidden"></div>
  <ul id="book-search-results"></ul>
</div>
<script>document.querySelector(".book-search").classList.remove("hidden")</script>












  



  
  <ul>
    
      
        <li class="book-section-flat" >
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenxdev/" class="">PepGenX Platform Developers</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Must Read</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/home/" class="">Prerequisites</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/kubernetes/" class="">Kubernetes</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/python/" class="">Python</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/libraries/" class="">Libraries</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/standards/" class="">Standards &amp; Patterns</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/docker/" class="">Docker Containers</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/language_model_frameworks/" class="">Language Model Frameworks</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/must_read/azure/" class="">Azure Services</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Platform components</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/platform_components/get_started/" class="">Get Started</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Orchestrator</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Traffic controller</a>
    </label>
  

          
  <ul>
    
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">API abstraction layer</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Service delivery</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Deployment controllers</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Registry &amp; Images</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">IAM</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenxdev/platform_components/security_analyzer/" class="">Security Analyzer</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Prompt Guard</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Observability</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Kiali observability</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Jaeger distributed tracing</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">EndUser Access Control</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Istio</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/platform_components/istio/configuration/" class="">Configuration</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Writing Documentation</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Markdown</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/overview/" class="">Overview</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/blockquotes/" class="">Blockquotes</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/bold/" class="">Bold</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/code/" class="">Code</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/emoji/" class="">Emoji</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/emphasis/" class="">Emphasis</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/escaping-characters/" class="">Escaping Characters</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/fenced-code-blocks/" class="">Fenced Code Blocks</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/footnotes/" class="">Footnotes</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/headings/" class="">Headings</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/horizontal-rules/" class="">Horizontal Rules</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/images/" class="">Images</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/italic/" class="">Italic</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/links/" class="">Links</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/lists/" class="">Lists</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/ordered_list/" class="">Ordered Lists</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/paragraphs/" class="">Paragraphs</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/strikethrough/" class="">Strikethrough</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/tables/" class="">Tables</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/task_list/" class="">Task List</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/markdown/unordered-lists/" class="">Unordered Lists</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">Hugo Markdown</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenxdev/shortcodes/hugo/section/" class="">Section</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/section/first-page/" class="">First Page</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/section/second-page/" class="">Second Page</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/buttons/" class="">Buttons</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/columns/" class="">Columns</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/details/" class="">Details</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/expand/" class="">Expand</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/hints/" class="">Hints</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/katex/" class="">KaTeX</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/mermaid/" class="">Mermaid Chart</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/shortcodes/hugo/tabs/" class="">Tabs</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a role="button" class="">ToDo</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenxdev/todo/korzun/" class="">Korzun</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li class="book-section-flat" >
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/" class="">Welcome to PepGenX</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/getting_started/" class="">Getting Started</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/what_is_pepgenx/" class="">What is PepGenX</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/environment/" class="">Environment</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/quick_start/" class="">Quick Start</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/getting_started/best_practicies/" class="">Best Practicies</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/concepts/" class="">Concepts</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/concepts/overview/" class="">Overview</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/concepts/architecture/" class="">Architecture</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/application_structure/" class="">Application Structure</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/operations/" class="">Operations</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/development/" class="">Development</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/deployment/" class="">Deployment</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/configuration/" class="">Configuration</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/integrations/" class="">Integrations</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/application_lifecycle/" class="">Application Lifecycle</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/operations/docker_images/" class="">Docker Images</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/" class="">Development Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/llm_frameworks/" class="">LLM Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/langchain/" class="">Langchain</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/llamaindex/" class="">Llamaindex</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/autogen/" class="">Autogen</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/haystack/" class="">Haystack</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/griptape/" class="">Griptape</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/langroid/" class="">Langroid</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/langstream/" class="">Langstream</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/agentcloud/" class="">Agentcloud</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/vercelai/" class="">Vercel AI</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/semantic_kernel/" class="">Semantic Kernel</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/crewai/" class="">Crewai</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/llm_frameworks/txtai/" class="">txtai</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/" class="">Software Development Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/python/" class="">Python</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/java/" class="">Java</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/node.js/" class="">Node.js</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/go/" class="">Go</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/ruby_on_rails/" class="">Ruby On Rails</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/php/" class="">PHP</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/software_development_frameworks/csharp/" class="">C#</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/3rd_party_frameworks/" class="">3&#39;rd Party Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/3rd_party_frameworks/rasa/" class="">RASA</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/3rd_party_frameworks/distylai/" class="">Distyl AI</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/development_frameworks/api_frameworks/" class="">REST API Frameworks</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/fastapi/" class="">FastAPI</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/springboot/" class="">SpringBoot</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/express.js/" class="">Express.js</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/gin/" class="">Gin</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/asp.netcore/" class="">ASP .Net Core</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/development_frameworks/api_frameworks/lumen/" class="">Lumen</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/pepgenx_services/" class="">PepGenX Services</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/" class="">Templates</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/prompts/" class="">Prompts</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/prompts/chains/" class="">Chains</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/prompts/trees/" class="">Trees</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/chains/" class="">Chains</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/workflows/" class="">Workflows</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/agents/" class="">Agents</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/functions/" class="">Functions</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/templates/components/" class="">Components &amp; Tools</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/configuration_tasks/" class="">Configuration Tasks</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/tutorials/" class="">Tutorials</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/reference/" class="">Reference</a>
    </label>
  

          
  <ul>
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/reference/api_overview/" class="">API Overview</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/reference/api_access_control/" class="">API Access Control</a>
  

        </li>
      
    
      
        <li>
          
  
  

  
    <a href="/docs/pepgenx/reference/security/" class="">Security</a>
  

        </li>
      
    
  </ul>

        </li>
      
    
      
        <li>
          
  
  

  
    <input type="checkbox" id="section-********************************" class="toggle"  />
    <label for="section-********************************" class="flex justify-between">
      <a href="/docs/pepgenx/frequently_asked_questions/" class="">Frequently Asked Questions</a>
    </label>
  

          
  <ul>
    
  </ul>

        </li>
      
    
  </ul>

        </li>
      
    
  </ul>











  
<ul>
  
  <li>
    <a href="/posts/"  >
        Updates
      </a>
  </li>
  
</ul>






</nav>




  <script>(function(){var e=document.querySelector("aside .book-menu-content");addEventListener("beforeunload",function(){localStorage.setItem("menu.scrollTop",e.scrollTop)}),e.scrollTop=localStorage.getItem("menu.scrollTop")})()</script>


 
      </div>
    </aside>

    <div class="book-page">
      <header class="book-header">
        
  <div class="flex align-center justify-between">
  <label for="menu-control">
    <img src="/svg/menu.svg" class="book-icon" alt="Menu" />
  </label>

  <strong>(Hu)go Template Primer</strong>

  <label for="toc-control">
    
    <img src="/svg/toc.svg" class="book-icon" alt="Table of Contents" />
    
  </label>
</div>

  
  <aside class="hidden clearfix">
    
  


<nav id="TableOfContents">
  <ul>
    <li>
      <ul>
        <li><a href="#introduction-to-go-templates">Introduction to Go Templates</a></li>
        <li><a href="#basic-syntax">Basic Syntax</a></li>
        <li><a href="#variables">Variables</a></li>
        <li><a href="#functions">Functions</a></li>
        <li><a href="#includes">Includes</a></li>
        <li><a href="#logic">Logic</a>
          <ul>
            <li><a href="#iteration">Iteration</a></li>
            <li><a href="#conditionals">Conditionals</a></li>
          </ul>
        </li>
        <li><a href="#pipes">Pipes</a></li>
        <li><a href="#context-aka-the-dot">Context (aka. the dot)</a></li>
      </ul>
    </li>
    <li><a href="#hugo-parameters">Hugo Parameters</a>
      <ul>
        <li><a href="#using-content-page-parameters">Using Content (page) Parameters</a></li>
        <li><a href="#using-site-config-parameters">Using Site (config) Parameters</a></li>
      </ul>
    </li>
  </ul>
</nav>

  </aside>
  
 
      </header>

      
      
<article class="markdown book-post">
  <h1>
    <a href="/posts/goisforlovers/">(Hu)go Template Primer</a>
  </h1>
  
  <h5>April 2, 2014</h5>



  
  <div>
    
      <a href="/categories/development/">Development</a>, 
      <a href="/categories/golang/">Golang</a>
  </div>
  

  
  <div>
    
      <a href="/tags/go/">Go</a>, 
      <a href="/tags/golang/">Golang</a>, 
      <a href="/tags/templates/">Templates</a>, 
      <a href="/tags/themes/">Themes</a>, 
      <a href="/tags/development/">Development</a>
  </div>
  



<p>Hugo uses the excellent <a href="https://golang.org/">Go</a> <a href="https://golang.org/pkg/html/template/">html/template</a> library for<br>
its template engine. It is an extremely lightweight engine that provides a very<br>
small amount of logic. In our experience that it is just the right amount of<br>
logic to be able to create a good static website. If you have used other<br>
template systems from different languages or frameworks you will find a lot of<br>
similarities in Go templates.</p>
<p>This document is a brief primer on using Go templates. The <a href="https://golang.org/pkg/html/template/">Go docs</a><br>
provide more details.</p>
<h2 id="introduction-to-go-templates">
  Introduction to Go Templates
  <a class="anchor" href="#introduction-to-go-templates">#</a>
</h2>
<p>Go templates provide an extremely simple template language. It adheres to the<br>
belief that only the most basic of logic belongs in the template or view layer.<br>
One consequence of this simplicity is that Go templates parse very quickly.</p>
<p>A unique characteristic of Go templates is they are content aware. Variables and<br>
content will be sanitized depending on the context of where they are used. More<br>
details can be found in the <a href="https://golang.org/pkg/html/template/">Go docs</a>.</p>
<h2 id="basic-syntax">
  Basic Syntax
  <a class="anchor" href="#basic-syntax">#</a>
</h2>
<p>Golang templates are HTML files with the addition of variables and<br>
functions.</p>
<p><strong>Go variables and functions are accessible within {{ }}</strong></p>
<p>Accessing a predefined variable &ldquo;foo&rdquo;:</p>
<pre><code>{{ foo }}
</code></pre>
<p><strong>Parameters are separated using spaces</strong></p>
<p>Calling the add function with input of 1, 2:</p>
<pre><code>{{ add 1 2 }}
</code></pre>
<p><strong>Methods and fields are accessed via dot notation</strong></p>
<p>Accessing the Page Parameter &ldquo;bar&rdquo;</p>
<pre><code>{{ .Params.bar }}
</code></pre>
<p><strong>Parentheses can be used to group items together</strong></p>
<pre><code>{{ if or (isset .Params &quot;alt&quot;) (isset .Params &quot;caption&quot;) }} Caption {{ end }}
</code></pre>
<h2 id="variables">
  Variables
  <a class="anchor" href="#variables">#</a>
</h2>
<p>Each Go template has a struct (object) made available to it. In hugo each<br>
template is passed either a page or a node struct depending on which type of<br>
page you are rendering. More details are available on the<br>
<a href="/layout/variables">variables</a> page.</p>
<p>A variable is accessed by referencing the variable name.</p>
<pre><code>&lt;title&gt;{{ .Title }}&lt;/title&gt;
</code></pre>
<p>Variables can also be defined and referenced.</p>
<pre><code>{{ $address := &quot;123 Main St.&quot;}}
{{ $address }}
</code></pre>
<h2 id="functions">
  Functions
  <a class="anchor" href="#functions">#</a>
</h2>
<p>Go template ship with a few functions which provide basic functionality. The Go<br>
template system also provides a mechanism for applications to extend the<br>
available functions with their own. <a href="/layout/functions">Hugo template<br>
functions</a> provide some additional functionality we believe<br>
are useful for building websites. Functions are called by using their name<br>
followed by the required parameters separated by spaces. Template<br>
functions cannot be added without recompiling hugo.</p>
<p><strong>Example:</strong></p>
<pre><code>{{ add 1 2 }}
</code></pre>
<h2 id="includes">
  Includes
  <a class="anchor" href="#includes">#</a>
</h2>
<p>When including another template you will pass to it the data it will be<br>
able to access. To pass along the current context please remember to<br>
include a trailing dot. The templates location will always be starting at<br>
the /layout/ directory within Hugo.</p>
<p><strong>Example:</strong></p>
<pre><code>{{ template &quot;chrome/header.html&quot; . }}
</code></pre>
<h2 id="logic">
  Logic
  <a class="anchor" href="#logic">#</a>
</h2>
<p>Go templates provide the most basic iteration and conditional logic.</p>
<h3 id="iteration">
  Iteration
  <a class="anchor" href="#iteration">#</a>
</h3>
<p>Just like in Go, the Go templates make heavy use of range to iterate over<br>
a map, array or slice. The following are different examples of how to use<br>
range.</p>
<p><strong>Example 1: Using Context</strong></p>
<pre><code>{{ range array }}
    {{ . }}
{{ end }}
</code></pre>
<p><strong>Example 2: Declaring value variable name</strong></p>
<pre><code>{{range $element := array}}
    {{ $element }}
{{ end }}
</code></pre>
<p><strong>Example 2: Declaring key and value variable name</strong></p>
<pre><code>{{range $index, $element := array}}
    {{ $index }}
    {{ $element }}
{{ end }}
</code></pre>
<h3 id="conditionals">
  Conditionals
  <a class="anchor" href="#conditionals">#</a>
</h3>
<p>If, else, with, or, &amp; and provide the framework for handling conditional<br>
logic in Go Templates. Like range, each statement is closed with <code>end</code>.</p>
<p>Go Templates treat the following values as false:</p>
<ul>
<li>false</li>
<li>0</li>
<li>any array, slice, map, or string of length zero</li>
</ul>
<p><strong>Example 1: If</strong></p>
<pre><code>{{ if isset .Params &quot;title&quot; }}&lt;h4&gt;{{ index .Params &quot;title&quot; }}&lt;/h4&gt;{{ end }}
</code></pre>
<p><strong>Example 2: If -&gt; Else</strong></p>
<pre><code>{{ if isset .Params &quot;alt&quot; }}
    {{ index .Params &quot;alt&quot; }}
{{else}}
    {{ index .Params &quot;caption&quot; }}
{{ end }}
</code></pre>
<p><strong>Example 3: And &amp; Or</strong></p>
<pre><code>{{ if and (or (isset .Params &quot;title&quot;) (isset .Params &quot;caption&quot;)) (isset .Params &quot;attr&quot;)}}
</code></pre>
<p><strong>Example 4: With</strong></p>
<p>An alternative way of writing &ldquo;if&rdquo; and then referencing the same value<br>
is to use &ldquo;with&rdquo; instead. With rebinds the context <code>.</code> within its scope,<br>
and skips the block if the variable is absent.</p>
<p>The first example above could be simplified as:</p>
<pre><code>{{ with .Params.title }}&lt;h4&gt;{{ . }}&lt;/h4&gt;{{ end }}
</code></pre>
<p><strong>Example 5: If -&gt; Else If</strong></p>
<pre><code>{{ if isset .Params &quot;alt&quot; }}
    {{ index .Params &quot;alt&quot; }}
{{ else if isset .Params &quot;caption&quot; }}
    {{ index .Params &quot;caption&quot; }}
{{ end }}
</code></pre>
<h2 id="pipes">
  Pipes
  <a class="anchor" href="#pipes">#</a>
</h2>
<p>One of the most powerful components of Go templates is the ability to<br>
stack actions one after another. This is done by using pipes. Borrowed<br>
from unix pipes, the concept is simple, each pipeline&rsquo;s output becomes the<br>
input of the following pipe.</p>
<p>Because of the very simple syntax of Go templates, the pipe is essential<br>
to being able to chain together function calls. One limitation of the<br>
pipes is that they only can work with a single value and that value<br>
becomes the last parameter of the next pipeline.</p>
<p>A few simple examples should help convey how to use the pipe.</p>
<p><strong>Example 1 :</strong></p>
<pre><code>{{ if eq 1 1 }} Same {{ end }}
</code></pre>
<p>is the same as</p>
<pre><code>{{ eq 1 1 | if }} Same {{ end }}
</code></pre>
<p>It does look odd to place the if at the end, but it does provide a good<br>
illustration of how to use the pipes.</p>
<p><strong>Example 2 :</strong></p>
<pre><code>{{ index .Params &quot;disqus_url&quot; | html }}
</code></pre>
<p>Access the page parameter called &ldquo;disqus_url&rdquo; and escape the HTML.</p>
<p><strong>Example 3 :</strong></p>
<pre><code>{{ if or (or (isset .Params &quot;title&quot;) (isset .Params &quot;caption&quot;)) (isset .Params &quot;attr&quot;)}}
Stuff Here
{{ end }}
</code></pre>
<p>Could be rewritten as</p>
<pre><code>{{  isset .Params &quot;caption&quot; | or isset .Params &quot;title&quot; | or isset .Params &quot;attr&quot; | if }}
Stuff Here
{{ end }}
</code></pre>
<h2 id="context-aka-the-dot">
  Context (aka. the dot)
  <a class="anchor" href="#context-aka-the-dot">#</a>
</h2>
<p>The most easily overlooked concept to understand about Go templates is that {{ . }}<br>
always refers to the current context. In the top level of your template this<br>
will be the data set made available to it. Inside of a iteration it will have<br>
the value of the current item. When inside of a loop the context has changed. .<br>
will no longer refer to the data available to the entire page. If you need to<br>
access this from within the loop you will likely want to set it to a variable<br>
instead of depending on the context.</p>
<p><strong>Example:</strong></p>
<pre><code>  {{ $title := .Site.Title }}
  {{ range .Params.tags }}
    &lt;li&gt; &lt;a href=&quot;{{ $baseurl }}/tags/{{ . | urlize }}&quot;&gt;{{ . }}&lt;/a&gt; - {{ $title }} &lt;/li&gt;
  {{ end }}
</code></pre>
<p>Notice how once we have entered the loop the value of {{ . }} has changed. We<br>
have defined a variable outside of the loop so we have access to it from within<br>
the loop.</p>
<h1 id="hugo-parameters">
  Hugo Parameters
  <a class="anchor" href="#hugo-parameters">#</a>
</h1>
<p>Hugo provides the option of passing values to the template language<br>
through the site configuration (for sitewide values), or through the meta<br>
data of each specific piece of content. You can define any values of any<br>
type (supported by your front matter/config format) and use them however<br>
you want to inside of your templates.</p>
<h2 id="using-content-page-parameters">
  Using Content (page) Parameters
  <a class="anchor" href="#using-content-page-parameters">#</a>
</h2>
<p>In each piece of content you can provide variables to be used by the<br>
templates. This happens in the <a href="/content/front-matter">front matter</a>.</p>
<p>An example of this is used in this documentation site. Most of the pages<br>
benefit from having the table of contents provided. Sometimes the TOC just<br>
doesn&rsquo;t make a lot of sense. We&rsquo;ve defined a variable in our front matter<br>
of some pages to turn off the TOC from being displayed.</p>
<p>Here is the example front matter:</p>
<pre tabindex="0"><code>---
title: &#34;Permalinks&#34;
date: &#34;2013-11-18&#34;
aliases:
  - &#34;/doc/permalinks/&#34;
groups: [&#34;extras&#34;]
groups_weight: 30
notoc: true
---
</code></pre><p>Here is the corresponding code inside of the template:</p>
<pre><code>  {{ if not .Params.notoc }}
    &lt;div id=&quot;toc&quot; class=&quot;well col-md-4 col-sm-6&quot;&gt;
    {{ .TableOfContents }}
    &lt;/div&gt;
  {{ end }}
</code></pre>
<h2 id="using-site-config-parameters">
  Using Site (config) Parameters
  <a class="anchor" href="#using-site-config-parameters">#</a>
</h2>
<p>In your top-level configuration file (eg, <code>config.yaml</code>) you can define site<br>
parameters, which are values which will be available to you in chrome.</p>
<p>For instance, you might declare:</p>
<div class="highlight"><pre tabindex="0" style="color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4;"><code class="language-yaml" data-lang="yaml"><span style="display:flex;"><span><span style="color:#f92672">params</span>:
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">CopyrightHTML</span>: <span style="color:#e6db74">&#34;Copyright &amp;#xA9; 2013 John Doe. All Rights Reserved.&#34;</span>
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">TwitterUser</span>: <span style="color:#e6db74">&#34;spf13&#34;</span>
</span></span><span style="display:flex;"><span>  <span style="color:#f92672">SidebarRecentLimit</span>: <span style="color:#ae81ff">5</span>
</span></span></code></pre></div><p>Within a footer layout, you might then declare a <code>&lt;footer&gt;</code> which is only<br>
provided if the <code>CopyrightHTML</code> parameter is provided, and if it is given,<br>
you would declare it to be HTML-safe, so that the HTML entity is not escaped<br>
again.  This would let you easily update just your top-level config file each<br>
January 1st, instead of hunting through your templates.</p>
<pre tabindex="0"><code>{{if .Site.Params.CopyrightHTML}}&lt;footer&gt;
&lt;div class=&#34;text-center&#34;&gt;{{.Site.Params.CopyrightHTML | safeHtml}}&lt;/div&gt;
&lt;/footer&gt;{{end}}
</code></pre><p>An alternative way of writing the &ldquo;if&rdquo; and then referencing the same value<br>
is to use &ldquo;with&rdquo; instead. With rebinds the context <code>.</code> within its scope,<br>
and skips the block if the variable is absent:</p>
<pre tabindex="0"><code>{{with .Site.Params.TwitterUser}}&lt;span class=&#34;twitter&#34;&gt;
&lt;a href=&#34;https://twitter.com/{{.}}&#34; rel=&#34;author&#34;&gt;
&lt;img src=&#34;/images/twitter.png&#34; width=&#34;48&#34; height=&#34;48&#34; title=&#34;Twitter: {{.}}&#34;
 alt=&#34;Twitter&#34;&gt;&lt;/a&gt;
&lt;/span&gt;{{end}}
</code></pre><p>Finally, if you want to pull &ldquo;magic constants&rdquo; out of your layouts, you can do<br>
so, such as in this example:</p>
<pre tabindex="0"><code>&lt;nav class=&#34;recent&#34;&gt;
  &lt;h1&gt;Recent Posts&lt;/h1&gt;
  &lt;ul&gt;{{range first .Site.Params.SidebarRecentLimit .Site.Recent}}
    &lt;li&gt;&lt;a href=&#34;{{.RelPermalink}}&#34;&gt;{{.Title}}&lt;/a&gt;&lt;/li&gt;
  {{end}}&lt;/ul&gt;
&lt;/nav&gt;
</code></pre></article>
 
      

      <footer class="book-footer">
        
  <div class="flex flex-wrap justify-between" style="font-size: 80%;">

<div>LastMod: June 26, 2024<br>
      By: Dariusz Korzun<br>
      Commit: 6ec1f7a66c5c001702f858f369db78c2221d2d9b<br>
  </div>

</div>
 
        
      </footer>

      
  
  <div class="book-comments">

</div>
  
 

      <label for="menu-control" class="hidden book-menu-overlay"></label>
    </div>

    
    <aside class="book-toc">
      <div class="book-toc-content">
        
        
        
        <nav class="pepgenx-toc">
        <ol>
      <div class="pepgenx-toc">
        <a href="/posts/goisforlovers/#introduction-to-go-templates">Introduction to Go Templates</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/goisforlovers/#basic-syntax">Basic Syntax</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/goisforlovers/#variables">Variables</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/goisforlovers/#functions">Functions</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/goisforlovers/#includes">Includes</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/goisforlovers/#logic">Logic</a>
          <ol>
      <div class="pepgenx-toc">
        <a href="/posts/goisforlovers/#iteration">Iteration</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/goisforlovers/#conditionals">Conditionals</a>
        </div>
          </ol>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/goisforlovers/#pipes">Pipes</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/goisforlovers/#context-aka-the-dot">Context (aka. the dot)</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/goisforlovers/#using-content-page-parameters">Using Content (page) Parameters</a>
        </div>
      <div class="pepgenx-toc">
        <a href="/posts/goisforlovers/#using-site-config-parameters">Using Site (config) Parameters</a>
        </div>
        </ol>
      </nav>
      </div>
    </aside>
    
  </main>

  
</body>
</html>












