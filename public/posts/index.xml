<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Updates on PepGenX Platform</title>
    <link>http://localhost:1313/posts/</link>
    <description>Recent content in Updates on PepGenX Platform</description>
    <generator>Hugo</generator>
    <language>en</language>
    <copyright>PepsiCo 2024</copyright>
    <atom:link href="http://localhost:1313/posts/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>Creating a New Theme</title>
      <link>http://localhost:1313/posts/creating-a-new-theme/</link>
      <pubDate>Sun, 28 Sep 2014 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/posts/creating-a-new-theme/</guid>
      <description>Introduction # This tutorial will show you how to create a simple theme in <PERSON>. I assume that you are familiar with HTM<PERSON>, the bash command line, and that you are comfortable using Markdown to format content. I&amp;rsquo;ll explain how <PERSON> uses templates and how you can organize your templates to create a theme. I won&amp;rsquo;t cover using CSS to style your theme.&#xA;We&amp;rsquo;ll start with creating a new site with a very basic template.</description>
    </item>
    <item>
      <title>Migrate to Hugo from Jekyll</title>
      <link>http://localhost:1313/posts/migrate-from-jekyll/</link>
      <pubDate>Mon, 10 Mar 2014 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/posts/migrate-from-jekyll/</guid>
      <description>Move static content to static # Jekyll has a rule that any directory not starting with _ will be copied as-is to the _site output. Hugo keeps all static content under static. You should therefore move it all there.&#xA;With Jekyll, something that looked like&#xA;▾ &amp;lt;root&amp;gt;/ ▾ images/ logo.png should become&#xA;▾ &amp;lt;root&amp;gt;/ ▾ static/ ▾ images/ logo.png Additionally, you&amp;rsquo;ll want any files that should reside at the root (such as CNAME) to be moved to static.</description>
    </item>
    <item>
      <title>(Hu)go Template Primer</title>
      <link>http://localhost:1313/posts/goisforlovers/</link>
      <pubDate>Wed, 02 Apr 2014 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/posts/goisforlovers/</guid>
      <description>Hugo uses the excellent Go html/template library for&#xA;its template engine. It is an extremely lightweight engine that provides a very&#xA;small amount of logic. In our experience that it is just the right amount of&#xA;logic to be able to create a good static website. If you have used other&#xA;template systems from different languages or frameworks you will find a lot of&#xA;similarities in Go templates.&#xA;This document is a brief primer on using Go templates.</description>
    </item>
    <item>
      <title>Getting Started with Hugo</title>
      <link>http://localhost:1313/posts/hugoisforlovers/</link>
      <pubDate>Wed, 02 Apr 2014 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/posts/hugoisforlovers/</guid>
      <description>Step 1. Install Hugo # Go to Hugo releases and download the&#xA;appropriate version for your OS and architecture.&#xA;Save it somewhere specific as we will be using it in the next step.&#xA;More complete instructions are available at Install Hugo&#xA;Step 2. Build the Docs # Hugo has its own example site which happens to also be the documentation site&#xA;you are reading right now.&#xA;Follow the following steps:</description>
    </item>
  </channel>
</rss>
