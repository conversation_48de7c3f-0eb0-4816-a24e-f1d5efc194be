<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Introduction on PepGenX Platform</title>
    <link>http://localhost:1313/</link>
    <description>Recent content in Introduction on PepGenX Platform</description>
    <generator>Hugo</generator>
    <language>en</language>
    <copyright>PepsiCo 2024</copyright>
    <atom:link href="http://localhost:1313/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>API Overview</title>
      <link>http://localhost:1313/docs/pepgenx/reference/api_overview/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/reference/api_overview/</guid>
      <description>API Overview # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Configuration</title>
      <link>http://localhost:1313/docs/pepgenxdev/platform_components/istio/configuration/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/platform_components/istio/configuration/</guid>
      <description>Enable sidecar injection # To automatically install sidecar to any new pods, you need to annotate your namespaces with the revision label corresponding to the control plane revision currently installed.&#xA;If you&amp;rsquo;re unsure which revision is installed, use:&#xA;Bash&#xA;az aks show &amp;ndash;resource-group ${RESOURCE_GROUP} &amp;ndash;name ${CLUSTER} &amp;ndash;query &amp;lsquo;serviceMeshProfile.istio.revisions&amp;rsquo;&#xA;Apply the revision label:&#xA;Bash&#xA;kubectl label namespace default istio.io/rev=asm-X-Y&#xA;Important # The default istio-injection=enabled labeling doesn&amp;rsquo;t work. Explicit versioning matching the control plane revision (ex: istio.</description>
    </item>
    <item>
      <title>Development</title>
      <link>http://localhost:1313/docs/pepgenx/operations/development/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/operations/development/</guid>
      <description>Development # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>FastAPI</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/api_frameworks/fastapi/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/api_frameworks/fastapi/</guid>
      <description>FastAPI # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Get Started</title>
      <link>http://localhost:1313/docs/pepgenxdev/platform_components/get_started/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/platform_components/get_started/</guid>
      <description>Get Started # PepGenX request parameters # External REQUEST parameters:&#xA;Parameter Description Validation Used by OKTA token authenticate a call from PepsiCo application Registered in OKTA Apigee and Istio Microservice ID authenticate PepGenX registered application Registered in PepGenX IAM Service Microservice Secret authenticate PepGenX registered application Registered in PepGenX IAM Service EndUser ID Maybe EndUser OKTA token Registered in OKTA PepGenX Service delivery Flow # flowchart TD Confidential/Restricted REQ[Request] --&gt;|API Call| API(Apigee) API --&gt; IGG{IngressGateway</description>
    </item>
    <item>
      <title>Korzun</title>
      <link>http://localhost:1313/docs/pepgenxdev/todo/korzun/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/todo/korzun/</guid>
      <description>To be done # Run web based markdown editor for developers writing documentation PepGenX Services Status page Required container interfaces and SIGTERM signals handling Lifecycle hooks AKS deisgn and configuration for different Data Classification Network Policy configuration Service Account with no access Function Container in an application POD DNS on node Topology aware routing Service internal traffic policy Istio / Kiali / Jaeger&#xA;Consumer access using Consumer pattern&#xA;Employee access</description>
    </item>
    <item>
      <title>Langchain</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/langchain/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/langchain/</guid>
      <description>Langchain # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Overview</title>
      <link>http://localhost:1313/docs/pepgenx/concepts/overview/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/concepts/overview/</guid>
      <description>Overview # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Overview</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/overview/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/overview/</guid>
      <description>Overview # Nearly all Markdown applications support the basic syntax outlined in the original Markdown design document.&#xA;Hugo uses the Goldmark Markdown processor which is fully CommonMark-compliant.&#xA;GitHub ... GitHub: yuin/goldmark&#xA;About: A markdown parser written in Go. Easy to extend, standard(CommonMark) compliant, well structured. PepGenX standard notation # Headings # First heading of a page must always start with #.&#xA;All subsequent headings at the same level as first heading should also use #.</description>
    </item>
    <item>
      <title>Prerequisites</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/home/<USER>/link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/home/<USER>/guid>
      <description>Getting Started # Before you begin working with the Platform # PepGenX Platform is a revolutionary platform designed to make building applications with generative AI and LLMs accessible to everyone. We&amp;rsquo;re building a vibrant developer community where talented individuals like yourself can contribute by coding components that will be the building blocks of the pepgenx ecosystem.&#xA;This guide provides an overview of the PepGenX Platform and its core components. Before diving into development, it&amp;rsquo;s essential to understand the fundamental concepts and technologies that underpin the framework.</description>
    </item>
    <item>
      <title>Python</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/software_development_frameworks/python/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/software_development_frameworks/python/</guid>
      <description>Python # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>RASA</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/3rd_party_frameworks/rasa/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/3rd_party_frameworks/rasa/</guid>
      <description>RASA # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Test environment</title>
      <link>http://localhost:1313/docs/pepgenxdev/platform_components/test_installation/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/platform_components/test_installation/</guid>
      <description>PAGE 378&#xA;Installation # K8S # Parameter Selection Networking kubenet Network Policy none Istio manual install Ingress and Egress Istio API Istio API (Virutal Service, Gateway) Azure Policy enabled Istio Gateway handles the L4 and L5 concerns, while VirtualService handles the L7 concerns Should we use mutual TLS for Restricted? By default, Istio allows any traffic out of the service mesh meshConfig.outboundTrafficPolicy.mode = REGISTRY_ONLY (ALLOW_ANY) requires ServiceEntry with location: MESH_EXTERNAL What response headers will be sent to end customer App istioctl x precheck istioctl install &amp;ndash;set profile=demo -y istioctl verify-install kubectl apply -f .</description>
    </item>
    <item>
      <title>Traffic controller</title>
      <link>http://localhost:1313/docs/pepgenxdev/platform_components/traffic_controller/home/<USER>/link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/platform_components/traffic_controller/home/<USER>/guid>
      <description></description>
    </item>
    <item>
      <title>What is PepGenX</title>
      <link>http://localhost:1313/docs/pepgenx/getting_started/what_is_pepgenx/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/getting_started/what_is_pepgenx/</guid>
      <description>What is PepGenX # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>API Access Control</title>
      <link>http://localhost:1313/docs/pepgenx/reference/api_access_control/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/reference/api_access_control/</guid>
      <description>API Access Control # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Architecture</title>
      <link>http://localhost:1313/docs/pepgenx/concepts/architecture/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/concepts/architecture/</guid>
      <description>Architecture # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Deployment</title>
      <link>http://localhost:1313/docs/pepgenx/operations/deployment/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/operations/deployment/</guid>
      <description>Deployment # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Distyl AI</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/3rd_party_frameworks/distylai/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/3rd_party_frameworks/distylai/</guid>
      <description>Distyl AI # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Environment</title>
      <link>http://localhost:1313/docs/pepgenx/getting_started/environment/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/getting_started/environment/</guid>
      <description>Environment # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Java</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/software_development_frameworks/java/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/software_development_frameworks/java/</guid>
      <description>Java # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Kubernetes</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/kubernetes/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/kubernetes/</guid>
      <description>Kubernetes container orchestration # Introduction # If you&amp;rsquo;re new to Kubernetes, here&amp;rsquo;s a learning path to get you started:&#xA;Fundamentals: Begin by understanding the core concepts of Kubernetes. This foundation will be crucial when you delve into more advanced topics like Admission Controllers and Operators. Kubernetes basic technical concepts Kubernetes components, functionality, their interactions, and implementation options Admission Controllers and Operators: These two concepts are the crucial components powering PepGenX platform.</description>
    </item>
    <item>
      <title>Llamaindex</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/llamaindex/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/llamaindex/</guid>
      <description>Llamaindex # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>SpringBoot</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/api_frameworks/springboot/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/api_frameworks/springboot/</guid>
      <description>SpringBoot # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Autogen</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/autogen/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/autogen/</guid>
      <description>Autogen # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Configuration</title>
      <link>http://localhost:1313/docs/pepgenx/operations/configuration/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/operations/configuration/</guid>
      <description>Configuration # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Express.js</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/api_frameworks/express.js/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/api_frameworks/express.js/</guid>
      <description>Express.js # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Node.js</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/software_development_frameworks/node.js/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/software_development_frameworks/node.js/</guid>
      <description>Node.js # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Python</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/python/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/python/</guid>
      <description> Python Programming Skills # Introduction # This section provides essential background knowledge required for coding PepGenX Platform components. A strong foundation in these Python concepts is crucial for your success as a PepGenX developer. Feel free to explore the provided resources and delve deeper into each topic to ensure you&amp;rsquo;re well-equipped to contribute to the PepGenX Platform!&#xA;Object-oriented programming (OOP) # &amp;ldquo;Python Classes and OOP&amp;rdquo; &amp;ldquo;Understanding Class and Instance Variables&amp;rdquo; &amp;ldquo;Python Object Oriented Programming (OOP) - For Beginners&amp;rdquo; &amp;ldquo;Object-Oriented Programming in Python&amp;rdquo; &amp;ldquo;Classes in Python&amp;rdquo; &amp;ldquo;Inheritance in Python&amp;rdquo; &amp;ldquo;Polymorphism in Python&amp;rdquo; Flow control # &amp;ldquo;Python Flow Control&amp;rdquo; &amp;ldquo;Conditional Statements in Python&amp;rdquo; Functions # &amp;ldquo;Defining Functions&amp;rdquo; &amp;ldquo;Defining Your Own Python Function&amp;rdquo; &amp;ldquo;Functions in Python&amp;rdquo; Data types # &amp;ldquo;Built-in Types Datatypes&amp;rdquo; &amp;ldquo;Basic Data Types in Python&amp;rdquo; Binary data # &amp;ldquo;Binary Data Services&amp;rdquo; &amp;ldquo;Creating a Binary Search in Python&amp;rdquo; Exception handling # &amp;ldquo;Errors and Exceptions&amp;rdquo; &amp;ldquo;Python Exceptions: An Introduction&amp;rdquo; &amp;ldquo;Raising and Handling Python Exceptions&amp;rdquo; JSON # &amp;ldquo;json — JSON encoder and decoder&amp;rdquo; &amp;ldquo;Working with JSON Data in Python&amp;rdquo; &amp;ldquo;JSONS Offcial&amp;rdquo; </description>
    </item>
    <item>
      <title>Quick Start</title>
      <link>http://localhost:1313/docs/pepgenx/getting_started/quick_start/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/getting_started/quick_start/</guid>
      <description>Quick Start # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Security</title>
      <link>http://localhost:1313/docs/pepgenx/reference/security/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/reference/security/</guid>
      <description>Security # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Best Practicies</title>
      <link>http://localhost:1313/docs/pepgenx/getting_started/best_practicies/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/getting_started/best_practicies/</guid>
      <description>Best Practicies # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Gin</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/api_frameworks/gin/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/api_frameworks/gin/</guid>
      <description>Gin # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Haystack</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/haystack/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/haystack/</guid>
      <description>Haystack # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Integrations</title>
      <link>http://localhost:1313/docs/pepgenx/operations/integrations/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/operations/integrations/</guid>
      <description>Integrations # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Libraries</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/libraries/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/libraries/</guid>
      <description>Python libraries # Introduction # A strong foundation in Python libraries is crucial for your success as PepGenX Platform developer. That&amp;rsquo;s why we&amp;rsquo;ve compiled a comprehensive list of essential tools along with valuable learning resources. We highly recommend familiarizing yourself with each library&amp;rsquo;s documentation and exploring the provided tutorials and video resources.&#xA;Each library listed below plays a critical role in building robust and efficient Python components for PepGenX Platform.</description>
    </item>
    <item>
      <title>Application Lifecycle</title>
      <link>http://localhost:1313/docs/pepgenx/operations/application_lifecycle/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/operations/application_lifecycle/</guid>
      <description>Application Lifecycle # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>ASP .Net Core</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/api_frameworks/asp.netcore/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/api_frameworks/asp.netcore/</guid>
      <description>ASP .Net Core # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Go</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/software_development_frameworks/go/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/software_development_frameworks/go/</guid>
      <description>Go # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Griptape</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/griptape/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/griptape/</guid>
      <description>Griptape # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Standards &amp; Patterns</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/standards/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/standards/</guid>
      <description>Standards &amp;amp; Patterns # Introduction # We&amp;rsquo;ve established a set of core Python standards and patterns that streamline development, ensure a consistent, high-quality user experience, and contribute to a well-organized and maintainable codebase. Following these guidelines is crucial for creating well-integrated code. We encourage you to explore the provided references.&#xA;RESTful API # &amp;ldquo;RESTful API Tutorial&amp;rdquo; &amp;ldquo;Python and REST APIs: Interacting With Web Services&amp;rdquo; &amp;ldquo;What is a REST API?&amp;rdquo; WebSocket API # &amp;ldquo;WebSockets handbook&amp;rdquo; &amp;ldquo;WebSockets Python library&amp;rdquo; &amp;ldquo;How To Build WebSocket Server And Client in Python&amp;rdquo; Asynchronous API Calls # &amp;ldquo;Async and Await in Python&amp;rdquo; &amp;ldquo;Asyncio Tutorial&amp;rdquo; JSON Web Token (JWT) # &amp;ldquo;JWT.</description>
    </item>
    <item>
      <title>Docker Containers</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/docker/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/docker/</guid>
      <description>Docker Containers # Introduction # This guide provides a comprehensive introduction to Docker, packed with valuable resources. These resources include both official Docker documentation and informative articles, offering a well-rounded learning experience. Our goal is to equip you with the knowledge to effectively build, deploy, and manage Docker containers for PepGenX Platform components.&#xA;Dockerfile # &amp;ldquo;Dockerfile reference&amp;rdquo; &amp;ldquo;Building best practices&amp;rdquo; Docker Images # &amp;ldquo;What is an image?&amp;rdquo; &amp;ldquo;Docker image&amp;rdquo; &amp;ldquo;What is Docker Image?</description>
    </item>
    <item>
      <title>Docker Images</title>
      <link>http://localhost:1313/docs/pepgenx/operations/docker_images/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/operations/docker_images/</guid>
      <description>Docker Images # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Langroid</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/langroid/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/langroid/</guid>
      <description>Langroid # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Lumen</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/api_frameworks/lumen/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/api_frameworks/lumen/</guid>
      <description>Lumen # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Ruby On Rails</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/software_development_frameworks/ruby_on_rails/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/software_development_frameworks/ruby_on_rails/</guid>
      <description>Ruby On Rails # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Langstream</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/langstream/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/langstream/</guid>
      <description>Langstream # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Language Model Frameworks</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/language_model_frameworks/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/language_model_frameworks/</guid>
      <description>Language Model Frameworks # Introduction # On this page, you&amp;rsquo;ll find a comprehensive overview of various Language Model Frameworks that integrate seamlessly with the PepGenX Platform. These frameworks offer a wealth of functionalities, from data management and indexing to orchestration and deployment. Dive deeper into the resources we&amp;rsquo;ve provided to learn more about each framework and explore its potential.&#xA;Llamaindex # LlamaIndex is an open-source data framework for building large language model (LLM) applications.</description>
    </item>
    <item>
      <title>PHP</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/software_development_frameworks/php/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/software_development_frameworks/php/</guid>
      <description>PHP # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Agentcloud</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/agentcloud/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/agentcloud/</guid>
      <description>Agentcloud # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Azure Services</title>
      <link>http://localhost:1313/docs/pepgenxdev/must_read/azure/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/must_read/azure/</guid>
      <description>Microsoft Azure Services # Introduction # PepGenX Platform is designed to seamlessly integrate with various Azure services, enabling developers to build robust, scalable, and secure applications. Below, you&amp;rsquo;ll find detailed insights into the core services provided by Microsoft Azure, which form the backbone of our platform infrastructure.&#xA;Azure Kubernetes Services # &amp;ldquo;Introduction to Azure Kubernetes Service&amp;rdquo; Azure Container Registries # &amp;ldquo;Introduction to Azure Container Registry&amp;rdquo; Azure Storage Account # &amp;ldquo;Introduction to Azure Storage&amp;rdquo; Azure Key Vault # &amp;ldquo;About Azure Key Vault&amp;rdquo; Azure AI Search # &amp;ldquo;What&amp;rsquo;s Azure AI Search?</description>
    </item>
    <item>
      <title>C#</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/software_development_frameworks/csharp/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/software_development_frameworks/csharp/</guid>
      <description>C# # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Vercel AI</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/vercelai/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/vercelai/</guid>
      <description>Vercel AI # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Creating a New Theme</title>
      <link>http://localhost:1313/posts/creating-a-new-theme/</link>
      <pubDate>Sun, 28 Sep 2014 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/posts/creating-a-new-theme/</guid>
      <description>Introduction # This tutorial will show you how to create a simple theme in Hugo. I assume that you are familiar with HTML, the bash command line, and that you are comfortable using Markdown to format content. I&amp;rsquo;ll explain how Hugo uses templates and how you can organize your templates to create a theme. I won&amp;rsquo;t cover using CSS to style your theme.&#xA;We&amp;rsquo;ll start with creating a new site with a very basic template.</description>
    </item>
    <item>
      <title>Migrate to Hugo from Jekyll</title>
      <link>http://localhost:1313/posts/migrate-from-jekyll/</link>
      <pubDate>Mon, 10 Mar 2014 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/posts/migrate-from-jekyll/</guid>
      <description>Move static content to static # Jekyll has a rule that any directory not starting with _ will be copied as-is to the _site output. Hugo keeps all static content under static. You should therefore move it all there.&#xA;With Jekyll, something that looked like&#xA;▾ &amp;lt;root&amp;gt;/ ▾ images/ logo.png should become&#xA;▾ &amp;lt;root&amp;gt;/ ▾ static/ ▾ images/ logo.png Additionally, you&amp;rsquo;ll want any files that should reside at the root (such as CNAME) to be moved to static.</description>
    </item>
    <item>
      <title>Semantic Kernel</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/semantic_kernel/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/semantic_kernel/</guid>
      <description>Semantic Kernel # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>Crewai</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/crewai/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/crewai/</guid>
      <description>Crewai # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>txtai</title>
      <link>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/txtai/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenx/development_frameworks/llm_frameworks/txtai/</guid>
      <description>txtai # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que&#xA;est. Pastor An arbor filia foedat, ne fugit&#xA;aliter, per. Helicona illas et&#xA;callida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite&#xA;famuli praesentem fortibus, quaeque vis foret si&#xA;frondes gelidos gravidae circumtulit inpulit armenta&#xA;nativum.&#xA;Te at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae.</description>
    </item>
    <item>
      <title>(Hu)go Template Primer</title>
      <link>http://localhost:1313/posts/goisforlovers/</link>
      <pubDate>Wed, 02 Apr 2014 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/posts/goisforlovers/</guid>
      <description>Hugo uses the excellent Go html/template library for&#xA;its template engine. It is an extremely lightweight engine that provides a very&#xA;small amount of logic. In our experience that it is just the right amount of&#xA;logic to be able to create a good static website. If you have used other&#xA;template systems from different languages or frameworks you will find a lot of&#xA;similarities in Go templates.&#xA;This document is a brief primer on using Go templates.</description>
    </item>
    <item>
      <title>Getting Started with Hugo</title>
      <link>http://localhost:1313/posts/hugoisforlovers/</link>
      <pubDate>Wed, 02 Apr 2014 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/posts/hugoisforlovers/</guid>
      <description>Step 1. Install Hugo # Go to Hugo releases and download the&#xA;appropriate version for your OS and architecture.&#xA;Save it somewhere specific as we will be using it in the next step.&#xA;More complete instructions are available at Install Hugo&#xA;Step 2. Build the Docs # Hugo has its own example site which happens to also be the documentation site&#xA;you are reading right now.&#xA;Follow the following steps:</description>
    </item>
    <item>
      <title></title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/section/first-page/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/section/first-page/</guid>
      <description>First page # Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.&#xA;Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</description>
    </item>
    <item>
      <title></title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/section/second-page/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/section/second-page/</guid>
      <description>Second Page # Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.&#xA;Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</description>
    </item>
    <item>
      <title>Blockquotes</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/blockquotes/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/blockquotes/</guid>
      <description>Blockquotes # To create a blockquote, add a &amp;gt; in front of a paragraph.&#xA;&amp;gt; Dorothy followed her through many of the beautiful rooms in her castle. The rendered output looks like this:&#xA;Dorothy followed her through many of the beautiful rooms in her castle.&#xA;Blockquotes with Multiple Paragraphs # Blockquotes can contain multiple paragraphs. Add a &amp;gt; on the blank lines between the paragraphs.&#xA;&amp;gt; Dorothy followed her through many of the beautiful rooms in her castle.</description>
    </item>
    <item>
      <title>Bold</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/bold/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/bold/</guid>
      <description>Bold # To bold text, add two asterisks or underscores before and after a word or phrase. To bold the middle of a word for emphasis, add two asterisks without spaces around the letters.&#xA;Markdown HTML Rendered Output I just love **bold text**. I just love &amp;lt;strong&amp;gt;bold text&amp;lt;/strong&amp;gt;. I just love bold text. I just love __bold text__. I just love &amp;lt;strong&amp;gt;bold text&amp;lt;/strong&amp;gt;. I just love bold text. Love**is**bold Love&amp;lt;strong&amp;gt;is&amp;lt;/strong&amp;gt;bold Loveisbold Bold Best Practices # Markdown applications don&amp;rsquo;t agree on how to handle underscores in the middle of a word.</description>
    </item>
    <item>
      <title>Buttons</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/buttons/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/buttons/</guid>
      <description> Buttons # Buttons are styled links that can lead to local page or external link.&#xA;Example # {{&amp;lt; button relref=&amp;#34;/&amp;#34; [class=&amp;#34;...&amp;#34;] &amp;gt;}}Get Home{{&amp;lt; /button &amp;gt;}} {{&amp;lt; button href=&amp;#34;https://github.com/alex-shpak/hugo-book&amp;#34; &amp;gt;}}Contribute{{&amp;lt; /button &amp;gt;}} Get Home Contribute </description>
    </item>
    <item>
      <title>Code</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/code/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/code/</guid>
      <description>Code # To denote a word or phrase as code, enclose it in backticks (`).&#xA;Markdown HTML Rendered Output At the command prompt, type `nano`. At the command prompt, type &amp;lt;code&amp;gt;nano&amp;lt;/code&amp;gt;. At the command prompt, type nano. Escaping Backticks # If the word or phrase you want to denote as code includes one or more backticks, you can escape it by enclosing the word or phrase in double backticks (``).</description>
    </item>
    <item>
      <title>Columns</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/columns/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/columns/</guid>
      <description>Columns # Columns help organize shorter pieces of content horizontally for readability.&#xA;{{&amp;lt; columns &amp;gt;}} &amp;lt;!-- begin columns block --&amp;gt; # Left Content Lorem markdownum insigne... &amp;lt;---&amp;gt; &amp;lt;!-- magic separator, between columns --&amp;gt; # Mid Content Lorem markdownum insigne... &amp;lt;---&amp;gt; &amp;lt;!-- magic separator, between columns --&amp;gt; # Right Content Lorem markdownum insigne... {{&amp;lt; /columns &amp;gt;}} Example # Left Content # Lorem markdownum insigne. Olympo signis Delphis! Retexi Nereius nova develat</description>
    </item>
    <item>
      <title>Details</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/details/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/details/</guid>
      <description>Details # Details shortcode is a helper for details html5 element. It is going to replace expand shortcode.&#xA;Example # {{&amp;lt; details &amp;#34;Title&amp;#34; [open] &amp;gt;}} ## Markdown content Lorem markdownum insigne... {{&amp;lt; /details &amp;gt;}} {{&amp;lt; details title=&amp;#34;Title&amp;#34; open=true &amp;gt;}} ## Markdown content Lorem markdownum insigne... {{&amp;lt; /details &amp;gt;}} Title Markdown content # Lorem markdownum insigne&amp;hellip;</description>
    </item>
    <item>
      <title>Emoji</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/emoji/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/emoji/</guid>
      <description>Emoji # There are two ways to add emoji to Markdown files: copy and paste the emoji into your Markdown-formatted text, or type emoji shortcodes.&#xA;Copying and Pasting Emoji # In most cases, you can simply copy an emoji from a source like Emojipedia and paste it into your document. Many Markdown applications will automatically display the emoji in the Markdown-formatted text. The HTML and PDF files you export from your Markdown application should display the emoji.</description>
    </item>
    <item>
      <title>Emphasis</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/emphasis/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/emphasis/</guid>
      <description>Bold and Italic # To emphasize text with bold and italics at the same time, add three asterisks or underscores before and after a word or phrase. To bold and italicize the middle of a word for emphasis, add three asterisks without spaces around the letters.&#xA;Markdown HTML Rendered Output This text is ***really important***. This text is &amp;lt;em&amp;gt;&amp;lt;strong&amp;gt;really important&amp;lt;/strong&amp;gt;&amp;lt;/em&amp;gt;. This text is really important. This text is ___really important___.</description>
    </item>
    <item>
      <title>Escaping Characters</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/escaping-characters/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/escaping-characters/</guid>
      <description>Escaping Characters # To display a literal character that would otherwise be used to format text in a Markdown document, add a backslash (\) in front of the character.&#xA;\* Without the backslash, this would be a bullet in an unordered list. The rendered output looks like this:&#xA;* Without the backslash, this would be a bullet in an unordered list.&#xA;Characters You Can Escape # You can use a backslash to escape the following characters.</description>
    </item>
    <item>
      <title>Expand</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/expand/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/expand/</guid>
      <description>Expand # Expand shortcode can help to decrease clutter on screen by hiding part of text. Expand content by clicking on it.&#xA;Example # Default # {{&amp;lt; expand &amp;gt;}} ## Markdown content Lorem markdownum insigne... {{&amp;lt; /expand &amp;gt;}} Expand ↕ Markdown content # Lorem markdownum insigne&amp;hellip;&#xA;With Custom Label # {{&amp;lt; expand &amp;#34;Custom Label&amp;#34; &amp;#34;...&amp;#34; &amp;gt;}} ## Markdown content Lorem markdownum insigne... {{&amp;lt; /expand &amp;gt;}} Custom Label ... Markdown content # Lorem markdownum insigne.</description>
    </item>
    <item>
      <title>Fenced Code Blocks</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/fenced-code-blocks/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/fenced-code-blocks/</guid>
      <description>Fenced Code Blocks # The basic Markdown syntax allows you to create code blocks by indenting lines by four spaces or one tab. If you find that inconvenient, try using fenced code blocks. Depending on your Markdown processor or editor, you&amp;rsquo;ll use three backticks (```) or three tildes (~~~) on the lines before and after the code block. The best part? You don&amp;rsquo;t have to indent any lines!&#xA;``` { &amp;#34;firstName&amp;#34;: &amp;#34;John&amp;#34;, &amp;#34;lastName&amp;#34;: &amp;#34;Smith&amp;#34;, &amp;#34;age&amp;#34;: 25 } ``` The rendered output looks like this:</description>
    </item>
    <item>
      <title>Footnotes</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/footnotes/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/footnotes/</guid>
      <description>Footnotes # Footnotes allow you to add notes and references without cluttering the body of the document. When you create a footnote, a superscript number with a link appears where you added the footnote reference. Readers can click the link to jump to the content of the footnote at the bottom of the page.&#xA;To create a footnote reference, add a caret and an identifier inside brackets ([^1]). Identifiers can be numbers or words, but they can&amp;rsquo;t contain spaces or tabs.</description>
    </item>
    <item>
      <title>Headings</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/headings/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/headings/</guid>
      <description>Heading # To create a heading, add number signs (#) in front of a word or phrase. The number of number signs you use should correspond to the heading level. For example, to create a heading level three (&amp;lt;h3&amp;gt;), use three number signs (e.g., ### My Header).&#xA;Markdown HTML Rendered Output # Heading level 1 &amp;lt;h1&amp;gt;Heading level 1&amp;lt;/h1&amp;gt; Heading level 1 ## Heading level 2 &amp;lt;h2&amp;gt;Heading level 2&amp;lt;/h2&amp;gt; Heading level 2 ### Heading level 3 &amp;lt;h3&amp;gt;Heading level 3&amp;lt;/h3&amp;gt; Heading level 3 #### Heading level 4 &amp;lt;h4&amp;gt;Heading level 4&amp;lt;/h4&amp;gt; Heading level 4 ##### Heading level 5 &amp;lt;h5&amp;gt;Heading level 5&amp;lt;/h5&amp;gt; Heading level 5 ###### Heading level 6 &amp;lt;h6&amp;gt;Heading level 6&amp;lt;/h6&amp;gt; Heading level 6 Alternate Syntax # Alternatively, on the line below the text, add any number of == characters for heading level 1 or -- characters for heading level 2.</description>
    </item>
    <item>
      <title>Hints</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/hints/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/hints/</guid>
      <description>Hints # Hint shortcode can be used as hint/alerts/notification block.&#xA;There are 3 colors to choose: info, warning and danger.&#xA;{{&amp;lt; hint [info|warning|danger] &amp;gt;}} **Markdown content** Lorem markdownum insigne. Olympo signis Delphis! Retexi Nereius nova develat stringit, frustra Saturnius uteroque inter! Oculis non ritibus Telethusa {{&amp;lt; /hint &amp;gt;}} Example # Markdown content&#xA;Lorem markdownum insigne. Olympo signis Delphis! Retexi Nereius nova develat&#xA;stringit, frustra Saturnius uteroque inter! Oculis non ritibus Telethusa Markdown content</description>
    </item>
    <item>
      <title>Horizontal Rules</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/horizontal-rules/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/horizontal-rules/</guid>
      <description>Horizontal Rules # To create a horizontal rule, use three or more asterisks (***), dashes (---), or underscores (___) on a line by themselves.&#xA;*** --- _________________ The rendered output of all three looks identical:&#xA;Horizontal Rule Best Practices # For compatibility, put blank lines before and after horizontal rules.&#xA;✅&amp;nbsp; Do this ❌&amp;nbsp; Don&#39;t do this Try to put a blank line before...&#xA;---&#xA;...and after a horizontal rule.</description>
    </item>
    <item>
      <title>Images</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/images/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/images/</guid>
      <description>Images # To add an image, add an exclamation mark (!), followed by alt text in brackets, and the path or URL to the image asset in parentheses. You can optionally add a title in quotation marks after the path or URL.&#xA;![The San Juan Mountains are beautiful!](/assets/images/san-juan-mountains.jpg &amp;#34;San Juan Mountains&amp;#34;) The rendered output looks like this:&#xA;{% include image.html file=&amp;quot;/assets/images/san-juan-mountains.jpg&amp;quot; alt=&amp;ldquo;The San Juan Mountains are beautiful!&amp;rdquo; title=&amp;ldquo;San Juan Mountains&amp;rdquo; lazy=&amp;ldquo;yes&amp;rdquo; %}</description>
    </item>
    <item>
      <title>Italic</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/italic/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/italic/</guid>
      <description>Italic # To italicize text, add one asterisk or underscore before and after a word or phrase. To italicize the middle of a word for emphasis, add one asterisk without spaces around the letters.&#xA;Markdown HTML Rendered Output Italicized text is the *cat&#39;s meow*. Italicized text is the &amp;lt;em&amp;gt;cat&#39;s meow&amp;lt;/em&amp;gt;. Italicized text is the cat’s meow. Italicized text is the _cat&#39;s meow_. Italicized text is the &amp;lt;em&amp;gt;cat&#39;s meow&amp;lt;/em&amp;gt;. Italicized text is the cat’s meow.</description>
    </item>
    <item>
      <title>KaTeX</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/katex/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/katex/</guid>
      <description>KaTeX # KaTeX shortcode let you render math typesetting in markdown document. See KaTeX&#xA;Example # {{&amp;lt; katex display=true class=&amp;#34;optional&amp;#34; &amp;gt;}} f(x) = \int_{-\infty}^\infty\hat f(\xi)\,e^{2 \pi i \xi x}\,d\xi {{&amp;lt; /katex &amp;gt;}} \[f(x) = \int_{-\infty}^\infty\hat f(\xi)\,e^{2 \pi i \xi x}\,d\xi\] Display Mode Example # Here is some inline example: \(\pi(x)\) , rendered in the same line. And below is display example, having display: block&#xA;\[f(x) = \int_{-\infty}^\infty\hat f(\xi)\,e^{2 \pi i \xi x}\,d\xi\] Text continues here.</description>
    </item>
    <item>
      <title>Links</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/links/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/links/</guid>
      <description>Links # To create a link, enclose the link text in brackets (e.g., [Duck Duck Go]) and then follow it immediately with the URL in parentheses (e.g., (https://duckduckgo.com)).&#xA;My favorite search engine is [Duck Duck Go](https://duckduckgo.com). The rendered output looks like this:&#xA;My favorite search engine is Duck Duck Go.&#xA;Note: To link to an element on the same page, see linking to heading IDs. To create a link that opens in a new tab or window, see the section on link targets.</description>
    </item>
    <item>
      <title>Lists</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/lists/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/lists/</guid>
      <description>Adding Elements in Lists # To add another element in a list while preserving the continuity of the list, indent the element four spaces or one tab, as shown in the following examples.&#xA;Tip: If things don&#39;t appear the way you expect, double check that you&#39;ve indented the elements in the list four spaces or one tab. Paragraphs # * This is the first list item. * Here&amp;#39;s the second list item.</description>
    </item>
    <item>
      <title>Mermaid Chart</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/mermaid/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/mermaid/</guid>
      <description>Mermaid Chart # MermaidJS is library for generating svg charts and diagrams from text.&#xA;Override Mermaid Initialization Config&#xA;To override the initialization config for Mermaid,&#xA;create a mermaid.json file in your assets folder!&#xA;Example # {{&amp;lt; mermaid class=&amp;#34;optional&amp;#34; &amp;gt;}} stateDiagram-v2 State1: The state with a note note right of State1 Important information! You can write notes. end note State1 --&amp;gt; State2 note left of State2 : This is the note to the left.</description>
    </item>
    <item>
      <title>Ordered Lists</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/ordered_list/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/ordered_list/</guid>
      <description>Ordered List # To create an ordered list, add line items with numbers followed by periods. The numbers don&amp;rsquo;t have to be in numerical order, but the list should start with the number one.&#xA;Markdown HTML Rendered Output 1. First item 2. Second item 3. Third item 4. Fourth item &amp;lt;ol&amp;gt;&#xA;&amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;First item&amp;lt;/li&amp;gt; &amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;Second item&amp;lt;/li&amp;gt; &amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;Third item&amp;lt;/li&amp;gt; &amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;Fourth item&amp;lt;/li&amp;gt; &amp;lt;/ol&amp;gt; First item Second item Third item Fourth item 1. First item 1.</description>
    </item>
    <item>
      <title>Paragraphs</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/paragraphs/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/paragraphs/</guid>
      <description>Paragraphs # To create paragraphs, use a blank line to separate one or more lines of text.&#xA;Markdown HTML Rendered Output I really like using Markdown.&#xA;I think I&#39;ll use it to format all of my documents from now on. &amp;lt;p&amp;gt;I really like using Markdown.&amp;lt;/p&amp;gt;&#xA;&amp;lt;p&amp;gt;I think I&#39;ll use it to format all of my documents from now on.&amp;lt;/p&amp;gt; I really like using Markdown.&#xA;I think I&#39;ll use it to format all of my documents from now on.</description>
    </item>
    <item>
      <title>Strikethrough</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/strikethrough/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/strikethrough/</guid>
      <description>Strikethrough # You can strikethrough words by putting a horizontal line through the center of them. The result looks like this. This feature allows you to indicate that certain words are a mistake not meant for inclusion in the document. To strikethrough words, use two tilde symbols (~~) before and after the words.&#xA;~~The world is flat.~~ We now know that the world is round. The rendered output looks like this:</description>
    </item>
    <item>
      <title>Tables</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/tables/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/tables/</guid>
      <description>Tables # To add a table, use three or more hyphens (---) to create each column&amp;rsquo;s header, and use pipes (|) to separate each column. For compatibility, you should also add a pipe on either end of the row.&#xA;| Syntax | Description | | ----------- | ----------- | | Header | Title | | Paragraph | Text | The rendered output looks like this:&#xA;Syntax Description Header Title Paragraph Text Cell widths can vary, as shown below.</description>
    </item>
    <item>
      <title>Tabs</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/tabs/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/hugo/tabs/</guid>
      <description>Tabs # Tabs let you organize content by context, for example installation instructions for each supported platform.&#xA;{{&amp;lt; tabs &amp;#34;uniqueid&amp;#34; &amp;gt;}} {{&amp;lt; tab &amp;#34;MacOS&amp;#34; &amp;gt;}} # MacOS Content {{&amp;lt; /tab &amp;gt;}} {{&amp;lt; tab &amp;#34;Linux&amp;#34; &amp;gt;}} # Linux Content {{&amp;lt; /tab &amp;gt;}} {{&amp;lt; tab &amp;#34;Windows&amp;#34; &amp;gt;}} # Windows Content {{&amp;lt; /tab &amp;gt;}} {{&amp;lt; /tabs &amp;gt;}} Example # MacOS MacOS # This is tab MacOS content.&#xA;Lorem markdownum insigne. Olympo signis Delphis!</description>
    </item>
    <item>
      <title>Task List</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/task_list/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/task_list/</guid>
      <description>Task List # Task lists (also referred to as checklists and todo lists) allow you to create a list of items with checkboxes. In Markdown applications that support task lists, checkboxes will be displayed next to the content. To create a task list, add dashes (-) and brackets with a space ([ ]) in front of task list items. To select a checkbox, add an x in between the brackets ([x]).</description>
    </item>
    <item>
      <title>Unordered Lists</title>
      <link>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/unordered-lists/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/docs/pepgenxdev/shortcodes/markdown/unordered-lists/</guid>
      <description>Unordered Lists # To create an unordered list, add dashes (-), asterisks (*), or plus signs (+) in front of line items. Indent one or more items to create a nested list.&#xA;Markdown HTML Rendered Output - First item - Second item - Third item - Fourth item &amp;lt;ul&amp;gt;&#xA;&amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;First item&amp;lt;/li&amp;gt; &amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;Second item&amp;lt;/li&amp;gt; &amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;Third item&amp;lt;/li&amp;gt; &amp;nbsp;&amp;nbsp;&amp;lt;li&amp;gt;Fourth item&amp;lt;/li&amp;gt; &amp;lt;/ul&amp;gt; First item Second item Third item Fourth item * First item * Second item</description>
    </item>
  </channel>
</rss>
