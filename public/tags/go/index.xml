<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Go on PepGenX Platform</title>
    <link>http://localhost:1313/tags/go/</link>
    <description>Recent content in Go on PepGenX Platform</description>
    <generator><PERSON></generator>
    <language>en</language>
    <copyright>PepsiCo 2024</copyright>
    <lastBuildDate>Fri, 05 Jul 2024 14:21:13 +0200</lastBuildDate>
    <atom:link href="http://localhost:1313/tags/go/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>(Hu)go Template Primer</title>
      <link>http://localhost:1313/posts/goisforlovers/</link>
      <pubDate>Wed, 02 Apr 2014 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/posts/goisforlovers/</guid>
      <description><PERSON> uses the excellent Go html/template library for&#xA;its template engine. It is an extremely lightweight engine that provides a very&#xA;small amount of logic. In our experience that it is just the right amount of&#xA;logic to be able to create a good static website. If you have used other&#xA;template systems from different languages or frameworks you will find a lot of&#xA;similarities in Go templates.&#xA;This document is a brief primer on using Go templates.</description>
    </item>
    <item>
      <title>Getting Started with Hugo</title>
      <link>http://localhost:1313/posts/hugoisforlovers/</link>
      <pubDate>Wed, 02 Apr 2014 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/posts/hugoisforlovers/</guid>
      <description>Step 1. Install Hugo # Go to Hugo releases and download the&#xA;appropriate version for your OS and architecture.&#xA;Save it somewhere specific as we will be using it in the next step.&#xA;More complete instructions are available at Install Hugo&#xA;Step 2. Build the Docs # Hugo has its own example site which happens to also be the documentation site&#xA;you are reading right now.&#xA;Follow the following steps:</description>
    </item>
  </channel>
</rss>
