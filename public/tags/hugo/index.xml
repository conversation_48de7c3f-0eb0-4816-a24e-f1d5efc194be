<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title><PERSON> on PepGenX Platform</title>
    <link>http://localhost:1313/tags/hugo/</link>
    <description>Recent content in <PERSON> on PepGenX Platform</description>
    <generator>Hugo</generator>
    <language>en</language>
    <copyright>PepsiCo 2024</copyright>
    <lastBuildDate>Fri, 05 Jul 2024 14:21:13 +0200</lastBuildDate>
    <atom:link href="http://localhost:1313/tags/hugo/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>Getting Started with <PERSON></title>
      <link>http://localhost:1313/posts/hugoisforlovers/</link>
      <pubDate>Wed, 02 Apr 2014 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/posts/hugoisforlovers/</guid>
      <description>Step 1. Install <PERSON> # Go to <PERSON> releases and download the&#xA;appropriate version for your OS and architecture.&#xA;Save it somewhere specific as we will be using it in the next step.&#xA;More complete instructions are available at Install <PERSON>&#xA;Step 2. Build the Docs # <PERSON> has its own example site which happens to also be the documentation site&#xA;you are reading right now.&#xA;Follow the following steps:</description>
    </item>
  </channel>
</rss>
