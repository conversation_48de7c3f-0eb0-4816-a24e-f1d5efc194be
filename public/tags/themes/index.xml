<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Themes on PepGenX Platform</title>
    <link>http://localhost:1313/tags/themes/</link>
    <description>Recent content in Themes on PepGenX Platform</description>
    <generator>Hugo</generator>
    <language>en</language>
    <copyright>PepsiCo 2024</copyright>
    <lastBuildDate>Wed, 26 Jun 2024 18:08:06 +0200</lastBuildDate>
    <atom:link href="http://localhost:1313/tags/themes/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>(Hu)go Template Primer</title>
      <link>http://localhost:1313/posts/goisforlovers/</link>
      <pubDate>Wed, 02 Apr 2014 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/posts/goisforlovers/</guid>
      <description><PERSON> uses the excellent Go html/template library for&#xA;its template engine. It is an extremely lightweight engine that provides a very&#xA;small amount of logic. In our experience that it is just the right amount of&#xA;logic to be able to create a good static website. If you have used other&#xA;template systems from different languages or frameworks you will find a lot of&#xA;similarities in Go templates.&#xA;This document is a brief primer on using Go templates.</description>
    </item>
  </channel>
</rss>
