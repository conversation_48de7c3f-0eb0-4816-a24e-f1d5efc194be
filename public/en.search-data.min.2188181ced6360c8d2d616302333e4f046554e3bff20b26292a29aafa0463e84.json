[{"id": 0, "href": "/docs/pepgenx/reference/api_overview/", "title": "API Overview", "section": "Reference", "content": " API Overview # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 1, "href": "/docs/pepgenx/templates/prompts/chains/", "title": "Chains", "section": "Prompts", "content": " Chains # Chain-of-Thought Chain-of-Action Recursive Chain-of-Feedback Pattern-Aware Chain-of-Thought Contrastive Chain-of-Thought Complexity-Based Prompting for Multi-Step Reasoning Chain-of-Knowledge Automatic Chain of Thought Verify-and-Edit: A Knowledge-Enhanced Chain-of-Thought Progressive-Hint Prompting Chain-of-event Chain-of-Verification Chain-of-Abstraction Reasoning Chain of Logic Chain-of-Discussion Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 2, "href": "/docs/pepgenx/operations/development/", "title": "Development", "section": "Operations", "content": " Development # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 3, "href": "/docs/pepgenx/development_frameworks/api_frameworks/fastapi/", "title": "FastAPI", "section": "REST API Frameworks", "content": " FastAPI # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 4, "href": "/docs/pepgenxdev/platform_components/get_started/", "title": "Get Started", "section": "Platform components", "content": " Get Started # Click on the image to make it bigger Required PepGenX application registration parameters:\nApplication_ID Application_Secret https://www.mermaidflow.app/editor\nsequenceDiagram autonumber participant CA as Client Application participant AG as Apigee participant OK as Okta participant IN as K8s Ingress participant IST as Istio participant TC as Traffic Controller participant CRD as Traffic Controller CRD participant JWT as JWT Auth service participant SA as Security Analyzer participant CAPI as Client Application API CA-->AG: Call Application API through Apigee AG->>+OK: Check Token OK-->>-AG: Token validated AG->>IN: Call PepGenX ingress IN->>IST: Load ballance to K8s nodes alt If Request originated FROM Apigee Note right of IST: Authorization Policy IST->>TC: Route to Traffic Controller else If Request originated NOT FROM Apigee Note left of IST: Authorization Policy IST->>+OK: Check Token OK-->>-IST: Token validated IST->>TC: Route to Traffic Controller end TC->>+CRD: Check Application_ID and Application_Secret CRD->>-TC: Return allowe PepGenX services rect RGB(245,245,245) TC->> IST: Call JWT Auth Note right of IST: Authorization Policy IST->>+JWT: Generate PepGenX session token JWT-->>-TC: Return generated session token end rect RGB(245,245,245) TC->>+IST: Call Security Analyzer Note right of IST: Authorization Policy IST->>+JWT: Check session token JWT-->>-IST: Token validated IST->>-SA: Check request content against Security Policies SA->>TC: Returns OK, Denied or Mutuated request end rect RGB(245,245,245) TC->>+IST: Call Client Application API Note right of IST: Authorization Policy IST->>+JWT: Check session token JWT-->>-IST: Token validated IST-)-CAPI: Send request to Client Application API end "}, {"id": 5, "href": "/docs/pepgenx/getting_started/", "title": "Getting Started", "section": "Welcome to PepGenX", "content": " Getting Started # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 6, "href": "/docs/pepgenxdev/todo/korzun/", "title": "<PERSON><PERSON><PERSON>", "section": "ToDo", "content": " To be done # Run web based markdown editor for developers writing documentation Required container interfaces and SIGTERM signals handling Lifecycle hooks AKS deisgn and configuration for different Data Classification Network Policy configuration Service Account with no access Function Container in an application POD DNS on node Topology aware routing Service internal traffic policy "}, {"id": 7, "href": "/docs/pepgenx/development_frameworks/llm_frameworks/langchain/", "title": "Langchain", "section": "LLM Frameworks", "content": " Langchain # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 8, "href": "/docs/pepgenx/development_frameworks/llm_frameworks/", "title": "LLM Frameworks", "section": "Development Frameworks", "content": " LLM Frameworks # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 9, "href": "/docs/pepgenx/concepts/overview/", "title": "Overview", "section": "Concepts", "content": " Overview # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 10, "href": "/docs/pepgenxdev/shortcodes/markdown/overview/", "title": "Overview", "section": "<PERSON><PERSON>", "content": " Overview # Nearly all Markdown applications support the basic syntax outlined in the original Markdown design document.\n<PERSON> uses the Goldmark Markdown processor which is fully CommonMark-compliant.\nGitHub ... GitHub: yuin/goldmark\nAbout: A markdown parser written in Go. Easy to extend, standard(CommonMark) compliant, well structured. PepGenX standard notation # Headings # First heading of a page must always start with #.\nAll subsequent headings at the same level as first heading should also use #.\nEvery heading must always be BOLD **heading**.\n# **First Heading** ## **First Subsection** # **Second Heading** Allowed maximum depth of headings is 4 ####. H4 has the same font size as the website template font size.\n"}, {"id": 11, "href": "/docs/pepgenxdev/", "title": "PepGenX Platform Developers", "section": "Docs", "content": " PepGenX Platform Development Team Site # This section of the documentation website serves as a comprehensive resource for various technical professionals involved in the PepGenX Platform.\nArchitects: Individuals responsible for designing, planning, and overseeing the overall infrastructure and technical roadmap of the PepGenX Platform. Developers: Programmers who build, test, and maintain the PepGenX Platform&rsquo;s codebase. They ensure the platform functions as intended and meets user requirements. Administrators: Associates tasked with managing user access, configurations, and system health of the PepGenX Platform. They ensure the platform runs smoothly and securely. Operators: Associates who monitor the day-to-day operations of the PepGenX Platform and its components. They identify and troubleshoot any issues that may arise to maintain platform stability and performance. "}, {"id": 12, "href": "/docs/pepgenxdev/must_read/home/", "title": "Prerequisites", "section": "Must Read", "content": " Getting Started # Before you begin working with the Platform # PepGenX Platform is a revolutionary platform designed to make building applications with generative AI and LLMs accessible to everyone. We&rsquo;re building a vibrant developer community where talented individuals like yourself can contribute by coding components that will be the building blocks of the pepgenx ecosystem.\nThis guide provides an overview of the PepGenX Platform and its core components. Before diving into development, it&rsquo;s essential to understand the fundamental concepts and technologies that underpin the framework.\nUnderstanding the Foundation # The PepGenX Platform is built upon the Kubernetes platform and its architectural model. A solid understanding of Kubernetes&rsquo; principles, component operations, and implementation will be instrumental in comprehending the architecture of both the PepGenX Platform itself and its individual components.\nPrerequisites # Kubernetes container orchestration # Familiarity with Kubernetes basic technical concepts. Understanding of Kubernetes components, functionality, their interactions, and implementation options. Knowledge of APIs and configuration management. Switch to: Kubernetes section\nPython Programming Skills # Object-oriented programming (OOP): Familiarity with core OOP concepts like classes, objects, inheritance, and polymorphism. Flow control: Ability to control the execution flow of your code using conditional statements (if/else) and loops (for/while). Functions: Defining and using functions to modularize your code and improve reusability. Data types: Understanding different data types (e.g., integers, strings, booleans) and their appropriate usage. Binary data: Understanding how to work with binary data, including byte manipulation and endianness. Exception handling: The ability to handle errors and exceptions gracefully to prevent program crashes. JSON: Knowledge of JSON format for data exchange between the framework and external systems. Switch to: Python\nPython libraries # API web framework - FastAPI: Familiarity with FastAPI for building web APIs. Kubernetes: Understanding of the Kubernetes library for managing and deploying containers. Pydantic: Proficiency with Pydantic for data validation and schema generation in data models. Request, response: Knowledge of handling HTTP requests and responses. Signal: Understanding of signal handling mechanisms for processing software interrupts. Switch to: Python libraries\nStandards &amp; Patterns # RESTful API: Familiarity with RESTful API design principles. WebSocket API: Understanding of WebSocket API for real-time, two-way communication. Asynchronous API Calls: Ability to implement asynchronous API calls for non-blocking communications. JSON Web Token (JWT): Knowledge of JSON Web Token (JWT) for secure authentication and authorization. Switch to: Standards &amp; Patterns\nDocker Containers # Dockerfile: Understanding of Dockerfile and its role in building Docker images. Docker Images: Familiarity with Docker images as templates defining container configurations. Docker Containers: Knowledge of Docker containers as isolated processes running instances of Docker images. Layers and Caching: Comprehension of Docker&rsquo;s layered approach for building images, including layer caching. Volumes and Bind Mounts: Proficiency with volumes and bind mounts for persisting data beyond container lifecycle. Container Ports: Understanding of container ports and how to expose internal container processes externally. Entrypoint: Knowledge of the entrypoint command executed when a container starts. Container Registry: Familiarity with container registries for storing and distributing Docker images. Switch to: Docker Containers\nLanguage Model Frameworks # A solid grasp of the structure and principles behind using at least one of the language model frameworks listed below. This includes knowledge of how to interact with the language model itself and utilize its supporting services.\nLlamaindex Langchain Autogen Semantic-kernel Haystack Langroid LangStream AutoGPT Griptape txtai Agentcloud OrchestrAI xlang-ai/OpenAgents Crewai Vercel AI Switch to: Language Model Frameworks\nMicrosoft Azure Services # Azure Kubernetes Services (AKS): Understanding of AKS-specific features and configurations. Azure Container Registries (ACR): Proficiency in working with ACR for storing and accessing Docker images. Azure Storage Account: Ability to access and securely use the different storage options available within Azure Storage Account. Azure Key Vault: Understanding how to integrate Key Vault with applications for secure data handling. Azure AI Search: Understanding of indexing data and implementing search functionalities within applications. Azure Content Safety Studio: Knowledge and understanding of Azure Content Safety Studio to detect and filter unsafe content. Azure AI Studio: Familiarity with Azure AI Studio for developing, training, and deploying models. Switch to: Microsoft Azure Services\nDatabases # A strong understanding of the fundamentals of different database types and their optimal application within systems, coupled with a working knowledge of utilizing the corresponding API interfaces and associated database query languages.\nRelational Databases (RDBMS): e.g. MySQL, Oracle Database, Microsoft SQL Server, PostgreSQL. NoSQL Databases: with various database structures: Key-value Stores: e.g. Redis, Memcached, Azure CosmosDB, Amazon DynamoDB. Document Stores: e.g. MongoDB, Couchbase, Azure CosmosDB, Amazon DocumentDB. Column-oriented Databases: e.g. HBase, Cassandra, ScyllaDB. Graph Databases: e.g. Neo4j, Apache Gremlin, Amazon Neptune, OrientDB. More advanced topics and in-depth explanations will be covered in subsequent documentation sections.\n"}, {"id": 13, "href": "/docs/pepgenx/templates/prompts/", "title": "Prompts", "section": "Templates", "content": " Prompts # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 14, "href": "/docs/pepgenx/development_frameworks/software_development_frameworks/python/", "title": "Python", "section": "Software Development Frameworks", "content": " Python # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 15, "href": "/docs/pepgenx/development_frameworks/3rd_party_frameworks/rasa/", "title": "RASA", "section": "3'rd Party Frameworks", "content": " RASA # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 16, "href": "/docs/pepgenx/templates/prompts/trees/", "title": "Trees", "section": "Prompts", "content": " Chains # Tree of Thoughts Tree of Uncertain Thoughts Tree of Clarifications Graph of Thoughts Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 17, "href": "/docs/pepgenx/getting_started/what_is_pepgenx/", "title": "What is PepGenX", "section": "Getting Started", "content": " What is PepGenX # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 18, "href": "/docs/pepgenx/reference/api_access_control/", "title": "API Access Control", "section": "Reference", "content": " API Access Control # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 19, "href": "/docs/pepgenx/concepts/architecture/", "title": "Architecture", "section": "Concepts", "content": " Architecture # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 20, "href": "/docs/pepgenx/templates/chains/", "title": "Chains", "section": "Templates", "content": " Chains # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 21, "href": "/docs/pepgenx/concepts/", "title": "Concepts", "section": "Welcome to PepGenX", "content": " Concepts # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 22, "href": "/docs/pepgenx/operations/deployment/", "title": "Deployment", "section": "Operations", "content": " Deployment # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 23, "href": "/docs/pepgenx/development_frameworks/3rd_party_frameworks/distylai/", "title": "Distyl AI", "section": "3'rd Party Frameworks", "content": " Distyl AI # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 24, "href": "/docs/pepgenx/getting_started/environment/", "title": "Environment", "section": "Getting Started", "content": " Environment # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 25, "href": "/docs/pepgenx/development_frameworks/software_development_frameworks/java/", "title": "Java", "section": "Software Development Frameworks", "content": " Java # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 26, "href": "/docs/pepgenxdev/must_read/kubernetes/", "title": "Kubernetes", "section": "Must Read", "content": " Kubernetes container orchestration # Introduction # If you&rsquo;re new to Kubernetes, here&rsquo;s a learning path to get you started:\nFundamentals: Begin by understanding the core concepts of Kubernetes. This foundation will be crucial when you delve into more advanced topics like Admission Controllers and Operators. Kubernetes basic technical concepts Kubernetes components, functionality, their interactions, and implementation options Admission Controllers and Operators: These two concepts are the crucial components powering PepGenX platform. Admission Controllers (Gatekeepers) intercept requests to the Kubernetes API server. They enforce predefined rules before allowing creation or modification within the cluster. Operators (Application Lifecycle) specialize in managing the complex lifecycles of applications running on Kubernetes from deployment and scaling to updates and health checks. APIs and configuration management Examples in Python Kubernetes basic technical concepts # &ldquo;Containers vs Pods&rdquo;. &ldquo;KubeAcademy&rdquo;. &ldquo;KUBE Campus&rdquo;. IBM &ldquo;Introduction to Containers w/ Docker, Kubernetes &amp; OpenShift&rdquo;. Linux Foundation &ldquo;LinuxFoundationX: Introduction to Kubernetes&rdquo;. Kubernetes components, functionality, their interactions, and implementation options # The official Kubernetes documentation. E-book &ldquo;Kubernetes from scratch&rdquo; by <PERSON><PERSON><PERSON>m. E-book &ldquo;Kubernetes Patterns&rdquo; by Bilgin Ibryam and Roland Huß. APIs and configuration management # &ldquo;Introduction to Kubernetes for Developers&rdquo;. &ldquo;Kubernetes Operators Explained&rdquo;. &ldquo;What The Heck Are Kubernetes Resources, CRs, CRDs, Operators, etc.?&rdquo;. &ldquo;Service and Network APIs (Service, Ingress, GatewayAPI)&rdquo;. &ldquo;Continuous Deployment to Kubernetes with ArgoCD&rdquo;. &ldquo;Customizing and Extending the Kubernetes API with Admission Controllers&rdquo; &ldquo;Admission Controllers Reference&rdquo; &ldquo;Custom Resources&rdquo; &ldquo;Operator pattern&rdquo; &ldquo;Kopf: Kubernetes Operators Framework&rdquo; &ldquo;Kubebuilder - SDK for building Kubernetes APIs using CRDs&rdquo; &ldquo;Kopf alternatives&rdquo; Examples in Python # Operator &ldquo;Kubernetes Operator Pythonic Framework (Kopf)&rdquo; &ldquo;Kubebuilder - SDK for building Kubernetes APIs using CRDs&rdquo; &ldquo;Trivy Operator&rdquo; Admission Controller &ldquo;Kubernetes Admission Controller Demo in Python&rdquo; &ldquo;Writing a Kubernetes Validating Admission Webhook&rdquo; "}, {"id": 27, "href": "/docs/pepgenx/development_frameworks/llm_frameworks/llamaindex/", "title": "Llamaindex", "section": "LLM Frameworks", "content": " Llamaindex # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 28, "href": "/docs/pepgenx/development_frameworks/software_development_frameworks/", "title": "Software Development Frameworks", "section": "Development Frameworks", "content": " Software Development Frameworks # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 29, "href": "/docs/pepgenx/development_frameworks/api_frameworks/springboot/", "title": "SpringBoot", "section": "REST API Frameworks", "content": " SpringBoot # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 30, "href": "/docs/pepgenx/development_frameworks/3rd_party_frameworks/", "title": "3'rd Party Frameworks", "section": "Development Frameworks", "content": " 3&rsquo;rd Party Frameworks # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 31, "href": "/docs/pepgenx/application_structure/", "title": "Application Structure", "section": "Welcome to PepGenX", "content": " Application Structure # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 32, "href": "/docs/pepgenx/development_frameworks/llm_frameworks/autogen/", "title": "Autogen", "section": "LLM Frameworks", "content": " Autogen # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 33, "href": "/docs/pepgenx/operations/configuration/", "title": "Configuration", "section": "Operations", "content": " Configuration # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 34, "href": "/docs/pepgenx/development_frameworks/api_frameworks/express.js/", "title": "Express.js", "section": "REST API Frameworks", "content": " Express.js # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 35, "href": "/docs/pepgenx/development_frameworks/software_development_frameworks/node.js/", "title": "Node.js", "section": "Software Development Frameworks", "content": " Node.js # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 36, "href": "/docs/pepgenx/operations/", "title": "Operations", "section": "Welcome to PepGenX", "content": " Operations # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 37, "href": "/docs/pepgenxdev/must_read/python/", "title": "Python", "section": "Must Read", "content": " Python Programming Skills # Introduction # This section provides essential background knowledge required for coding PepGenX Platform components. A strong foundation in these Python concepts is crucial for your success as a PepGenX developer. Feel free to explore the provided resources and delve deeper into each topic to ensure you&rsquo;re well-equipped to contribute to the PepGenX Platform!\nObject-oriented programming (OOP) # &ldquo;Python Classes and OOP&rdquo; &ldquo;Understanding Class and Instance Variables&rdquo; &ldquo;Python Object Oriented Programming (OOP) - For Beginners&rdquo; &ldquo;Object-Oriented Programming in Python&rdquo; &ldquo;Classes in Python&rdquo; &ldquo;Inheritance in Python&rdquo; &ldquo;Polymorphism in Python&rdquo; Flow control # &ldquo;Python Flow Control&rdquo; &ldquo;Conditional Statements in Python&rdquo; Functions # &ldquo;Defining Functions&rdquo; &ldquo;Defining Your Own Python Function&rdquo; &ldquo;Functions in Python&rdquo; Data types # &ldquo;Built-in Types Datatypes&rdquo; &ldquo;Basic Data Types in Python&rdquo; Binary data # &ldquo;Binary Data Services&rdquo; &ldquo;Creating a Binary Search in Python&rdquo; Exception handling # &ldquo;Errors and Exceptions&rdquo; &ldquo;Python Exceptions: An Introduction&rdquo; &ldquo;Raising and Handling Python Exceptions&rdquo; JSON # &ldquo;json — JSON encoder and decoder&rdquo; &ldquo;Working with JSON Data in Python&rdquo; &ldquo;JSONS Offcial&rdquo; "}, {"id": 38, "href": "/docs/pepgenx/getting_started/quick_start/", "title": "Quick Start", "section": "Getting Started", "content": " Quick Start # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 39, "href": "/docs/pepgenx/reference/security/", "title": "Security", "section": "Reference", "content": " Security # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 40, "href": "/docs/pepgenx/", "title": "Welcome to PepGenX", "section": "Docs", "content": " Welcome to PepGenX # The PepGenX Platform website is your one-stop shop to launch yourself into the world of building applications. We&rsquo;ve designed it with you, the end user, in mind, providing all the resources you need to get started and become a platform pro.\nGet on Board Quickly # Hit the ground running with our getting started guides. These clear, step-by-step instructions will have you set up and ready to use the platform in no time.\nDeepen Your Knowledge # Don&rsquo;t stop at the basics! Dive into our comprehensive tutorials that unpack the platform&rsquo;s features and functionalities in detail. Learn how to leverage its full potential to turn your application ideas into reality.\nSeamless Integration # Our well-structured API documentation provides all the information you need to integrate PepGenX&rsquo;s functionalities into your applications. With clear references for each API, you&rsquo;ll be able to connect everything smoothly.\nLearn from Examples # See it in action! We offer code samples and real-world examples that showcase how to effectively use the platform&rsquo;s capabilities. Get inspired and learn from practical applications.\nGot Questions? We&rsquo;ve Got Answers! # Stuck on something? No problem! Our Frequently Asked Questions (FAQs) section provides answers to common queries, saving you valuable time and effort.\nJoin the Community # Connect with a network of fellow PepGenX users in our vibrant community forum. Share knowledge, ask questions, and collaborate with others to push your development boundaries and propel your projects forward.\nAlways Up-to-Date # At PepGenX, we recognize the importance of staying informed. Our website resources are meticulously updated to ensure they reflect all recent platform modifications and additions. This guarantees you always have access to the most current and accurate information. To learn more about the latest updates, please visit the Updates section of our website.\n"}, {"id": 41, "href": "/docs/pepgenx/templates/workflows/", "title": "Workflows", "section": "Templates", "content": " Workflows # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 42, "href": "/docs/pepgenx/templates/agents/", "title": "Agents", "section": "Templates", "content": " Agents # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 43, "href": "/docs/pepgenx/getting_started/best_practicies/", "title": "Best Practicies", "section": "Getting Started", "content": " Best Practicies # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 44, "href": "/docs/pepgenx/development_frameworks/", "title": "Development Frameworks", "section": "Welcome to PepGenX", "content": " Development Frameworks # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 45, "href": "/docs/pepgenx/development_frameworks/api_frameworks/gin/", "title": "Gin", "section": "REST API Frameworks", "content": " Gin # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 46, "href": "/docs/pepgenx/development_frameworks/llm_frameworks/haystack/", "title": "Haystack", "section": "LLM Frameworks", "content": " Haystack # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 47, "href": "/docs/pepgenx/operations/integrations/", "title": "Integrations", "section": "Operations", "content": " Integrations # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 48, "href": "/docs/pepgenxdev/must_read/libraries/", "title": "Libraries", "section": "Must Read", "content": " Python libraries # Introduction # A strong foundation in Python libraries is crucial for your success as PepGenX Platform developer. That&rsquo;s why we&rsquo;ve compiled a comprehensive list of essential tools along with valuable learning resources. We highly recommend familiarizing yourself with each library&rsquo;s documentation and exploring the provided tutorials and video resources.\nEach library listed below plays a critical role in building robust and efficient Python components for PepGenX Platform.\nFastAPI # Official Documentation: &ldquo;FastAPI Documentation&rdquo; Online Book: &ldquo;FastAPI Handbook – How to Develop, Test, and Deploy APIs&rdquo; YouTube: &ldquo;4 Tips for Building a Production-Ready FastAPI Backend&rdquo; Kubernetes # Official Documentation: &ldquo;API Overview&rdquo; Kubernetes Client Official Python library: &ldquo;Python&rdquo; Pydantic # Official Documentation: &ldquo;Pydantic Documentation&rdquo; YouTube: &ldquo;Pydantic V2 - Full Course - Learn the BEST Library for Data Validation and Parsing&rdquo; YouTube: &ldquo;Why You Should Use Pydantic in 2024 | Tutorial&rdquo; Request, Response # Python Requests Library Documentation: &ldquo;Requests: HTTP for Humans&rdquo; YouTube: &ldquo;Requests Library in Python - Beginner Crash Course&rdquo; Signal # Python Signal Module Documentation: &ldquo;signal - Set handlers for asynchronous events&rdquo; Tutorial: &ldquo;Handling Signals with Python&rdquo; YouTube: &ldquo;Processing &amp; Handling Signals in Python&rdquo; "}, {"id": 49, "href": "/docs/pepgenx/development_frameworks/api_frameworks/", "title": "REST API Frameworks", "section": "Development Frameworks", "content": " REST API Frameworks # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 50, "href": "/docs/pepgenx/operations/application_lifecycle/", "title": "Application Lifecycle", "section": "Operations", "content": " Application Lifecycle # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 51, "href": "/docs/pepgenx/development_frameworks/api_frameworks/asp.netcore/", "title": "ASP .Net Core", "section": "REST API Frameworks", "content": " ASP .Net Core # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 52, "href": "/docs/pepgenx/templates/functions/", "title": "Functions", "section": "Templates", "content": " Functions # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 53, "href": "/docs/pepgenx/development_frameworks/software_development_frameworks/go/", "title": "Go", "section": "Software Development Frameworks", "content": " Go # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 54, "href": "/docs/pepgenx/development_frameworks/llm_frameworks/griptape/", "title": "G<PERSON>tape", "section": "LLM Frameworks", "content": " Griptape # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 55, "href": "/docs/pepgenx/pepgenx_services/", "title": "PepGenX Services", "section": "Welcome to PepGenX", "content": " PepGenX Services # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 56, "href": "/docs/pepgenxdev/must_read/standards/", "title": "Standards & Patterns", "section": "Must Read", "content": " Standards &amp; Patterns # Introduction # We&rsquo;ve established a set of core Python standards and patterns that streamline development, ensure a consistent, high-quality user experience, and contribute to a well-organized and maintainable codebase. Following these guidelines is crucial for creating well-integrated code. We encourage you to explore the provided references.\nRESTful API # &ldquo;RESTful API Tutorial&rdquo; &ldquo;Python and REST APIs: Interacting With Web Services&rdquo; &ldquo;What is a REST API?&rdquo; WebSocket API # &ldquo;WebSockets handbook&rdquo; &ldquo;WebSockets Python library&rdquo; &ldquo;How To Build WebSocket Server And Client in Python&rdquo; Asynchronous API Calls # &ldquo;Async and Await in Python&rdquo; &ldquo;Asyncio Tutorial&rdquo; JSON Web Token (JWT) # &ldquo;JWT.io&rdquo; &ldquo;PyJWT Documentation&rdquo; &ldquo;What Is JWT and Why Should You Use JWT&rdquo; "}, {"id": 57, "href": "/docs/pepgenx/templates/components/", "title": "Components & Tools", "section": "Templates", "content": " Components &amp; Tools # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 58, "href": "/docs/pepgenxdev/must_read/docker/", "title": "Docker Containers", "section": "Must Read", "content": " Docker Containers # Introduction # This guide provides a comprehensive introduction to Docker, packed with valuable resources. These resources include both official Docker documentation and informative articles, offering a well-rounded learning experience. Our goal is to equip you with the knowledge to effectively build, deploy, and manage Docker containers for PepGenX Platform components.\nDockerfile # &ldquo;Dockerfile reference&rdquo; &ldquo;Building best practices&rdquo; Docker Images # &ldquo;What is an image?&rdquo; &ldquo;Docker image&rdquo; &ldquo;What is Docker Image?&rdquo; &ldquo;Docker Images&rdquo; Docker Containers # &ldquo;What is a container (Docker)&rdquo; &ldquo;What is a container (Aquasec)&rdquo; Layers and Caching # &ldquo;Using the build cache&rdquo; &ldquo;Docker Cache – How to Do a Clean Image Rebuild and Clear Docker&rsquo;s Cache&rdquo; Volumes and Bind Mounts # &ldquo;Manage data in Docker&rdquo; &ldquo;Persisting container data&rdquo; Container Ports # &ldquo;Networking overview&rdquo; &ldquo;Publishing and exposing ports&rdquo; Entrypoint # &ldquo;How to Use Docker EntryPoint&rdquo; &ldquo;ENTRYPOINT&rdquo; Container Registry # &ldquo;What is a registry?&rdquo; &ldquo;About registries, repositories, and artifacts&rdquo; "}, {"id": 59, "href": "/docs/pepgenx/operations/docker_images/", "title": "Docker Images", "section": "Operations", "content": " Docker Images # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 60, "href": "/docs/pepgenx/development_frameworks/llm_frameworks/langroid/", "title": "<PERSON><PERSON>", "section": "LLM Frameworks", "content": " Langroid # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 61, "href": "/docs/pepgenx/development_frameworks/api_frameworks/lumen/", "title": "<PERSON><PERSON>", "section": "REST API Frameworks", "content": " Lumen # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 62, "href": "/docs/pepgenx/development_frameworks/software_development_frameworks/ruby_on_rails/", "title": "Ruby On Rails", "section": "Software Development Frameworks", "content": " Ruby On Rails # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 63, "href": "/docs/pepgenx/templates/", "title": "Templates", "section": "Welcome to PepGenX", "content": " Templates # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 64, "href": "/docs/pepgenx/configuration_tasks/", "title": "Configuration Tasks", "section": "Welcome to PepGenX", "content": " Configuration Tasks # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 65, "href": "/docs/pepgenx/development_frameworks/llm_frameworks/langstream/", "title": "Langstream", "section": "LLM Frameworks", "content": " Langstream # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 66, "href": "/docs/pepgenxdev/must_read/language_model_frameworks/", "title": "Language Model Frameworks", "section": "Must Read", "content": " Language Model Frameworks # Introduction # On this page, you&rsquo;ll find a comprehensive overview of various Language Model Frameworks that integrate seamlessly with the PepGenX Platform. These frameworks offer a wealth of functionalities, from data management and indexing to orchestration and deployment. Dive deeper into the resources we&rsquo;ve provided to learn more about each framework and explore its potential.\nLlamaindex # LlamaIndex is an open-source data framework for building large language model (LLM) applications. It allows developers to turn enterprise data into production-ready LLM applications. LlamaIndex provides tools for various stages of the LLM application development lifecycle, including data loading, indexing, querying, and evaluation. Additionally, LlamaIndex offers a rich set of community-contributed resources, including connectors, tools, and datasets. Developers can also integrate LlamaIndex with various vector stores, large language models, and data sources.\nLearn more about Llamaindex\nLangchain # LangChain is a suite of products that supports developers throughout the entire lifecycle of LLM application development. LangChain provides a framework to build applications with LLMs. It includes LangSmith, a tool to debug, collaborate, test, and monitor LLM apps. Additionally, LangChain offers LangGraph, a framework for building controllable agentic workflows. With LangChain, developers can build context-aware, reasoning applications that leverage their company’s data and APIs. LangChain promotes vendor optionality by allowing developers to choose the best tools for the job. Overall, LangChain empowers developers to build, run, and manage LLM applications.\nLearn more about Langchain\nAutoGen # AutoGen is a collection of pre-built systems that can be used to build various applications. These systems cover a wide range of domains and complexities, so there is a good chance you will find something that meets your needs. AutoGen provides a high-level abstraction called a multi-agent conversation framework. This framework makes it easier to build workflows that use large language models (LLMs). In addition, AutoGen offers enhanced APIs for interacting with LLMs. These APIs can improve the performance of your workflows and reduce the cost of running them.\nLearn more about AutoGen\nSemantic-Kernel # Semantic Kernel is an open-source developer kit that lets developers easily build AI agents and integrate AI models into their code. It works across C#, Python, and Java. Semantic Kernel is designed to be modular and future-proof. This means that developers can easily add new features and functionality as needed. Semantic Kernel also allows developers to automate business processes. Overall, Semantic Kernel is a powerful tool that can help developers build and deploy AI-powered applications quickly and easily.\nLearn more about Semantic-Kernel\nHaystack # Haystack is an open-source framework that simplifies building question answering systems. It provides tools for various stages of the development pipeline, from data access and preprocessing to information retrieval and question answering. Haystack offers flexibility in choosing retrieval models and answer scoring strategies, allowing developers to customize their QA systems. With its modular architecture and pre-built components, Haystack accelerates development. Haystack scales well for large datasets and complex workflows, making it suitable for production-grade QA systems. The freely available framework fosters a growing community and allows developers to contribute. The comprehensive Haystack documentation offers guides and tutorials to get you started quickly.\nLearn more about Haystack\nLangroid # Langroid is a Python framework designed for building complex applications powered by Large Language Models (LLMs). It accomplishes this through a multi-agent programming approach. Langroid offers a number of features that make it well-suited for developers, including: a focus on agents as the primary unit of interaction, task delegation for breaking down complex tasks, and caching to improve efficiency. Langroid also supports a variety of tools and integrations, including OpenAI LLMs, vector stores, and retrieval-augmented generation.\nLearn more about Langroid\nLangStream # LangStream is a framework built for developers who want to create real-time Generative AI applications. It simplifies the process by allowing you to combine powerful tools like large language models and vector databases with real-time data processing. This lets you build effective Gen AI applications. LangStream uses an event-driven architecture, making it easy to develop responsive Gen AI applications. This architecture offers advantages like scalability, fault-tolerance, and high availability – all crucial aspects for robust applications.\nLearn more about LangStream\nAutoGPT # AutoGPT is an open-source project that consists of four main components: Agent, Benchmark, Forge, and Frontend. It aims to provide access to AI assistance and to build the future transparently. Agent, also known as AutoGPT, is a semi-autonomous agent powered by large language models (LLMs) to execute any task for you. Benchmark is a tool that measures your agent&rsquo;s performance. Forge is a ready-to-go template to create your own agent application. Frontend is an easy-to-use and open source frontend for any Agent Protocol-compliant agent.\nLearn more about AutoGPT\nGriptape # Griptape is a framework designed to help developers build secure and effective AI applications with Large Language Models (LLMs). It allows you to create AI systems that balance predictability with creativity. Griptape enforces structures and utilizes long-term memory for reliable outcomes, while also providing tools and short-term memory to leverage LLM creativity with external data sources. This lets you switch between predictable and creative functionalities based on your specific needs. Griptape goes beyond just utilizing LLMs’ potential. It enforces data security through trust boundaries and permission controls. By doing so, Griptape allows you to leverage LLMs’ reasoning capabilities while adhering to strict security guidelines.\nLearn more about Griptape\ntxtai # Txtai is an all-in-one embeddings database that allows you to search for similar text, documents, code, audio, images, and video. It provides functionalities like semantic search, LLM orchestration, and language model workflows. Txtai supports vector search with SQL, topic modeling, and retrieval augmented generation. You can use txtai to create embeddings for various data formats and use them for various retrieval tasks.\nLearn more about txtai\nAgentcloud # Agent Cloud is a platform that enables companies to host their own AI App platform. It allows developers to build and deploy two types of conversational chat apps and process apps. Conversational chat apps are similar to OpenAI GPTs but can use any LLM and access a library of tools as well as retrieve knowledge from hundreds of data sources. Process apps allow developers to automate processes by allocating goals and tasks for agents to complete.\nLearn more about Agentcloud\nOrchestrAI # OrchestrAI is a Python-based framework designed to help developers build and test custom autonomous agents. It leverages the networkx library to manage the dependencies between various AI modules, and YAML to define and manage task pipelines. This framework empowers developers to define and contrast variations of strategies and settings, ultimately assisting them in finding the optimal approach for their specific use case. While current modules only communicate with OpenAI, OrchestrAI can be extended to encompass other language models as well.\nLearn more about OrchestrAI\nxlang-ai/OpenAgents # OpenAgents is an open-source framework designed for deploying and using language agents in real-world scenarios. Unlike frameworks focused on testing, OpenAgents prioritizes user experience with a web interface for easy interaction. Developers benefit too, with seamless local deployment for their own agents. The platform includes three pre-built agents: a Data Agent for data analysis, a Plugin Agent with over 200 tools, and a Web Agent for web browsing. OpenAgents empowers further development through its open source code, allowing researchers and developers to build upon this foundation and create innovative language agents.\nLearn more about OpenAgents\nCrewai # Crewai is an open-source framework for developers to build systems powered by multiple, collaborative AI agents. Crewai lets create teams of agents, each with its own strengths, working together to achieve a complex goal. The framework is designed to be adaptable and can integrate with different large language models especially Langchain and Llamaindex. Crewai offers an intuitive workflow for both development and deployment. With Crewai, developers can leverage the power of multiple AI agents to automate tasks and solve problems in innovative ways.\nLearn more about Crewai\nVercel AI # Vercel is a platform for developers that provides the tools, workflows, and infrastructure you need to build and deploy your web apps faster, without the need for additional configuration. It simplifies the integration of large language models (LLMs) into applications built with React, Next.js, Vue, Svelte, Node.js, and more. The Vercel AI SDK offers a unified API for generating text, structured objects, and tool calls with LLMs. It also provides framework-agnostic hooks for building chat and generative user interfaces. Notably, the Vercel AI SDK includes React and Svelte hooks for data fetching and rendering streaming text responses, enabling real-time data representation.\nLearn more about Vercel AI\n"}, {"id": 67, "href": "/docs/pepgenx/development_frameworks/software_development_frameworks/php/", "title": "PHP", "section": "Software Development Frameworks", "content": " PHP # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 68, "href": "/docs/pepgenx/development_frameworks/llm_frameworks/agentcloud/", "title": "Agentcloud", "section": "LLM Frameworks", "content": " Agentcloud # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 69, "href": "/docs/pepgenxdev/must_read/azure/", "title": "Azure Services", "section": "Must Read", "content": " Microsoft Azure Services # Introduction # PepGenX Platform is designed to seamlessly integrate with various Azure services, enabling developers to build robust, scalable, and secure applications. Below, you&rsquo;ll find detailed insights into the core services provided by Microsoft Azure, which form the backbone of our platform infrastructure.\nAzure Kubernetes Services # &ldquo;Introduction to Azure Kubernetes Service&rdquo; Azure Container Registries # &ldquo;Introduction to Azure Container Registry&rdquo; Azure Storage Account # &ldquo;Introduction to Azure Storage&rdquo; Azure Key Vault # &ldquo;About Azure Key Vault&rdquo; Azure AI Search # &ldquo;What&rsquo;s Azure AI Search?&rdquo; &ldquo;Fundamentals of Knowledge Mining and Azure AI Search&rdquo; Azure Content Safety Studio # &ldquo;What is Azure AI Content Safety?&rdquo; Azure AI Studio # &ldquo;Introduction to Azure AI Studio&rdquo; "}, {"id": 70, "href": "/docs/pepgenx/development_frameworks/software_development_frameworks/csharp/", "title": "C#", "section": "Software Development Frameworks", "content": " C# # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 71, "href": "/docs/pepgenxdev/platform_components/security_analyzer/", "title": "Security Analyzer", "section": "Platform components", "content": "Standarized Key for content:\nlogit_bias - map (text) prompt - text messages - text system - text functions - text function_call - text input - text (speech/embeddings) file - binary (transcript/translation) training_file - fine tuning prompt - images mask - images image - images instructions - assistant "}, {"id": 72, "href": "/docs/pepgenx/tutorials/", "title": "Tutorials", "section": "Welcome to PepGenX", "content": " Tutorials # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 73, "href": "/docs/pepgenx/reference/", "title": "Reference", "section": "Welcome to PepGenX", "content": " API # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 74, "href": "/docs/pepgenx/development_frameworks/llm_frameworks/vercelai/", "title": "Vercel AI", "section": "LLM Frameworks", "content": " Vercel AI # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 75, "href": "/posts/creating-a-new-theme/", "title": "Creating a New Theme", "section": "Updates", "content": " Introduction # This tutorial will show you how to create a simple theme in <PERSON>. I assume that you are familiar with HTML, the bash command line, and that you are comfortable using Markdown to format content. I&rsquo;ll explain how <PERSON> uses templates and how you can organize your templates to create a theme. I won&rsquo;t cover using CSS to style your theme.\nWe&rsquo;ll start with creating a new site with a very basic template. Then we&rsquo;ll add in a few pages and posts. With small variations on that, you will be able to create many different types of web sites.\nIn this tutorial, commands that you enter will start with the &ldquo;$&rdquo; prompt. The output will follow. Lines that start with &ldquo;#&rdquo; are comments that I&rsquo;ve added to explain a point. When I show updates to a file, the &ldquo;:wq&rdquo; on the last line means to save the file.\nHere&rsquo;s an example:\n## this is a comment $ echo this is a command this is a command ## edit the file $ vi foo.md +++ date = &#34;2014-09-28&#34; title = &#34;creating a new theme&#34; +++ bah and humbug :wq ## show it $ cat foo.md +++ date = &#34;2014-09-28&#34; title = &#34;creating a new theme&#34; +++ bah and humbug $ Some Definitions # There are a few concepts that you need to understand before creating a theme.\nSkins # Skins are the files responsible for the look and feel of your site. It’s the CSS that controls colors and fonts, it’s the Javascript that determines actions and reactions. It’s also the rules that Hugo uses to transform your content into the HTML that the site will serve to visitors.\nYou have two ways to create a skin. The simplest way is to create it in the layouts/ directory. If you do, then you don’t have to worry about configuring Hugo to recognize it. The first place that Hugo will look for rules and files is in the layouts/ directory so it will always find the skin.\nYour second choice is to create it in a sub-directory of the themes/ directory. If you do, then you must always tell Hugo where to search for the skin. It’s extra work, though, so why bother with it?\nThe difference between creating a skin in layouts/ and creating it in themes/ is very subtle. A skin in layouts/ can’t be customized without updating the templates and static files that it is built from. A skin created in themes/, on the other hand, can be and that makes it easier for other people to use it.\nThe rest of this tutorial will call a skin created in the themes/ directory a theme.\nNote that you can use this tutorial to create a skin in the layouts/ directory if you wish to. The main difference will be that you won’t need to update the site’s configuration file to use a theme.\nThe Home Page # The home page, or landing page, is the first page that many visitors to a site see. It is the index.html file in the root directory of the web site. Since Hugo writes files to the public/ directory, our home page is public/index.html.\nSite Configuration File # When Hugo runs, it looks for a configuration file that contains settings that override default values for the entire site. The file can use TOML, YAML, or JSON. I prefer to use TOML for my configuration files. If you prefer to use JSON or YAML, you’ll need to translate my examples. You’ll also need to change the name of the file since Hugo uses the extension to determine how to process it.\nHugo translates Markdown files into HTML. By default, Hugo expects to find Markdown files in your content/ directory and template files in your themes/ directory. It will create HTML files in your public/ directory. You can change this by specifying alternate locations in the configuration file.\nContent # Content is stored in text files that contain two sections. The first section is the “front matter,” which is the meta-information on the content. The second section contains Markdown that will be converted to HTML.\nFront Matter # The front matter is information about the content. Like the configuration file, it can be written in TOML, YAML, or JSON. Unlike the configuration file, Hugo doesn’t use the file’s extension to know the format. It looks for markers to signal the type. TOML is surrounded by “+++”, YAML by “---”, and JSON is enclosed in curly braces. I prefer to use TOML, so you’ll need to translate my examples if you prefer YAML or JSON.\nThe information in the front matter is passed into the template before the content is rendered into HTML.\nMarkdown # Content is written in Markdown which makes it easier to create the content. Hugo runs the content through a Markdown engine to create the HTML which will be written to the output file.\nTemplate Files # Hugo uses template files to render content into HTML. Template files are a bridge between the content and presentation. Rules in the template define what content is published, where it&rsquo;s published to, and how it will rendered to the HTML file. The template guides the presentation by specifying the style to use.\nThere are three types of templates: single, list, and partial. Each type takes a bit of content as input and transforms it based on the commands in the template.\nHugo uses its knowledge of the content to find the template file used to render the content. If it can’t find a template that is an exact match for the content, it will shift up a level and search from there. It will continue to do so until it finds a matching template or runs out of templates to try. If it can’t find a template, it will use the default template for the site.\nPlease note that you can use the front matter to influence Hugo’s choice of templates.\nSingle Template # A single template is used to render a single piece of content. For example, an article or post would be a single piece of content and use a single template.\nList Template # A list template renders a group of related content. That could be a summary of recent postings or all articles in a category. List templates can contain multiple groups.\nThe homepage template is a special type of list template. Hugo assumes that the home page of your site will act as the portal for the rest of the content in the site.\nPartial Template # A partial template is a template that can be included in other templates. Partial templates must be called using the “partial” template command. They are very handy for rolling up common behavior. For example, your site may have a banner that all pages use. Instead of copying the text of the banner into every single and list template, you could create a partial with the banner in it. That way if you decide to change the banner, you only have to change the partial template.\nCreate a New Site # Let&rsquo;s use Hugo to create a new web site. I&rsquo;m a Mac user, so I&rsquo;ll create mine in my home directory, in the Sites folder. If you&rsquo;re using Linux, you might have to create the folder first.\nThe &ldquo;new site&rdquo; command will create a skeleton of a site. It will give you the basic directory structure and a useable configuration file.\n$ hugo new site ~/Sites/zafta $ cd ~/Sites/zafta $ ls -l total 8 drwxr-xr-x 7 <USER> <GROUP> 238 Sep 29 16:49 . drwxr-xr-x 3 <USER> <GROUP> 102 Sep 29 16:49 .. drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 16:49 archetypes -rw-r--r-- 1 <USER> <GROUP> 82 Sep 29 16:49 config.toml drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 16:49 content drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 16:49 layouts drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 16:49 static $ Take a look in the content/ directory to confirm that it is empty.\nThe other directories (archetypes/, layouts/, and static/) are used when customizing a theme. That&rsquo;s a topic for a different tutorial, so please ignore them for now.\nGenerate the HTML For the New Site # Running the hugo command with no options will read all the available content and generate the HTML files. It will also copy all static files (that&rsquo;s everything that&rsquo;s not content). Since we have an empty site, it won&rsquo;t do much, but it will do it very quickly.\n$ hugo --verbose INFO: 2014/09/29 Using config file: config.toml INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/ WARN: 2014/09/29 Unable to locate layout: [index.html _default/list.html _default/single.html] WARN: 2014/09/29 Unable to locate layout: [404.html] 0 draft content 0 future content 0 pages created 0 tags created 0 categories created in 2 ms $ The &ldquo;--verbose&rdquo; flag gives extra information that will be helpful when we build the template. Every line of the output that starts with &ldquo;INFO:&rdquo; or &ldquo;WARN:&rdquo; is present because we used that flag. The lines that start with &ldquo;WARN:&rdquo; are warning messages. We&rsquo;ll go over them later.\nWe can verify that the command worked by looking at the directory again.\n$ ls -l total 8 drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 16:49 archetypes -rw-r--r-- 1 <USER> <GROUP> 82 Sep 29 16:49 config.toml drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 16:49 content drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 16:49 layouts drwxr-xr-x 4 <USER> <GROUP> 136 Sep 29 17:02 public drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 16:49 static $ See that new public/ directory? Hugo placed all generated content there. When you&rsquo;re ready to publish your web site, that&rsquo;s the place to start. For now, though, let&rsquo;s just confirm that we have what we&rsquo;d expect from a site with no content.\n$ ls -l public total 16 -rw-r--r-- 1 <USER> <GROUP> 416 Sep 29 17:02 index.xml -rw-r--r-- 1 <USER> <GROUP> 262 Sep 29 17:02 sitemap.xml $ Hugo created two XML files, which is standard, but there are no HTML files.\nTest the New Site # Verify that you can run the built-in web server. It will dramatically shorten your development cycle if you do. Start it by running the &ldquo;server&rdquo; command. If it is successful, you will see output similar to the following:\n$ hugo server --verbose INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/ WARN: 2014/09/29 Unable to locate layout: [index.html _default/list.html _default/single.html] WARN: 2014/09/29 Unable to locate layout: [404.html] 0 draft content 0 future content 0 pages created 0 tags created 0 categories created in 2 ms Serving pages from /Users/<USER>/Sites/zafta/public Web Server is available at http://localhost:1313 Press Ctrl+C to stop Connect to the listed URL (it&rsquo;s on the line that starts with &ldquo;Web Server&rdquo;). If everything is working correctly, you should get a page that shows the following:\nindex.xml sitemap.xml That&rsquo;s a listing of your public/ directory. Hugo didn&rsquo;t create a home page because our site has no content. When there&rsquo;s no index.html file in a directory, the server lists the files in the directory, which is what you should see in your browser.\nLet’s go back and look at those warnings again.\nWARN: 2014/09/29 Unable to locate layout: [index.html _default/list.html _default/single.html] WARN: 2014/09/29 Unable to locate layout: [404.html] That second warning is easier to explain. We haven’t created a template to be used to generate “page not found errors.” The 404 message is a topic for a separate tutorial.\nNow for the first warning. It is for the home page. You can tell because the first layout that it looked for was “index.html.” That’s only used by the home page.\nI like that the verbose flag causes Hugo to list the files that it&rsquo;s searching for. For the home page, they are index.html, _default/list.html, and _default/single.html. There are some rules that we&rsquo;ll cover later that explain the names and paths. For now, just remember that Hugo couldn&rsquo;t find a template for the home page and it told you so.\nAt this point, you&rsquo;ve got a working installation and site that we can build upon. All that’s left is to add some content and a theme to display it.\nCreate a New Theme # Hugo doesn&rsquo;t ship with a default theme. There are a few available (I counted a dozen when I first installed Hugo) and Hugo comes with a command to create new themes.\nWe&rsquo;re going to create a new theme called &ldquo;zafta.&rdquo; Since the goal of this tutorial is to show you how to fill out the files to pull in your content, the theme will not contain any CSS. In other words, ugly but functional.\nAll themes have opinions on content and layout. For example, Zafta uses &ldquo;post&rdquo; over &ldquo;blog&rdquo;. Strong opinions make for simpler templates but differing opinions make it tougher to use themes. When you build a theme, consider using the terms that other themes do.\nCreate a Skeleton # Use the hugo &ldquo;new&rdquo; command to create the skeleton of a theme. This creates the directory structure and places empty files for you to fill out.\n$ hugo new theme zafta $ ls -l total 8 drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 16:49 archetypes -rw-r--r-- 1 <USER> <GROUP> 82 Sep 29 16:49 config.toml drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 16:49 content drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 16:49 layouts drwxr-xr-x 4 <USER> <GROUP> 136 Sep 29 17:02 public drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 16:49 static drwxr-xr-x 3 <USER> <GROUP> 102 Sep 29 17:31 themes $ find themes -type f | xargs ls -l -rw-r--r-- 1 <USER> <GROUP> 1081 Sep 29 17:31 themes/zafta/LICENSE.md -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 17:31 themes/zafta/archetypes/default.md -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 17:31 themes/zafta/layouts/_default/list.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 17:31 themes/zafta/layouts/_default/single.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 17:31 themes/zafta/layouts/index.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 17:31 themes/zafta/layouts/partials/footer.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 17:31 themes/zafta/layouts/partials/header.html -rw-r--r-- 1 <USER> <GROUP> 93 Sep 29 17:31 themes/zafta/theme.toml $ The skeleton includes templates (the files ending in .html), license file, a description of your theme (the theme.toml file), and an empty archetype.\nPlease take a minute to fill out the theme.toml and LICENSE.md files. They&rsquo;re optional, but if you&rsquo;re going to be distributing your theme, it tells the world who to praise (or blame). It&rsquo;s also nice to declare the license so that people will know how they can use the theme.\n$ vi themes/zafta/theme.toml author = &#34;michael d henderson&#34; description = &#34;a minimal working template&#34; license = &#34;MIT&#34; name = &#34;zafta&#34; source_repo = &#34;&#34; tags = [&#34;tags&#34;, &#34;categories&#34;] :wq ## also edit themes/zafta/LICENSE.md and change ## the bit that says &#34;YOUR_NAME_HERE&#34; Note that the the skeleton&rsquo;s template files are empty. Don&rsquo;t worry, we&rsquo;ll be changing that shortly.\n$ find themes/zafta -name &#39;*.html&#39; | xargs ls -l -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 17:31 themes/zafta/layouts/_default/list.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 17:31 themes/zafta/layouts/_default/single.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 17:31 themes/zafta/layouts/index.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 17:31 themes/zafta/layouts/partials/footer.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 17:31 themes/zafta/layouts/partials/header.html $ Update the Configuration File to Use the Theme # Now that we&rsquo;ve got a theme to work with, it&rsquo;s a good idea to add the theme name to the configuration file. This is optional, because you can always add &ldquo;-t zafta&rdquo; on all your commands. I like to put it the configuration file because I like shorter command lines. If you don&rsquo;t put it in the configuration file or specify it on the command line, you won&rsquo;t use the template that you&rsquo;re expecting to.\nEdit the file to add the theme, add a title for the site, and specify that all of our content will use the TOML format.\n$ vi config.toml theme = &#34;zafta&#34; baseurl = &#34;&#34; languageCode = &#34;en-us&#34; title = &#34;zafta - totally refreshing&#34; MetaDataFormat = &#34;toml&#34; :wq $ Generate the Site # Now that we have an empty theme, let&rsquo;s generate the site again.\n$ hugo --verbose INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/themes/zafta/static/ to /Users/<USER>/Sites/zafta/public/ INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/ WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html] 0 draft content 0 future content 0 pages created 0 tags created 0 categories created in 2 ms $ Did you notice that the output is different? The warning message for the home page has disappeared and we have an additional information line saying that Hugo is syncing from the theme&rsquo;s directory.\nLet&rsquo;s check the public/ directory to see what Hugo&rsquo;s created.\n$ ls -l public total 16 drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 17:56 css -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 17:56 index.html -rw-r--r-- 1 <USER> <GROUP> 407 Sep 29 17:56 index.xml drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 17:56 js -rw-r--r-- 1 <USER> <GROUP> 243 Sep 29 17:56 sitemap.xml $ Notice four things:\nHugo created a home page. This is the file public/index.html. Hugo created a css/ directory. Hugo created a js/ directory. Hugo claimed that it created 0 pages. It created a file and copied over static files, but didn&rsquo;t create any pages. That&rsquo;s because it considers a &ldquo;page&rdquo; to be a file created directly from a content file. It doesn&rsquo;t count things like the index.html files that it creates automatically. The Home Page # Hugo supports many different types of templates. The home page is special because it gets its own type of template and its own template file. The file, layouts/index.html, is used to generate the HTML for the home page. The Hugo documentation says that this is the only required template, but that depends. Hugo&rsquo;s warning message shows that it looks for three different templates:\nWARN: 2014/09/29 Unable to locate layout: [index.html _default/list.html _default/single.html] If it can&rsquo;t find any of these, it completely skips creating the home page. We noticed that when we built the site without having a theme installed.\nWhen Hugo created our theme, it created an empty home page template. Now, when we build the site, Hugo finds the template and uses it to generate the HTML for the home page. Since the template file is empty, the HTML file is empty, too. If the template had any rules in it, then Hugo would have used them to generate the home page.\n$ find . -name index.html | xargs ls -l -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 20:21 ./public/index.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 17:31 ./themes/zafta/layouts/index.html $ The Magic of Static # Hugo does two things when generating the site. It uses templates to transform content into HTML and it copies static files into the site. Unlike content, static files are not transformed. They are copied exactly as they are.\nHugo assumes that your site will use both CSS and JavaScript, so it creates directories in your theme to hold them. Remember opinions? Well, Hugo&rsquo;s opinion is that you&rsquo;ll store your CSS in a directory named css/ and your JavaScript in a directory named js/. If you don&rsquo;t like that, you can change the directory names in your theme directory or even delete them completely. Hugo&rsquo;s nice enough to offer its opinion, then behave nicely if you disagree.\n$ find themes/zafta -type d | xargs ls -ld drwxr-xr-x 7 <USER> <GROUP> 238 Sep 29 17:38 themes/zafta drwxr-xr-x 3 <USER> <GROUP> 102 Sep 29 17:31 themes/zafta/archetypes drwxr-xr-x 5 <USER> <GROUP> 170 Sep 29 17:31 themes/zafta/layouts drwxr-xr-x 4 <USER> <GROUP> 136 Sep 29 17:31 themes/zafta/layouts/_default drwxr-xr-x 4 <USER> <GROUP> 136 Sep 29 17:31 themes/zafta/layouts/partials drwxr-xr-x 4 <USER> <GROUP> 136 Sep 29 17:31 themes/zafta/static drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 17:31 themes/zafta/static/css drwxr-xr-x 2 <USER> <GROUP> 68 Sep 29 17:31 themes/zafta/static/js $ The Theme Development Cycle # When you&rsquo;re working on a theme, you will make changes in the theme&rsquo;s directory, rebuild the site, and check your changes in the browser. Hugo makes this very easy:\nPurge the public/ directory. Run the built in web server in watch mode. Open your site in a browser. Update the theme. Glance at your browser window to see changes. Return to step 4. I’ll throw in one more opinion: never work on a theme on a live site. Always work on a copy of your site. Make changes to your theme, test them, then copy them up to your site. For added safety, use a tool like Git to keep a revision history of your content and your theme. Believe me when I say that it is too easy to lose both your mind and your changes.\nCheck the main Hugo site for information on using Git with Hugo.\nPurge the public/ Directory # When generating the site, Hugo will create new files and update existing ones in the public/ directory. It will not delete files that are no longer used. For example, files that were created in the wrong directory or with the wrong title will remain. If you leave them, you might get confused by them later. I recommend cleaning out your site prior to generating it.\nNote: If you&rsquo;re building on an SSD, you should ignore this. Churning on a SSD can be costly.\nHugo&rsquo;s Watch Option # Hugo&rsquo;s &ldquo;--watch&rdquo; option will monitor the content/ and your theme directories for changes and rebuild the site automatically.\nLive Reload # Hugo&rsquo;s built in web server supports live reload. As pages are saved on the server, the browser is told to refresh the page. Usually, this happens faster than you can say, &ldquo;Wow, that&rsquo;s totally amazing.&rdquo;\nDevelopment Commands # Use the following commands as the basis for your workflow.\n## purge old files. hugo will recreate the public directory. ## $ rm -rf public ## ## run hugo in watch mode ## $ hugo server --watch --verbose Here&rsquo;s sample output showing Hugo detecting a change to the template for the home page. Once generated, the web browser automatically reloaded the page. I&rsquo;ve said this before, it&rsquo;s amazing.\n$ rm -rf public $ hugo server --watch --verbose INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/themes/zafta/static/ to /Users/<USER>/Sites/zafta/public/ INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/ WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html] 0 draft content 0 future content 0 pages created 0 tags created 0 categories created in 2 ms Watching for changes in /Users/<USER>/Sites/zafta/content Serving pages from /Users/<USER>/Sites/zafta/public Web Server is available at http://localhost:1313 Press Ctrl+C to stop INFO: 2014/09/29 File System Event: [&#34;/Users/<USER>/Sites/zafta/themes/zafta/layouts/index.html&#34;: MODIFY|ATTRIB] Change detected, rebuilding site WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html] 0 draft content 0 future content 0 pages created 0 tags created 0 categories created in 1 ms Update the Home Page Template # The home page is one of a few special pages that Hugo creates automatically. As mentioned earlier, it looks for one of three files in the theme&rsquo;s layout/ directory:\nindex.html _default/list.html _default/single.html We could update one of the default templates, but a good design decision is to update the most specific template available. That&rsquo;s not a hard and fast rule (in fact, we&rsquo;ll break it a few times in this tutorial), but it is a good generalization.\nMake a Static Home Page # Right now, that page is empty because we don&rsquo;t have any content and we don&rsquo;t have any logic in the template. Let&rsquo;s change that by adding some text to the template.\n$ vi themes/zafta/layouts/index.html &lt;!DOCTYPE html&gt; &lt;html&gt; &lt;body&gt; &lt;p&gt;hugo says hello!&lt;/p&gt; &lt;/body&gt; &lt;/html&gt; :wq $ Build the web site and then verify the results.\n$ hugo --verbose INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/themes/zafta/static/ to /Users/<USER>/Sites/zafta/public/ INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/ WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html] 0 draft content 0 future content 0 pages created 0 tags created 0 categories created in 2 ms $ find public -type f -name &#39;*.html&#39; | xargs ls -l -rw-r--r-- 1 <USER> <GROUP> 78 Sep 29 21:26 public/index.html $ cat public/index.html &lt;!DOCTYPE html&gt; &lt;html&gt; &lt;body&gt; &lt;p&gt;hugo says hello!&lt;/p&gt; &lt;/html&gt; Live Reload # Note: If you&rsquo;re running the server with the --watch option, you&rsquo;ll see different content in the file:\n$ cat public/index.html &lt;!DOCTYPE html&gt; &lt;html&gt; &lt;body&gt; &lt;p&gt;hugo says hello!&lt;/p&gt; &lt;script&gt;document.write(&#39;&lt;script src=&#34;http://&#39; + (location.host || &#39;localhost&#39;).split(&#39;:&#39;)[0] + &#39;:1313/livereload.js?mindelay=10&#34;&gt;&lt;/&#39; + &#39;script&gt;&#39;)&lt;/script&gt;&lt;/body&gt; &lt;/html&gt; When you use --watch, the Live Reload script is added by Hugo. Look for live reload in the documentation to see what it does and how to disable it.\nBuild a &ldquo;Dynamic&rdquo; Home Page # &ldquo;Dynamic home page?&rdquo; Hugo&rsquo;s a static web site generator, so this seems an odd thing to say. I mean let&rsquo;s have the home page automatically reflect the content in the site every time Hugo builds it. We&rsquo;ll use iteration in the template to do that.\nCreate New Posts # Now that we have the home page generating static content, let&rsquo;s add some content to the site. We&rsquo;ll display these posts as a list on the home page and on their own page, too.\nHugo has a command to generate a skeleton post, just like it does for sites and themes.\n$ hugo --verbose new post/first.md INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml INFO: 2014/09/29 attempting to create post/first.md of post INFO: 2014/09/29 curpath: /Users/<USER>/Sites/zafta/themes/zafta/archetypes/default.md ERROR: 2014/09/29 Unable to Cast &lt;nil&gt; to map[string]interface{} $ That wasn&rsquo;t very nice, was it?\nThe &ldquo;new&rdquo; command uses an archetype to create the post file. Hugo created an empty default archetype file, but that causes an error when there&rsquo;s a theme. For me, the workaround was to create an archetypes file specifically for the post type.\n$ vi themes/zafta/archetypes/post.md +++ Description = &#34;&#34; Tags = [] Categories = [] +++ :wq $ find themes/zafta/archetypes -type f | xargs ls -l -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 21:53 themes/zafta/archetypes/default.md -rw-r--r-- 1 <USER> <GROUP> 51 Sep 29 21:54 themes/zafta/archetypes/post.md $ hugo --verbose new post/first.md INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml INFO: 2014/09/29 attempting to create post/first.md of post INFO: 2014/09/29 curpath: /Users/<USER>/Sites/zafta/themes/zafta/archetypes/post.md INFO: 2014/09/29 creating /Users/<USER>/Sites/zafta/content/post/first.md /Users/<USER>/Sites/zafta/content/post/first.md created $ hugo --verbose new post/second.md INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml INFO: 2014/09/29 attempting to create post/second.md of post INFO: 2014/09/29 curpath: /Users/<USER>/Sites/zafta/themes/zafta/archetypes/post.md INFO: 2014/09/29 creating /Users/<USER>/Sites/zafta/content/post/second.md /Users/<USER>/Sites/zafta/content/post/second.md created $ ls -l content/post total 16 -rw-r--r-- 1 <USER> <GROUP> 104 Sep 29 21:54 first.md -rw-r--r-- 1 <USER> <GROUP> 105 Sep 29 21:57 second.md $ cat content/post/first.md +++ Categories = [] Description = &#34;&#34; Tags = [] date = &#34;2014-09-29T21:54:53-05:00&#34; title = &#34;first&#34; +++ my first post $ cat content/post/second.md +++ Categories = [] Description = &#34;&#34; Tags = [] date = &#34;2014-09-29T21:57:09-05:00&#34; title = &#34;second&#34; +++ my second post $ Build the web site and then verify the results.\n$ rm -rf public $ hugo --verbose INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/themes/zafta/static/ to /Users/<USER>/Sites/zafta/public/ INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/ INFO: 2014/09/29 found taxonomies: map[string]string{&#34;category&#34;:&#34;categories&#34;, &#34;tag&#34;:&#34;tags&#34;} WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html] 0 draft content 0 future content 2 pages created 0 tags created 0 categories created in 4 ms $ The output says that it created 2 pages. Those are our new posts:\n$ find public -type f -name &#39;*.html&#39; | xargs ls -l -rw-r--r-- 1 <USER> <GROUP> 78 Sep 29 22:13 public/index.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 22:13 public/post/first/index.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 22:13 public/post/index.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 22:13 public/post/second/index.html $ The new files are empty because because the templates used to generate the content are empty. The homepage doesn&rsquo;t show the new content, either. We have to update the templates to add the posts.\nList and Single Templates # In Hugo, we have three major kinds of templates. There&rsquo;s the home page template that we updated previously. It is used only by the home page. We also have &ldquo;single&rdquo; templates which are used to generate output for a single content file. We also have &ldquo;list&rdquo; templates that are used to group multiple pieces of content before generating output.\nGenerally speaking, list templates are named &ldquo;list.html&rdquo; and single templates are named &ldquo;single.html.&rdquo;\nThere are three other types of templates: partials, content views, and terms. We will not go into much detail on these.\nAdd Content to the Homepage # The home page will contain a list of posts. Let&rsquo;s update its template to add the posts that we just created. The logic in the template will run every time we build the site.\n$ vi themes/zafta/layouts/index.html &lt;!DOCTYPE html&gt; &lt;html&gt; &lt;body&gt; {{ range first 10 .Data.Pages }} &lt;h1&gt;{{ .Title }}&lt;/h1&gt; {{ end }} &lt;/body&gt; &lt;/html&gt; :wq $ Hugo uses the Go template engine. That engine scans the template files for commands which are enclosed between &ldquo;{{&rdquo; and &ldquo;}}&rdquo;. In our template, the commands are:\nrange .Title end The &ldquo;range&rdquo; command is an iterator. We&rsquo;re going to use it to go through the first ten pages. Every HTML file that Hugo creates is treated as a page, so looping through the list of pages will look at every file that will be created.\nThe &ldquo;.Title&rdquo; command prints the value of the &ldquo;title&rdquo; variable. Hugo pulls it from the front matter in the Markdown file.\nThe &ldquo;end&rdquo; command signals the end of the range iterator. The engine loops back to the top of the iteration when it finds &ldquo;end.&rdquo; Everything between the &ldquo;range&rdquo; and &ldquo;end&rdquo; is evaluated every time the engine goes through the iteration. In this file, that would cause the title from the first ten pages to be output as heading level one.\nIt&rsquo;s helpful to remember that some variables, like .Data, are created before any output files. Hugo loads every content file into the variable and then gives the template a chance to process before creating the HTML files.\nBuild the web site and then verify the results.\n$ rm -rf public $ hugo --verbose INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/themes/zafta/static/ to /Users/<USER>/Sites/zafta/public/ INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/ INFO: 2014/09/29 found taxonomies: map[string]string{&#34;tag&#34;:&#34;tags&#34;, &#34;category&#34;:&#34;categories&#34;} WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html] 0 draft content 0 future content 2 pages created 0 tags created 0 categories created in 4 ms $ find public -type f -name &#39;*.html&#39; | xargs ls -l -rw-r--r-- 1 <USER> <GROUP> 94 Sep 29 22:23 public/index.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 22:23 public/post/first/index.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 22:23 public/post/index.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 22:23 public/post/second/index.html $ cat public/index.html &lt;!DOCTYPE html&gt; &lt;html&gt; &lt;body&gt; &lt;h1&gt;second&lt;/h1&gt; &lt;h1&gt;first&lt;/h1&gt; &lt;/body&gt; &lt;/html&gt; $ Congratulations, the home page shows the title of the two posts. The posts themselves are still empty, but let&rsquo;s take a moment to appreciate what we&rsquo;ve done. Your template now generates output dynamically. Believe it or not, by inserting the range command inside of those curly braces, you&rsquo;ve learned everything you need to know to build a theme. All that&rsquo;s really left is understanding which template will be used to generate each content file and becoming familiar with the commands for the template engine.\nAnd, if that were entirely true, this tutorial would be much shorter. There are a few things to know that will make creating a new template much easier. Don&rsquo;t worry, though, that&rsquo;s all to come.\nAdd Content to the Posts # We&rsquo;re working with posts, which are in the content/post/ directory. That means that their section is &ldquo;post&rdquo; (and if we don&rsquo;t do something weird, their type is also &ldquo;post&rdquo;).\nHugo uses the section and type to find the template file for every piece of content. Hugo will first look for a template file that matches the section or type name. If it can&rsquo;t find one, then it will look in the _default/ directory. There are some twists that we&rsquo;ll cover when we get to categories and tags, but for now we can assume that Hugo will try post/single.html, then _default/single.html.\nNow that we know the search rule, let&rsquo;s see what we actually have available:\n$ find themes/zafta -name single.html | xargs ls -l -rw-r--r-- 1 <USER> <GROUP> 132 Sep 29 17:31 themes/zafta/layouts/_default/single.html We could create a new template, post/single.html, or change the default. Since we don&rsquo;t know of any other content types, let&rsquo;s start with updating the default.\nRemember, any content that we haven&rsquo;t created a template for will end up using this template. That can be good or bad. Bad because I know that we&rsquo;re going to be adding different types of content and we&rsquo;re going to end up undoing some of the changes we&rsquo;ve made. It&rsquo;s good because we&rsquo;ll be able to see immediate results. It&rsquo;s also good to start here because we can start to build the basic layout for the site. As we add more content types, we&rsquo;ll refactor this file and move logic around. Hugo makes that fairly painless, so we&rsquo;ll accept the cost and proceed.\nPlease see the Hugo documentation on template rendering for all the details on determining which template to use. And, as the docs mention, if you&rsquo;re building a single page application (SPA) web site, you can delete all of the other templates and work with just the default single page. That&rsquo;s a refreshing amount of joy right there.\nUpdate the Template File # $ vi themes/zafta/layouts/_default/single.html &lt;!DOCTYPE html&gt; &lt;html&gt; &lt;head&gt; &lt;title&gt;{{ .Title }}&lt;/title&gt; &lt;/head&gt; &lt;body&gt; &lt;h1&gt;{{ .Title }}&lt;/h1&gt; {{ .Content }} &lt;/body&gt; &lt;/html&gt; :wq $ Build the web site and verify the results.\n$ rm -rf public $ hugo --verbose INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/themes/zafta/static/ to /Users/<USER>/Sites/zafta/public/ INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/ INFO: 2014/09/29 found taxonomies: map[string]string{&#34;tag&#34;:&#34;tags&#34;, &#34;category&#34;:&#34;categories&#34;} WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html] 0 draft content 0 future content 2 pages created 0 tags created 0 categories created in 4 ms $ find public -type f -name &#39;*.html&#39; | xargs ls -l -rw-r--r-- 1 <USER> <GROUP> 94 Sep 29 22:40 public/index.html -rw-r--r-- 1 <USER> <GROUP> 125 Sep 29 22:40 public/post/first/index.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 22:40 public/post/index.html -rw-r--r-- 1 <USER> <GROUP> 128 Sep 29 22:40 public/post/second/index.html $ cat public/post/first/index.html &lt;!DOCTYPE html&gt; &lt;html&gt; &lt;head&gt; &lt;title&gt;first&lt;/title&gt; &lt;/head&gt; &lt;body&gt; &lt;h1&gt;first&lt;/h1&gt; &lt;p&gt;my first post&lt;/p&gt; &lt;/body&gt; &lt;/html&gt; $ cat public/post/second/index.html &lt;!DOCTYPE html&gt; &lt;html&gt; &lt;head&gt; &lt;title&gt;second&lt;/title&gt; &lt;/head&gt; &lt;body&gt; &lt;h1&gt;second&lt;/h1&gt; &lt;p&gt;my second post&lt;/p&gt; &lt;/body&gt; &lt;/html&gt; $ Notice that the posts now have content. You can go to localhost:1313/post/first to verify.\nLinking to Content # The posts are on the home page. Let&rsquo;s add a link from there to the post. Since this is the home page, we&rsquo;ll update its template.\n$ vi themes/zafta/layouts/index.html &lt;!DOCTYPE html&gt; &lt;html&gt; &lt;body&gt; {{ range first 10 .Data.Pages }} &lt;h1&gt;&lt;a href=&#34;{{ .Permalink }}&#34;&gt;{{ .Title }}&lt;/a&gt;&lt;/h1&gt; {{ end }} &lt;/body&gt; &lt;/html&gt; Build the web site and verify the results.\n$ rm -rf public $ hugo --verbose INFO: 2014/09/29 Using config file: /Users/<USER>/Sites/zafta/config.toml INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/themes/zafta/static/ to /Users/<USER>/Sites/zafta/public/ INFO: 2014/09/29 syncing from /Users/<USER>/Sites/zafta/static/ to /Users/<USER>/Sites/zafta/public/ INFO: 2014/09/29 found taxonomies: map[string]string{&#34;tag&#34;:&#34;tags&#34;, &#34;category&#34;:&#34;categories&#34;} WARN: 2014/09/29 Unable to locate layout: [404.html theme/404.html] 0 draft content 0 future content 2 pages created 0 tags created 0 categories created in 4 ms $ find public -type f -name &#39;*.html&#39; | xargs ls -l -rw-r--r-- 1 <USER> <GROUP> 149 Sep 29 22:44 public/index.html -rw-r--r-- 1 <USER> <GROUP> 125 Sep 29 22:44 public/post/first/index.html -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 22:44 public/post/index.html -rw-r--r-- 1 <USER> <GROUP> 128 Sep 29 22:44 public/post/second/index.html $ cat public/index.html &lt;!DOCTYPE html&gt; &lt;html&gt; &lt;body&gt; &lt;h1&gt;&lt;a href=&#34;/post/second/&#34;&gt;second&lt;/a&gt;&lt;/h1&gt; &lt;h1&gt;&lt;a href=&#34;/post/first/&#34;&gt;first&lt;/a&gt;&lt;/h1&gt; &lt;/body&gt; &lt;/html&gt; $ Create a Post Listing # We have the posts displaying on the home page and on their own page. We also have a file public/post/index.html that is empty. Let&rsquo;s make it show a list of all posts (not just the first ten).\nWe need to decide which template to update. This will be a listing, so it should be a list template. Let&rsquo;s take a quick look and see which list templates are available.\n$ find themes/zafta -name list.html | xargs ls -l -rw-r--r-- 1 <USER> <GROUP> 0 Sep 29 17:31 themes/zafta/layouts/_default/list.html As with the single post, we have to decide to update _default/list.html or create post/list.html. We still don&rsquo;t have multiple content types, so let&rsquo;s stay consistent and update the default list template.\nCreating Top Level Pages # Let&rsquo;s add an &ldquo;about&rdquo; page and display it at the top level (as opposed to a sub-level like we did with posts).\nThe default in Hugo is to use the directory structure of the content/ directory to guide the location of the generated html in the public/ directory. Let&rsquo;s verify that by creating an &ldquo;about&rdquo; page at the top level:\n$ vi content/about.md +++ title = &#34;about&#34; description = &#34;about this site&#34; date = &#34;2014-09-27&#34; slug = &#34;about time&#34; +++ ## about us i&#39;m speechless :wq Generate the web site and verify the results.\n$ find public -name &#39;*.html&#39; | xargs ls -l -rw-rw-r-- 1 <USER> <GROUP> 334 Sep 27 15:08 public/about-time/index.html -rw-rw-r-- 1 <USER> <GROUP> 527 Sep 27 15:08 public/index.html -rw-rw-r-- 1 <USER> <GROUP> 358 Sep 27 15:08 public/post/first-post/index.html -rw-rw-r-- 1 <USER> <GROUP> 0 Sep 27 15:08 public/post/index.html -rw-rw-r-- 1 <USER> <GROUP> 342 Sep 27 15:08 public/post/second-post/index.html Notice that the page wasn&rsquo;t created at the top level. It was created in a sub-directory named &lsquo;about-time/&rsquo;. That name came from our slug. Hugo will use the slug to name the generated content. It&rsquo;s a reasonable default, by the way, but we can learn a few things by fighting it for this file.\nOne other thing. Take a look at the home page.\n$ cat public/index.html &lt;!DOCTYPE html&gt; &lt;html&gt; &lt;body&gt; &lt;h1&gt;&lt;a href=&#34;http://localhost:1313/post/theme/&#34;&gt;creating a new theme&lt;/a&gt;&lt;/h1&gt; &lt;h1&gt;&lt;a href=&#34;http://localhost:1313/about-time/&#34;&gt;about&lt;/a&gt;&lt;/h1&gt; &lt;h1&gt;&lt;a href=&#34;http://localhost:1313/post/second-post/&#34;&gt;second&lt;/a&gt;&lt;/h1&gt; &lt;h1&gt;&lt;a href=&#34;http://localhost:1313/post/first-post/&#34;&gt;first&lt;/a&gt;&lt;/h1&gt; &lt;script&gt;document.write(&#39;&lt;script src=&#34;http://&#39; + (location.host || &#39;localhost&#39;).split(&#39;:&#39;)[0] + &#39;:1313/livereload.js?mindelay=10&#34;&gt;&lt;/&#39; + &#39;script&gt;&#39;)&lt;/script&gt;&lt;/body&gt; &lt;/html&gt; Notice that the &ldquo;about&rdquo; link is listed with the posts? That&rsquo;s not desirable, so let&rsquo;s change that first.\n$ vi themes/zafta/layouts/index.html &lt;!DOCTYPE html&gt; &lt;html&gt; &lt;body&gt; &lt;h1&gt;posts&lt;/h1&gt; {{ range first 10 .Data.Pages }} {{ if eq .Type &#34;post&#34;}} &lt;h2&gt;&lt;a href=&#34;{{ .Permalink }}&#34;&gt;{{ .Title }}&lt;/a&gt;&lt;/h2&gt; {{ end }} {{ end }} &lt;h1&gt;pages&lt;/h1&gt; {{ range .Data.Pages }} {{ if eq .Type &#34;page&#34; }} &lt;h2&gt;&lt;a href=&#34;{{ .Permalink }}&#34;&gt;{{ .Title }}&lt;/a&gt;&lt;/h2&gt; {{ end }} {{ end }} &lt;/body&gt; &lt;/html&gt; :wq Generate the web site and verify the results. The home page has two sections, posts and pages, and each section has the right set of headings and links in it.\nBut, that about page still renders to about-time/index.html.\n$ find public -name &#39;*.html&#39; | xargs ls -l -rw-rw-r-- 1 <USER> <GROUP> 334 Sep 27 15:33 public/about-time/index.html -rw-rw-r-- 1 <USER> <GROUP> 645 Sep 27 15:33 public/index.html -rw-rw-r-- 1 <USER> <GROUP> 358 Sep 27 15:33 public/post/first-post/index.html -rw-rw-r-- 1 <USER> <GROUP> 0 Sep 27 15:33 public/post/index.html -rw-rw-r-- 1 <USER> <GROUP> 342 Sep 27 15:33 public/post/second-post/index.html Knowing that hugo is using the slug to generate the file name, the simplest solution is to change the slug. Let&rsquo;s do it the hard way and change the permalink in the configuration file.\n$ vi config.toml [permalinks] page = &#34;/:title/&#34; about = &#34;/:filename/&#34; Generate the web site and verify that this didn&rsquo;t work. Hugo lets &ldquo;slug&rdquo; or &ldquo;URL&rdquo; override the permalinks setting in the configuration file. Go ahead and comment out the slug in content/about.md, then generate the web site to get it to be created in the right place.\nSharing Templates # If you&rsquo;ve been following along, you probably noticed that posts have titles in the browser and the home page doesn&rsquo;t. That&rsquo;s because we didn&rsquo;t put the title in the home page&rsquo;s template (layouts/index.html). That&rsquo;s an easy thing to do, but let&rsquo;s look at a different option.\nWe can put the common bits into a shared template that&rsquo;s stored in the themes/zafta/layouts/partials/ directory.\nCreate the Header and Footer Partials # In Hugo, a partial is a sugar-coated template. Normally a template reference has a path specified. Partials are different. Hugo searches for them along a TODO defined search path. This makes it easier for end-users to override the theme&rsquo;s presentation.\n$ vi themes/zafta/layouts/partials/header.html &lt;!DOCTYPE html&gt; &lt;html&gt; &lt;head&gt; &lt;title&gt;{{ .Title }}&lt;/title&gt; &lt;/head&gt; &lt;body&gt; :wq $ vi themes/zafta/layouts/partials/footer.html &lt;/body&gt; &lt;/html&gt; :wq Update the Home Page Template to Use the Partials # The most noticeable difference between a template call and a partials call is the lack of path:\n{{ template &#34;theme/partials/header.html&#34; . }} versus\n{{ partial &#34;header.html&#34; . }} Both pass in the context.\nLet&rsquo;s change the home page template to use these new partials.\n$ vi themes/zafta/layouts/index.html {{ partial &#34;header.html&#34; . }} &lt;h1&gt;posts&lt;/h1&gt; {{ range first 10 .Data.Pages }} {{ if eq .Type &#34;post&#34;}} &lt;h2&gt;&lt;a href=&#34;{{ .Permalink }}&#34;&gt;{{ .Title }}&lt;/a&gt;&lt;/h2&gt; {{ end }} {{ end }} &lt;h1&gt;pages&lt;/h1&gt; {{ range .Data.Pages }} {{ if or (eq .Type &#34;page&#34;) (eq .Type &#34;about&#34;) }} &lt;h2&gt;&lt;a href=&#34;{{ .Permalink }}&#34;&gt;{{ .Type }} - {{ .Title }} - {{ .RelPermalink }}&lt;/a&gt;&lt;/h2&gt; {{ end }} {{ end }} {{ partial &#34;footer.html&#34; . }} :wq Generate the web site and verify the results. The title on the home page is now &ldquo;your title here&rdquo;, which comes from the &ldquo;title&rdquo; variable in the config.toml file.\nUpdate the Default Single Template to Use the Partials # $ vi themes/zafta/layouts/_default/single.html {{ partial &#34;header.html&#34; . }} &lt;h1&gt;{{ .Title }}&lt;/h1&gt; {{ .Content }} {{ partial &#34;footer.html&#34; . }} :wq Generate the web site and verify the results. The title on the posts and the about page should both reflect the value in the markdown file.\nAdd “Date Published” to Posts # It&rsquo;s common to have posts display the date that they were written or published, so let&rsquo;s add that. The front matter of our posts has a variable named &ldquo;date.&rdquo; It&rsquo;s usually the date the content was created, but let&rsquo;s pretend that&rsquo;s the value we want to display.\nAdd “Date Published” to the Template # We&rsquo;ll start by updating the template used to render the posts. The template code will look like:\n{{ .Date.Format &#34;Mon, Jan 2, 2006&#34; }} Posts use the default single template, so we&rsquo;ll change that file.\n$ vi themes/zafta/layouts/_default/single.html {{ partial &#34;header.html&#34; . }} &lt;h1&gt;{{ .Title }}&lt;/h1&gt; &lt;h2&gt;{{ .Date.Format &#34;Mon, Jan 2, 2006&#34; }}&lt;/h2&gt; {{ .Content }} {{ partial &#34;footer.html&#34; . }} :wq Generate the web site and verify the results. The posts now have the date displayed in them. There&rsquo;s a problem, though. The &ldquo;about&rdquo; page also has the date displayed.\nAs usual, there are a couple of ways to make the date display only on posts. We could do an &ldquo;if&rdquo; statement like we did on the home page. Another way would be to create a separate template for posts.\nThe &ldquo;if&rdquo; solution works for sites that have just a couple of content types. It aligns with the principle of &ldquo;code for today,&rdquo; too.\nLet&rsquo;s assume, though, that we&rsquo;ve made our site so complex that we feel we have to create a new template type. In Hugo-speak, we&rsquo;re going to create a section template.\nLet&rsquo;s restore the default single template before we forget.\n$ mkdir themes/zafta/layouts/post $ vi themes/zafta/layouts/_default/single.html {{ partial &#34;header.html&#34; . }} &lt;h1&gt;{{ .Title }}&lt;/h1&gt; {{ .Content }} {{ partial &#34;footer.html&#34; . }} :wq Now we&rsquo;ll update the post&rsquo;s version of the single template. If you remember Hugo&rsquo;s rules, the template engine will use this version over the default.\n$ vi themes/zafta/layouts/post/single.html {{ partial &#34;header.html&#34; . }} &lt;h1&gt;{{ .Title }}&lt;/h1&gt; &lt;h2&gt;{{ .Date.Format &#34;Mon, Jan 2, 2006&#34; }}&lt;/h2&gt; {{ .Content }} {{ partial &#34;footer.html&#34; . }} :wq Note that we removed the date logic from the default template and put it in the post template. Generate the web site and verify the results. Posts have dates and the about page doesn&rsquo;t.\nDon&rsquo;t Repeat Yourself # DRY is a good design goal and Hugo does a great job supporting it. Part of the art of a good template is knowing when to add a new template and when to update an existing one. While you&rsquo;re figuring that out, accept that you&rsquo;ll be doing some refactoring. Hugo makes that easy and fast, so it&rsquo;s okay to delay splitting up a template.\n"}, {"id": 76, "href": "/posts/migrate-from-jekyll/", "title": "Migrating from Jekyll", "section": "Updates", "content": " Move static content to static # <PERSON><PERSON><PERSON> has a rule that any directory not starting with _ will be copied as-is to the _site output. <PERSON> keeps all static content under static. You should therefore move it all there.\nWith <PERSON><PERSON><PERSON>, something that looked like\n▾ &lt;root&gt;/ ▾ images/ logo.png should become\n▾ &lt;root&gt;/ ▾ static/ ▾ images/ logo.png Additionally, you&rsquo;ll want any files that should reside at the root (such as CNAME) to be moved to static.\nCreate your Hugo configuration file # <PERSON> can read your configuration as JSON, YAML or TOML. <PERSON> supports parameters custom configuration too. Refer to the Hugo configuration documentation for details.\nSet your configuration publish folder to _site # The default is for <PERSON><PERSON><PERSON> to publish to _site and for <PERSON> to publish to public. If, like me, you have _site mapped to a git submodule on the gh-pages branch, you&rsquo;ll want to do one of two alternatives:\nChange your submodule to point to map gh-pages to public instead of _site (recommended).\ngit submodule deinit _site git rm _site git submodule add -b gh-pages **************:your-username/your-repo.git public Or, change the Hugo configuration to use _site instead of public.\n{ .. &quot;publishdir&quot;: &quot;_site&quot;, .. } Convert Jekyll templates to Hugo templates # That&rsquo;s the bulk of the work right here. The documentation is your friend. You should refer to Jekyll&rsquo;s template documentation if you need to refresh your memory on how you built your blog and <PERSON>&rsquo;s template to learn <PERSON>&rsquo;s way.\nAs a single reference data point, converting my templates for heyitsalex.net took me no more than a few hours.\nConvert Jekyll plugins to <PERSON> shortcodes # Jekyll has plugins; <PERSON> has shortcodes. It&rsquo;s fairly trivial to do a port.\nImplementation # As an example, I was using a custom image_tag plugin to generate figures with caption when running Jekyll. As I read about shortcodes, I found Hugo had a nice built-in shortcode that does exactly the same thing.\nJekyll&rsquo;s plugin:\nmodule Jekyll class ImageTag &lt; Liquid::Tag @url = nil @caption = nil @class = nil @link = nil // Patterns IMAGE_URL_WITH_CLASS_AND_CAPTION = IMAGE_URL_WITH_CLASS_AND_CAPTION_AND_LINK = /(\\w+)(\\s+)((https?:\\/\\/|\\/)(\\S+))(\\s+)&quot;(.*?)&quot;(\\s+)-&gt;((https?:\\/\\/|\\/)(\\S+))(\\s*)/i IMAGE_URL_WITH_CAPTION = /((https?:\\/\\/|\\/)(\\S+))(\\s+)&quot;(.*?)&quot;/i IMAGE_URL_WITH_CLASS = /(\\w+)(\\s+)((https?:\\/\\/|\\/)(\\S+))/i IMAGE_URL = /((https?:\\/\\/|\\/)(\\S+))/i def initialize(tag_name, markup, tokens) super if markup =~ IMAGE_URL_WITH_CLASS_AND_CAPTION_AND_LINK @class = $1 @url = $3 @caption = $7 @link = $9 elsif markup =~ IMAGE_URL_WITH_CLASS_AND_CAPTION @class = $1 @url = $3 @caption = $7 elsif markup =~ IMAGE_URL_WITH_CAPTION @url = $1 @caption = $5 elsif markup =~ IMAGE_URL_WITH_CLASS @class = $1 @url = $3 elsif markup =~ IMAGE_URL @url = $1 end end def render(context) if @class source = &quot;&lt;figure class='#{@class}'&gt;&quot; else source = &quot;&lt;figure&gt;&quot; end if @link source += &quot;&lt;a href=\\&quot;#{@link}\\&quot;&gt;&quot; end source += &quot;&lt;img src=\\&quot;#{@url}\\&quot;&gt;&quot; if @link source += &quot;&lt;/a&gt;&quot; end source += &quot;&lt;figcaption&gt;#{@caption}&lt;/figcaption&gt;&quot; if @caption source += &quot;&lt;/figure&gt;&quot; source end end end Liquid::Template.register_tag('image', Jekyll::ImageTag) is written as this Hugo shortcode:\n&lt;!-- image --&gt; &lt;figure {{ with .Get &quot;class&quot; }}class=&quot;{{.}}&quot;{{ end }}&gt; {{ with .Get &quot;link&quot;}}&lt;a href=&quot;{{.}}&quot;&gt;{{ end }} &lt;img src=&quot;{{ .Get &quot;src&quot; }}&quot; {{ if or (.Get &quot;alt&quot;) (.Get &quot;caption&quot;) }}alt=&quot;{{ with .Get &quot;alt&quot;}}{{.}}{{else}}{{ .Get &quot;caption&quot; }}{{ end }}&quot;{{ end }} /&gt; {{ if .Get &quot;link&quot;}}&lt;/a&gt;{{ end }} {{ if or (or (.Get &quot;title&quot;) (.Get &quot;caption&quot;)) (.Get &quot;attr&quot;)}} &lt;figcaption&gt;{{ if isset .Params &quot;title&quot; }} {{ .Get &quot;title&quot; }}{{ end }} {{ if or (.Get &quot;caption&quot;) (.Get &quot;attr&quot;)}}&lt;p&gt; {{ .Get &quot;caption&quot; }} {{ with .Get &quot;attrlink&quot;}}&lt;a href=&quot;{{.}}&quot;&gt; {{ end }} {{ .Get &quot;attr&quot; }} {{ if .Get &quot;attrlink&quot;}}&lt;/a&gt; {{ end }} &lt;/p&gt; {{ end }} &lt;/figcaption&gt; {{ end }} &lt;/figure&gt; &lt;!-- image --&gt; Usage # I simply changed:\n{% image full http://farm5.staticflickr.com/4136/4829260124_57712e570a_o_d.jpg &quot;One of my favorite touristy-type photos. I secretly waited for the good light while we were &quot;having fun&quot; and took this. Only regret: a stupid pole in the top-left corner of the frame I had to clumsily get rid of at post-processing.&quot; -&gt;http://www.flickr.com/photos/alexnormand/4829260124/in/set-72157624547713078/ %} to this (this example uses a slightly extended version named fig, different than the built-in figure):\n{{% fig class=&quot;full&quot; src=&quot;http://farm5.staticflickr.com/4136/4829260124_57712e570a_o_d.jpg&quot; title=&quot;One of my favorite touristy-type photos. I secretly waited for the good light while we were having fun and took this. Only regret: a stupid pole in the top-left corner of the frame I had to clumsily get rid of at post-processing.&quot; link=&quot;http://www.flickr.com/photos/alexnormand/4829260124/in/set-72157624547713078/&quot; %}} As a bonus, the shortcode named parameters are, arguably, more readable.\nFinishing touches # Fix content # Depending on the amount of customization that was done with each post with Jekyll, this step will require more or less effort. There are no hard and fast rules here except that hugo server --watch is your friend. Test your changes and fix errors as needed.\nClean up # You&rsquo;ll want to remove the Jekyll configuration at this point. If you have anything else that isn&rsquo;t used, delete it.\nA practical example in a diff # Hey, it&rsquo;s Alex was migrated in less than a father-with-kids day from Jekyll to Hugo. You can see all the changes (and screw-ups) by looking at this diff.\n"}, {"id": 77, "href": "/docs/pepgenx/frequently_asked_questions/", "title": "Frequently Asked Questions", "section": "Welcome to PepGenX", "content": " Frequently Asked Questions # Ferre hinnitibus erat accipitrem dixi Troiae tollens # Lorem markdownum, a quoque nutu est quodcumque mandasset veluti. Passim\ninportuna totidemque nympha fert; repetens pendent, poenarum guttura sed vacet\nnon, mortali undas. Omnis pharetramque gramen portentificisque membris servatum\nnovabis fallit de nubibus atque silvas mihi. Dixit repetitaque Quid; verrit\nlonga; sententia mandat quascumque nescio\nsolebat litore; noctes. Hostem haerentem circuit\nplenaque tamen.\nPedum ne indigenae finire invergens carpebat Velit posses summoque De fumos illa foret Est simul fameque tauri qua ad # Locum nullus nisi vomentes. Ab Persea sermone vela, miratur aratro; eandem\nArgolicas gener.\nMe sol # Nec dis certa fuit socer, Nonacria dies manet tacitaque sibi? Sucis est\niactata Castrumque iudex, et iactato quoque terraeque es tandem et maternos\nvittis. Lumina litus bene poenamque animos callem ne tuas in leones illam dea\ncadunt genus, et pleno nunc in quod. Anumque crescentesque sanguinis\nprogenies nuribus rustica tinguet. Pater\nomnes liquido creditis noctem.\nif (mirrored(icmp_dvd_pim, 3, smbMirroredHard) != lion(clickImportQueue, viralItunesBalancing, bankruptcy_file_pptp)) { file += ip_cybercrime_suffix; } if (runtimeSmartRom == netMarketingWord) { virusBalancingWin *= scriptPromptBespoke + raster(post_drive, windowsSli); cd = address_hertz_trojan; soap_ccd.pcbServerGigahertz(asp_hardware_isa, offlinePeopleware, nui); } else { megabyte.api = modem_flowchart - web + syntaxHalftoneAddress; } if (3 &lt; mebibyteNetworkAnimated) { pharming_regular_error *= jsp_ribbon + algorithm * recycleMediaKindle( dvrSyntax, cdma); adf_sla *= hoverCropDrive; templateNtfs = -1 - vertical; } else { expressionCompressionVariable.bootMulti = white_eup_javascript( table_suffix); guidPpiPram.tracerouteLinux += rtfTerabyteQuicktime(1, managementRosetta(webcamActivex), 740874); } var virusTweetSsl = nullGigo; Trepident sitimque # Sentiet et ferali errorem fessam, coercet superbus, Ascaniumque in pennis\nmediis; dolor? Vidit imi Aeacon perfida propositos adde, tua Somni Fluctibus\nerrante lustrat non.\nTamen inde, vos videt e flammis Scythica parantem rupisque pectora umbras. Haec\nficta canistris repercusso simul ego aris Dixit! Esse Fama trepidare hunc\ncrescendo vigor ululasse vertice exspatiantur celer tepidique petita aversata\noculis iussa est me ferro.\n"}, {"id": 78, "href": "/docs/pepgenx/development_frameworks/llm_frameworks/semantic_kernel/", "title": "<PERSON><PERSON><PERSON>", "section": "LLM Frameworks", "content": " Semantic Kernel # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 79, "href": "/docs/pepgenx/development_frameworks/llm_frameworks/crewai/", "title": "Crewai", "section": "LLM Frameworks", "content": " Crewai # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 80, "href": "/docs/pepgenx/development_frameworks/llm_frameworks/txtai/", "title": "txtai", "section": "LLM Frameworks", "content": " txtai # Cognita laeva illo fracta # Lorem markdownum pavent auras, surgit nunc cingentibus libet Laomedonque que\nest. Pastor An arbor filia foedat, ne fugit\naliter, per. <PERSON><PERSON><PERSON> illas et\ncallida neptem est Oresitrophos caput, dentibus est venit. Tenet reddite\nfamuli praesentem fortibus, quaeque vis foret si\nfrondes gelidos gravidae circumtulit inpulit armenta\nnativum.\nTe at cruciabere vides rubentis manebo Maturuit in praetemptat ruborem ignara postquam habitasse Subitarum supplevit quoque fontesque venabula spretis modo Montis tot est mali quasque gravis Quinquennem domus arsit ipse Pellem turis pugnabant locavit Natus quaerere # Pectora et sine mulcere, coniuge dum tincta incurvae. Quis iam; est dextra\nPeneosque, metuis a verba, primo. Illa sed colloque suis: magno: gramen, aera\nexcutiunt concipit.\nPhrygiae petendo suisque extimuit, super, pars quod audet! Turba negarem.\nFuerat attonitus; et dextra retinet sidera ulnas undas instimulat vacuae\ngeneris? Agnus dabat et ignotis dextera, sic tibi pacis feriente at mora\neuhoeque comites hostem vestras Phineus. Vultuque sanguine dominoque metuit\nrisi fama vergit summaque meus clarissimus\nartesque tinguebat successor nominis cervice caelicolae.\nLimitibus misere sit # Aurea non fata repertis praerupit feruntur simul, meae hosti lentaque citius\nlevibus, cum sede dixit, Phaethon texta. Albentibus summos multifidasque\niungitur loquendi an pectore, mihi ursaque omnia adfata, aeno parvumque in animi\nperlucentes. Epytus agis ait vixque clamat ornum adversam spondet, quid sceptra\nipsum est. Reseret nec; saeva suo passu debentia linguam terga et aures et\ncervix de ubera. Coercet gelidumque manus,\ndoluit volvitur induta?\nEnim sua # Iuvenilior filia inlustre templa quidem herbis permittat trahens huic. In\ncruribus proceres sole crescitque fata, quos quos; merui maris se non tamen\nin, mea.\nGermana aves pignus tecta # Mortalia rudibusque caelum cognosceret tantum aquis redito felicior texit, nec,\naris parvo acre. Me parum contulerant multi tenentem, gratissime suis; vultum tu\noccupat deficeret corpora, sonum. E Actaea inplevit Phinea concepit nomenque\npotest sanguine captam nulla et, in duxisses campis non; mercede. Dicere cur\nLeucothoen obitum?\nPostibus mittam est nubibus principium pluma, exsecratur facta et. Iunge\nMnemonidas pallamque pars; vere restitit alis flumina quae quoque, est\nignara infestus Pyrrha. Di ducis terris maculatum At sede praemia manes\nnullaque!\n"}, {"id": 81, "href": "/docs/pepgenxdev/shortcodes/hugo/section/", "title": "Section", "section": "<PERSON>", "content": " Section # Section renders pages in section as definition list, using title and description. Optional param summary can be used to show or hide page summary\nExample # {{&lt; section [summary] &gt;}} First Page First page # Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Second Page Second Page # Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. "}, {"id": 82, "href": "/posts/goisforlovers/", "title": "(Hu)go Template Primer", "section": "Updates", "content": "<PERSON> uses the excellent Go html/template library for\nits template engine. It is an extremely lightweight engine that provides a very\nsmall amount of logic. In our experience that it is just the right amount of\nlogic to be able to create a good static website. If you have used other\ntemplate systems from different languages or frameworks you will find a lot of\nsimilarities in Go templates.\nThis document is a brief primer on using Go templates. The Go docs\nprovide more details.\nIntroduction to Go Templates # Go templates provide an extremely simple template language. It adheres to the\nbelief that only the most basic of logic belongs in the template or view layer.\nOne consequence of this simplicity is that Go templates parse very quickly.\nA unique characteristic of Go templates is they are content aware. Variables and\ncontent will be sanitized depending on the context of where they are used. More\ndetails can be found in the Go docs.\nBasic Syntax # Golang templates are HTML files with the addition of variables and\nfunctions.\nGo variables and functions are accessible within {{ }}\nAccessing a predefined variable &ldquo;foo&rdquo;:\n{{ foo }} Parameters are separated using spaces\nCalling the add function with input of 1, 2:\n{{ add 1 2 }} Methods and fields are accessed via dot notation\nAccessing the Page Parameter &ldquo;bar&rdquo;\n{{ .Params.bar }} Parentheses can be used to group items together\n{{ if or (isset .Params &quot;alt&quot;) (isset .Params &quot;caption&quot;) }} Caption {{ end }} Variables # Each Go template has a struct (object) made available to it. In hugo each\ntemplate is passed either a page or a node struct depending on which type of\npage you are rendering. More details are available on the\nvariables page.\nA variable is accessed by referencing the variable name.\n&lt;title&gt;{{ .Title }}&lt;/title&gt; Variables can also be defined and referenced.\n{{ $address := &quot;123 Main St.&quot;}} {{ $address }} Functions # Go template ship with a few functions which provide basic functionality. The Go\ntemplate system also provides a mechanism for applications to extend the\navailable functions with their own. Hugo template\nfunctions provide some additional functionality we believe\nare useful for building websites. Functions are called by using their name\nfollowed by the required parameters separated by spaces. Template\nfunctions cannot be added without recompiling hugo.\nExample:\n{{ add 1 2 }} Includes # When including another template you will pass to it the data it will be\nable to access. To pass along the current context please remember to\ninclude a trailing dot. The templates location will always be starting at\nthe /layout/ directory within Hugo.\nExample:\n{{ template &quot;chrome/header.html&quot; . }} Logic # Go templates provide the most basic iteration and conditional logic.\nIteration # Just like in Go, the Go templates make heavy use of range to iterate over\na map, array or slice. The following are different examples of how to use\nrange.\nExample 1: Using Context\n{{ range array }} {{ . }} {{ end }} Example 2: Declaring value variable name\n{{range $element := array}} {{ $element }} {{ end }} Example 2: Declaring key and value variable name\n{{range $index, $element := array}} {{ $index }} {{ $element }} {{ end }} Conditionals # If, else, with, or, &amp; and provide the framework for handling conditional\nlogic in Go Templates. Like range, each statement is closed with end.\nGo Templates treat the following values as false:\nfalse 0 any array, slice, map, or string of length zero Example 1: If\n{{ if isset .Params &quot;title&quot; }}&lt;h4&gt;{{ index .Params &quot;title&quot; }}&lt;/h4&gt;{{ end }} Example 2: If -&gt; Else\n{{ if isset .Params &quot;alt&quot; }} {{ index .Params &quot;alt&quot; }} {{else}} {{ index .Params &quot;caption&quot; }} {{ end }} Example 3: And &amp; Or\n{{ if and (or (isset .Params &quot;title&quot;) (isset .Params &quot;caption&quot;)) (isset .Params &quot;attr&quot;)}} Example 4: With\nAn alternative way of writing &ldquo;if&rdquo; and then referencing the same value\nis to use &ldquo;with&rdquo; instead. With rebinds the context . within its scope,\nand skips the block if the variable is absent.\nThe first example above could be simplified as:\n{{ with .Params.title }}&lt;h4&gt;{{ . }}&lt;/h4&gt;{{ end }} Example 5: If -&gt; Else If\n{{ if isset .Params &quot;alt&quot; }} {{ index .Params &quot;alt&quot; }} {{ else if isset .Params &quot;caption&quot; }} {{ index .Params &quot;caption&quot; }} {{ end }} Pipes # One of the most powerful components of Go templates is the ability to\nstack actions one after another. This is done by using pipes. Borrowed\nfrom unix pipes, the concept is simple, each pipeline&rsquo;s output becomes the\ninput of the following pipe.\nBecause of the very simple syntax of Go templates, the pipe is essential\nto being able to chain together function calls. One limitation of the\npipes is that they only can work with a single value and that value\nbecomes the last parameter of the next pipeline.\nA few simple examples should help convey how to use the pipe.\nExample 1 :\n{{ if eq 1 1 }} Same {{ end }} is the same as\n{{ eq 1 1 | if }} Same {{ end }} It does look odd to place the if at the end, but it does provide a good\nillustration of how to use the pipes.\nExample 2 :\n{{ index .Params &quot;disqus_url&quot; | html }} Access the page parameter called &ldquo;disqus_url&rdquo; and escape the HTML.\nExample 3 :\n{{ if or (or (isset .Params &quot;title&quot;) (isset .Params &quot;caption&quot;)) (isset .Params &quot;attr&quot;)}} Stuff Here {{ end }} Could be rewritten as\n{{ isset .Params &quot;caption&quot; | or isset .Params &quot;title&quot; | or isset .Params &quot;attr&quot; | if }} Stuff Here {{ end }} Context (aka. the dot) # The most easily overlooked concept to understand about Go templates is that {{ . }}\nalways refers to the current context. In the top level of your template this\nwill be the data set made available to it. Inside of a iteration it will have\nthe value of the current item. When inside of a loop the context has changed. .\nwill no longer refer to the data available to the entire page. If you need to\naccess this from within the loop you will likely want to set it to a variable\ninstead of depending on the context.\nExample:\n{{ $title := .Site.Title }} {{ range .Params.tags }} &lt;li&gt; &lt;a href=&quot;{{ $baseurl }}/tags/{{ . | urlize }}&quot;&gt;{{ . }}&lt;/a&gt; - {{ $title }} &lt;/li&gt; {{ end }} Notice how once we have entered the loop the value of {{ . }} has changed. We\nhave defined a variable outside of the loop so we have access to it from within\nthe loop.\nHugo Parameters # Hugo provides the option of passing values to the template language\nthrough the site configuration (for sitewide values), or through the meta\ndata of each specific piece of content. You can define any values of any\ntype (supported by your front matter/config format) and use them however\nyou want to inside of your templates.\nUsing Content (page) Parameters # In each piece of content you can provide variables to be used by the\ntemplates. This happens in the front matter.\nAn example of this is used in this documentation site. Most of the pages\nbenefit from having the table of contents provided. Sometimes the TOC just\ndoesn&rsquo;t make a lot of sense. We&rsquo;ve defined a variable in our front matter\nof some pages to turn off the TOC from being displayed.\nHere is the example front matter:\n--- title: &#34;Permalinks&#34; date: &#34;2013-11-18&#34; aliases: - &#34;/doc/permalinks/&#34; groups: [&#34;extras&#34;] groups_weight: 30 notoc: true --- Here is the corresponding code inside of the template:\n{{ if not .Params.notoc }} &lt;div id=&quot;toc&quot; class=&quot;well col-md-4 col-sm-6&quot;&gt; {{ .TableOfContents }} &lt;/div&gt; {{ end }} Using Site (config) Parameters # In your top-level configuration file (eg, config.yaml) you can define site\nparameters, which are values which will be available to you in chrome.\nFor instance, you might declare:\nparams: CopyrightHTML: &#34;Copyright &amp;#xA9; 2013 John Doe. All Rights Reserved.&#34; TwitterUser: &#34;spf13&#34; SidebarRecentLimit: 5 Within a footer layout, you might then declare a &lt;footer&gt; which is only\nprovided if the CopyrightHTML parameter is provided, and if it is given,\nyou would declare it to be HTML-safe, so that the HTML entity is not escaped\nagain. This would let you easily update just your top-level config file each\nJanuary 1st, instead of hunting through your templates.\n{{if .Site.Params.CopyrightHTML}}&lt;footer&gt; &lt;div class=&#34;text-center&#34;&gt;{{.Site.Params.CopyrightHTML | safeHtml}}&lt;/div&gt; &lt;/footer&gt;{{end}} An alternative way of writing the &ldquo;if&rdquo; and then referencing the same value\nis to use &ldquo;with&rdquo; instead. With rebinds the context . within its scope,\nand skips the block if the variable is absent:\n{{with .Site.Params.TwitterUser}}&lt;span class=&#34;twitter&#34;&gt; &lt;a href=&#34;https://twitter.com/{{.}}&#34; rel=&#34;author&#34;&gt; &lt;img src=&#34;/images/twitter.png&#34; width=&#34;48&#34; height=&#34;48&#34; title=&#34;Twitter: {{.}}&#34; alt=&#34;Twitter&#34;&gt;&lt;/a&gt; &lt;/span&gt;{{end}} Finally, if you want to pull &ldquo;magic constants&rdquo; out of your layouts, you can do\nso, such as in this example:\n&lt;nav class=&#34;recent&#34;&gt; &lt;h1&gt;Recent Posts&lt;/h1&gt; &lt;ul&gt;{{range first .Site.Params.SidebarRecentLimit .Site.Recent}} &lt;li&gt;&lt;a href=&#34;{{.RelPermalink}}&#34;&gt;{{.Title}}&lt;/a&gt;&lt;/li&gt; {{end}}&lt;/ul&gt; &lt;/nav&gt; "}, {"id": 83, "href": "/posts/hugoisforlovers/", "title": "Getting Started with <PERSON>", "section": "Updates", "content": " Step 1. Install Hugo # Go to <PERSON> releases and download the\nappropriate version for your OS and architecture.\nSave it somewhere specific as we will be using it in the next step.\nMore complete instructions are available at Install Hugo\nStep 2. Build the Docs # Hugo has its own example site which happens to also be the documentation site\nyou are reading right now.\nFollow the following steps:\nClone the Hugo repository Go into the repo Run hugo in server mode and build the docs Open your browser to http://localhost:1313 Corresponding pseudo commands:\ngit clone https://github.com/spf13/hugo cd hugo /path/to/where/you/installed/hugo server --source=./docs &gt; 29 pages created &gt; 0 tags index created &gt; in 27 ms &gt; Web Server is available at http://localhost:1313 &gt; Press ctrl+c to stop Once you&rsquo;ve gotten here, follow along the rest of this page on your local build.\nStep 3. Change the docs site # Stop the Hugo process by hitting Ctrl+C.\nNow we are going to run hugo again, but this time with hugo in watch mode.\n/path/to/hugo/from/step/1/hugo server --source=./docs --watch &gt; 29 pages created &gt; 0 tags index created &gt; in 27 ms &gt; Web Server is available at http://localhost:1313 &gt; Watching for changes in /Users/<USER>/Code/hugo/docs/content &gt; Press ctrl+c to stop Open your favorite editor and change one of the source\ncontent pages. How about changing this very file to fix the typo. How about changing this very file to fix the typo.\nContent files are found in docs/content/. Unless otherwise specified, files\nare located at the same relative location as the url, in our case\ndocs/content/overview/quickstart.md.\nChange and save this file.. Notice what happened in your terminal.\n&gt; Change detected, rebuilding site &gt; 29 pages created &gt; 0 tags index created &gt; in 26 ms Refresh the browser and observe that the typo is now fixed.\nNotice how quick that was. Try to refresh the site before it&rsquo;s finished building. I double dare you.\nHaving nearly instant feedback enables you to have your creativity flow without waiting for long builds.\nStep 4. Have fun # The best way to learn something is to play with it.\n"}, {"id": 84, "href": "/docs/pepgenxdev/shortcodes/hugo/section/first-page/", "title": "First Page", "section": "Section", "content": " First page # Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\nDuis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n"}, {"id": 85, "href": "/docs/pepgenxdev/shortcodes/hugo/section/second-page/", "title": "Second Page", "section": "Section", "content": " Second Page # Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\nDuis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n"}, {"id": 86, "href": "/docs/pepgenxdev/shortcodes/markdown/blockquotes/", "title": "Blockquotes", "section": "<PERSON><PERSON>", "content": " Blockquotes # To create a blockquote, add a &gt; in front of a paragraph.\n&gt; <PERSON> followed her through many of the beautiful rooms in her castle. The rendered output looks like this:\n<PERSON> followed her through many of the beautiful rooms in her castle.\nBlockquotes with Multiple Paragraphs # Blockquotes can contain multiple paragraphs. Add a &gt; on the blank lines between the paragraphs.\n&gt; <PERSON> followed her through many of the beautiful rooms in her castle. &gt; &gt; The Witch bade her clean the pots and kettles and sweep the floor and keep the fire fed with wood. The rendered output looks like this:\n<PERSON> followed her through many of the beautiful rooms in her castle.\nThe Witch bade her clean the pots and kettles and sweep the floor and keep the fire fed with wood.\nNested Blockquotes # Blockquotes can be nested. Add a &gt;&gt; in front of the paragraph you want to nest.\n&gt; <PERSON> followed her through many of the beautiful rooms in her castle. &gt; &gt;&gt; The Witch bade her clean the pots and kettles and sweep the floor and keep the fire fed with wood. The rendered output looks like this:\n<PERSON> followed her through many of the beautiful rooms in her castle.\nThe Witch bade her clean the pots and kettles and sweep the floor and keep the fire fed with wood.\nBlockquotes with Other Elements # Blockquotes can contain other Markdown formatted elements. Not all elements can be used — you&rsquo;ll need to experiment to see which ones work.\n&gt; #### The quarterly results look great! &gt; &gt; - Revenue was off the chart. &gt; - Profits were higher than ever. &gt; &gt; *Everything* is going according to **plan**. The rendered output looks like this:\nThe quarterly results look great! # Revenue was off the chart. Profits were higher than ever. Everything is going according to plan.\nBlockquotes Best Practices # For compatibility, put blank lines before and after blockquotes.\n✅&nbsp; Do this ❌&nbsp; Don't do this Try to put a blank line before...\n> This is a blockquote\n...and after a blockquote. Without blank lines, this might not look right.\n> This is a blockquote\nDon't do this! "}, {"id": 87, "href": "/docs/pepgenxdev/shortcodes/markdown/bold/", "title": "Bold", "section": "<PERSON><PERSON>", "content": " Bold # To bold text, add two asterisks or underscores before and after a word or phrase. To bold the middle of a word for emphasis, add two asterisks without spaces around the letters.\nMarkdown HTML Rendered Output I just love **bold text**. I just love &lt;strong&gt;bold text&lt;/strong&gt;. I just love bold text. I just love __bold text__. I just love &lt;strong&gt;bold text&lt;/strong&gt;. I just love bold text. Love**is**bold Love&lt;strong&gt;is&lt;/strong&gt;bold Loveisbold Bold Best Practices # Markdown applications don&rsquo;t agree on how to handle underscores in the middle of a word. For compatibility, use asterisks to bold the middle of a word for emphasis.\n✅&nbsp; Do this ❌&nbsp; Don't do this Love**is**bold Love__is__bold "}, {"id": 88, "href": "/docs/pepgenxdev/shortcodes/hugo/buttons/", "title": "Buttons", "section": "<PERSON>", "content": " Buttons # Buttons are styled links that can lead to local page or external link.\nExample # {{&lt; button relref=&#34;/&#34; [class=&#34;...&#34;] &gt;}}Get Home{{&lt; /button &gt;}} {{&lt; button href=&#34;https://github.com/alex-shpak/hugo-book&#34; &gt;}}Contribute{{&lt; /button &gt;}} Get Home Contribute "}, {"id": 89, "href": "/docs/pepgenxdev/shortcodes/markdown/code/", "title": "Code", "section": "<PERSON><PERSON>", "content": " Code # To denote a word or phrase as code, enclose it in backticks (`).\nMarkdown HTML Rendered Output At the command prompt, type `nano`. At the command prompt, type &lt;code&gt;nano&lt;/code&gt;. At the command prompt, type nano. Escaping Backticks # If the word or phrase you want to denote as code includes one or more backticks, you can escape it by enclosing the word or phrase in double backticks (``).\nMarkdown HTML Rendered Output ``Use `code` in your Markdown file.`` &lt;code&gt;Use `code` in your Markdown file.&lt;/code&gt; Use `code` in your Markdown file. Code Blocks # To create code blocks, indent every line of the block by at least four spaces or one tab.\n&lt;html&gt; &lt;head&gt; &lt;/head&gt; &lt;/html&gt; The rendered output looks like this:\n&lt;html&gt; &lt;head&gt; &lt;/head&gt; &lt;/html&gt; Note: To create code blocks without indenting lines, use fenced code blocks. "}, {"id": 90, "href": "/docs/pepgenxdev/shortcodes/hugo/columns/", "title": "Columns", "section": "<PERSON>", "content": " Columns # Columns help organize shorter pieces of content horizontally for readability.\n{{&lt; columns &gt;}} &lt;!-- begin columns block --&gt; # Left Content Lorem markdownum insigne... &lt;---&gt; &lt;!-- magic separator, between columns --&gt; # Mid Content Lorem markdownum insigne... &lt;---&gt; &lt;!-- magic separator, between columns --&gt; # Right Content Lorem markdownum insigne... {{&lt; /columns &gt;}} Example # Left Content # Lorem markdownum insigne. Olympo signis Delphis! Retexi Nereius nova develat\nstringit, frustra Saturnius uteroque inter! Oculis non ritibus Telethusa\nprotulit, sed sed aere valvis inhaesuro Pallas animam: qui quid, ignes.\nMiseratus fonte Ditis conubia.\nMid Content # Lorem markdownum insigne. Olympo signis Delphis! Retexi Nereius nova develat\nstringit, frustra Saturnius uteroque inter!\nRight Content # Lorem markdownum insigne. Olympo signis Delphis! Retexi Nereius nova develat\nstringit, frustra Saturnius uteroque inter! Oculis non ritibus Telethusa\nprotulit, sed sed aere valvis inhaesuro Pallas animam: qui quid, ignes.\nMiseratus fonte Ditis conubia.\n"}, {"id": 91, "href": "/docs/pepgenxdev/shortcodes/hugo/details/", "title": "Details", "section": "<PERSON>", "content": " Details # Details shortcode is a helper for details html5 element. It is going to replace expand shortcode.\nExample # {{&lt; details &#34;Title&#34; [open] &gt;}} ## Markdown content Lorem markdownum insigne... {{&lt; /details &gt;}} {{&lt; details title=&#34;Title&#34; open=true &gt;}} ## Markdown content Lorem markdownum insigne... {{&lt; /details &gt;}} Title Markdown content # Lorem markdownum insigne&hellip;\n"}, {"id": 92, "href": "/docs/pepgenxdev/shortcodes/markdown/emoji/", "title": "<PERSON><PERSON><PERSON>", "section": "<PERSON><PERSON>", "content": " Emoji # There are two ways to add emoji to Markdown files: copy and paste the emoji into your Markdown-formatted text, or type emoji shortcodes.\nCopying and Pasting Emoji # In most cases, you can simply copy an emoji from a source like Emojipedia and paste it into your document. Many Markdown applications will automatically display the emoji in the Markdown-formatted text. The HTML and PDF files you export from your Markdown application should display the emoji.\nUsing Emoji Shortcodes # Some Markdown applications allow you to insert emoji by typing emoji shortcodes. These begin and end with a colon and include the name of an emoji.\nGone camping! :tent: Be back soon. That is so funny! :joy: The rendered output looks like this:\nGone camping! ⛺ Be back soon.\nThat is so funny! 😂\nNote: You can use this list of emoji shortcodes, but keep in mind that emoji shortcodes vary from application to application. Refer to your Markdown application's documentation for more information. "}, {"id": 93, "href": "/docs/pepgenxdev/shortcodes/markdown/emphasis/", "title": "Emphasis", "section": "<PERSON><PERSON>", "content": " Bold and Italic # To emphasize text with bold and italics at the same time, add three asterisks or underscores before and after a word or phrase. To bold and italicize the middle of a word for emphasis, add three asterisks without spaces around the letters.\nMarkdown HTML Rendered Output This text is ***really important***. This text is &lt;em&gt;&lt;strong&gt;really important&lt;/strong&gt;&lt;/em&gt;. This text is really important. This text is ___really important___. This text is &lt;em&gt;&lt;strong&gt;really important&lt;/strong&gt;&lt;/em&gt;. This text is really important. This text is __*really important*__. This text is &lt;em&gt;&lt;strong&gt;really important&lt;/strong&gt;&lt;/em&gt;. This text is really important. This text is **_really important_**. This text is &lt;em&gt;&lt;strong&gt;really important&lt;/strong&gt;&lt;/em&gt;. This text is really important. This is really***very***important text. This is really&lt;em&gt;&lt;strong&gt;very&lt;/strong&gt;&lt;/em&gt;important text. This is reallyveryimportant text. Note: The order of the em and strong tags might be reversed depending on the Markdown processor you're using. Bold and Italic Best Practices # Markdown applications don&rsquo;t agree on how to handle underscores in the middle of a word. For compatibility, use asterisks to bold and italicize the middle of a word for emphasis.\n✅&nbsp; Do this ❌&nbsp; Don't do this This is really***very***important text. This is really___very___important text. "}, {"id": 94, "href": "/docs/pepgenxdev/shortcodes/markdown/escaping-characters/", "title": "Escaping Characters", "section": "<PERSON><PERSON>", "content": " Escaping Characters # To display a literal character that would otherwise be used to format text in a Markdown document, add a backslash (\\) in front of the character.\n\\* Without the backslash, this would be a bullet in an unordered list. The rendered output looks like this:\n* Without the backslash, this would be a bullet in an unordered list.\nCharacters You Can Escape # You can use a backslash to escape the following characters.\nCharacter Name \\ backslash ` backtick (see also escaping backticks in code) * asterisk _ underscore { } curly braces [ ] brackets < > angle brackets ( ) parentheses # pound sign + plus sign - minus sign (hyphen) . dot ! exclamation mark | pipe (see also escaping pipe in tables) "}, {"id": 95, "href": "/docs/pepgenxdev/shortcodes/hugo/expand/", "title": "Expand", "section": "<PERSON>", "content": " Expand # Expand shortcode can help to decrease clutter on screen by hiding part of text. Expand content by clicking on it.\nExample # Default # {{&lt; expand &gt;}} ## Markdown content Lorem markdownum insigne... {{&lt; /expand &gt;}} Expand ↕ Markdown content # Lorem markdownum insigne&hellip;\nWith Custom Label # {{&lt; expand &#34;Custom Label&#34; &#34;...&#34; &gt;}} ## Markdown content Lorem markdownum insigne... {{&lt; /expand &gt;}} Custom Label ... Markdown content # Lorem markdownum insigne. Olympo signis Delphis! Retexi Nereius nova develat\nstringit, frustra Saturnius uteroque inter! Oculis non ritibus Telethusa\nprotulit, sed sed aere valvis inhaesuro Pallas animam: qui quid, ignes.\nMiseratus fonte Ditis conubia.\n"}, {"id": 96, "href": "/docs/pepgenxdev/shortcodes/markdown/fenced-code-blocks/", "title": "Fenced Code Blocks", "section": "<PERSON><PERSON>", "content": " Fenced Code Blocks # The basic Markdown syntax allows you to create code blocks by indenting lines by four spaces or one tab. If you find that inconvenient, try using fenced code blocks. Depending on your Markdown processor or editor, you&rsquo;ll use three backticks (```) or three tildes (~~~) on the lines before and after the code block. The best part? You don&rsquo;t have to indent any lines!\n``` { &#34;firstName&#34;: &#34;<PERSON>&#34;, &#34;lastName&#34;: &#34;<PERSON>&#34;, &#34;age&#34;: 25 } ``` The rendered output looks like this:\n{ &#34;firstName&#34;: &#34;John&#34;, &#34;lastName&#34;: &#34;Smith&#34;, &#34;age&#34;: 25 } Tip: Need to display backticks inside a code block? See this section to learn how to escape them. Syntax Highlighting # Many Markdown processors support syntax highlighting for fenced code blocks. This feature allows you to add color highlighting for whatever language your code was written in. To add syntax highlighting, specify a language next to the backticks before the fenced code block.\n```json { &#34;firstName&#34;: &#34;<PERSON>&#34;, &#34;lastName&#34;: &#34;<PERSON>&#34;, &#34;age&#34;: 25 } ``` The rendered output looks like this:\n{ &#34;firstName&#34;: &#34;<PERSON>&#34;, &#34;lastName&#34;: &#34;Smith&#34;, &#34;age&#34;: 25 } "}, {"id": 97, "href": "/docs/pepgenxdev/shortcodes/markdown/footnotes/", "title": "Footnotes", "section": "<PERSON><PERSON>", "content": " Footnotes # Footnotes allow you to add notes and references without cluttering the body of the document. When you create a footnote, a superscript number with a link appears where you added the footnote reference. Readers can click the link to jump to the content of the footnote at the bottom of the page.\nTo create a footnote reference, add a caret and an identifier inside brackets ([^1]). Identifiers can be numbers or words, but they can&rsquo;t contain spaces or tabs. Identifiers only correlate the footnote reference with the footnote itself — in the output, footnotes are numbered sequentially.\nAdd the footnote using another caret and number inside brackets with a colon and text ([^1]: My footnote.). You don&rsquo;t have to put footnotes at the end of the document. You can put them anywhere except inside other elements like lists, block quotes, and tables.\nHere&#39;s a simple footnote,[^1] and here&#39;s a longer one.[^bignote] [^1]: This is the first footnote. [^bignote]: Here&#39;s one with multiple paragraphs and code. Indent paragraphs to include them in the footnote. `{ my code }` Add as many paragraphs as you like. The rendered output looks like this:\nHere&rsquo;s a simple footnote,1 and here&rsquo;s a longer one.2\nThis is the first footnote.&#160;&#x21a9;&#xfe0e;\nHere&rsquo;s one with multiple paragraphs and code.\nIndent paragraphs to include them in the footnote.\n{ my code }\nAdd as many paragraphs as you like.&#160;&#x21a9;&#xfe0e;\n"}, {"id": 98, "href": "/docs/pepgenxdev/shortcodes/markdown/headings/", "title": "Headings", "section": "<PERSON><PERSON>", "content": " Heading # To create a heading, add number signs (#) in front of a word or phrase. The number of number signs you use should correspond to the heading level. For example, to create a heading level three (&lt;h3&gt;), use three number signs (e.g., ### My Header).\nMarkdown HTML Rendered Output # Heading level 1 &lt;h1&gt;Heading level 1&lt;/h1&gt; Heading level 1 ## Heading level 2 &lt;h2&gt;Heading level 2&lt;/h2&gt; Heading level 2 ### Heading level 3 &lt;h3&gt;Heading level 3&lt;/h3&gt; Heading level 3 #### Heading level 4 &lt;h4&gt;Heading level 4&lt;/h4&gt; Heading level 4 ##### Heading level 5 &lt;h5&gt;Heading level 5&lt;/h5&gt; Heading level 5 ###### Heading level 6 &lt;h6&gt;Heading level 6&lt;/h6&gt; Heading level 6 Alternate Syntax # Alternatively, on the line below the text, add any number of == characters for heading level 1 or -- characters for heading level 2.\nMarkdown HTML Rendered Output Heading level 1=============== &lt;h1&gt;Heading level 1&lt;/h1&gt; Heading level 1 Heading level 2--------------- &lt;h2&gt;Heading level 2&lt;/h2&gt; Heading level 2 Heading Best Practices # Markdown applications don&rsquo;t agree on how to handle a missing space between the number signs (#) and the heading name. For compatibility, always put a space between the number signs and the heading name.\n✅&nbsp; Do this ❌&nbsp; Don't do this # Here's a Heading\n#Here's a Heading You should also put blank lines before and after a heading for compatibility.\n✅&nbsp; Do this ❌&nbsp; Don't do this Try to put a blank line before...\n# Heading\n...and after a heading. Without blank lines, this might not look right.\n# Heading\nDon't do this! "}, {"id": 99, "href": "/docs/pepgenxdev/shortcodes/hugo/hints/", "title": "Hints", "section": "<PERSON>", "content": " Hints # Hint shortcode can be used as hint/alerts/notification block.\nThere are 3 colors to choose: info, warning and danger.\n{{&lt; hint [info|warning|danger] &gt;}} **Markdown content** Lorem markdownum insigne. Olympo signis Delphis! Retexi Nereius nova develat stringit, frustra Saturnius uteroque inter! Oculis non ritibus Telethusa {{&lt; /hint &gt;}} Example # Markdown content\nLorem markdownum insigne. Olympo signis Delphis! Retexi Nereius nova develat\nstringit, frustra Saturnius uteroque inter! Oculis non ritibus Telethusa Markdown content\nLorem markdownum insigne. Olympo signis Delphis! Retexi Nereius nova develat\nstringit, frustra Saturnius uteroque inter! Oculis non ritibus Telethusa Markdown content\nLorem markdownum insigne. Olympo signis Delphis! Retexi Nereius nova develat\nstringit, frustra Saturnius uteroque inter! Oculis non ritibus Telethusa "}, {"id": 100, "href": "/docs/pepgenxdev/shortcodes/markdown/horizontal-rules/", "title": "Horizontal Rules", "section": "<PERSON><PERSON>", "content": " Horizontal Rules # To create a horizontal rule, use three or more asterisks (***), dashes (---), or underscores (___) on a line by themselves.\n*** --- _________________ The rendered output of all three looks identical:\nHorizontal Rule Best Practices # For compatibility, put blank lines before and after horizontal rules.\n✅&nbsp; Do this ❌&nbsp; Don't do this Try to put a blank line before...\n---\n...and after a horizontal rule. Without blank lines, this would be a heading.\n---\nDon't do this! "}, {"id": 101, "href": "/docs/pepgenxdev/shortcodes/markdown/images/", "title": "Images", "section": "<PERSON><PERSON>", "content": " Images # To add an image, add an exclamation mark (!), followed by alt text in brackets, and the path or URL to the image asset in parentheses. You can optionally add a title in quotation marks after the path or URL.\n![The San Juan Mountains are beautiful!](/assets/images/san-juan-mountains.jpg &#34;San Juan Mountains&#34;) The rendered output looks like this:\n{% include image.html file=&quot;/assets/images/san-juan-mountains.jpg&quot; alt=&ldquo;The San Juan Mountains are beautiful!&rdquo; title=&ldquo;San Juan Mountains&rdquo; lazy=&ldquo;yes&rdquo; %}\nLinking Images # To add a link to an image, enclose the Markdown for the image in brackets, and then add the link in parentheses.\n[![An old rock in the desert](/assets/images/shiprock.jpg &#34;Shiprock, New Mexico by Beau Rogers&#34;)](https://www.flickr.com/photos/beaurogers/31833779864/in/photolist-Qv3rFw-34mt9F-a9Cmfy-5Ha3Zi-9msKdv-o3hgjr-hWpUte-4WMsJ1-KUQ8N-deshUb-vssBD-6CQci6-8AFCiD-zsJWT-nNfsgB-dPDwZJ-bn9JGn-5HtSXY-6CUhAL-a4UTXB-ugPum-KUPSo-fBLNm-6CUmpy-4WMsc9-8a7D3T-83KJev-6CQ2bK-nNusHJ-a78rQH-nw3NvT-7aq2qf-8wwBso-3nNceh-ugSKP-4mh4kh-bbeeqH-a7biME-q3PtTf-brFpgb-cg38zw-bXMZc-nJPELD-f58Lmo-bXMYG-bz8AAi-bxNtNT-bXMYi-bXMY6-bXMYv) The rendered output looks like this:\n"}, {"id": 102, "href": "/docs/pepgenxdev/shortcodes/markdown/italic/", "title": "Italic", "section": "<PERSON><PERSON>", "content": " Italic # To italicize text, add one asterisk or underscore before and after a word or phrase. To italicize the middle of a word for emphasis, add one asterisk without spaces around the letters.\nMarkdown HTML Rendered Output Italicized text is the *cat's meow*. Italicized text is the &lt;em&gt;cat's meow&lt;/em&gt;. Italicized text is the cat’s meow. Italicized text is the _cat's meow_. Italicized text is the &lt;em&gt;cat's meow&lt;/em&gt;. Italicized text is the cat’s meow. A*cat*meow A&lt;em&gt;cat&lt;/em&gt;meow Acatmeow Italic Best Practices # Markdown applications don&rsquo;t agree on how to handle underscores in the middle of a word. For compatibility, use asterisks to italicize the middle of a word for emphasis.\n✅&nbsp; Do this ❌&nbsp; Don't do this A*cat*meow A_cat_meow "}, {"id": 103, "href": "/docs/pepgenxdev/shortcodes/hugo/katex/", "title": "KaTeX", "section": "<PERSON>", "content": " KaTeX # KaTeX shortcode let you render math typesetting in markdown document. See KaTeX\nExample # {{&lt; katex display=true class=&#34;optional&#34; &gt;}} f(x) = \\int_{-\\infty}^\\infty\\hat f(\\xi)\\,e^{2 \\pi i \\xi x}\\,d\\xi {{&lt; /katex &gt;}} \\[f(x) = \\int_{-\\infty}^\\infty\\hat f(\\xi)\\,e^{2 \\pi i \\xi x}\\,d\\xi\\] Display Mode Example # Here is some inline example: \\(\\pi(x)\\) , rendered in the same line. And below is display example, having display: block\n\\[f(x) = \\int_{-\\infty}^\\infty\\hat f(\\xi)\\,e^{2 \\pi i \\xi x}\\,d\\xi\\] Text continues here.\n"}, {"id": 104, "href": "/docs/pepgenxdev/shortcodes/markdown/links/", "title": "Links", "section": "<PERSON><PERSON>", "content": " Links # To create a link, enclose the link text in brackets (e.g., [Duck Duck Go]) and then follow it immediately with the URL in parentheses (e.g., (https://duckduckgo.com)).\nMy favorite search engine is [Duck Duck Go](https://duckduckgo.com). The rendered output looks like this:\nMy favorite search engine is Duck Duck Go.\nNote: To link to an element on the same page, see linking to heading IDs. To create a link that opens in a new tab or window, see the section on link targets. Adding Titles # You can optionally add a title for a link. This will appear as a tooltip when the user hovers over the link. To add a title, enclose it in quotation marks after the URL.\nMy favorite search engine is [Duck Duck Go](https://duckduckgo.com &#34;The best search engine for privacy&#34;). The rendered output looks like this:\nMy favorite search engine is Duck Duck Go.\nURLs and Email Addresses # To quickly turn a URL or email address into a link, enclose it in angle brackets.\n&lt;https://www.markdownguide.org&gt; &lt;<EMAIL>&gt; The rendered output looks like this:\nhttps://www.markdownguide.org\n<EMAIL>\nFormatting Links # To emphasize links, add asterisks before and after the brackets and parentheses. To denote links as code, add backticks in the brackets.\nI love supporting the **[EFF](https://eff.org)**. This is the *[Markdown Guide](https://www.markdownguide.org)*. See the section on [`code`](#code). The rendered output looks like this:\nI love supporting the EFF.\nThis is the Markdown Guide.\nSee the section on code.\nReference-style Links # Reference-style links are a special kind of link that make URLs easier to display and read in Markdown. Reference-style links are constructed in two parts: the part you keep inline with your text and the part you store somewhere else in the file to keep the text easy to read.\nFormatting the First Part of the Link # The first part of a reference-style link is formatted with two sets of brackets. The first set of brackets surrounds the text that should appear linked. The second set of brackets displays a label used to point to the link you&rsquo;re storing elsewhere in your document.\nAlthough not required, you can include a space between the first and second set of brackets. The label in the second set of brackets is not case sensitive and can include letters, numbers, spaces, or punctuation.\nThis means the following example formats are roughly equivalent for the first part of the link:\n[hobbit-hole][1] [hobbit-hole] [1] Formatting the Second Part of the Link # The second part of a reference-style link is formatted with the following attributes:\nThe label, in brackets, followed immediately by a colon and at least one space (e.g., [label]: ). The URL for the link, which you can optionally enclose in angle brackets. The optional title for the link, which you can enclose in double quotes, single quotes, or parentheses. This means the following example formats are all roughly equivalent for the second part of the link:\n[1]: https://en.wikipedia.org/wiki/Hobbit#Lifestyle [1]: https://en.wikipedia.org/wiki/Hobbit#Lifestyle &quot;Hobbit lifestyles&quot; [1]: https://en.wikipedia.org/wiki/Hobbit#Lifestyle 'Hobbit lifestyles' [1]: https://en.wikipedia.org/wiki/Hobbit#Lifestyle (Hobbit lifestyles) [1]: &lt;https://en.wikipedia.org/wiki/Hobbit#Lifestyle&gt; &quot;Hobbit lifestyles&quot; [1]: &lt;https://en.wikipedia.org/wiki/Hobbit#Lifestyle&gt; 'Hobbit lifestyles' [1]: &lt;https://en.wikipedia.org/wiki/Hobbit#Lifestyle&gt; (Hobbit lifestyles) You can place this second part of the link anywhere in your Markdown document. Some people place them immediately after the paragraph in which they appear while other people place them at the end of the document (like endnotes or footnotes).\nAn Example Putting the Parts Together # Say you add a URL as a standard URL link to a paragraph and it looks like this in Markdown:\nIn a hole in the ground there lived a hobbit. Not a nasty, dirty, wet hole, filled with the ends of worms and an oozy smell, nor yet a dry, bare, sandy hole with nothing in it to sit down on or to eat: it was a [hobbit-hole](https://en.wikipedia.org/wiki/Hobbit#Lifestyle &#34;Hobbit lifestyles&#34;), and that means comfort. Though it may point to interesting additional information, the URL as displayed really doesn&rsquo;t add much to the existing raw text other than making it harder to read. To fix that, you could format the URL like this instead:\nIn a hole in the ground there lived a hobbit. Not a nasty, dirty, wet hole, filled with the ends of worms and an oozy smell, nor yet a dry, bare, sandy hole with nothing in it to sit down on or to eat: it was a [hobbit-hole][1], and that means comfort. [1]: &lt;https://en.wikipedia.org/wiki/Hobbit#Lifestyle&gt; &#34;Hobbit lifestyles&#34; In both instances above, the rendered output would be identical:\nIn a hole in the ground there lived a hobbit. Not a nasty, dirty, wet hole, filled with the ends of worms and an oozy smell, nor yet a dry, bare, sandy hole with nothing in it to sit down on or to eat: it was a hobbit-hole, and that means comfort.\nand the HTML for the link would be:\n&lt;a href=&#34;https://en.wikipedia.org/wiki/Hobbit#Lifestyle&#34; title=&#34;Hobbit lifestyles&#34;&gt;hobbit-hole&lt;/a&gt; Link Best Practices # Markdown applications don&rsquo;t agree on how to handle spaces in the middle of a URL. For compatibility, try to URL encode any spaces with %20. Alternatively, if your Markdown application supports HTML, you could use the a HTML tag.\n✅&nbsp; Do this ❌&nbsp; Don't do this [link](https://www.example.com/my%20great%20page)\n&lt;a href=\"https://www.example.com/my great page\"&gt;link&lt;/a&gt;\n[link](https://www.example.com/my great page)\nParentheses in the middle of a URL can also be problematic. For compatibility, try to URL encode the opening parenthesis (() with %28 and the closing parenthesis ()) with %29. Alternatively, if your Markdown application supports HTML, you could use the a HTML tag.\n✅&nbsp; Do this ❌&nbsp; Don't do this [a novel](https://en.wikipedia.org/wiki/The_Milagro_Beanfield_War_%28novel%29)\n&lt;a href=\"https://en.wikipedia.org/wiki/The_Milagro_Beanfield_War_(novel)\"&gt;a novel&lt;/a&gt;\n[a novel](https://en.wikipedia.org/wiki/The_Milagro_Beanfield_War_(novel)) "}, {"id": 105, "href": "/docs/pepgenxdev/shortcodes/markdown/lists/", "title": "Lists", "section": "<PERSON><PERSON>", "content": " Adding Elements in Lists # To add another element in a list while preserving the continuity of the list, indent the element four spaces or one tab, as shown in the following examples.\nTip: If things don't appear the way you expect, double check that you've indented the elements in the list four spaces or one tab. Paragraphs # * This is the first list item. * Here&#39;s the second list item. I need to add another paragraph below the second list item. * And here&#39;s the third list item. The rendered output looks like this:\nThis is the first list item.\nHere&rsquo;s the second list item.\nI need to add another paragraph below the second list item.\nAnd here&rsquo;s the third list item.\nBlockquotes # * This is the first list item. * Here&#39;s the second list item. &gt; A blockquote would look great below the second list item. * And here&#39;s the third list item. The rendered output looks like this:\nThis is the first list item.\nHere&rsquo;s the second list item.\nA blockquote would look great below the second list item.\nAnd here&rsquo;s the third list item.\nCode Blocks # Code blocks are normally indented four spaces or one tab. When they&rsquo;re in a list, indent them eight spaces or two tabs.\n1. Open the file. 2. Find the following code block on line 21: &lt;html&gt; &lt;head&gt; &lt;title&gt;Test&lt;/title&gt; &lt;/head&gt; 3. Update the title to match the name of your website. The rendered output looks like this:\nOpen the file.\nFind the following code block on line 21:\n&lt;html&gt; &lt;head&gt; &lt;title&gt;Test&lt;/title&gt; &lt;/head&gt; Update the title to match the name of your website.\nLists # You can nest an unordered list in an ordered list, or vice versa.\n1. First item 2. Second item 3. Third item - Indented item - Indented item 4. Fourth item The rendered output looks like this:\nFirst item Second item Third item Indented item Indented item Fourth item "}, {"id": 106, "href": "/docs/pepgenxdev/shortcodes/hugo/mermaid/", "title": "Mermaid Chart", "section": "<PERSON>", "content": " Mermaid Chart # MermaidJS is library for generating svg charts and diagrams from text.\nOverride Mermaid Initialization Config\nTo override the initialization config for Mermaid,\ncreate a mermaid.json file in your assets folder!\nExample # {{&lt; mermaid class=&#34;optional&#34; &gt;}} stateDiagram-v2 State1: The state with a note note right of State1 Important information! You can write notes. end note State1 --&gt; State2 note left of State2 : This is the note to the left. {{&lt; /mermaid &gt;}} stateDiagram-v2 State1: The state with a note note right of State1 Important information! You can write notes. end note State1 --> State2 note left of State2 : This is the note to the left. "}, {"id": 107, "href": "/docs/pepgenxdev/shortcodes/markdown/ordered_list/", "title": "Ordered Lists", "section": "<PERSON><PERSON>", "content": " Ordered List # To create an ordered list, add line items with numbers followed by periods. The numbers don&rsquo;t have to be in numerical order, but the list should start with the number one.\nMarkdown HTML Rendered Output 1. First item 2. Second item 3. Third item 4. Fourth item &lt;ol&gt;\n&nbsp;&nbsp;&lt;li&gt;First item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Second item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Third item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Fourth item&lt;/li&gt; &lt;/ol&gt; First item Second item Third item Fourth item 1. First item 1. Second item 1. Third item 1. Fourth item &lt;ol&gt;\n&nbsp;&nbsp;&lt;li&gt;First item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Second item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Third item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Fourth item&lt;/li&gt; &lt;/ol&gt; First item Second item Third item Fourth item 1. First item 8. Second item 3. Third item 5. Fourth item &lt;ol&gt;\n&nbsp;&nbsp;&lt;li&gt;First item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Second item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Third item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Fourth item&lt;/li&gt; &lt;/ol&gt; First item Second item Third item Fourth item 1. First item 2. Second item 3. Third item &nbsp;&nbsp;&nbsp;&nbsp;1. Indented item &nbsp;&nbsp;&nbsp;&nbsp;2. Indented item 4. Fourth item &lt;ol&gt;\n&nbsp;&nbsp;&lt;li&gt;First item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Second item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Third item &nbsp;&nbsp;&nbsp;&nbsp;&lt;ol&gt;\n&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li&gt;Indented item&lt;/li&gt; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li&gt;Indented item&lt;/li&gt; &nbsp;&nbsp;&nbsp;&nbsp;&lt;/ol&gt; &nbsp;&nbsp;&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Fourth item&lt;/li&gt; &lt;/ol&gt; First item Second item Third item Indented item Indented item Fourth item Ordered List Best Practices # CommonMark and a few other lightweight markup languages let you use a parenthesis ()) as a delimiter (e.g., 1) First item), but not all Markdown applications support this, so it isn’t a great option from a compatibility perspective. For compatibility, use periods only.\n✅&nbsp; Do this ❌&nbsp; Don't do this 1. First item\n2. Second item 1) First item\n2) Second item "}, {"id": 108, "href": "/docs/pepgenxdev/shortcodes/markdown/paragraphs/", "title": "Paragraphs", "section": "<PERSON><PERSON>", "content": " Paragraphs # To create paragraphs, use a blank line to separate one or more lines of text.\nMarkdown HTML Rendered Output I really like using Markdown.\nI think I'll use it to format all of my documents from now on. &lt;p&gt;I really like using Markdown.&lt;/p&gt;\n&lt;p&gt;I think I'll use it to format all of my documents from now on.&lt;/p&gt; I really like using Markdown.\nI think I'll use it to format all of my documents from now on.\nParagraph Best Practices # Don&rsquo;t indent paragraphs with spaces or tabs.\nNote: If you need to indent paragraphs in the output, see the section on how to indent (tab). ✅&nbsp; Do this ❌&nbsp; Don't do this Don't put tabs or spaces in front of your paragraphs.\nKeep lines left-aligned like this.\n&nbsp;&nbsp;&nbsp;&nbsp;This can result in unexpected formatting problems.\n&nbsp;&nbsp;Don't add tabs or spaces in front of paragraphs. "}, {"id": 109, "href": "/docs/pepgenxdev/shortcodes/markdown/strikethrough/", "title": "Strikethrough", "section": "<PERSON><PERSON>", "content": " Strikethrough # You can strikethrough words by putting a horizontal line through the center of them. The result looks like this. This feature allows you to indicate that certain words are a mistake not meant for inclusion in the document. To strikethrough words, use two tilde symbols (~~) before and after the words.\n~~The world is flat.~~ We now know that the world is round. The rendered output looks like this:\nThe world is flat. We now know that the world is round.\n"}, {"id": 110, "href": "/docs/pepgenxdev/shortcodes/markdown/tables/", "title": "Tables", "section": "<PERSON><PERSON>", "content": " Tables # To add a table, use three or more hyphens (---) to create each column&rsquo;s header, and use pipes (|) to separate each column. For compatibility, you should also add a pipe on either end of the row.\n| Syntax | Description | | ----------- | ----------- | | Header | Title | | Paragraph | Text | The rendered output looks like this:\nSyntax Description Header Title Paragraph Text Cell widths can vary, as shown below. The rendered output will look the same.\n| Syntax | Description | | --- | ----------- | | Header | Title | | Paragraph | Text | Tip: Creating tables with hyphens and pipes can be tedious. To speed up the process, try using the Markdown Tables Generator or AnyWayData Markdown Export. Build a table using the graphical interface, and then copy the generated Markdown-formatted text into your file. Alignment # You can align text in the columns to the left, right, or center by adding a colon (:) to the left, right, or on both side of the hyphens within the header row.\n| Syntax | Description | Test Text | | :--- | :----: | ---: | | Header | Title | Here&#39;s this | | Paragraph | Text | And more | The rendered output looks like this:\nSyntax Description Test Text Header Title Here’s this Paragraph Text And more Escaping Pipe Characters in Tables # You can display a pipe (|) character in a table by using its HTML character code (&amp;#124;).\n"}, {"id": 111, "href": "/docs/pepgenxdev/shortcodes/hugo/tabs/", "title": "Tabs", "section": "<PERSON>", "content": " Tabs # Tabs let you organize content by context, for example installation instructions for each supported platform.\n{{&lt; tabs &#34;uniqueid&#34; &gt;}} {{&lt; tab &#34;MacOS&#34; &gt;}} # MacOS Content {{&lt; /tab &gt;}} {{&lt; tab &#34;Linux&#34; &gt;}} # Linux Content {{&lt; /tab &gt;}} {{&lt; tab &#34;Windows&#34; &gt;}} # Windows Content {{&lt; /tab &gt;}} {{&lt; /tabs &gt;}} Example # MacOS MacOS # This is tab MacOS content.\nLorem markdownum insigne. Olympo signis Delphis! Retexi Nereius nova develat\nstringit, frustra Saturnius uteroque inter! Oculis non ritibus Telethusa\nprotulit, sed sed aere valvis inhaesuro Pallas animam: qui quid, ignes.\nMiseratus fonte Ditis conubia.\nLinux Linux # This is tab Linux content.\nLorem markdownum insigne. Olympo signis Delphis! Retexi Nereius nova develat\nstringit, frustra Saturnius uteroque inter! Oculis non ritibus Telethusa\nprotulit, sed sed aere valvis inhaesuro Pallas animam: qui quid, ignes.\nMiseratus fonte Ditis conubia.\nWindows Windows # This is tab Windows content.\nLorem markdownum insigne. Olympo signis Delphis! Retexi Nereius nova develat\nstringit, frustra Saturnius uteroque inter! Oculis non ritibus Telethusa\nprotulit, sed sed aere valvis inhaesuro Pallas animam: qui quid, ignes.\nMiseratus fonte Ditis conubia.\n"}, {"id": 112, "href": "/docs/pepgenxdev/shortcodes/markdown/task_list/", "title": "Task List", "section": "<PERSON><PERSON>", "content": " Task List # Task lists (also referred to as checklists and todo lists) allow you to create a list of items with checkboxes. In Markdown applications that support task lists, checkboxes will be displayed next to the content. To create a task list, add dashes (-) and brackets with a space ([ ]) in front of task list items. To select a checkbox, add an x in between the brackets ([x]).\n- [x] Write the press release - [ ] Update the website - [ ] Contact the media The rendered output looks like this:\nWrite the press release Update the website Contact the media "}, {"id": 113, "href": "/docs/pepgenxdev/shortcodes/markdown/unordered-lists/", "title": "Unordered Lists", "section": "<PERSON><PERSON>", "content": " Unordered Lists # To create an unordered list, add dashes (-), asterisks (*), or plus signs (+) in front of line items. Indent one or more items to create a nested list.\nMarkdown HTML Rendered Output - First item - Second item - Third item - Fourth item &lt;ul&gt;\n&nbsp;&nbsp;&lt;li&gt;First item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Second item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Third item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Fourth item&lt;/li&gt; &lt;/ul&gt; First item Second item Third item Fourth item * First item * Second item\n* Third item * Fourth item &lt;ul&gt;\n&nbsp;&nbsp;&lt;li&gt;First item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Second item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Third item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Fourth item&lt;/li&gt; &lt;/ul&gt; First item Second item Third item Fourth item + First item + Second item + Third item + Fourth item &lt;ul&gt;\n&nbsp;&nbsp;&lt;li&gt;First item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Second item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Third item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Fourth item&lt;/li&gt; &lt;/ul&gt; First item Second item Third item Fourth item - First item - Second item - Third item &nbsp;&nbsp;&nbsp;&nbsp;- Indented item &nbsp;&nbsp;&nbsp;&nbsp;- Indented item - Fourth item &lt;ul&gt; &nbsp;&nbsp;&lt;li&gt;First item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Second item&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Third item &nbsp;&nbsp;&nbsp;&nbsp;&lt;ul&gt; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li&gt;Indented item&lt;/li&gt; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li&gt;Indented item&lt;/li&gt; &nbsp;&nbsp;&nbsp;&nbsp;&lt;/ul&gt; &nbsp;&nbsp;&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;Fourth item&lt;/li&gt; &lt;/ul&gt; First item Second item Third item Indented item Indented item Fourth item Starting Unordered List Items With Numbers # If you need to start an unordered list item with a number followed by a period, you can use a backslash (\\) to escape the period.\nMarkdown HTML Rendered Output - 1968\\. A great year! - I think 1969 was second best. &lt;ul&gt;\n&nbsp;&nbsp;&lt;li&gt;1968. A great year!&lt;/li&gt; &nbsp;&nbsp;&lt;li&gt;I think 1969 was second best.&lt;/li&gt; &lt;/ul&gt; 1968. A great year! I think 1969 was second best. Unordered List Best Practices # Markdown applications don’t agree on how to handle different delimiters in the same list. For compatibility, don&rsquo;t mix and match delimiters in the same list — pick one and stick with it.\n✅&nbsp; Do this ❌&nbsp; Don't do this - First item\n- Second item\n- Third item\n- Fourth item + First item\n* Second item\n- Third item\n+ Fourth item "}]