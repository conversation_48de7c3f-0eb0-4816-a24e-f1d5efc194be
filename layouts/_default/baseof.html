<!DOCTYPE html>
<html lang="{{ default .Site.Language.Lang .Site.LanguageCode  }}" dir="{{ default "ltr" .Site.Language.LanguageDirection }}">
<head>
  {{ partial "docs/html-head" . }}
  {{ partial "docs/inject/head" . }}
</head>
<body dir="{{ default "ltr" .Site.Language.LanguageDirection }}">
  <input type="checkbox" class="hidden toggle" id="menu-control" />
  <input type="checkbox" class="hidden toggle" id="toc-control" />
  <main class="container flex">
    <aside class="book-menu">
      <div class="book-menu-content">
        {{ template "menu" . }} <!-- Left menu Content -->
      </div>
    </aside>

    <div class="book-page">
      <header class="book-header">
        {{ template "header" . }} <!-- Mobile layout header -->
      </header>

      {{ partial "docs/catalog-nav" . }}
      {{ partial "docs/inject/content-before" . }}
      {{ template "main" . }} <!-- Page Content -->
      {{ partial "docs/inject/content-after" . }}

      {{ partial "docs/tags" . }}

      <footer class="book-footer">
        {{ template "footer" . }} <!-- Footer under page content -->
        {{ partial "docs/inject/footer" . }}
      </footer>

      {{ template "comments" . }} <!-- Comments block -->

      <label for="menu-control" class="hidden book-menu-overlay"></label>
    </div>

    {{ if default true (default .Site.Params.BookToC .Params.BookToC) }}
    <aside class="book-toc">
      <div class="book-toc-content">
        <!--{{ template "toc" . }} -->
        <!-- Table of Contents -->
        {{ partial "docs/toc-fragments-to-html.html" . }}
      </div>
    </aside>
    {{ end }}
  </main>

  {{ partial "docs/inject/body" . }}
</body>
</html>

{{ define "menu" }}
  {{ partial "docs/menu" . }}
{{ end }}

{{ define "header" }}
  {{ partial "docs/header" . }}

  {{ if default true (default .Site.Params.BookToC .Params.BookToC) }}
  <aside class="hidden clearfix">
    {{ template "toc" . }}
  </aside>
  {{ end }}
{{ end }}

{{ define "footer" }}
  {{ partial "docs/footer" . }}
{{ end }}

{{ define "comments" }}
  {{ if and .Content (default true (default .Site.Params.BookComments .Params.BookComments)) }}
  <div class="book-comments">
    {{- partial "docs/comments" . -}}
  </div>
  {{ end }}
{{ end }}

{{ define "main" }}
  <article class="markdown book-article">
    {{- .Content -}}
  </article>
{{ end }}

{{ define "toc" }}
  {{ partial "docs/toc" . }}
{{ end }}
