{{ define "main" }}
  {{ range sort .Paginator.Pages }}
  <article class="markdown book-post">
    <h2>
      <a href="{{ .RelPermalink }}">{{ partial "docs/title.html" . }}</a>
    </h2>
    {{ partial "docs/post-meta" . }}
    <p>
      {{- .Summary -}}
      {{ if .Truncated }}
        <a href="{{ .RelPermalink }}">...</a> !!Taxo!!
      {{ end }}
    </p>
  </article>
  {{ end }}

  {{ template "_internal/pagination.html" . }}
{{ end }}

{{ define "toc" }}
  {{ partial "docs/taxonomy" . }}
{{ end }}
