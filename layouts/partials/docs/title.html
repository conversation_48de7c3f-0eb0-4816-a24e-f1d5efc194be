<!-- 
  Partial to generate page name from Title or File name.
  Accepts <PERSON> as context
-->
{{ $title := "" }}

{{ if .LinkTitle }}
  {{ $title = .LinkTitle }}
{{ else if .Title }}
  {{ $title = .Title }}
{{ else if and .IsSection .File }}
  {{ $title = path.Base .File.Dir | humanize | title }}
{{ else if and .IsPage .File }}
  {{ $title = .File.BaseFileName | humanize | title }}
{{ end }}

{{ return $title }}
