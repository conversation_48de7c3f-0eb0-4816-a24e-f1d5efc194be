<nav>
  <ul>
  {{ range $term, $_ := .Site.Taxonomies }}
    {{ with $.Site.GetPage (printf "/%s" $term | urlize) }}
    <li class="book-section-flat">
      <strong>{{ .Title | title }}</strong>
      <ul>
      {{ range .Pages }}
        <li class="flex justify-between">
          <a href="{{ .RelPermalink }}">{{ .Title }}</a>
          <span>{{ len .Pages }}</span>
        </li>
      {{ end }}
      </ul>
    </li>
    {{ end }}
  {{ end }}
  </ul>
</nav>
