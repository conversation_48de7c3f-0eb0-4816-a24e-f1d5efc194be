<div class="flex flex-wrap justify-between" style="font-size: 80%;">
{{ if $.GitInfo }}
<div>
    {{- $date := partial "docs/date" (dict "Date" .GitInfo.AuthorDate.Local "Format" .Site.Params.BookDateFormat) -}}
    {{- $commitPath := default "commit" .Site.Params.BookCommitPath -}}
      LastMod: {{ $date }}<br>
      By: {{ .GitInfo.AuthorName }}<br>
      Commit: {{ .GitInfo.Hash }}<br>
  </div>
{{ end }}
</div>