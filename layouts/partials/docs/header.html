<div class="flex align-center justify-between">
  <label for="menu-control">
    <img src="{{ "svg/menu.svg" | relURL }}" class="book-icon" alt="Menu" />
  </label>

  <strong>{{ partial "docs/title" . }}</strong>

  <label for="toc-control">
    {{ if default true (default .Site.Params.BookToC .Params.BookToC) }}
    <img src="{{ "svg/toc.svg" | relURL }}" class="book-icon" alt="Table of Contents" />
    {{ end }}
  </label>
</div>