{{ $bookSection := default "docs" .Site.Params.BookSection }}
<div class="catalog-nav">
  <ul>
    {{ with .Site.GetPage $bookSection }}
      {{ range (where .Pages "Kind" "section") }}
        {{ $section := . }}
        {{ $sectionPath := .File.Path }}
        {{ $sectionPathParts := split $sectionPath "/" }}
        {{ $sectionID := index $sectionPathParts 1 }}
        {{ $isCurrent := false }}

        {{ if $.File }}
          {{ $currentFilePath := $.File.Path }}
          {{ $currentPathParts := split $currentFilePath "/" }}

          {{ if ge (len $currentPathParts) 2 }}
            {{ if eq (index $currentPathParts 1) $sectionID }}
              {{ $isCurrent = true }}
            {{ end }}
          {{ end }}
        {{ else }}
          {{ $currentURL := $.RelPermalink }}
          {{ $sectionURL := $section.RelPermalink }}
          {{ if hasPrefix $currentURL $sectionURL }}
            {{ $isCurrent = true }}
          {{ end }}
        {{ end }}

        {{ $hasSubfolders := gt (len (where .Pages "Kind" "section")) 0 }}
        <li class="{{ if $isCurrent }}active{{ end }} {{ if $hasSubfolders }}has-dropdown{{ end }}" data-section="{{ $sectionID }}">
          <a href="{{ .RelPermalink }}" class="main-link">{{ .Title }}</a>
          {{ if $hasSubfolders }}
            <div class="dropdown-content">
              {{ range (where .Pages "Kind" "section") }}
                {{ $subSectionPath := .File.Path }}
                {{ $subSectionPathParts := split $subSectionPath "/" }}
                {{ $isSubCurrent := false }}

                {{ if $.File }}
                  {{ $currentFilePath := $.File.Path }}
                  {{ $currentPathParts := split $currentFilePath "/" }}

                  {{ if ge (len $currentPathParts) 3 }}
                    {{ if and (eq (index $currentPathParts 1) $sectionID) (eq (index $currentPathParts 2) (index $subSectionPathParts 2)) }}
                      {{ $isSubCurrent = true }}
                    {{ end }}
                  {{ end }}
                {{ end }}

                <a href="{{ .RelPermalink }}" class="{{ if $isSubCurrent }}active{{ end }}">{{ .Title }}</a>
              {{ end }}
            </div>
          {{ end }}
        </li>
      {{ end }}
    {{ end }}
  </ul>
</div>
<style>
  .catalog-nav {
    padding: 5px 1rem 0.5rem 1rem;
    border-bottom: 1px solid var(--gray-200);
    background-color: var(--body-background);
    width: 100%;
    color: var(--body-font-color);
    font-family: inherit;
    letter-spacing: 0.33px;
    font-weight: var(--body-font-weight, 400);
    position: sticky;
    top: 0;
    z-index: 100;
  }
  .catalog-nav ul {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    list-style: none;
    margin: 0;
    padding: 0;
  }
  .catalog-nav li {
    margin: 0;
    position: relative;
  }
  .catalog-nav li a {
    color: var(--body-font-color);
  }
  /* Active state for main menu items - only the main link should be bold */
  .catalog-nav li.active-section a.main-link {
    font-weight: bold;
    color: black;
  }

  /* Active state for dropdown items - no bold, just background highlight */
  .catalog-nav .dropdown-content a.active {
    background-color: rgba(0,0,0,0.05);
  }

  /* Hover state for non-active items - should not make them bold */
  .catalog-nav li:not(.active-section) a.main-link:hover {
    font-weight: normal;
  }
  .catalog-nav li a:hover {
    opacity: 0.5;
  }

  /* Dropdown styles */
  .catalog-nav li.has-dropdown {
    cursor: pointer;
    position: relative;
  }

  /* Removed dropdown arrow */

  .catalog-nav .dropdown-content {
    display: none;
    position: absolute;
    background-color: var(--body-background);
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 101;
    border-radius: 4px;
    padding: 6px 0;
    top: calc(100% - 5px); /* Position it closer to the parent */
    left: 0;
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.2s ease, transform 0.2s ease;
    line-height: 1.4; /* Match the line height of TOC */
  }

  /* When dropdown is shown, animate it in */
  .catalog-nav .dropdown-content[style*="display: block"] {
    opacity: 1;
    transform: translateY(0);
  }

  .catalog-nav .dropdown-content a {
    color: var(--body-font-color);
    padding: 4px 16px;
    text-decoration: none;
    display: block;
    text-align: left;
    font-size: 0.85rem; /* Match the font size used in pepgenx-toc */
  }

  .catalog-nav .dropdown-content a:hover {
    background-color: rgba(0,0,0,0.05);
    opacity: 1;
  }

  /* Show dropdown on hover for desktop */
  @media (hover: hover) and (pointer: fine) {
    .catalog-nav li.has-dropdown {
      padding-bottom: 10px; /* Add padding to create hover area */
    }

    .catalog-nav li.has-dropdown:hover .dropdown-content {
      display: block;
    }

    /* Add a pseudo-element to bridge any gap between the menu item and dropdown */
    .catalog-nav li.has-dropdown::after {
      content: '';
      position: absolute;
      height: 10px; /* Match the padding-bottom */
      width: 100%;
      bottom: 0;
      left: 0;
    }
  }

  /* Responsive styles for mobile */
  @media screen and (max-width: 768px) {
    .catalog-nav .dropdown-content {
      position: static;
      box-shadow: none;
      border-radius: 0;
      margin-top: 5px;
      padding: 0 0 0 15px;
      background-color: transparent;
    }

    .catalog-nav .dropdown-content a {
      padding: 4px 10px;
      font-size: 0.85rem; /* Keep consistent with desktop */
    }
  }
</style>