{{ $bookSection := default "docs" .Site.Params.BookSection  }}
{{ if eq $bookSection "*" }}
  {{ $bookSection = "/" }}{{/* Backward compatibility */}}
{{ end }}

{{ with .Site.GetPage $bookSection }}
  {{ $currentPage := $ }}
  {{ $currentDir := "" }}
  {{ $rootSections := .Pages }}

  {{ if $currentPage.File }}
    {{ $currentFilePath := $currentPage.File.Path }}
    {{ range $rootSections }}
      {{ if eq .Kind "section" }}
        {{ $sectionPath := .File.Path }}
        {{ $sectionPathParts := split $sectionPath "/" }}
        {{ $currentPathParts := split $currentFilePath "/" }}

        {{ if ge (len $currentPathParts) 2 }}
          {{ if eq (index $currentPathParts 1) (index $sectionPathParts 1) }}
            {{ $currentDir = . }}
          {{ end }}
        {{ end }}
      {{ end }}
    {{ end }}
  {{ end }}

  {{ if $currentDir }}
    {{ template "book-section-children" (dict "Section" $currentDir "CurrentPage" $currentPage) }}
  {{ else }}
    {{ template "book-section-children" (dict "Section" . "CurrentPage" $currentPage) }}
  {{ end }}
{{ end }}

{{ define "book-section-children" }}{{/* (dict "Section" .Section "CurrentPage" .CurrentPage) */}}
  <ul>
    <!-- Display the section's _index.md page only if it's the top-level folder -->
    {{ if and .Section.File .Section.Content }}
      {{ $sectionPath := .Section.File.Path }}
      {{ $pathParts := split $sectionPath "/" }}
      {{ $isTopLevel := eq (len $pathParts) 2 }} <!-- Top level has format like "content/_index.md" -->
      {{ if $isTopLevel }}
        <li>
          {{ template "book-page-link" (dict "Page" .Section "CurrentPage" $.CurrentPage) }}
        </li>
      {{ end }}
    {{ end }}

    {{ range (where .Section.Pages "Params.bookhidden" "ne" true) }}
      {{ if .IsSection }}
        <li {{- if .Params.BookFlatSection }} class="book-section-flat" {{ end -}}>
          {{ template "book-page-link" (dict "Page" . "CurrentPage" $.CurrentPage) }}
          {{ template "book-section-children" (dict "Section" . "CurrentPage" $.CurrentPage) }}
        </li>
      {{ else if and .IsPage .Content }}
        <!-- Only display regular pages, not _index.md files in subdirectories -->
        {{ $filePath := .File.Path }}
        {{ $fileName := index (last 1 (split $filePath "/")) 0 }}
        {{ if ne $fileName "_index.md" }}
          <li>
            {{ template "book-page-link" (dict "Page" . "CurrentPage" $.CurrentPage) }}
          </li>
        {{ end }}
      {{ end }}
    {{ end }}
  </ul>
{{ end }}

{{ define "book-page-link" }}{{/* (dict "Page" .Page "CurrentPage" .CurrentPage) */}}
  {{ $current := eq .CurrentPage .Page }}
  {{ $ancestor := .Page.IsAncestor .CurrentPage }}
  {{ if .Page.Params.BookCollapseSection }}
    <input type="checkbox" id="section-{{ md5 .Page }}" class="toggle" {{ if or $current $ancestor }}checked{{ end }} />
    <label for="section-{{ md5 .Page }}" class="flex justify-between">
      <a {{ if .Page.Content }}href="{{ .Page.RelPermalink }}"{{ else }}role="button"{{ end }} class="{{ if $current }}active{{ end }}">
        {{- partial "docs/title" .Page -}}
      </a>
    </label>
  {{ else if .Page.Params.BookHref }}
    <a href="{{ .Page.Params.BookHref }}" class="{{ if $current }}active{{ end }}" target="_blank" rel="noopener">
      {{- partial "docs/title" .Page -}}
    </a>
  {{ else if .Page.Content }}
    <a href="{{ .Page.RelPermalink }}" class="{{ if $current }}active{{ end }}">
      {{- partial "docs/title" .Page -}}
    </a>
  {{ else }}
    <span>{{- partial "docs/title" .Page -}}</span>
  {{ end }}
{{ end }}
