<script>
document.addEventListener('DOMContentLoaded', function() {
  // Variable to store timeout for dropdown hiding
  let dropdownTimeout;

  // Store the current path to determine active section
  const currentPath = window.location.pathname;

  // Check if we're on the main page
  const isMainPage = currentPath === '/' || currentPath === '/index.html';

  // Function to check if a URL is part of a section
  function isUrlInSection(url, sectionUrl) {
    // If we're on the main page, no section should be active
    if (isMainPage) {
      return false;
    }

    // Remove trailing slashes for comparison
    const cleanUrl = url.endsWith('/') ? url.slice(0, -1) : url;
    const cleanSectionUrl = sectionUrl.endsWith('/') ? sectionUrl.slice(0, -1) : sectionUrl;

    // Check if URL starts with section URL
    return cleanUrl === cleanSectionUrl || cleanUrl.startsWith(cleanSectionUrl + '/');
  }
  // Add click event listeners to all TOC links
  const tocLinks = document.querySelectorAll('.pepgenx-toc a, .book-toc-content a');

  tocLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();

      // Get the target element
      const targetId = this.getAttribute('href').split('#')[1];
      const targetElement = document.getElementById(targetId);

      if (targetElement) {
        // Get the height of the sticky catalog-nav
        const catalogNav = document.querySelector('.catalog-nav');
        const offset = catalogNav ? catalogNav.offsetHeight + 20 : 20; // Add extra padding

        // Calculate the adjusted scroll position
        const elementPosition = targetElement.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - offset;

        // Scroll to the adjusted position
        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });

        // Update the URL hash without scrolling
        history.pushState(null, null, this.getAttribute('href'));
      }
    });
  });

  // Also handle initial page load with hash in URL
  if (window.location.hash) {
    setTimeout(function() {
      const targetId = window.location.hash.substring(1);
      const targetElement = document.getElementById(targetId);

      if (targetElement) {
        const catalogNav = document.querySelector('.catalog-nav');
        const offset = catalogNav ? catalogNav.offsetHeight + 20 : 20;

        const elementPosition = targetElement.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - offset;

        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });
      }
    }, 300); // Small delay to ensure everything is loaded
  }

  // Handle catalog-nav dropdown menu functionality
  const dropdownItems = document.querySelectorAll('.catalog-nav li.has-dropdown');
  const navItems = document.querySelectorAll('.catalog-nav li');

  // Check if current URL is in any section and mark it as active
  navItems.forEach(item => {
    const mainLink = item.querySelector('.main-link');
    if (mainLink && isUrlInSection(currentPath, mainLink.getAttribute('href'))) {
      item.classList.add('active-section');
    }
  });

  // For desktop devices, add hover handling with delay
  if (!('ontouchstart' in window)) {
    dropdownItems.forEach(item => {
      const dropdownContent = item.querySelector('.dropdown-content');
      const isActiveSection = item.classList.contains('active-section');

      // Mouse enter - show dropdown
      item.addEventListener('mouseenter', function() {
        // Clear any existing timeout
        if (dropdownTimeout) {
          clearTimeout(dropdownTimeout);
          dropdownTimeout = null;
        }

        // Close all other dropdowns first
        document.querySelectorAll('.catalog-nav .dropdown-content').forEach(dropdown => {
          if (dropdown !== dropdownContent) {
            dropdown.style.display = 'none';
          }
        });
        document.querySelectorAll('.catalog-nav li.has-dropdown').forEach(li => {
          if (li !== item && !li.classList.contains('active-section')) {
            li.classList.remove('active');
          }
        });

        // Show this dropdown with animation
        dropdownContent.style.display = 'block';
        // Flag to track if mouse is over the item
        item.dataset.mouseOver = 'true';
        // We don't need to add the active class anymore since we don't want non-active items to appear bold
      });

      // Mouse leave - hide dropdown with delay for all sections
      item.addEventListener('mouseleave', function() {
        // Set flag that mouse is no longer over the item
        item.dataset.mouseOver = 'false';

        // Set timeout to hide dropdown
        dropdownTimeout = setTimeout(function() {
          // Only hide if mouse is not over the dropdown (which would have cleared the timeout)
          dropdownContent.style.display = 'none';
          if (!isActiveSection) {
            item.classList.remove('active');
          }
        }, 300); // 300ms delay before hiding
      });

      // When entering the dropdown content, clear the timeout
      dropdownContent.addEventListener('mouseenter', function() {
        // Set flag that mouse is over the dropdown
        dropdownContent.dataset.mouseOver = 'true';

        // Clear any hide timeout
        if (dropdownTimeout) {
          clearTimeout(dropdownTimeout);
          dropdownTimeout = null;
        }
      });

      // When leaving the dropdown content, hide it with delay for all sections
      dropdownContent.addEventListener('mouseleave', function() {
        // Set flag that mouse is no longer over the dropdown
        dropdownContent.dataset.mouseOver = 'false';

        // Only hide if mouse is not over the parent item
        if (item.dataset.mouseOver === 'false') {
          dropdownTimeout = setTimeout(function() {
            dropdownContent.style.display = 'none';
            if (!isActiveSection) {
              item.classList.remove('active');
            }
          }, 300); // 300ms delay before hiding
        }
      });

      // Add click handlers for dropdown links to maintain active state
      const dropdownLinks = dropdownContent.querySelectorAll('a');
      dropdownLinks.forEach(link => {
        link.addEventListener('click', function() {
          // Store the section ID in localStorage to maintain state across page loads
          localStorage.setItem('activeSection', item.dataset.section);
        });
      });

      // Main link click also stores the section
      if (mainLink = item.querySelector('.main-link')) {
        mainLink.addEventListener('click', function() {
          // Store the section ID in localStorage
          const href = mainLink.getAttribute('href');

          // If this link points to the main page, clear the active section
          if (href === '/' || href === '/index.html') {
            localStorage.removeItem('activeSection');
          } else {
            localStorage.setItem('activeSection', item.dataset.section);
          }

          // Allow navigation on first click (don't prevent default)
          // The browser will navigate to the href automatically
        });
      }
    });
  }

  // For mobile/touch devices, add click handling for dropdown menus
  dropdownItems.forEach(item => {
    const mainLink = item.querySelector('.main-link');
    const dropdownContent = item.querySelector('.dropdown-content');
    const isActiveSection = item.classList.contains('active-section');

    // Add click event for touch devices
    mainLink.addEventListener('click', function(e) {
      // Store the section ID in localStorage
      const href = mainLink.getAttribute('href');

      // If this link points to the main page, clear the active section
      if (href === '/' || href === '/index.html') {
        localStorage.removeItem('activeSection');
      } else {
        localStorage.setItem('activeSection', item.dataset.section);
      }

      // Allow navigation on first click (don't prevent default)
      // The browser will navigate to the href automatically
    });

    // Add click handlers for dropdown links to maintain active state on mobile
    if ('ontouchstart' in window) {
      const dropdownLinks = dropdownContent.querySelectorAll('a');
      dropdownLinks.forEach(link => {
        link.addEventListener('click', function() {
          // Store the section ID in localStorage to maintain state across page loads
          const href = link.getAttribute('href');

          // If this link points to the main page, clear the active section
          if (href === '/' || href === '/index.html') {
            localStorage.removeItem('activeSection');
          } else {
            localStorage.setItem('activeSection', item.dataset.section);
          }
        });
      });
    }
  });

  // Close dropdowns when clicking outside
  document.addEventListener('click', function(e) {
    if (!e.target.closest('.catalog-nav li.has-dropdown')) {
      document.querySelectorAll('.catalog-nav .dropdown-content').forEach(dropdown => {
        dropdown.style.display = 'none';
      });
      document.querySelectorAll('.catalog-nav li.has-dropdown').forEach(li => {
        if (!li.classList.contains('active-section')) {
          li.classList.remove('active');
        }
      });
    }
  });

  // If we're on the main page, clear any stored active section
  if (isMainPage) {
    localStorage.removeItem('activeSection');
    // Make sure no sections are marked as active
    document.querySelectorAll('.catalog-nav li.active-section').forEach(li => {
      li.classList.remove('active-section');
    });
  } else {
    // Check for stored active section from previous page
    const storedActiveSection = localStorage.getItem('activeSection');
    if (storedActiveSection) {
      const sectionItem = document.querySelector(`.catalog-nav li[data-section="${storedActiveSection}"]`);
      if (sectionItem) {
        // If we're not already in this section, mark it as active
        if (!sectionItem.classList.contains('active-section')) {
          // Clear any existing active sections first
          document.querySelectorAll('.catalog-nav li.active-section').forEach(li => {
            li.classList.remove('active-section');
          });
          sectionItem.classList.add('active-section');
        }
        // We no longer automatically show the dropdown for the active section
      }
    }
  }
});
</script>