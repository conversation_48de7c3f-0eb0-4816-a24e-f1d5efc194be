<!-- Merge home and current page translations -->
{{ $bookTranslatedOnly := default false .Site.Params.BookTranslatedOnly }}
{{ $translations := dict }}
{{ if (eq $bookTranslatedOnly false ) }}
  {{ range .Site.Home.Translations }}
    {{ $translations = merge $translations (dict .Language.Lang .) }}
  {{ end }}
{{ end }}
{{ range .Translations }}
  {{ $translations = merge $translations (dict .Language.Lang .) }}
{{ end }}

<ul class="book-languages">
  <li>
    <input type="checkbox" id="languages" class="toggle" />
    <label for="languages" class="flex justify-between">
      <a role="button" class="flex align-center">
        <img src="{{ "svg/translate.svg" | relURL }}" class="book-icon" alt="Languages" />
        {{ $.Site.Language.LanguageName }}
      </a>
    </label>

    <ul>
      {{ range .Site.Languages }}{{ with index $translations .Lang }}
      <li>
        <a href="{{ .Permalink }}">
          {{ .Language.LanguageName }}
        </a>
      </li>
      {{ end }}{{ end }}
    </ul>
  </li>
</ul>
