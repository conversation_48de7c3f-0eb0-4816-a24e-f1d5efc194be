<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="{{ default .Summary .Description }}">
<meta name="theme-color" media="(prefers-color-scheme: light)" content="#ffffff">
<meta name="theme-color" media="(prefers-color-scheme: dark)" content="#343a40">
<meta name="color-scheme" content="light dark">

{{- with .Page.Params.BookHref -}}
  <meta http-equiv="Refresh" content="0; url='{{ . }}'" />
{{- end -}}

{{- template "_internal/opengraph.html" . -}}

<title>{{ partial "docs/html-head-title" . }}</title>

{{- $manifest := resources.Get "manifest.json" | resources.ExecuteAsTemplate "manifest.json" . }}
<link rel="manifest" href="{{ $manifest.RelPermalink }}">
<link rel="icon" href="{{ .Site.Params.BookFavicon | default "favicon.png" | relURL }}" >
<link rel="canonical" href="{{ .Permalink }}">

{{- range .Translations }}
  <link rel="alternate" hreflang="{{ default .Language.Lang .Site.LanguageCode }}" href="{{ .Permalink }}" title="{{ partial "docs/title" . }}">
{{- end -}}

<!-- Theme stylesheet, you can customize scss by creating `assets/custom.scss` in your website -->
{{- $styles := resources.Get "book.scss" | resources.ExecuteAsTemplate "book.scss" . | resources.ToCSS | resources.Minify | resources.Fingerprint }}
<link rel="stylesheet" href="{{ $styles.RelPermalink }}" {{ template "integrity" $styles }}>

{{- if default true .Site.Params.BookSearch -}}
  {{- $searchJSFile := printf "%s.search.js" .Language.Lang }}
  {{- $searchJS := resources.Get "search.js" | resources.ExecuteAsTemplate $searchJSFile . | resources.Minify | resources.Fingerprint }}
  <script defer src="{{ "fuse.min.js" | relURL }}"></script>
  <script defer src="{{ $searchJS.RelPermalink }}" {{ template "integrity" $searchJS }}></script>
{{ end -}}

{{- if .Site.Params.BookServiceWorker -}}
  {{- $swJS := resources.Get "sw-register.js" | resources.ExecuteAsTemplate "sw.js" . | resources.Minify | resources.Fingerprint }}
  <script defer src="{{ $swJS.RelPermalink }}" {{ template "integrity" $swJS }}></script>
{{ end -}}

{{- template "_internal/google_analytics.html" . -}}

<!-- RSS -->
{{- with .OutputFormats.Get "rss" -}}
  {{ printf `<link rel="%s" type="%s" href="%s" title="%s" />` .Rel .MediaType.Type .Permalink $.Site.Title | safeHTML }}
{{ end -}}

{{ "<!--" | safeHTML }}
Made with Book Theme
https://github.com/alex-shpak/hugo-book
{{ "-->" | safeHTML }}

{{- define "integrity" -}}
  {{- if (urls.Parse .Permalink).Host -}}
    integrity="{{ .Data.Integrity }}" crossorigin="anonymous"
  {{- end -}}
{{- end -}}
