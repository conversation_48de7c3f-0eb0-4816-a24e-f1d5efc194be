{{- if not (.Page.Scratch.Get "katex") -}}
<!-- Include katex only first time -->
<link rel="stylesheet" href="{{ "katex/katex.min.css" | relURL }}" />
<script defer src="{{ "katex/katex.min.js" | relURL }}"></script>
<script defer src="{{ "katex/auto-render.min.js" | relURL }}" onload="renderMathInElement(document.body);"></script>
{{- .Page.Scratch.Set "katex" true -}}
{{- end -}}

<span {{- with .Get "class" }} class="{{ . }}"{{ end }}>
  {{ with .Get "display" }}\[{{else}}\({{end}}
  {{- trim .Inner "\n" -}}
  {{ with .Get "display" }}\]{{else}}\){{end}}
</span>
